<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.wonhigh.baize</groupId>
        <artifactId>ts-retail-baize</artifactId>
        <version>1.0.28-SNAPSHOT</version>
    </parent>
    <artifactId>ts-retail-baize-api</artifactId>
    <name>ts-retail-baize-api</name>
    <url>http://maven.apache.org</url>


    <dependencies>
        <dependency>
            <groupId>cn.wonhigh.baize</groupId>
            <artifactId>ts-retail-baize-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-domain</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>