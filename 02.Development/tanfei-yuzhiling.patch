Subject: [PATCH] Merge branch 'develop' of http://c7n-gitlab.ts.wonhigh.cn/operation-isp/ts-retail-baize.git into develop
调整单审核更新剩余锁库数量调整
调整锁库数量不可为0
---
Index: 02.Development/ts-retail-baize-app/admin/src/pages/inventoryActiveLock/children/SaveDtlModal.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-app/admin/src/pages/inventoryActiveLock/children/SaveDtlModal.tsx b/02.Development/ts-retail-baize-app/admin/src/pages/inventoryActiveLock/children/SaveDtlModal.tsx
--- a/02.Development/ts-retail-baize-app/admin/src/pages/inventoryActiveLock/children/SaveDtlModal.tsx	(revision 85e0c6b639be24b0b28dcbc07536d84b7af0aa63)
+++ b/02.Development/ts-retail-baize-app/admin/src/pages/inventoryActiveLock/children/SaveDtlModal.tsx	(revision eafbaf9787455f94af332548581db9190c3022bb)
@@ -1,6 +1,7 @@
 
 import React, { useEffect } from 'react';
 import { Col, Form, Input, InputNumber, Modal, Row } from "antd";
+import { validatePositiveInt } from '../../../utils/index'
 import api from "../../../domain/service/api";
 import { Combogrid } from "@mars/core";
 
@@ -13,24 +14,6 @@
   vstoreCode?: string
 }
 
-  // 校验正整数
- function validatePositiveInt(value) {
-  if (value === '' || value === null) return false; // 处理空值
-  
-  // 核心正则：排除前导负号和小数点
-  const regex = /^[0-9]\d*$/;
-  
-  // 转为字符串处理数字类型
-  const strVal = String(value).trim();
-  
-  // 排除空值、负数、小数、前导零和特殊符号
-  return regex.test(strVal) && 
-         !strVal.includes('.') && 
-         !strVal.includes('-') && 
-         !strVal.includes(' ');
-}
-
-
 
 const SaveDtlModal = (props: SaveDtlModalProps) => {
 
@@ -232,7 +215,7 @@
               }
             ]} >
             <InputNumber
-              min={0}
+              min={1}
               step={1}
               type={"number"}
               style={{ width: '100%' }}
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/business/active/adjust/SingleActiveLockAdjustProcess.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/business/active/adjust/SingleActiveLockAdjustProcess.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/business/active/adjust/SingleActiveLockAdjustProcess.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/business/active/adjust/SingleActiveLockAdjustProcess.java	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/business/active/adjust/SingleActiveLockAdjustProcess.java	(revision 0d2ae91680445afb52edfeba9d4fcd0f15a314f5)
@@ -126,10 +126,7 @@
      */
     private void addAuditAdjust() {
         Pair<Boolean, List<InventoryActiveLockDtl>> pair = icsActiveLockAdjustManager.auditActiveLockAdjust(
-                adjustDtlList,
-                this::updateActiveLockDtl,
-                this::occupiedAdjustInventory
-        );
+                adjustDtlList, this::updateActiveLockDtl, this::occupiedAdjustInventory);
         LOGGER.info("更新活动锁库单和预占库存, result={}", pair.getLeft());
         Assert.isTrue(pair.getLeft(), "活动锁库调整单审核失败");
     }
@@ -139,10 +136,7 @@
      */
     private void subtractAuditAdjust() {
         Pair<Boolean, List<InventoryActiveLockDtl>> pair = icsActiveLockAdjustManager.auditActiveLockAdjust(
-                adjustDtlList,
-                this::updateActiveLockDtl,
-                this::releaseAdjustInventory
-        );
+                adjustDtlList, this::updateActiveLockDtl, this::releaseAdjustInventory);
         LOGGER.info("更新活动锁库单和释放库存, result={}", pair.getLeft());
         Assert.isTrue(pair.getLeft(), "活动锁库调整单审核失败");
     }
@@ -185,10 +179,10 @@
         Map<String, InventoryActiveLockDtl> groupMap = activeLockDtlList.stream().collect(Collectors.toMap(InventoryActiveLockDtl::getUniqueKey, Function.identity(), (d1, d2) ->  d2));
         List<InventoryActiveLockDtl> updateLockDtlList = adjustDtlList.stream().filter(dtl -> groupMap.containsKey(dtl.getUniqueKey())).map(dtl -> {
             InventoryActiveLockDtl activeLockDtl = groupMap.get(dtl.getUniqueKey());
-            activeLockDtl.setBalanceLockQty(dtl.getAdjustQty() * adjust.getAdjustType() + activeLockDtl.getBalanceLockQty());
+            activeLockDtl.setBalanceLockQty(dtl.getAdjustQty() * adjust.getAdjustType());
             return activeLockDtl;
         }).collect(Collectors.toList()) ;
-        inventoryActiveLockDtlManager.batchSaveOrUpdateDtl(updateLockDtlList);
+        inventoryActiveLockDtlManager.batchUpdateDtlForAudit(updateLockDtlList);
         return updateLockDtlList;
     }
 
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/IInventoryActiveLockDtlManager.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/IInventoryActiveLockDtlManager.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/IInventoryActiveLockDtlManager.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/IInventoryActiveLockDtlManager.java	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/IInventoryActiveLockDtlManager.java	(revision 0d2ae91680445afb52edfeba9d4fcd0f15a314f5)
@@ -36,4 +36,10 @@
      * @return
      */
     List<InventoryActiveLockDtl> selectByAdjustBillNo(String billNo, Integer syncStatus);
+
+    /**
+     * 审核成功更新剩余锁库
+     * @param updateLockDtlList
+     */
+	int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList);
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/InventoryActiveLockDtlManager.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/InventoryActiveLockDtlManager.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/InventoryActiveLockDtlManager.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/InventoryActiveLockDtlManager.java	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/InventoryActiveLockDtlManager.java	(revision 0d2ae91680445afb52edfeba9d4fcd0f15a314f5)
@@ -72,4 +72,9 @@
 		return service.selectByAdjustBillNo(params);
 	}
 
+	@Override
+	public int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList) {
+		return service.batchUpdateDtlForAudit(updateLockDtlList);
+	}
+
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/InventoryActiveLockDtlRepository.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/InventoryActiveLockDtlRepository.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/InventoryActiveLockDtlRepository.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/InventoryActiveLockDtlRepository.java	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/InventoryActiveLockDtlRepository.java	(revision 0d2ae91680445afb52edfeba9d4fcd0f15a314f5)
@@ -30,4 +30,6 @@
     void batchUpdateByBillNo(@Param("billNo") String billNo, @Param("status") Integer syncStatus);
 
     List<Map<String, Object>> selectStatusByBillNo(@Param("billNo") String billNo);
+
+	int batchUpdateDtlForAudit(@Param("list") List<InventoryActiveLockDtl> updateLockDtlList);
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/IInventoryActiveLockDtlService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/IInventoryActiveLockDtlService.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/IInventoryActiveLockDtlService.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/IInventoryActiveLockDtlService.java	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/IInventoryActiveLockDtlService.java	(revision 0d2ae91680445afb52edfeba9d4fcd0f15a314f5)
@@ -1,7 +1,6 @@
 /**  **/
 package cn.wonhigh.baize.service.gms;
 
-import cn.hutool.core.map.MapBuilder;
 import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
 import topmall.framework.service.IService;
 
@@ -55,4 +54,11 @@
 
     List<Map<String, Object>> selectStatusByBillNo(String billNo);
 
+    /**
+     * 审核成功更新剩余锁库数量
+     * @param updateLockDtlList
+     * @return
+     */
+	int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList);
+
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/InventoryActiveLockDtlService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/InventoryActiveLockDtlService.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/InventoryActiveLockDtlService.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/InventoryActiveLockDtlService.java	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/InventoryActiveLockDtlService.java	(revision 0d2ae91680445afb52edfeba9d4fcd0f15a314f5)
@@ -70,4 +70,9 @@
     public  List<Map<String, Object>> selectStatusByBillNo(String billNo) {
         return repository.selectStatusByBillNo(billNo);
     }
+
+	@Override
+	public int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList) {
+		return repository.batchUpdateDtlForAudit(updateLockDtlList);
+	}
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/InventoryActiveLockDtlMapper.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/InventoryActiveLockDtlMapper.xml b/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/InventoryActiveLockDtlMapper.xml
--- a/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/InventoryActiveLockDtlMapper.xml	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/InventoryActiveLockDtlMapper.xml	(revision 0d2ae91680445afb52edfeba9d4fcd0f15a314f5)
@@ -626,5 +626,17 @@
         FROM inventory_active_lock_dtl WHERE bill_no = #{billNo}
         GROUP BY sync_status
     </select>
+    
+    <update id="batchUpdateDtlForAudit" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl">
+    	INSERT INTO inventory_active_lock_dtl (<include refid="column_list">
+    	</include>) values
+        <foreach collection="list" item="item" separator=",">
+            ( #{item.orderUnitName},#{item.orderUnitNo},  #{item.storeName},#{item.storeNo}, #{item.storeType},
+            #{item.brandNo}, #{item.id}, #{item.billNo}, #{item.skuNo}, #{item.createTime}, #{item.createUser},
+            #{item.itemCode}, #{item.lockQty}, #{item.barcode}, #{item.sizeNo}, #{item.balanceLockQty},
+            #{item.updateUser}, #{item.updateTime}, #{item.syncStatus})
+        </foreach>
+        ON DUPLICATE KEY UPDATE balance_lock_qty = balance_lock_qty + values(balance_lock_qty)
+    </update>
     <!-- auto generate end-->
 </mapper>
\ No newline at end of file
