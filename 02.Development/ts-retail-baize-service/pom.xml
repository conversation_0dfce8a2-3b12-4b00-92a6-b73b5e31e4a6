<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.wonhigh.baize</groupId>
        <artifactId>ts-retail-baize</artifactId>
        <version>1.0.28-SNAPSHOT</version>
    </parent>
    <artifactId>ts-retail-baize-service</artifactId>
    <name>ts-retail-baize-service</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>cn.wonhigh.baize</groupId>
            <artifactId>ts-retail-baize-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.baize</groupId>
            <artifactId>ts-retail-baize-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-servicemodel-core</artifactId> 
        </dependency> 
    </dependencies>
</project>