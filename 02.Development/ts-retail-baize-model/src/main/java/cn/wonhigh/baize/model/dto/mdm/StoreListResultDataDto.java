package cn.wonhigh.baize.model.dto.mdm;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;


public class StoreListResultDataDto implements Serializable {
    private static final long serialVersionUID = 3069161898567231984L;
    @JsonProperty("storeType")
    private Integer storeType;
    @JsonProperty("parentSourceNo")
    private String parentSourceNo;
    @JsonProperty("updateUser")
    private String updateUser;
    @JsonProperty("sourceNo")
    private String sourceNo;
    @JsonProperty("updateTime")
    private Long updateTime;
    @JsonProperty("sourceLevel")
    private Integer sourceLevel;
    @JsonProperty("firstSourceName")
    private String firstSourceName;
    @JsonProperty("storeNo")
    private String storeNo;
    @JsonProperty("createTime")
    private Long createTime;
    @JsonProperty("firstSourceNo")
    private String firstSourceNo;
    @JsonProperty("statusName")
    private String statusName;
    @JsonProperty("createUser")
    private String createUser;
    @JsonProperty("secondSourceNo")
    private String secondSourceNo;
    @JsonProperty("sourceName")
    private String sourceName;
    @JsonProperty("secondSourceName")
    private String secondSourceName;
    @JsonProperty("status")
    private Integer status;


    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getFirstSourceName() {
        return firstSourceName;
    }

    public void setFirstSourceName(String firstSourceName) {
        this.firstSourceName = firstSourceName;
    }

    public String getFirstSourceNo() {
        return firstSourceNo;
    }

    public void setFirstSourceNo(String firstSourceNo) {
        this.firstSourceNo = firstSourceNo;
    }

    public String getParentSourceNo() {
        return parentSourceNo;
    }

    public void setParentSourceNo(String parentSourceNo) {
        this.parentSourceNo = parentSourceNo;
    }

    public String getSecondSourceName() {
        return secondSourceName;
    }

    public void setSecondSourceName(String secondSourceName) {
        this.secondSourceName = secondSourceName;
    }

    public String getSecondSourceNo() {
        return secondSourceNo;
    }

    public void setSecondSourceNo(String secondSourceNo) {
        this.secondSourceNo = secondSourceNo;
    }

    public Integer getSourceLevel() {
        return sourceLevel;
    }

    public void setSourceLevel(Integer sourceLevel) {
        this.sourceLevel = sourceLevel;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceNo() {
        return sourceNo;
    }

    public void setSourceNo(String sourceNo) {
        this.sourceNo = sourceNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(String storeNo) {
        this.storeNo = storeNo;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
}
