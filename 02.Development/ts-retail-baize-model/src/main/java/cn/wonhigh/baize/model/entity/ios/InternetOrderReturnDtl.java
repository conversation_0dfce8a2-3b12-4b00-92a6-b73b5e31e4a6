/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 网销退单明细表
**/
public class InternetOrderReturnDtl  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778670L;
    
    /**
    *其它扣款金额
    **/ 
    @Label("其它扣款金额") 
    private BigDecimal otherdeductamount;
    

    public BigDecimal getOtherdeductamount(){
        return  otherdeductamount;
    }
    public void setOtherdeductamount(BigDecimal val ){
        otherdeductamount = val;
    }
    /**
    *新旧档期标识 1新0旧
    **/ 
    @Label("新旧档期标识") 
    private Integer schedule;
    

    public Integer getSchedule(){
        return  schedule;
    }
    public void setSchedule(Integer val ){
        schedule = val;
    }
    /**
    *运费
    **/ 
    @Label("运费") 
    private BigDecimal freight;
    

    public BigDecimal getFreight(){
        return  freight;
    }
    public void setFreight(BigDecimal val ){
        freight = val;
    }
    /**
    *商品sku
    **/ 
    @Label("商品") 
    private String skuNo;
    

    public String getSkuNo(){
        return  skuNo;
    }
    public void setSkuNo(String val ){
        skuNo = val;
    }
    /**
    *品牌编码
    **/ 
    @Label("品牌编码") 
    private String brandNo;
    

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    /**
    *退货单号
    **/ 
    @Label("退货单号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    /**
    *商品编码
    **/ 
    @Label("商品编码") 
    private String itemCode;
    

    public String getItemCode(){
        return  itemCode;
    }
    public void setItemCode(String val ){
        itemCode = val;
    }
    /**
    *尺寸编码
    **/ 
    @Label("尺寸编码") 
    private String sizeNo;
    

    public String getSizeNo(){
        return  sizeNo;
    }
    public void setSizeNo(String val ){
        sizeNo = val;
    }
    /**
    *商品名称
    **/ 
    @Label("商品名称") 
    private String itemName;
    

    public String getItemName(){
        return  itemName;
    }
    public void setItemName(String val ){
        itemName = val;
    }
    /**
    *货品单价(现价),非成交价
    **/ 
    @Label("货品单价") 
    private BigDecimal goodsPrice;
    

    public BigDecimal getGoodsPrice(){
        return  goodsPrice;
    }
    public void setGoodsPrice(BigDecimal val ){
        goodsPrice = val;
    }
    /**
    *箱号
    **/ 
    @Label("箱号") 
    private String boxNo;
    

    public String getBoxNo(){
        return  boxNo;
    }
    public void setBoxNo(String val ){
        boxNo = val;
    }
    /**
    *唯品会PO单号
    **/ 
    @Label("唯品会") 
    private String poNo;
    

    public String getPoNo(){
        return  poNo;
    }
    public void setPoNo(String val ){
        poNo = val;
    }
    /**
    *入库类型 0-正常品，1-原残,2-客残
    **/ 
    @Label("入库类型") 
    private Integer storageType;
    

    public Integer getStorageType(){
        return  storageType;
    }
    public void setStorageType(Integer val ){
        storageType = val;
    }
    /**
    *分库字段:本部+序列
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *PO开始时间
    **/ 
    @Label("开始时间") 
    private Date poStartDate;
    

    public Date getPoStartDate(){
        return  poStartDate;
    }
    public void setPoStartDate(Date val ){
        poStartDate = val;
    }
    /**
    *退供单号/调整单号/质检单号
    **/ 
    @Label("退供单号") 
    private String sellReturnCode;
    

    public String getSellReturnCode(){
        return  sellReturnCode;
    }
    public void setSellReturnCode(String val ){
        sellReturnCode = val;
    }
    /**
    *外部订单号
    **/ 
    @Label("外部订单号") 
    private String outOrderId;
    

    public String getOutOrderId(){
        return  outOrderId;
    }
    public void setOutOrderId(String val ){
        outOrderId = val;
    }
    /**
    *货品结算总金额(成交价=prod_total_amt/commodity_num)
    **/ 
    @Label("货品结算总金额") 
    private BigDecimal prodTotalAmt;
    

    public BigDecimal getProdTotalAmt(){
        return  prodTotalAmt;
    }
    public void setProdTotalAmt(BigDecimal val ){
        prodTotalAmt = val;
    }
    /**
    *礼品卡分摊金额
    **/ 
    @Label("礼品卡分摊金额") 
    private BigDecimal giftCardAmount;
    

    public BigDecimal getGiftCardAmount(){
        return  giftCardAmount;
    }
    public void setGiftCardAmount(BigDecimal val ){
        giftCardAmount = val;
    }
    /**
    *促销优惠金额
    **/ 
    @Label("促销优惠金额") 
    private BigDecimal promotionAmount;
    

    public BigDecimal getPromotionAmount(){
        return  promotionAmount;
    }
    public void setPromotionAmount(BigDecimal val ){
        promotionAmount = val;
    }
    /**
    *1普通商品，2赠品，3配件
    **/ 
    @Label("普通商品") 
    private Integer commodityType;
    

    public Integer getCommodityType(){
        return  commodityType;
    }
    public void setCommodityType(Integer val ){
        commodityType = val;
    }
    /**
    *实际退款金额
    **/ 
    @Label("实际退款金额") 
    private BigDecimal actualrefundamount;
    

    public BigDecimal getActualrefundamount(){
        return  actualrefundamount;
    }
    public void setActualrefundamount(BigDecimal val ){
        actualrefundamount = val;
    }
    /**
    *申请退款金额
    **/ 
    @Label("申请退款金额") 
    private BigDecimal applyrefundamount;
    

    public BigDecimal getApplyrefundamount(){
        return  applyrefundamount;
    }
    public void setApplyrefundamount(BigDecimal val ){
        applyrefundamount = val;
    }
    /**
    *上上级会员提成率
    **/ 
    @Label("上上级会员提成率") 
    private BigDecimal cpercent2;
    

    public BigDecimal getCpercent2(){
        return  cpercent2;
    }
    public void setCpercent2(BigDecimal val ){
        cpercent2 = val;
    }
    /**
    *上级会员提成率
    **/ 
    @Label("上级会员提成率") 
    private BigDecimal cpercent1;
    

    public BigDecimal getCpercent1(){
        return  cpercent1;
    }
    public void setCpercent1(BigDecimal val ){
        cpercent1 = val;
    }
    /**
    *基本积分
    **/ 
    @Label("基本积分") 
    private Integer basescore;
    

    public Integer getBasescore(){
        return  basescore;
    }
    public void setBasescore(Integer val ){
        basescore = val;
    }
    /**
    *尺码名称
    **/ 
    @Label("尺码名称") 
    private String sizeName;
    

    public String getSizeName(){
        return  sizeName;
    }
    public void setSizeName(String val ){
        sizeName = val;
    }
    /**
    *颜色名称
    **/ 
    @Label("颜色名称") 
    private String colorName;
    

    public String getColorName(){
        return  colorName;
    }
    public void setColorName(String val ){
        colorName = val;
    }
    /**
    *优惠券分摊金额
    **/ 
    @Label("优惠券分摊金额") 
    private BigDecimal couponAmount;
    

    public BigDecimal getCouponAmount(){
        return  couponAmount;
    }
    public void setCouponAmount(BigDecimal val ){
        couponAmount = val;
    }
    /**
    *尺码编码
    **/ 
    @Label("尺码编码") 
    private String sizeCode;
    

    public String getSizeCode(){
        return  sizeCode;
    }
    public void setSizeCode(String val ){
        sizeCode = val;
    }
    /**
    *颜色编码
    **/ 
    @Label("颜色编码") 
    private String colorCode;
    

    public String getColorCode(){
        return  colorCode;
    }
    public void setColorCode(String val ){
        colorCode = val;
    }
    /**
    *商品收货数量
    **/ 
    @Label("商品收货数量") 
    private Integer commodityNum;
    

    public Integer getCommodityNum(){
        return  commodityNum;
    }
    public void setCommodityNum(Integer val ){
        commodityNum = val;
    }
    /**
    *商品条形码
    **/ 
    @Label("商品条形码") 
    private String barcode;
    

    public String getBarcode(){
        return  barcode;
    }
    public void setBarcode(String val ){
        barcode = val;
    }
    /**
    *内销单价
    **/ 
    @Label("内销单价") 
    private BigDecimal newGoodsPrice;
    

    public BigDecimal getNewGoodsPrice(){
        return  newGoodsPrice;
    }
    public void setNewGoodsPrice(BigDecimal val ){
        newGoodsPrice = val;
    }
    /**
    *内销金额
    **/ 
    @Label("内销金额") 
    private BigDecimal newTotalAmt;
    

    public BigDecimal getNewTotalAmt(){
        return  newTotalAmt;
    }
    public void setNewTotalAmt(BigDecimal val ){
        newTotalAmt = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetOrderReturnDtlBuilder build(){
        return new InternetOrderReturnDtlBuilder(this);
    }

    public static class InternetOrderReturnDtlBuilder extends AbstractEntryBuilder<InternetOrderReturnDtl>{

        private InternetOrderReturnDtlBuilder(InternetOrderReturnDtl entry){
            this.obj = entry;
        }

       @Override
		public InternetOrderReturnDtl object() {
			return this.obj;
		}

        
        public InternetOrderReturnDtlBuilder otherdeductamount(BigDecimal value ){
            
            this.obj.otherdeductamount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("otherdeductamount", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder schedule(Integer value ){
            
            this.obj.schedule = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("schedule", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder freight(BigDecimal value ){
            
            this.obj.freight = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("freight", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder skuNo(String value ){
            
            this.obj.skuNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder brandNo(String value ){
            
            this.obj.brandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder itemCode(String value ){
            
            this.obj.itemCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemCode", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder sizeNo(String value ){
            
            this.obj.sizeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeNo", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder itemName(String value ){
            
            this.obj.itemName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemName", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder goodsPrice(BigDecimal value ){
            
            this.obj.goodsPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("goodsPrice", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder boxNo(String value ){
            
            this.obj.boxNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("boxNo", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder poNo(String value ){
            
            this.obj.poNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("poNo", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder storageType(Integer value ){
            
            this.obj.storageType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storageType", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder poStartDate(Date value ){
            
            this.obj.poStartDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("poStartDate", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder sellReturnCode(String value ){
            
            this.obj.sellReturnCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sellReturnCode", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder outOrderId(String value ){
            
            this.obj.outOrderId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outOrderId", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder prodTotalAmt(BigDecimal value ){
            
            this.obj.prodTotalAmt = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodTotalAmt", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder giftCardAmount(BigDecimal value ){
            
            this.obj.giftCardAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("giftCardAmount", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder promotionAmount(BigDecimal value ){
            
            this.obj.promotionAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("promotionAmount", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder commodityType(Integer value ){
            
            this.obj.commodityType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commodityType", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder actualrefundamount(BigDecimal value ){
            
            this.obj.actualrefundamount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("actualrefundamount", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder applyrefundamount(BigDecimal value ){
            
            this.obj.applyrefundamount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("applyrefundamount", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder cpercent2(BigDecimal value ){
            
            this.obj.cpercent2 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cpercent2", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder cpercent1(BigDecimal value ){
            
            this.obj.cpercent1 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cpercent1", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder basescore(Integer value ){
            
            this.obj.basescore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("basescore", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder sizeName(String value ){
            
            this.obj.sizeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeName", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder colorName(String value ){
            
            this.obj.colorName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("colorName", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder couponAmount(BigDecimal value ){
            
            this.obj.couponAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("couponAmount", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder sizeCode(String value ){
            
            this.obj.sizeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeCode", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder colorCode(String value ){
            
            this.obj.colorCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("colorCode", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder commodityNum(Integer value ){
            
            this.obj.commodityNum = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commodityNum", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder barcode(String value ){
            
            this.obj.barcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("barcode", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder newGoodsPrice(BigDecimal value ){
            
            this.obj.newGoodsPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("newGoodsPrice", value);
            return this;
        }
        
        public InternetOrderReturnDtlBuilder newTotalAmt(BigDecimal value ){
            
            this.obj.newTotalAmt = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("newTotalAmt", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }