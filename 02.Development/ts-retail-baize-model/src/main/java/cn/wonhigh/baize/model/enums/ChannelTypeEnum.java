package cn.wonhigh.baize.model.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2025/3/24 16:06
 */
public enum ChannelTypeEnum {

    OMS(1,"OMS"),
    XCX(2,"小程序"),
    THIRD(3,"第三方店铺"),
    ;

    private Integer code;
    private String name;

    ChannelTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getName(Integer code) {
        for (ChannelTypeEnum channelTypeEnum : ChannelTypeEnum.values()) {
            if (channelTypeEnum.getCode().equals(code)) {
                return channelTypeEnum.getName();
            }
        }
        return "";
    }
}
