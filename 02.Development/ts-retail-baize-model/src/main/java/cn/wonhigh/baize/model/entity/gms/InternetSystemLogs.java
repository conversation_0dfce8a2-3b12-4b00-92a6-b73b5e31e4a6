/**  **/
package cn.wonhigh.baize.model.entity.gms;

import cn.mercury.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* ISP系统操作日志表
**/
public class InternetSystemLogs  extends BaseEntity<Long>
{

    private static final long serialVersionUID = 1695002322206L;
    
    /**
    *检索关键字2
    **/ 
    @Label("检索关键字") 
    private String keyword2;
    

    public String getKeyword2(){
        return  keyword2;
    }
    public void setKeyword2(String val ){
        keyword2 = val;
    }
    /**
    *检索关键字2描述
    **/ 
    @Label("检索关键字") 
    private String keyword2info;
    

    public String getKeyword2info(){
        return  keyword2info;
    }
    public void setKeyword2info(String val ){
        keyword2info = val;
    }
    /**
    *菜单编码
    **/ 
    @Label("菜单编码") 
    private String syscode;
    

    public String getSyscode(){
        return  syscode;
    }
    public void setSyscode(String val ){
        syscode = val;
    }
    /**
    *菜单名称
    **/ 
    @Label("菜单名称") 
    private String sysname;
    

    public String getSysname(){
        return  sysname;
    }
    public void setSysname(String val ){
        sysname = val;
    }
    /**
    *操作动作：insert,update,delete
    **/ 
    @Label("操作动作") 
    private String opscode;
    

    public String getOpscode(){
        return  opscode;
    }
    public void setOpscode(String val ){
        opscode = val;
    }
    /**
    *检索关键字1
    **/ 
    @Label("检索关键字") 
    private String keyword1;
    

    public String getKeyword1(){
        return  keyword1;
    }
    public void setKeyword1(String val ){
        keyword1 = val;
    }
    /**
    *检索关键字1描述
    **/ 
    @Label("检索关键字") 
    private String keyword1info;
    

    public String getKeyword1info(){
        return  keyword1info;
    }
    public void setKeyword1info(String val ){
        keyword1info = val;
    }
    /**
    *操作描述
    **/ 
    @Label("操作描述") 
    private String remark;
    

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetSystemLogsBuilder build(){
        return new InternetSystemLogsBuilder(this);
    }

    public static class InternetSystemLogsBuilder extends AbstractEntryBuilder<InternetSystemLogs>{

        private InternetSystemLogsBuilder(InternetSystemLogs entry){
            this.obj = entry;
        }

       @Override
		public InternetSystemLogs object() {
			return this.obj;
		}

        
        public InternetSystemLogsBuilder keyword2(String value ){
            
            this.obj.keyword2 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("keyword2", value);
            return this;
        }
        
        public InternetSystemLogsBuilder keyword2info(String value ){
            
            this.obj.keyword2info = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("keyword2info", value);
            return this;
        }
        
        public InternetSystemLogsBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetSystemLogsBuilder id(Long value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetSystemLogsBuilder syscode(String value ){
            
            this.obj.syscode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("syscode", value);
            return this;
        }
        
        public InternetSystemLogsBuilder sysname(String value ){
            
            this.obj.sysname = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sysname", value);
            return this;
        }
        
        public InternetSystemLogsBuilder opscode(String value ){
            
            this.obj.opscode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("opscode", value);
            return this;
        }
        
        public InternetSystemLogsBuilder keyword1(String value ){
            
            this.obj.keyword1 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("keyword1", value);
            return this;
        }
        
        public InternetSystemLogsBuilder keyword1info(String value ){
            
            this.obj.keyword1info = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("keyword1info", value);
            return this;
        }
        
        public InternetSystemLogsBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public InternetSystemLogsBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }