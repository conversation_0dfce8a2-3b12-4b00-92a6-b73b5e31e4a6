package cn.wonhigh.baize.model.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2025/4/2 11:43
 */
public enum OpscodeEnum {

    ADD("insert", "新增"),
    DEL("delete", "删除"),
    UP("update", "修改"),
    AUDIT("audit", "审核"),
    CANCEL("cancel", "取消"),
    EFFECT("effect", "生效"),
    SYNC("sync", "同步"),
    FAIL_SYNC("failSync", "失败同步");

    private String code;
    private String name;

    OpscodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String getName(String code){
        OpscodeEnum[] enums = OpscodeEnum.values();
        for (OpscodeEnum anEnum : enums) {
            if(anEnum.code.equals(code)){
                return anEnum.getName();
            }
        }
        return code;
    }

    public static OpscodeEnum getOpsCodeEnum(String code) {
    	return Arrays.stream(OpscodeEnum.values()).filter(e -> code.equals(e.getCode())).findFirst().orElse(null);
    }
}
