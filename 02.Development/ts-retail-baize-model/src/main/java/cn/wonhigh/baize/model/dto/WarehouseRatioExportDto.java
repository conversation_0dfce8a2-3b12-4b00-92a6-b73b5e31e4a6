package cn.wonhigh.baize.model.dto;

import cn.mercury.domain.IEntry;

public class WarehouseRatioExportDto implements IEntry{
     private String warehouseCode;
    private String warehouseName;
    private String shopCode;
    private String shopName;
    private String brandCode;
    private String brandName;
    private String platformAmplifyRatio;
    private String omsAmplifyRatio;
    private String shopAmplifyRatio;

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getPlatformAmplifyRatio() {
        return platformAmplifyRatio;
    }

    public void setPlatformAmplifyRatio(String platformAmplifyRatio) {
        this.platformAmplifyRatio = platformAmplifyRatio;
    }

    public String getOmsAmplifyRatio() {
        return omsAmplifyRatio;
    }

    public void setOmsAmplifyRatio(String omsAmplifyRatio) {
        this.omsAmplifyRatio = omsAmplifyRatio;
    }

    public String getShopAmplifyRatio() {
        return shopAmplifyRatio;
    }

    public void setShopAmplifyRatio(String shopAmplifyRatio) {
        this.shopAmplifyRatio = shopAmplifyRatio;
    }

}
