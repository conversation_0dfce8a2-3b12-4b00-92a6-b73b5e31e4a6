package cn.wonhigh.baize.model.entity.gms;

import cn.mercury.annotation.Label;
import cn.mercury.basic.query.Query;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;


/**
 * oms锁库(IcsInventoryOmsLock)实体类
 *
 * <AUTHOR>
 * @since 2024-12-28 10:04:34
 */
public class IcsInventoryOmsLock extends BaseEntity<String> implements Serializable {
    private static final long serialVersionUID = -10904237444350456L;
    /**
     * 虚仓编码
     */
    @Label("虚仓编码")
    private String vstoreCode;
    /**
     * 虚拟仓名称
     */
    @Label("虚拟仓名称")
    private String vstoreName;
    /**
     * sku编码
     */
    @Label("sku编码")
    private String skuNo;
    /**
     * 锁存数量
     */
    @Label("锁存数量")
    private Integer lockQty;
    /**
     * 状态0关闭1开启
     */
    @Label("状态0关闭1开启")
    private Integer status;
    /**
     * 备注
     */
    @Label("备注")
    private String remark;
    /**
     * 创建时间
     */
    @Label("创建时间")
    private Date createTime;


    public String getVstoreCode() {
        return vstoreCode;
    }

    public void setVstoreCode(String vstoreCode) {
        this.vstoreCode = vstoreCode;
    }

    public String getVstoreName() {
        return vstoreName;
    }

    public void setVstoreName(String vstoreName) {
        this.vstoreName = vstoreName;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public Integer getLockQty() {
        return lockQty;
    }

    public void setLockQty(Integer lockQty) {
        this.lockQty = lockQty;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static IcsInventoryOmsLockBuilder build() {
        return new IcsInventoryOmsLockBuilder(new IcsInventoryOmsLock());
    }

    public static IcsInventoryOmsLockBuilder build(IcsInventoryOmsLock value) {
        return new IcsInventoryOmsLockBuilder(value);
    }

    public static class IcsInventoryOmsLockBuilder extends AbstractEntryBuilder<IcsInventoryOmsLock> {

        private IcsInventoryOmsLockBuilder(IcsInventoryOmsLock entry) {
            this.obj = entry;
        }

        @Override
        public IcsInventoryOmsLock object() {
            return this.obj;
        }

        public IcsInventoryOmsLockBuilder id(String value) {

            this.obj.setId(value);

            if (query == null)
                query = new Query();
            this.query.where("id", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder vstoreCode(String value) {

            this.obj.setVstoreCode(value);

            if (query == null)
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder vstoreName(String value) {

            this.obj.setVstoreName(value);

            if (query == null)
                query = new Query();
            this.query.where("vstoreName", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder skuNo(String value) {

            this.obj.setSkuNo(value);

            if (query == null)
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder lockQty(Integer value) {

            this.obj.setLockQty(value);

            if (query == null)
                query = new Query();
            this.query.where("lockQty", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder status(Integer value) {

            this.obj.setStatus(value);

            if (query == null)
                query = new Query();
            this.query.where("status", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder remark(String value) {

            this.obj.setRemark(value);

            if (query == null)
                query = new Query();
            this.query.where("remark", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder createUser(String value) {

            this.obj.setCreateUser(value);

            if (query == null)
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder createTime(Date value) {

            this.obj.setCreateTime(value);

            if (query == null)
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder updateUser(String value) {

            this.obj.setUpdateUser(value);

            if (query == null)
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }

        public IcsInventoryOmsLockBuilder updateTime(Date value) {

            this.obj.setUpdateTime(value);

            if (query == null)
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
    }
}
