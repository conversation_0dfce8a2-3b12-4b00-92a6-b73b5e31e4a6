/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 取消订单申请
**/
public class InternetOrderCancelApply  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1677468996264L;
    
    /**
    *不同意的原因
    **/ 
    @Label("不同意的原因") 
    private String reason;
    

    public String getReason(){
        return  reason;
    }
    public void setReason(String val ){
        reason = val;
    }
    /**
    *分库字段：本部+序号
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *子订单号
    **/ 
    @Label("子订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    /**
    *部分取消skuNo
    **/ 
    @Label("部分取消") 
    private String skuNo;
    

    public String getSkuNo(){
        return  skuNo;
    }
    public void setSkuNo(String val ){
        skuNo = val;
    }
    /**
    *退款时间
    **/ 
    @Label("退款时间") 
    private Date refundTime;
    

    public Date getRefundTime(){
        return  refundTime;
    }
    public void setRefundTime(Date val ){
        refundTime = val;
    }
    /**
    *单据类型(0:未确认退款 1：已退款成功)
    **/ 
    @Label("单据类型") 
    private Integer orderType;
    

    public Integer getOrderType(){
        return  orderType;
    }
    public void setOrderType(Integer val ){
        orderType = val;
    }
    /**
    *取消申请单编号
    **/ 
    @Label("取消申请单编号") 
    private String refundNo;
    

    public String getRefundNo(){
        return  refundNo;
    }
    public void setRefundNo(String val ){
        refundNo = val;
    }
    /**
    *1拦截成功，2拦截失败，3已申请拦截wms未返回
    **/ 
    @Label("拦截成功") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *通知单号
    **/ 
    @Label("通知单号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetOrderCancelApplyBuilder build(){
        return new InternetOrderCancelApplyBuilder(this);
    }

    public static class InternetOrderCancelApplyBuilder extends AbstractEntryBuilder<InternetOrderCancelApply>{

        private InternetOrderCancelApplyBuilder(InternetOrderCancelApply entry){
            this.obj = entry;
        }

       @Override
		public InternetOrderCancelApply object() {
			return this.obj;
		}

        
        public InternetOrderCancelApplyBuilder reason(String value ){
            
            this.obj.reason = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("reason", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder skuNo(String value ){
            
            this.obj.skuNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder refundTime(Date value ){
            
            this.obj.refundTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refundTime", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder orderType(Integer value ){
            
            this.obj.orderType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderType", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder refundNo(String value ){
            
            this.obj.refundNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refundNo", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public InternetOrderCancelApplyBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }