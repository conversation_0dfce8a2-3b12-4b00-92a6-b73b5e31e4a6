/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 结算公司分区配置表
**/
public class CompanyPartConfig  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1727143483477L;
    
    //时间戳
    @Label("时间戳") 
    private Integer timeSeq;
    
    //大区别名
    @Label("大区别名") 
    private String zoneAlias;
    
    //地区编号
    @Label("地区编号") 
    private String zoneNo;
    
    //本部编码
    @Label("本部编码") 
    private String organTypeNo;
    
    //分区编码
    @Label("分区编码") 
    private String partitionNo;
    
    //结算公司编码
    @Label("结算公司编码") 
    private String companyNo;
    
    
    public Integer getTimeSeq(){
        return  timeSeq;
    }
    public void setTimeSeq(Integer val ){
        timeSeq = val;
    }
    
    public String getZoneAlias(){
        return  zoneAlias;
    }
    public void setZoneAlias(String val ){
        zoneAlias = val;
    }
    
    public String getZoneNo(){
        return  zoneNo;
    }
    public void setZoneNo(String val ){
        zoneNo = val;
    }
    
    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    
    public String getPartitionNo(){
        return  partitionNo;
    }
    public void setPartitionNo(String val ){
        partitionNo = val;
    }
    
    public String getCompanyNo(){
        return  companyNo;
    }
    public void setCompanyNo(String val ){
        companyNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public CompanyPartConfigBuilder build(){
        return new CompanyPartConfigBuilder(this);
    }

    public static class CompanyPartConfigBuilder extends AbstractEntryBuilder<CompanyPartConfig>{

        private CompanyPartConfigBuilder(CompanyPartConfig entry){
            this.obj = entry;
        }

       @Override
		public CompanyPartConfig object() {
			return this.obj;
		}

        
        public CompanyPartConfigBuilder timeSeq(Integer value ){
            this.obj.timeSeq = value;
            if( query == null  )
                query = new Query();
            this.query.where("timeSeq", value);
            return this;
        }
        
        public CompanyPartConfigBuilder zoneAlias(String value ){
            this.obj.zoneAlias = value;
            if( query == null  )
                query = new Query();
            this.query.where("zoneAlias", value);
            return this;
        }
        
        public CompanyPartConfigBuilder zoneNo(String value ){
            this.obj.zoneNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("zoneNo", value);
            return this;
        }
        
        public CompanyPartConfigBuilder organTypeNo(String value ){
            this.obj.organTypeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
        public CompanyPartConfigBuilder partitionNo(String value ){
            this.obj.partitionNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("partitionNo", value);
            return this;
        }
        
        public CompanyPartConfigBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public CompanyPartConfigBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public CompanyPartConfigBuilder companyNo(String value ){
            this.obj.companyNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("companyNo", value);
            return this;
        }
        
        public CompanyPartConfigBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public CompanyPartConfigBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}