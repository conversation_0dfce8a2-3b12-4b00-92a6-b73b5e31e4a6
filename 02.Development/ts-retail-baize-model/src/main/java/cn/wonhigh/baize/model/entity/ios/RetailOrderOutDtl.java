/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 网络销售出库单明细
**/
public class RetailOrderOutDtl  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778674L;
    
    /**
    *牌价
    **/ 
    @Label("牌价") 
    private BigDecimal quotePrice;
    

    public BigDecimal getQuotePrice(){
        return  quotePrice;
    }
    public void setQuotePrice(BigDecimal val ){
        quotePrice = val;
    }
    /**
    *快递公司名称
    **/ 
    @Label("快递公司名称") 
    private String logisticsCompanyName;
    

    public String getLogisticsCompanyName(){
        return  logisticsCompanyName;
    }
    public void setLogisticsCompanyName(String val ){
        logisticsCompanyName = val;
    }
    /**
    *快递公司编码
    **/ 
    @Label("快递公司编码") 
    private String logisticCompanyCode;
    

    public String getLogisticCompanyCode(){
        return  logisticCompanyCode;
    }
    public void setLogisticCompanyCode(String val ){
        logisticCompanyCode = val;
    }
    /**
    *快递单号
    **/ 
    @Label("快递单号") 
    private String expressNo;
    

    public String getExpressNo(){
        return  expressNo;
    }
    public void setExpressNo(String val ){
        expressNo = val;
    }
    /**
    *箱号
    **/ 
    @Label("箱号") 
    private String boxNo;
    

    public String getBoxNo(){
        return  boxNo;
    }
    public void setBoxNo(String val ){
        boxNo = val;
    }
    /**
    *零售分库字段：本部+大区
    **/ 
    @Label("零售分库字段") 
    private String retailFlag;
    

    public String getRetailFlag(){
        return  retailFlag;
    }
    public void setRetailFlag(String val ){
        retailFlag = val;
    }
    /**
    *售后图片备注
    **/ 
    @Label("售后图片备注") 
    private String imageRemark;
    

    public String getImageRemark(){
        return  imageRemark;
    }
    public void setImageRemark(String val ){
        imageRemark = val;
    }
    /**
    *售后图片URL
    **/ 
    @Label("售后图片") 
    private String imageUrl;
    

    public String getImageUrl(){
        return  imageUrl;
    }
    public void setImageUrl(String val ){
        imageUrl = val;
    }
    /**
    *pos订单详情id, 回调pos接口用
    **/ 
    @Label("订单详情") 
    private String posDtlId;
    

    public String getPosDtlId(){
        return  posDtlId;
    }
    public void setPosDtlId(String val ){
        posDtlId = val;
    }
    /**
    *分库字段：本部+序号
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *备注
    **/ 
    @Label("备注") 
    private String remark;
    

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    /**
    *单据编号
    **/ 
    @Label("单据编号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    /**
    *sku编码
    **/ 
    @Label("编码") 
    private String skuNo;
    

    public String getSkuNo(){
        return  skuNo;
    }
    public void setSkuNo(String val ){
        skuNo = val;
    }
    /**
    *商品内码
    **/ 
    @Label("商品内码") 
    private String itemNo;
    

    public String getItemNo(){
        return  itemNo;
    }
    public void setItemNo(String val ){
        itemNo = val;
    }
    /**
    *商品条码
    **/ 
    @Label("商品条码") 
    private String barcode;
    

    public String getBarcode(){
        return  barcode;
    }
    public void setBarcode(String val ){
        barcode = val;
    }
    /**
    *商品编码
    **/ 
    @Label("商品编码") 
    private String itemCode;
    

    public String getItemCode(){
        return  itemCode;
    }
    public void setItemCode(String val ){
        itemCode = val;
    }
    /**
    *商品名称
    **/ 
    @Label("商品名称") 
    private String itemName;
    

    public String getItemName(){
        return  itemName;
    }
    public void setItemName(String val ){
        itemName = val;
    }
    /**
    *用于网站上展示的商品名称
    **/ 
    @Label("用于网站上展示的商品名称") 
    private String itemNameForshow;
    

    public String getItemNameForshow(){
        return  itemNameForshow;
    }
    public void setItemNameForshow(String val ){
        itemNameForshow = val;
    }
    /**
    *颜色编码
    **/ 
    @Label("颜色编码") 
    private String colorNo;
    

    public String getColorNo(){
        return  colorNo;
    }
    public void setColorNo(String val ){
        colorNo = val;
    }
    /**
    *颜色名称
    **/ 
    @Label("颜色名称") 
    private String colorName;
    

    public String getColorName(){
        return  colorName;
    }
    public void setColorName(String val ){
        colorName = val;
    }
    /**
    *品牌编码
    **/ 
    @Label("品牌编码") 
    private String brandNo;
    

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    /**
    *品牌名称
    **/ 
    @Label("品牌名称") 
    private String brandName;
    

    public String getBrandName(){
        return  brandName;
    }
    public void setBrandName(String val ){
        brandName = val;
    }
    /**
    *类别编码
    **/ 
    @Label("类别编码") 
    private String categoryNo;
    

    public String getCategoryNo(){
        return  categoryNo;
    }
    public void setCategoryNo(String val ){
        categoryNo = val;
    }
    /**
    *尺寸编号
    **/ 
    @Label("尺寸编号") 
    private String sizeNo;
    

    public String getSizeNo(){
        return  sizeNo;
    }
    public void setSizeNo(String val ){
        sizeNo = val;
    }
    /**
    *尺寸分类
    **/ 
    @Label("尺寸分类") 
    private String sizeKind;
    

    public String getSizeKind(){
        return  sizeKind;
    }
    public void setSizeKind(String val ){
        sizeKind = val;
    }
    /**
    *规格,商品颜色尺码，以逗号劈开
    **/ 
    @Label("规格") 
    private String commoditySpecificationStr;
    

    public String getCommoditySpecificationStr(){
        return  commoditySpecificationStr;
    }
    public void setCommoditySpecificationStr(String val ){
        commoditySpecificationStr = val;
    }
    /**
    *折扣率
    **/ 
    @Label("折扣率") 
    private BigDecimal discount;
    

    public BigDecimal getDiscount(){
        return  discount;
    }
    public void setDiscount(BigDecimal val ){
        discount = val;
    }
    /**
    *通知数量
    **/ 
    @Label("通知数量") 
    private Integer askQty;
    

    public Integer getAskQty(){
        return  askQty;
    }
    public void setAskQty(Integer val ){
        askQty = val;
    }
    /**
    *出库数量
    **/ 
    @Label("出库数量") 
    private Integer sendOutQty;
    

    public Integer getSendOutQty(){
        return  sendOutQty;
    }
    public void setSendOutQty(Integer val ){
        sendOutQty = val;
    }
    /**
    *商品类型(0：普通商品 1：赠品 2：换购 3：从商品--针对加价购的促销)
    **/ 
    @Label("商品类型") 
    private Integer commodityType;
    

    public Integer getCommodityType(){
        return  commodityType;
    }
    public void setCommodityType(Integer val ){
        commodityType = val;
    }
    /**
    *销售价
    **/ 
    @Label("销售价") 
    private BigDecimal salePrice;


    private RetailOrderOut retailOrderOut;
    private InternetOrder internetOrder;


    public InternetOrder getInternetOrder() {
        return internetOrder;
    }

    public void setInternetOrder(InternetOrder internetOrder) {
        this.internetOrder = internetOrder;
    }

    public BigDecimal getSalePrice(){
        return  salePrice;
    }
    public void setSalePrice(BigDecimal val ){
        salePrice = val;
    }


    public RetailOrderOut getRetailOrderOut() {
        return retailOrderOut;
    }

    public void setRetailOrderOut(RetailOrderOut retailOrderOut) {
        this.retailOrderOut = retailOrderOut;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public RetailOrderOutDtlBuilder build(){
        return new RetailOrderOutDtlBuilder(this);
    }

    public static class RetailOrderOutDtlBuilder extends AbstractEntryBuilder<RetailOrderOutDtl>{

        private RetailOrderOutDtlBuilder(RetailOrderOutDtl entry){
            this.obj = entry;
        }

       @Override
		public RetailOrderOutDtl object() {
			return this.obj;
		}

        
        public RetailOrderOutDtlBuilder quotePrice(BigDecimal value ){
            
            this.obj.quotePrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("quotePrice", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder logisticsCompanyName(String value ){
            
            this.obj.logisticsCompanyName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsCompanyName", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder logisticCompanyCode(String value ){
            
            this.obj.logisticCompanyCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticCompanyCode", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder expressNo(String value ){
            
            this.obj.expressNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder boxNo(String value ){
            
            this.obj.boxNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("boxNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder retailFlag(String value ){
            
            this.obj.retailFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailFlag", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder imageRemark(String value ){
            
            this.obj.imageRemark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("imageRemark", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder imageUrl(String value ){
            
            this.obj.imageUrl = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("imageUrl", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder posDtlId(String value ){
            
            this.obj.posDtlId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("posDtlId", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder skuNo(String value ){
            
            this.obj.skuNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder itemNo(String value ){
            
            this.obj.itemNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder barcode(String value ){
            
            this.obj.barcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("barcode", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder itemCode(String value ){
            
            this.obj.itemCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemCode", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder itemName(String value ){
            
            this.obj.itemName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemName", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder itemNameForshow(String value ){
            
            this.obj.itemNameForshow = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemNameForshow", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder colorNo(String value ){
            
            this.obj.colorNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("colorNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder colorName(String value ){
            
            this.obj.colorName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("colorName", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder brandNo(String value ){
            
            this.obj.brandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder brandName(String value ){
            
            this.obj.brandName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandName", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder categoryNo(String value ){
            
            this.obj.categoryNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("categoryNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder sizeNo(String value ){
            
            this.obj.sizeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeNo", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder sizeKind(String value ){
            
            this.obj.sizeKind = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeKind", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder commoditySpecificationStr(String value ){
            
            this.obj.commoditySpecificationStr = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commoditySpecificationStr", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder discount(BigDecimal value ){
            
            this.obj.discount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("discount", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder askQty(Integer value ){
            
            this.obj.askQty = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("askQty", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder sendOutQty(Integer value ){
            
            this.obj.sendOutQty = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendOutQty", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder commodityType(Integer value ){
            
            this.obj.commodityType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commodityType", value);
            return this;
        }
        
        public RetailOrderOutDtlBuilder salePrice(BigDecimal value ){
            
            this.obj.salePrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("salePrice", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }