/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;
import java.math.BigDecimal;
/** 
*auto generate start ,don't modify
* 售罄率明细表
**/
public class SellOutConfigDtl  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1754883307407L;
    
    //主表单据编号
    @Label("主表单据编号") 
    private String billNo;
    
    //目标字段 (1,2,3...91)
    @Label("目标字段") 
    private String targetField;
    
    //目标值
    @Label("目标值") 
    private BigDecimal targetValue;
    
    
    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    
    public String getTargetField(){
        return  targetField;
    }
    public void setTargetField(String val ){
        targetField = val;
    }
    
    public BigDecimal getTargetValue(){
        return  targetValue;
    }
    public void setTargetValue(BigDecimal val ){
        targetValue = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public SellOutConfigDtlBuilder build(){
        return new SellOutConfigDtlBuilder(this);
    }

    public static class SellOutConfigDtlBuilder extends AbstractEntryBuilder<SellOutConfigDtl>{

        private SellOutConfigDtlBuilder(SellOutConfigDtl entry){
            this.obj = entry;
        }

       @Override
		public SellOutConfigDtl object() {
			return this.obj;
		}

        
        public SellOutConfigDtlBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public SellOutConfigDtlBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public SellOutConfigDtlBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public SellOutConfigDtlBuilder billNo(String value ){
            this.obj.billNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public SellOutConfigDtlBuilder targetField(String value ){
            this.obj.targetField = value;
            if( query == null  )
                query = new Query();
            this.query.where("targetField", value);
            return this;
        }
        
        public SellOutConfigDtlBuilder targetValue(BigDecimal value ){
            this.obj.targetValue = value;
            if( query == null  )
                query = new Query();
            this.query.where("targetValue", value);
            return this;
        }
        
        public SellOutConfigDtlBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public SellOutConfigDtlBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}