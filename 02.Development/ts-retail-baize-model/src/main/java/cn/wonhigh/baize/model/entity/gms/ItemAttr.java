package cn.wonhigh.baize.model.entity.gms;

import java.math.BigDecimal;
import java.util.Date;

public class ItemAttr extends cn.mercury.domain.BasicEntity {

    private static final long serialVersionUID = 1545814831148L;

    private String attrNo;

    private String attrName;

    private String brandNo;

    private String attrDtlNo;

    private String attrDtlName;

    public String getAttrNo() {
        return attrNo;
    }

    public void setAttrNo(String attrNo) {
        this.attrNo = attrNo;
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public String getAttrDtlNo() {
        return attrDtlNo;
    }

    public void setAttrDtlNo(String attrDtlNo) {
        this.attrDtlNo = attrDtlNo;
    }

    public String getAttrDtlName() {
        return attrDtlName;
    }

    public void setAttrDtlName(String attrDtlName) {
        this.attrDtlName = attrDtlName;
    }
}