/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 结算公司信息表
**/
public class Company  extends cn.mercury.domain.BaseEntity<Integer>  {

    private static final long serialVersionUID = 1722566348188L;
    
    //结算公司编码
    @Label("结算公司编码") 
    private String companyNo;
    
    //结算公司名称
    @Label("结算公司名称") 
    private String name;
    
    //状态(0 = 撤消 1 = 正常)
    @Label(value = "状态", defaultVal = "1") 
    private Integer status;
    
    //开户银行
    @Label("开户银行") 
    private String bankName;
    
    //银行帐号
    @Label("银行帐号") 
    private String bankAccount;
    
    //银行账户名
    @Label("银行账户名") 
    private String bankAccountName;
    
    //联系人
    @Label("联系人") 
    private String contactName;
    
    //电话号码
    @Label("电话号码") 
    private String tel;
    
    //税务登记号
    @Label("税务登记号") 
    private String taxRegistryNo;
    
    //纳税级别(0:一般纳税人 1:小规模纳税人)
    @Label(value = "纳税级别", defaultVal = "0") 
    private String taxLevel;
    
    //法人代表
    @Label("法人代表") 
    private String legalPerson;
    
    //营业证号/身份证号
    @Label("营业证号") 
    private String identityCard;
    
    //传真号
    @Label("传真号") 
    private String fax;
    
    //电子邮箱
    @Label("电子邮箱") 
    private String email;
    
    //经营区域编号
    @Label("经营区域编号") 
    private String zoneNo;
    
    //NC项目组织编码
    @Label("项目组织编码") 
    private String ncProjectOrganizeNo;
    
    //备注
    @Label("备注") 
    private String remark;
    
    //时间序列
    @Label("时间序列") 
    private Long timeSeq;
    
    //检索码
    @Label("检索码") 
    private String searchCode;
    
    //结算公司地址
    @Label("结算公司地址") 
    private String address;
    
    //本部编码
    @Label("本部编码") 
    private String organTypeNo;
    
    //是否批发联营(0 否, 1 是)
    @Label(value = "是否批发联营", defaultVal = "0") 
    private Integer whetherWholesalePool;
    
    //邮编
    @Label("邮编") 
    private String postcode;
    
    
    public String getCompanyNo(){
        return  companyNo;
    }
    public void setCompanyNo(String val ){
        companyNo = val;
    }
    
    public String getName(){
        return  name;
    }
    public void setName(String val ){
        name = val;
    }
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public String getBankName(){
        return  bankName;
    }
    public void setBankName(String val ){
        bankName = val;
    }
    
    public String getBankAccount(){
        return  bankAccount;
    }
    public void setBankAccount(String val ){
        bankAccount = val;
    }
    
    public String getBankAccountName(){
        return  bankAccountName;
    }
    public void setBankAccountName(String val ){
        bankAccountName = val;
    }
    
    public String getContactName(){
        return  contactName;
    }
    public void setContactName(String val ){
        contactName = val;
    }
    
    public String getTel(){
        return  tel;
    }
    public void setTel(String val ){
        tel = val;
    }
    
    public String getTaxRegistryNo(){
        return  taxRegistryNo;
    }
    public void setTaxRegistryNo(String val ){
        taxRegistryNo = val;
    }
    
    public String getTaxLevel(){
        return  taxLevel;
    }
    public void setTaxLevel(String val ){
        taxLevel = val;
    }
    
    public String getLegalPerson(){
        return  legalPerson;
    }
    public void setLegalPerson(String val ){
        legalPerson = val;
    }
    
    public String getIdentityCard(){
        return  identityCard;
    }
    public void setIdentityCard(String val ){
        identityCard = val;
    }
    
    public String getFax(){
        return  fax;
    }
    public void setFax(String val ){
        fax = val;
    }
    
    public String getEmail(){
        return  email;
    }
    public void setEmail(String val ){
        email = val;
    }
    
    public String getZoneNo(){
        return  zoneNo;
    }
    public void setZoneNo(String val ){
        zoneNo = val;
    }
    
    public String getNcProjectOrganizeNo(){
        return  ncProjectOrganizeNo;
    }
    public void setNcProjectOrganizeNo(String val ){
        ncProjectOrganizeNo = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    public Long getTimeSeq(){
        return  timeSeq;
    }
    public void setTimeSeq(Long val ){
        timeSeq = val;
    }
    
    public String getSearchCode(){
        return  searchCode;
    }
    public void setSearchCode(String val ){
        searchCode = val;
    }
    
    public String getAddress(){
        return  address;
    }
    public void setAddress(String val ){
        address = val;
    }
    
    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    
    public Integer getWhetherWholesalePool(){
        return  whetherWholesalePool;
    }
    public void setWhetherWholesalePool(Integer val ){
        whetherWholesalePool = val;
    }
    
    public String getPostcode(){
        return  postcode;
    }
    public void setPostcode(String val ){
        postcode = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public CompanyBuilder build(){
        return new CompanyBuilder(this);
    }

    public static class CompanyBuilder extends AbstractEntryBuilder<Company>{

        private CompanyBuilder(Company entry){
            this.obj = entry;
        }

       @Override
		public Company object() {
			return this.obj;
		}

        
        public CompanyBuilder id(Integer value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public CompanyBuilder companyNo(String value ){
            this.obj.companyNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("companyNo", value);
            return this;
        }
        
        public CompanyBuilder name(String value ){
            this.obj.name = value;
            if( query == null  )
                query = new Query();
            this.query.where("name", value);
            return this;
        }
        
        public CompanyBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public CompanyBuilder bankName(String value ){
            this.obj.bankName = value;
            if( query == null  )
                query = new Query();
            this.query.where("bankName", value);
            return this;
        }
        
        public CompanyBuilder bankAccount(String value ){
            this.obj.bankAccount = value;
            if( query == null  )
                query = new Query();
            this.query.where("bankAccount", value);
            return this;
        }
        
        public CompanyBuilder bankAccountName(String value ){
            this.obj.bankAccountName = value;
            if( query == null  )
                query = new Query();
            this.query.where("bankAccountName", value);
            return this;
        }
        
        public CompanyBuilder contactName(String value ){
            this.obj.contactName = value;
            if( query == null  )
                query = new Query();
            this.query.where("contactName", value);
            return this;
        }
        
        public CompanyBuilder tel(String value ){
            this.obj.tel = value;
            if( query == null  )
                query = new Query();
            this.query.where("tel", value);
            return this;
        }
        
        public CompanyBuilder taxRegistryNo(String value ){
            this.obj.taxRegistryNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("taxRegistryNo", value);
            return this;
        }
        
        public CompanyBuilder taxLevel(String value ){
            this.obj.taxLevel = value;
            if( query == null  )
                query = new Query();
            this.query.where("taxLevel", value);
            return this;
        }
        
        public CompanyBuilder legalPerson(String value ){
            this.obj.legalPerson = value;
            if( query == null  )
                query = new Query();
            this.query.where("legalPerson", value);
            return this;
        }
        
        public CompanyBuilder identityCard(String value ){
            this.obj.identityCard = value;
            if( query == null  )
                query = new Query();
            this.query.where("identityCard", value);
            return this;
        }
        
        public CompanyBuilder fax(String value ){
            this.obj.fax = value;
            if( query == null  )
                query = new Query();
            this.query.where("fax", value);
            return this;
        }
        
        public CompanyBuilder email(String value ){
            this.obj.email = value;
            if( query == null  )
                query = new Query();
            this.query.where("email", value);
            return this;
        }
        
        public CompanyBuilder zoneNo(String value ){
            this.obj.zoneNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("zoneNo", value);
            return this;
        }
        
        public CompanyBuilder ncProjectOrganizeNo(String value ){
            this.obj.ncProjectOrganizeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("ncProjectOrganizeNo", value);
            return this;
        }
        
        public CompanyBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public CompanyBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public CompanyBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public CompanyBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public CompanyBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public CompanyBuilder timeSeq(Long value ){
            this.obj.timeSeq = value;
            if( query == null  )
                query = new Query();
            this.query.where("timeSeq", value);
            return this;
        }
        
        public CompanyBuilder searchCode(String value ){
            this.obj.searchCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("searchCode", value);
            return this;
        }
        
        public CompanyBuilder address(String value ){
            this.obj.address = value;
            if( query == null  )
                query = new Query();
            this.query.where("address", value);
            return this;
        }
        
        public CompanyBuilder organTypeNo(String value ){
            this.obj.organTypeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
        public CompanyBuilder whetherWholesalePool(Integer value ){
            this.obj.whetherWholesalePool = value;
            if( query == null  )
                query = new Query();
            this.query.where("whetherWholesalePool", value);
            return this;
        }
        
        public CompanyBuilder postcode(String value ){
            this.obj.postcode = value;
            if( query == null  )
                query = new Query();
            this.query.where("postcode", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}