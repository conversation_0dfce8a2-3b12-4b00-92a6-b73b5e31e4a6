package cn.wonhigh.baize.model.dto.activelock;

import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;

public class InventoryActiveLockDtlOccupied extends InventoryActiveLockDtl {

    private Integer occupiedQty;


    public Integer getOccupiedQty() {
        return occupiedQty == null ? 0 : occupiedQty;
    }

    public void setOccupiedQty(Integer occupiedQty) {
        this.occupiedQty = occupiedQty;
    }

}
