/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

/**
*auto generate start ,don't modify
* 售罄配置表
**/
public class SellOutConfig  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1726282483448L;

    @Label("单据编号")
    private String billNo;
    
    //状态 (0-按分类,1-按商品)
    @Label(value = "状态", defaultVal = "0") 
    private Integer type;
    
    //品牌编码
    @Label("品牌编码") 
    private String brandNo;
    
    //品牌名称
    @Label("品牌名称") 
    private String brandName;
    
    //属性分类编码
    @Label("属性分类编码") 
    private String classifyCode;
    
    //属性分类名称
    @Label("属性分类名称") 
    private String classifyName;
    
    //属性值编码
    @Label("属性值编码") 
    private String classifyValueCode;
    
    //属性值名称
    @Label("属性值名称") 
    private String classifyValueName;
    
    //商品内码
    @Label("商品内码") 
    private String itemNo;
    
    //尺寸编号
    @Label("尺寸编号") 
    private String sizeNo;
    
    //商品编码
    @Label("商品编码") 
    private String itemCode;
    
    //目标字段(1,2,3,4,5...91)
    @Label("目标字段") 
    private String targetField;
    
    //目标值
    @Label("目标值") 
    private BigDecimal targetValue;
    
    //状态 (0-启用,1-停用)
    @Label(value = "状态", defaultVal = "0") 
    private Integer status;

    private String statusName;

    private List<SellOutConfigDtl> sellOutConfigDtlList;

    public List<SellOutConfigDtl> getSellOutConfigDtlList() {
        return sellOutConfigDtlList;
    }

    public void setSellOutConfigDtlList(List<SellOutConfigDtl> sellOutConfigDtlList) {
        this.sellOutConfigDtlList = sellOutConfigDtlList;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Integer getType(){
        return  type;
    }
    public void setType(Integer val ){
        type = val;
    }
    
    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    
    public String getBrandName(){
        return  brandName;
    }
    public void setBrandName(String val ){
        brandName = val;
    }
    
    public String getClassifyCode(){
        return  classifyCode;
    }
    public void setClassifyCode(String val ){
        classifyCode = val;
    }
    
    public String getClassifyName(){
        return  classifyName;
    }
    public void setClassifyName(String val ){
        classifyName = val;
    }
    
    public String getClassifyValueCode(){
        return  classifyValueCode;
    }
    public void setClassifyValueCode(String val ){
        classifyValueCode = val;
    }
    
    public String getClassifyValueName(){
        return  classifyValueName;
    }
    public void setClassifyValueName(String val ){
        classifyValueName = val;
    }
    
    public String getItemNo(){
        return  itemNo;
    }
    public void setItemNo(String val ){
        itemNo = val;
    }
    
    public String getSizeNo(){
        return  sizeNo;
    }
    public void setSizeNo(String val ){
        sizeNo = val;
    }
    
    public String getItemCode(){
        return  itemCode;
    }
    public void setItemCode(String val ){
        itemCode = val;
    }
    
    public String getTargetField(){
        return  targetField;
    }
    public void setTargetField(String val ){
        targetField = val;
    }
    
    public BigDecimal getTargetValue() {
		return targetValue;
	}

	public void setTargetValue(BigDecimal targetValue) {
		this.targetValue = targetValue;
	}

	public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
        this.setStatusName(1 == status ? "禁用" : "启用");
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public SellOutConfigBuilder build(){
        return new SellOutConfigBuilder(this);
    }

    public static class SellOutConfigBuilder extends AbstractEntryBuilder<SellOutConfig>{

        private SellOutConfigBuilder(SellOutConfig entry){
            this.obj = entry;
        }

       @Override
		public SellOutConfig object() {
			return this.obj;
		}

        
        public SellOutConfigBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }

        public SellOutConfigBuilder billNo(String value ){
            this.obj.billNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public SellOutConfigBuilder type(Integer value ){
            this.obj.type = value;
            if( query == null  )
                query = new Query();
            this.query.where("type", value);
            return this;
        }
        
        public SellOutConfigBuilder brandNo(String value ){
            this.obj.brandNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public SellOutConfigBuilder brandName(String value ){
            this.obj.brandName = value;
            if( query == null  )
                query = new Query();
            this.query.where("brandName", value);
            return this;
        }
        
        public SellOutConfigBuilder classifyCode(String value ){
            this.obj.classifyCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("classifyCode", value);
            return this;
        }
        
        public SellOutConfigBuilder classifyName(String value ){
            this.obj.classifyName = value;
            if( query == null  )
                query = new Query();
            this.query.where("classifyName", value);
            return this;
        }
        
        public SellOutConfigBuilder classifyValueCode(String value ){
            this.obj.classifyValueCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("classifyValueCode", value);
            return this;
        }
        
        public SellOutConfigBuilder classifyValueName(String value ){
            this.obj.classifyValueName = value;
            if( query == null  )
                query = new Query();
            this.query.where("classifyValueName", value);
            return this;
        }
        
        public SellOutConfigBuilder itemNo(String value ){
            this.obj.itemNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("itemNo", value);
            return this;
        }
        
        public SellOutConfigBuilder sizeNo(String value ){
            this.obj.sizeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("sizeNo", value);
            return this;
        }
        
        public SellOutConfigBuilder itemCode(String value ){
            this.obj.itemCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("itemCode", value);
            return this;
        }
        
        public SellOutConfigBuilder targetField(String value ){
            this.obj.targetField = value;
            if( query == null  )
                query = new Query();
            this.query.where("targetField", value);
            return this;
        }
        
        public SellOutConfigBuilder targetValue(BigDecimal value ){
            this.obj.targetValue = value;
            if( query == null  )
                query = new Query();
            this.query.where("targetValue", value);
            return this;
        }
        
        public SellOutConfigBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public SellOutConfigBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public SellOutConfigBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public SellOutConfigBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public SellOutConfigBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}