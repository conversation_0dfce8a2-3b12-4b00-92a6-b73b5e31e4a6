package cn.wonhigh.baize.model.dto.brandInventoryRange;

import cn.mercury.annotation.Label;

import java.io.Serializable;
import java.util.List;

public class BrandInventoryRangeSaveDto implements Serializable {

    //渠道(1-OMS 2-天虹 3-ADSFS)
    private Integer channel;

    //类型：1-鞋,2-体育,3-新业务,4-小程序,5-大众点评,6-童鞋.修改类型为varchar(32)便于支持虚仓编码
    private String bussinessType;

    //安全库存
    private Integer safetyStock;

    //共享比例
    private Integer sharingRatio;

    //季节
    private List<String> purchaseSeason;

    //年份(指上市的年份,下拉框选择,值: 2006~2026,默认当年)
    private List<String> years;

    //品牌编码
    private String brandNo;
    private String brandName;

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public String getBussinessType() {
        return bussinessType;
    }

    public void setBussinessType(String bussinessType) {
        this.bussinessType = bussinessType;
    }

    public Integer getSafetyStock() {
        return safetyStock;
    }

    public void setSafetyStock(Integer safetyStock) {
        this.safetyStock = safetyStock;
    }

    public Integer getSharingRatio() {
        return sharingRatio;
    }

    public void setSharingRatio(Integer sharingRatio) {
        this.sharingRatio = sharingRatio;
    }

    public List<String> getYears() {
        return years;
    }

    public void setYears(List<String> years) {
        this.years = years;
    }

    public List<String> getPurchaseSeason() {
        return purchaseSeason;
    }

    public void setPurchaseSeason(List<String> purchaseSeason) {
        this.purchaseSeason = purchaseSeason;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
}
