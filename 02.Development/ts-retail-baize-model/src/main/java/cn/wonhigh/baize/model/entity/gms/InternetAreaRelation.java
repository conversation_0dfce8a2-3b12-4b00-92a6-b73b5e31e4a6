/** zkh **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 优购与新零售地区关系表
**/
public class InternetAreaRelation  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1722501306522L;
    
    //优购省/市/县编码
    @Label("优购省") 
    private String outsideCode;
    
    //优购省/市/县称
    @Label("优购省") 
    private String outsideName;
    
    //新零售省/市/县编码
    @Label("新零售省") 
    private String retailCode;
    
    //新零售省/市/县名称
    @Label("新零售省") 
    private String retailName;
    
    //区域等级：1 省，2 市，3 县(地级市)
    @Label(value = "区域等级", defaultVal = "0") 
    private Integer level;
    
    //状态；0 无效，1有效
    @Label(value = "状态", defaultVal = "0") 
    private Integer status;
    
    //访问平台
    @Label("访问平台") 
    private String visitor;
    
    
    public String getOutsideCode(){
        return  outsideCode;
    }
    public void setOutsideCode(String val ){
        outsideCode = val;
    }
    
    public String getOutsideName(){
        return  outsideName;
    }
    public void setOutsideName(String val ){
        outsideName = val;
    }
    
    public String getRetailCode(){
        return  retailCode;
    }
    public void setRetailCode(String val ){
        retailCode = val;
    }
    
    public String getRetailName(){
        return  retailName;
    }
    public void setRetailName(String val ){
        retailName = val;
    }
    
    public Integer getLevel(){
        return  level;
    }
    public void setLevel(Integer val ){
        level = val;
    }
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public String getVisitor(){
        return  visitor;
    }
    public void setVisitor(String val ){
        visitor = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetAreaRelationBuilder build(){
        return new InternetAreaRelationBuilder(this);
    }

    public static class InternetAreaRelationBuilder extends AbstractEntryBuilder<InternetAreaRelation>{

        private InternetAreaRelationBuilder(InternetAreaRelation entry){
            this.obj = entry;
        }

       @Override
		public InternetAreaRelation object() {
			return this.obj;
		}

        
        public InternetAreaRelationBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetAreaRelationBuilder outsideCode(String value ){
            this.obj.outsideCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("outsideCode", value);
            return this;
        }
        
        public InternetAreaRelationBuilder outsideName(String value ){
            this.obj.outsideName = value;
            if( query == null  )
                query = new Query();
            this.query.where("outsideName", value);
            return this;
        }
        
        public InternetAreaRelationBuilder retailCode(String value ){
            this.obj.retailCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("retailCode", value);
            return this;
        }
        
        public InternetAreaRelationBuilder retailName(String value ){
            this.obj.retailName = value;
            if( query == null  )
                query = new Query();
            this.query.where("retailName", value);
            return this;
        }
        
        public InternetAreaRelationBuilder level(Integer value ){
            this.obj.level = value;
            if( query == null  )
                query = new Query();
            this.query.where("level", value);
            return this;
        }
        
        public InternetAreaRelationBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public InternetAreaRelationBuilder visitor(String value ){
            this.obj.visitor = value;
            if( query == null  )
                query = new Query();
            this.query.where("visitor", value);
            return this;
        }
        
        public InternetAreaRelationBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetAreaRelationBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetAreaRelationBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetAreaRelationBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}