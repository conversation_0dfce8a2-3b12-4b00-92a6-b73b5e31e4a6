/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 仓库(店铺)和订货单、位品牌关系信息
**/
public class OrgUnitBrandRel  extends cn.mercury.domain.BaseEntity<Integer>  
{

    private static final long serialVersionUID = 1694512246108L;
    
    /**
    *仓库(店铺)检索码
    **/ 
    @Label("仓库") 
    private String orgSearchCode;
    

    public String getOrgSearchCode(){
        return  orgSearchCode;
    }
    public void setOrgSearchCode(String val ){
        orgSearchCode = val;
    }
    /**
    *订货单位检索码
    **/ 
    @Label("订货单位检索码") 
    private String orderSearchCode;
    

    public String getOrderSearchCode(){
        return  orderSearchCode;
    }
    public void setOrderSearchCode(String val ){
        orderSearchCode = val;
    }
    /**
    *结算公司编码
    **/ 
    @Label("结算公司编码") 
    private String companyNo;
    

    public String getCompanyNo(){
        return  companyNo;
    }
    public void setCompanyNo(String val ){
        companyNo = val;
    }
    /**
    *店仓编码
    **/ 
    @Label("店仓编码") 
    private String shopStoreNo;
    

    public String getShopStoreNo(){
        return  shopStoreNo;
    }
    public void setShopStoreNo(String val ){
        shopStoreNo = val;
    }
    /**
    *店仓代号
    **/ 
    @Label("店仓代号") 
    private String shopStoreCode;
    

    public String getShopStoreCode(){
        return  shopStoreCode;
    }
    public void setShopStoreCode(String val ){
        shopStoreCode = val;
    }
    /**
    *店仓名称
    **/ 
    @Label("店仓名称") 
    private String shopStoreName;
    

    public String getShopStoreName(){
        return  shopStoreName;
    }
    public void setShopStoreName(String val ){
        shopStoreName = val;
    }
    /**
    *店仓检索码
    **/ 
    @Label("店仓检索码") 
    private String storeSearchCode;
    

    public String getStoreSearchCode(){
        return  storeSearchCode;
    }
    public void setStoreSearchCode(String val ){
        storeSearchCode = val;
    }
    /**
    *组织类型
    **/ 
    @Label("组织类型") 
    private Long orgType;
    

    public Long getOrgType(){
        return  orgType;
    }
    public void setOrgType(Long val ){
        orgType = val;
    }
    /**
    *经营区域编号
    **/ 
    @Label("经营区域编号") 
    private String zoneNo;
    

    public String getZoneNo(){
        return  zoneNo;
    }
    public void setZoneNo(String val ){
        zoneNo = val;
    }
    /**
    *经营区域名称
    **/ 
    @Label("经营区域名称") 
    private String zoneName;
    

    public String getZoneName(){
        return  zoneName;
    }
    public void setZoneName(String val ){
        zoneName = val;
    }
    /**
    *物流仓库名称
    **/ 
    @Label("物流仓库名称") 
    private String wmsStoreName;
    

    public String getWmsStoreName(){
        return  wmsStoreName;
    }
    public void setWmsStoreName(String val ){
        wmsStoreName = val;
    }
    /**
    *仓库(店铺)编码
    **/ 
    @Label("仓库") 
    private String storeNo;
    

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    /**
    *机构代号
    **/ 
    @Label("机构代号") 
    private String storeCode;
    

    public String getStoreCode(){
        return  storeCode;
    }
    public void setStoreCode(String val ){
        storeCode = val;
    }
    /**
    *仓库(店铺)名称
    **/ 
    @Label("仓库") 
    private String storeName;
    

    public String getStoreName(){
        return  storeName;
    }
    public void setStoreName(String val ){
        storeName = val;
    }
    /**
    *品牌编码
    **/ 
    @Label("品牌编码") 
    private String brandNo;
    

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    /**
    *仓库(店铺)状态
    **/ 
    @Label("仓库") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *机构类型(21:店仓  22:仓库)
    **/ 
    @Label("机构类型") 
    private Integer storeType;
    

    public Integer getStoreType(){
        return  storeType;
    }
    public void setStoreType(Integer val ){
        storeType = val;
    }
    /**
    *关系状态
    **/ 
    @Label("关系状态") 
    private Integer relStatus;
    

    public Integer getRelStatus(){
        return  relStatus;
    }
    public void setRelStatus(Integer val ){
        relStatus = val;
    }
    /**
    *经营城市编号
    **/ 
    @Label("经营城市编号") 
    private String organNo;
    

    public String getOrganNo(){
        return  organNo;
    }
    public void setOrganNo(String val ){
        organNo = val;
    }
    /**
    *物流仓库代号
    **/ 
    @Label("物流仓库代号") 
    private String wmsStoreNo;
    

    public String getWmsStoreNo(){
        return  wmsStoreNo;
    }
    public void setWmsStoreNo(String val ){
        wmsStoreNo = val;
    }
    /**
    *订货单位编号
    **/ 
    @Label("订货单位编号") 
    private String orderUnitNo;
    

    public String getOrderUnitNo(){
        return  orderUnitNo;
    }
    public void setOrderUnitNo(String val ){
        orderUnitNo = val;
    }
    /**
    *订货单位编码
    **/ 
    @Label("订货单位编码") 
    private String orderUnitCode;
    

    public String getOrderUnitCode(){
        return  orderUnitCode;
    }
    public void setOrderUnitCode(String val ){
        orderUnitCode = val;
    }
    /**
    *订货单位名称
    **/ 
    @Label("订货单位名称") 
    private String orderUnitName;
    

    public String getOrderUnitName(){
        return  orderUnitName;
    }
    public void setOrderUnitName(String val ){
        orderUnitName = val;
    }



    /**
     * 店铺细类
     **/
    private String multi;

    /**
     * 实虚仓类型(0：实仓, 1：虚仓, 2：外部虚仓)
     **/
    private Integer virtPhyStorageType;

    public String getMulti() {
        return multi;
    }

    public void setMulti(String multi) {
        this.multi = multi;
    }

    public Integer getVirtPhyStorageType() {
        return virtPhyStorageType;
    }

    public void setVirtPhyStorageType(Integer virtPhyStorageType) {
        this.virtPhyStorageType = virtPhyStorageType;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public static OrgUnitBrandRelBuilder build(){
        return new OrgUnitBrandRelBuilder(new OrgUnitBrandRel());
    }
    public static OrgUnitBrandRelBuilder build(OrgUnitBrandRel rel){
        return new OrgUnitBrandRelBuilder(rel);
    }

    public static class OrgUnitBrandRelBuilder extends AbstractEntryBuilder<OrgUnitBrandRel>{

        private OrgUnitBrandRelBuilder(OrgUnitBrandRel entry){
            this.obj = entry;
        }

       @Override
		public OrgUnitBrandRel object() {
			return this.obj;
		}
        
        public OrgUnitBrandRelBuilder orgSearchCode(String value ){
            
            this.obj.orgSearchCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orgSearchCode", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder orderSearchCode(String value ){
            
            this.obj.orderSearchCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSearchCode", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder companyNo(String value ){
            
            this.obj.companyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder shopStoreNo(String value ){
            
            this.obj.shopStoreNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopStoreNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder shopStoreCode(String value ){
            
            this.obj.shopStoreCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopStoreCode", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder shopStoreName(String value ){
            
            this.obj.shopStoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopStoreName", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder storeSearchCode(String value ){
            
            this.obj.storeSearchCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeSearchCode", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder orgType(Long value ){
            
            this.obj.orgType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orgType", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder zoneNo(String value ){
            
            this.obj.zoneNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("zoneNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder zoneName(String value ){
            
            this.obj.zoneName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("zoneName", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder wmsStoreName(String value ){
            
            this.obj.wmsStoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("wmsStoreName", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder id(Integer value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder storeNo(String value ){
            
            this.obj.storeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder storeCode(String value ){
            
            this.obj.storeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeCode", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder storeName(String value ){
            
            this.obj.storeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder brandNo(String value ){
            
            this.obj.brandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder storeType(Integer value ){
            
            this.obj.storeType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeType", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder relStatus(Integer value ){
            
            this.obj.relStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("relStatus", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder organNo(String value ){
            
            this.obj.organNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder wmsStoreNo(String value ){
            
            this.obj.wmsStoreNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("wmsStoreNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder orderUnitNo(String value ){
            
            this.obj.orderUnitNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder orderUnitCode(String value ){
            
            this.obj.orderUnitCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitCode", value);
            return this;
        }
        
        public OrgUnitBrandRelBuilder orderUnitName(String value ){
            
            this.obj.orderUnitName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitName", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }