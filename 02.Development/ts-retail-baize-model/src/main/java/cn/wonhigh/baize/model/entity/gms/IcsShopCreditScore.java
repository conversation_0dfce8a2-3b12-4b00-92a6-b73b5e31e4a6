/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 
**/
public class IcsShopCreditScore  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1726726567378L;
    
    //信用分日期
    @Label("信用分日期") 
    private Date creditDate;
    
    //备注
    @Label("备注") 
    private String remark;
    
    //状态:0关闭1开启
    @Label(value = "状态", defaultVal = "1") 
    private Integer status;
    
    //信用分
    @Label(value = "信用分", defaultVal = "0") 
    private Integer creditScore;
    
    //店铺名称
    @Label("店铺名称") 
    private String shopName;
    
    //店铺编码
    @Label("店铺编码") 
    private String shopNo;
    
    
    public Date getCreditDate(){
        return  creditDate;
    }
    public void setCreditDate(Date val ){
        creditDate = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public Integer getCreditScore(){
        return  creditScore;
    }
    public void setCreditScore(Integer val ){
        creditScore = val;
    }
    
    public String getShopName(){
        return  shopName;
    }
    public void setShopName(String val ){
        shopName = val;
    }
    
    public String getShopNo(){
        return  shopNo;
    }
    public void setShopNo(String val ){
        shopNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public IcsShopCreditScoreBuilder build(){
        return new IcsShopCreditScoreBuilder(this);
    }

    public static class IcsShopCreditScoreBuilder extends AbstractEntryBuilder<IcsShopCreditScore>{

        private IcsShopCreditScoreBuilder(IcsShopCreditScore entry){
            this.obj = entry;
        }

       @Override
		public IcsShopCreditScore object() {
			return this.obj;
		}

        
        public IcsShopCreditScoreBuilder creditDate(Date value ){
            this.obj.creditDate = value;
            if( query == null  )
                query = new Query();
            this.query.where("creditDate", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder creditScore(Integer value ){
            this.obj.creditScore = value;
            if( query == null  )
                query = new Query();
            this.query.where("creditScore", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder shopName(String value ){
            this.obj.shopName = value;
            if( query == null  )
                query = new Query();
            this.query.where("shopName", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder shopNo(String value ){
            this.obj.shopNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("shopNo", value);
            return this;
        }
        
        public IcsShopCreditScoreBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}