/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 质检信息明细
**/
public class RetailOrderReturnDtl  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778676L;
    
    /**
    *质检商品退货单价
    **/ 
    @Label("质检商品退货单价") 
    private BigDecimal salePrice;
    

    public BigDecimal getSalePrice(){
        return  salePrice;
    }
    public void setSalePrice(BigDecimal val ){
        salePrice = val;
    }
    /**
    *用于网站上展示的质检商品名称
    **/ 
    @Label("用于网站上展示的质检商品名称") 
    private String itemNameForshow;
    

    public String getItemNameForshow(){
        return  itemNameForshow;
    }
    public void setItemNameForshow(String val ){
        itemNameForshow = val;
    }
    /**
    *质检商品类型：0-正常品，1-原残,2-客残
    **/ 
    @Label("质检商品类型") 
    private String inStorageType;
    

    public String getInStorageType(){
        return  inStorageType;
    }
    public void setInStorageType(String val ){
        inStorageType = val;
    }
    /**
    *质检商品内码
    **/ 
    @Label("质检商品内码") 
    private String itemNo;
    

    public String getItemNo(){
        return  itemNo;
    }
    public void setItemNo(String val ){
        itemNo = val;
    }
    /**
    *质检商品条码
    **/ 
    @Label("质检商品条码") 
    private String barcode;
    

    public String getBarcode(){
        return  barcode;
    }
    public void setBarcode(String val ){
        barcode = val;
    }
    /**
    *质检商品编码
    **/ 
    @Label("质检商品编码") 
    private String itemCode;
    

    public String getItemCode(){
        return  itemCode;
    }
    public void setItemCode(String val ){
        itemCode = val;
    }
    /**
    *质检商品名称
    **/ 
    @Label("质检商品名称") 
    private String itemName;
    

    public String getItemName(){
        return  itemName;
    }
    public void setItemName(String val ){
        itemName = val;
    }
    /**
    *新旧档期标识 1新0旧
    **/ 
    @Label("新旧档期标识") 
    private Integer schedule;
    

    public Integer getSchedule(){
        return  schedule;
    }
    public void setSchedule(Integer val ){
        schedule = val;
    }
    /**
    *质检商品颜色编码
    **/ 
    @Label("质检商品颜色编码") 
    private String colorNo;
    

    public String getColorNo(){
        return  colorNo;
    }
    public void setColorNo(String val ){
        colorNo = val;
    }
    /**
    *质检商品颜色名称
    **/ 
    @Label("质检商品颜色名称") 
    private String colorName;
    

    public String getColorName(){
        return  colorName;
    }
    public void setColorName(String val ){
        colorName = val;
    }
    /**
    *质检商品品牌编码
    **/ 
    @Label("质检商品品牌编码") 
    private String brandNo;
    

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    /**
    *质检商品品牌名称
    **/ 
    @Label("质检商品品牌名称") 
    private String brandName;
    

    public String getBrandName(){
        return  brandName;
    }
    public void setBrandName(String val ){
        brandName = val;
    }
    /**
    *质检商品尺寸编号
    **/ 
    @Label("质检商品尺寸编号") 
    private String sizeNo;
    

    public String getSizeNo(){
        return  sizeNo;
    }
    public void setSizeNo(String val ){
        sizeNo = val;
    }
    /**
    *质检商品尺寸分类
    **/ 
    @Label("质检商品尺寸分类") 
    private String sizeKind;
    

    public String getSizeKind(){
        return  sizeKind;
    }
    public void setSizeKind(String val ){
        sizeKind = val;
    }
    /**
    *质检商品规格,商品颜色尺码，以逗号劈开
    **/ 
    @Label("质检商品规格") 
    private String commoditySpecificationStr;
    

    public String getCommoditySpecificationStr(){
        return  commoditySpecificationStr;
    }
    public void setCommoditySpecificationStr(String val ){
        commoditySpecificationStr = val;
    }
    /**
    *折扣率
    **/ 
    @Label("折扣率") 
    private BigDecimal discount;
    

    public BigDecimal getDiscount(){
        return  discount;
    }
    public void setDiscount(BigDecimal val ){
        discount = val;
    }
    /**
    *通知质检数量
    **/ 
    @Label("通知质检数量") 
    private Integer askQty;
    

    public Integer getAskQty(){
        return  askQty;
    }
    public void setAskQty(Integer val ){
        askQty = val;
    }
    /**
    *质检商品数量
    **/ 
    @Label("质检商品数量") 
    private Integer sendOutQty;
    

    public Integer getSendOutQty(){
        return  sendOutQty;
    }
    public void setSendOutQty(Integer val ){
        sendOutQty = val;
    }
    /**
    *质检商品退货总金额
    **/ 
    @Label("质检商品退货总金额") 
    private BigDecimal quotePrice;
    

    public BigDecimal getQuotePrice(){
        return  quotePrice;
    }
    public void setQuotePrice(BigDecimal val ){
        quotePrice = val;
    }
    /**
    *质检商品类别编码
    **/ 
    @Label("质检商品类别编码") 
    private String categoryNo;
    

    public String getCategoryNo(){
        return  categoryNo;
    }
    public void setCategoryNo(String val ){
        categoryNo = val;
    }
    /**
    *单据编号
    **/ 
    @Label("单据编号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    /**
    *质检sku编码
    **/ 
    @Label("质检") 
    private String skuNo;
    

    public String getSkuNo(){
        return  skuNo;
    }
    public void setSkuNo(String val ){
        skuNo = val;
    }
    /**
    *外部订单号
    **/ 
    @Label("外部订单号") 
    private String outOrderId;
    

    public String getOutOrderId(){
        return  outOrderId;
    }
    public void setOutOrderId(String val ){
        outOrderId = val;
    }
    /**
    *分库字段：本部+序号
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *零售分库字段：本部+大区
    **/ 
    @Label("零售分库字段") 
    private String retailFlag;
    

    public String getRetailFlag(){
        return  retailFlag;
    }
    public void setRetailFlag(String val ){
        retailFlag = val;
    }
    /**
    *问题原因
    **/ 
    @Label("问题原因") 
    private String questionReason;
    

    public String getQuestionReason(){
        return  questionReason;
    }
    public void setQuestionReason(String val ){
        questionReason = val;
    }
    /**
    *问题类型（WMS给的枚举类型）
    **/ 
    @Label("问题类型") 
    private String questionType;
    

    public String getQuestionType(){
        return  questionType;
    }
    public void setQuestionType(String val ){
        questionType = val;
    }
    /**
    *实际发货商品尺寸编号
    **/ 
    @Label("实际发货商品尺寸编号") 
    private String delevirSizeNo;
    

    public String getDelevirSizeNo(){
        return  delevirSizeNo;
    }
    public void setDelevirSizeNo(String val ){
        delevirSizeNo = val;
    }
    /**
    *实际发货商品品牌名称
    **/ 
    @Label("实际发货商品品牌名称") 
    private String delevirBrandName;
    

    public String getDelevirBrandName(){
        return  delevirBrandName;
    }
    public void setDelevirBrandName(String val ){
        delevirBrandName = val;
    }
    /**
    *实际发货商品品牌编码
    **/ 
    @Label("实际发货商品品牌编码") 
    private String delevirBrandNo;
    

    public String getDelevirBrandNo(){
        return  delevirBrandNo;
    }
    public void setDelevirBrandNo(String val ){
        delevirBrandNo = val;
    }
    /**
    *实际发货商品编码
    **/ 
    @Label("实际发货商品编码") 
    private String delevirItemCode;
    

    public String getDelevirItemCode(){
        return  delevirItemCode;
    }
    public void setDelevirItemCode(String val ){
        delevirItemCode = val;
    }
    /**
    *实际发货商品条码
    **/ 
    @Label("实际发货商品条码") 
    private String delevirBarcode;
    

    public String getDelevirBarcode(){
        return  delevirBarcode;
    }
    public void setDelevirBarcode(String val ){
        delevirBarcode = val;
    }
    /**
    *实际发货商品内码
    **/ 
    @Label("实际发货商品内码") 
    private String delevirItemNo;
    

    public String getDelevirItemNo(){
        return  delevirItemNo;
    }
    public void setDelevirItemNo(String val ){
        delevirItemNo = val;
    }
    /**
    *实际发货sku编码
    **/ 
    @Label("实际发货") 
    private String delevirSkuNo;
    

    public String getDelevirSkuNo(){
        return  delevirSkuNo;
    }
    public void setDelevirSkuNo(String val ){
        delevirSkuNo = val;
    }
    /**
    *唯品会PO单号
    **/ 
    @Label("唯品会") 
    private String poNo;
    

    public String getPoNo(){
        return  poNo;
    }
    public void setPoNo(String val ){
        poNo = val;
    }
    /**
    *箱号
    **/ 
    @Label("箱号") 
    private String boxNo;
    

    public String getBoxNo(){
        return  boxNo;
    }
    public void setBoxNo(String val ){
        boxNo = val;
    }
    /**
    *售后图片备注
    **/ 
    @Label("售后图片备注") 
    private String imageRemark;
    

    public String getImageRemark(){
        return  imageRemark;
    }
    public void setImageRemark(String val ){
        imageRemark = val;
    }
    /**
    *售后图片URL
    **/ 
    @Label("售后图片") 
    private String imageUrl;
    

    public String getImageUrl(){
        return  imageUrl;
    }
    public void setImageUrl(String val ){
        imageUrl = val;
    }
    /**
    *pos订单详情id, 回调pos接口用
    **/ 
    @Label("订单详情") 
    private String posDtlId;
    

    public String getPosDtlId(){
        return  posDtlId;
    }
    public void setPosDtlId(String val ){
        posDtlId = val;
    }
    /**
    *商品类型(0：普通商品 1：赠品 2：换购 3：从商品--针对加价购的促销)
    **/ 
    @Label("商品类型") 
    private Integer commodityType;
    

    public Integer getCommodityType(){
        return  commodityType;
    }
    public void setCommodityType(Integer val ){
        commodityType = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public RetailOrderReturnDtlBuilder build(){
        return new RetailOrderReturnDtlBuilder(this);
    }

    public static class RetailOrderReturnDtlBuilder extends AbstractEntryBuilder<RetailOrderReturnDtl>{

        private RetailOrderReturnDtlBuilder(RetailOrderReturnDtl entry){
            this.obj = entry;
        }

       @Override
		public RetailOrderReturnDtl object() {
			return this.obj;
		}

        
        public RetailOrderReturnDtlBuilder salePrice(BigDecimal value ){
            
            this.obj.salePrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("salePrice", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder itemNameForshow(String value ){
            
            this.obj.itemNameForshow = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemNameForshow", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder inStorageType(String value ){
            
            this.obj.inStorageType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("inStorageType", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder itemNo(String value ){
            
            this.obj.itemNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder barcode(String value ){
            
            this.obj.barcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("barcode", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder itemCode(String value ){
            
            this.obj.itemCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemCode", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder itemName(String value ){
            
            this.obj.itemName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemName", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder schedule(Integer value ){
            
            this.obj.schedule = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("schedule", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder colorNo(String value ){
            
            this.obj.colorNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("colorNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder colorName(String value ){
            
            this.obj.colorName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("colorName", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder brandNo(String value ){
            
            this.obj.brandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder brandName(String value ){
            
            this.obj.brandName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandName", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder sizeNo(String value ){
            
            this.obj.sizeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder sizeKind(String value ){
            
            this.obj.sizeKind = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeKind", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder commoditySpecificationStr(String value ){
            
            this.obj.commoditySpecificationStr = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commoditySpecificationStr", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder discount(BigDecimal value ){
            
            this.obj.discount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("discount", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder askQty(Integer value ){
            
            this.obj.askQty = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("askQty", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder sendOutQty(Integer value ){
            
            this.obj.sendOutQty = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendOutQty", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder quotePrice(BigDecimal value ){
            
            this.obj.quotePrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("quotePrice", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder categoryNo(String value ){
            
            this.obj.categoryNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("categoryNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder skuNo(String value ){
            
            this.obj.skuNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder outOrderId(String value ){
            
            this.obj.outOrderId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outOrderId", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder retailFlag(String value ){
            
            this.obj.retailFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailFlag", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder questionReason(String value ){
            
            this.obj.questionReason = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("questionReason", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder questionType(String value ){
            
            this.obj.questionType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("questionType", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder delevirSizeNo(String value ){
            
            this.obj.delevirSizeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("delevirSizeNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder delevirBrandName(String value ){
            
            this.obj.delevirBrandName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("delevirBrandName", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder delevirBrandNo(String value ){
            
            this.obj.delevirBrandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("delevirBrandNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder delevirItemCode(String value ){
            
            this.obj.delevirItemCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("delevirItemCode", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder delevirBarcode(String value ){
            
            this.obj.delevirBarcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("delevirBarcode", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder delevirItemNo(String value ){
            
            this.obj.delevirItemNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("delevirItemNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder delevirSkuNo(String value ){
            
            this.obj.delevirSkuNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("delevirSkuNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder poNo(String value ){
            
            this.obj.poNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("poNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder boxNo(String value ){
            
            this.obj.boxNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("boxNo", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder imageRemark(String value ){
            
            this.obj.imageRemark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("imageRemark", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder imageUrl(String value ){
            
            this.obj.imageUrl = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("imageUrl", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder posDtlId(String value ){
            
            this.obj.posDtlId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("posDtlId", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder commodityType(Integer value ){
            
            this.obj.commodityType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commodityType", value);
            return this;
        }
        
        public RetailOrderReturnDtlBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }