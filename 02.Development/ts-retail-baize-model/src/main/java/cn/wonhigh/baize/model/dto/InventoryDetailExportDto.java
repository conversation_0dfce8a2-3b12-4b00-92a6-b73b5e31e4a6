package cn.wonhigh.baize.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2024/8/21 15:45
 */
public class InventoryDetailExportDto implements Serializable {
    private static final long serialVersionUID = -8002119375739088926L;
    @ExcelProperty("渠道店铺编码")
    private String shopNo;
    @ExcelProperty("聚合仓编码")
    private String vstoreCode;
    @ExcelProperty("聚合仓名称")
    private String vstoreName;
    @ExcelProperty("机构编码")
    private String storeNo;
    @ExcelProperty("机构名称")
    private String storeName;
    @ExcelProperty("货管编码")
    private String orderUnitNo;
    @ExcelProperty("货管名称")
    private String orderUnitName;
    @ExcelProperty("品牌编码")
    private String brandNo;
    @ExcelProperty("商品编码")
    private String itemCode;
    @ExcelProperty("sku编码")
    private String skuNo;
    @ExcelProperty("实物存")
    private int balanceQty;
    @ExcelProperty("可售存")
    private int availableQty;
    @ExcelProperty("可派单库存")
    private int shareQty;
    @ExcelProperty("保护店")
    private String unavailableShopFlag;
    @ExcelProperty("拒绝派单记录")
    private String refuseRecordFlag;
    @ExcelProperty("线上不可派库存")
    private int defectiveGoodsQty;
    @ExcelProperty("不可用库存")
    private int unQty;

    public String getShopNo() {
        return shopNo;
    }

    public void setShopNo(String shopNo) {
        this.shopNo = shopNo;
    }

    public String getVstoreCode() {
        return vstoreCode;
    }

    public void setVstoreCode(String vstoreCode) {
        this.vstoreCode = vstoreCode;
    }

    public String getVstoreName() {
        return vstoreName;
    }

    public void setVstoreName(String vstoreName) {
        this.vstoreName = vstoreName;
    }

    public String getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(String storeNo) {
        this.storeNo = storeNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getOrderUnitNo() {
        return orderUnitNo;
    }

    public void setOrderUnitNo(String orderUnitNo) {
        this.orderUnitNo = orderUnitNo;
    }

    public String getOrderUnitName() {
        return orderUnitName;
    }

    public void setOrderUnitName(String orderUnitName) {
        this.orderUnitName = orderUnitName;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public int getBalanceQty() {
        return balanceQty;
    }

    public void setBalanceQty(int balanceQty) {
        this.balanceQty = balanceQty;
    }

    public int getAvailableQty() {
        return availableQty;
    }

    public void setAvailableQty(int availableQty) {
        this.availableQty = availableQty;
    }

    public int getShareQty() {
        return shareQty;
    }

    public void setShareQty(int shareQty) {
        this.shareQty = shareQty;
    }

    public String getUnavailableShopFlag() {
        return unavailableShopFlag;
    }

    public void setUnavailableShopFlag(String unavailableShopFlag) {
        this.unavailableShopFlag = unavailableShopFlag;
    }

    public String getRefuseRecordFlag() {
        return refuseRecordFlag;
    }

    public void setRefuseRecordFlag(String refuseRecordFlag) {
        this.refuseRecordFlag = refuseRecordFlag;
    }

    public int getDefectiveGoodsQty() {
        return defectiveGoodsQty;
    }

    public void setDefectiveGoodsQty(int defectiveGoodsQty) {
        this.defectiveGoodsQty = defectiveGoodsQty;
    }

    public int getUnQty() {
        return unQty;
    }

    public void setUnQty(int unQty) {
        this.unQty = unQty;
    }
}
