package cn.wonhigh.baize.model.entity.gms;

import cn.mercury.utils.JsonDateSerializer$19;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.util.Date;
public class ShareInventoryRange extends cn.mercury.domain.BaseEntity<String>   {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 主键id
	 */
	private String id;

	/**
	 * '商品编码',
	 */
	@ExcelProperty("商品编码")
	private String itemCode;

	/**
	 * 商品系统唯一编码
	 */
	private String itemNo;

	/**
	 * 商品名称
	 */
	@ExcelProperty("商品名称")
	private String itemName;

	/**
	 * 品牌名称
	 */
	private String brandName;
	/**
	 * 品牌编码
	 */
	@ExcelProperty("品牌编码")
	private String brandNo;

	/**
	 * 尺寸分类
	 */
	private String sizeKind;

	/**
	 * 地区编码
	 */
	private String zoneNo;

	/**
	 * 地区名字
	 */
	private String zoneName;

	/**
	 * 组织类别编码 category
	 */
	private String organTypeNo;

	/**
	 * 组织类别名称 belonger
	 */
	private String organTypeName;

	/**
	 * 尺寸编码
	 */
	@ExcelProperty("尺寸编码")
	private String sizeNo;

	/**
	 * 共享比例
	 */
	@ExcelProperty("共享比例")
	private Integer sharingRatio;

	/**
	 * 商品条码
	 */
	@ExcelProperty("商品条码")
	private String barcode;

	/**
	 * sku编码
	 */
	private String skuNo;

	@ExcelProperty("聚合仓名称")
	private String vstoreName;
	/**
	 * @return the vstoreName
	 */
	public String getVstoreName() {
		return vstoreName;
	}

	/**
	 * @param vstoreName the vstoreName to set
	 */
	public void setVstoreName(String vstoreName) {
		this.vstoreName = vstoreName;
	}

	/**
	 * 创建时间
	 */
	@ExcelProperty("创建时间")
	@JsonSerialize(using = JsonDateSerializer$19.class)
	private Date createTime;

	/**
	 * 修改时间
	 */
	@ExcelProperty("修改时间")
	@JsonSerialize(using = JsonDateSerializer$19.class)
	private Date updateTime;

	/**
	 * safety_stock 安全库存
	 */
	@ExcelProperty("安全库存")
	private Integer safetyStock;

	/**
	 * 开始时间
	 * 
	 */
	@ExcelProperty("开始时间")
	@JsonSerialize(using = JsonDateSerializer$19.class)
	private Date startTime;

	/**
	 * 结束时间
	 * 
	 */
	@ExcelProperty("结束时间")
	@JsonSerialize(using = JsonDateSerializer$19.class)
	private Date endTime;

	@ExcelProperty("仓库编码")
	private String interfacePlatform;

	@ExcelProperty("创建人")
	private String createUser;

	@ExcelProperty("更新人")
	private String updateUser;

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}

	/**
	 * @return the interfacePlatform
	 */
	public String getInterfacePlatform() {
		return interfacePlatform;
	}

	/**
	 * @param interfacePlatform the interfacePlatform to set
	 */
	public void setInterfacePlatform(String interfacePlatform) {
		this.interfacePlatform = interfacePlatform;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Integer getSafetyStock() {
		return safetyStock;
	}

	public void setSafetyStock(Integer safetyStock) {
		this.safetyStock = safetyStock;
	}

	public ShareInventoryRange(String skuNo) {
		this.skuNo = skuNo;
	}

	public ShareInventoryRange() {
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getBrandNo() {
		return brandNo;
	}

	public void setBrandNo(String brandNo) {
		this.brandNo = brandNo;
	}

	public String getSizeKind() {
		return sizeKind;
	}

	public void setSizeKind(String sizeKind) {
		this.sizeKind = sizeKind;
	}

	public String getZoneNo() {
		return zoneNo;
	}

	public void setZoneNo(String zoneNo) {
		this.zoneNo = zoneNo;
	}

	public String getZoneName() {
		return zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public String getOrganTypeNo() {
		return organTypeNo;
	}

	public void setOrganTypeNo(String organTypeNo) {
		this.organTypeNo = organTypeNo;
	}

	public String getOrganTypeName() {
		return organTypeName;
	}

	public void setOrganTypeName(String organTypeName) {
		this.organTypeName = organTypeName;
	}

	public String getSizeNo() {
		return sizeNo;
	}

	public void setSizeNo(String sizeNo) {
		this.sizeNo = sizeNo;
	}

	public Integer getSharingRatio() {
		return sharingRatio;
	}

	public void setSharingRatio(Integer sharingRatio) {
		this.sharingRatio = sharingRatio;
	}

	public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	public String getSkuNo() {
		return skuNo;
	}

	public void setSkuNo(String skuNo) {
		this.skuNo = skuNo;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	private String status;
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	private Integer rowIndex;
	public Integer getRowIndex() {
		return rowIndex;
	}

	public void setRowIndex(Integer rowIndex) {
		this.rowIndex = rowIndex;
	}

}