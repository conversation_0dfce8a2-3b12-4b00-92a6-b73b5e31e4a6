package cn.wonhigh.baize.model.enums;

public enum StoreTypeEnums {

    /**
     * 21 - 店
     */
    SHOP("21", "店", 21),

    /**
     * 22 - 仓
     */
    STORE("22", "仓", 22);

    /**
     * 仓店类型
     */
    private String type;

    /**
     * 状态描述
     */
    private String desc;

    /**
     * 仓店数字类型
     */
    private Integer intType;

    /**
     * @param type
     * @param desc
     * @param intType
     */
    private StoreTypeEnums(String type, String desc, Integer intType) {
        this.type = type;
        this.desc = desc;
        this.intType = intType;
    }

    /**
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @return the desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * @return the intType
     */
    public Integer getIntType() {
        return intType;
    }

    public static StoreTypeEnums getByIntType(int intType) {
        for (StoreTypeEnums value : values()) {
            if (value.intType.equals(intType)) {
                return value;
            }
        }
        return null;
    }

}
