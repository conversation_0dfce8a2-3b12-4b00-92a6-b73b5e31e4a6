package cn.wonhigh.baize.model.entity.ios;

import cn.mercury.annotation.Label;
import cn.mercury.domain.BasicEntity;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;

/**
 * auto generate start, don't modify
 * 网络销售出库通知单
 */
public class RetailOrderOutNt extends BasicEntity {

    private static final long serialVersionUID = 1L;

    // 单据编号
    @Label("单据编号")
    private String billNo;

    // 单据类型 4001
    @Label(value = "单据类型", defaultVal = "0")
    private Integer billType;

    // 来源单号
    @Label("来源单号")
    private String refBillNo;

    // 来源类型 目前是网销通知单4000
    @Label("来源类型")
    private Integer refBillType;

    // 订单状态 :5 确认 28 缺货 52部分缺货 99作废 100完结 70未下发
    @Label("订单状态")
    private Integer status;


    // 子订单号
    @Label("子订单号")
    private String orderSubNo;

    // 原始订单号
    @Label("原始订单号")
    private String originalOrderNo;

    // 来源平台
    @Label("来源平台")
    private String originPlatform;

    // 1店发，2仓发
    @Label("发货类型")
    private Integer sendStoreType;

    // 仓库编码
    @Label("仓库编码")
    private String sendStoreNo;

    // 发货方名称
    @Label("发货方名称")
    private String sendStoreName;

    // 订货单位
    @Label("订货单位")
    private String orderUnitNo;

    // 订货单位名称
    @Label("订货单位名称")
    private String orderUnitName;

    // 通知时间
    @Label("通知时间")
    private Date noticeDate;

    // 总数量
    @Label("总数量")
    private Integer sendDetailTotal;

    // 销售总价
    @Label("销售总价")
    private BigDecimal totalPrice;


    // 备注
    @Label("备注")
    private String remark;

    // 分库字段：本部+序号
    @Label("分库字段")
    private String shardingFlag;

    // 零售分库字段：本部+大区
    @Label("零售分库字段")
    private String retailFlag;

    // 订单销售总价
    @Label("订单销售总价")
    private BigDecimal orderPayTotalAmount;

    // 平台名称
    @Label("平台名称")
    private String originPlatformName;

    // 店铺名称
    @Label("店铺名称")
    private String shopName;

    // 店铺编码
    @Label("店铺编码")
    private String shopNo;

    // 订单创建时间
    @Label("订单创建时间")
    private Date orderCreateTime;

    // 结算公司
    @Label("结算公司")
    private String companyNo;

    // 客户地址编码
    @Label("客户地址编码")
    private Integer addressId;

    // 业绩所属店铺（初次注册店铺）
    @Label("业绩所属店铺")
    private String buyerStoreid;

    // 接口平台
    @Label("接口平台")
    private String interfacePlatform;

    // 基本积分
    @Label(value = "基本积分", defaultVal = "0")
    private Integer basescore;

    // 是否唯品会JIT单据 0-否 1-是
    @Label(value = "是否唯品会JIT单据", defaultVal = "0")
    private Integer isVip;

    // 销售店铺结算公司
    @Label("销售店铺结算公司")
    private String saleCompanyNo;

    // 运输方式
    @Label("运输方式")
    private String transportType;

    // 要求到货时间
    @Label("要求到货时间")
    private Date arriveTime;

    // 要求出库时间
    @Label("要求出库时间")
    private Date outStoreTime;

    // 关闭编码
    @Label("关闭编码")
    private String closeCode;

    // 渠道预占编码
    @Label("渠道预占编码")
    private String channelOccupiedNo;


    // Getter and Setter


    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public String getRefBillNo() {
        return refBillNo;
    }

    public void setRefBillNo(String refBillNo) {
        this.refBillNo = refBillNo;
    }

    public Integer getRefBillType() {
        return refBillType;
    }

    public void setRefBillType(Integer refBillType) {
        this.refBillType = refBillType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOrderSubNo() {
        return orderSubNo;
    }

    public void setOrderSubNo(String orderSubNo) {
        this.orderSubNo = orderSubNo;
    }

    public String getOriginalOrderNo() {
        return originalOrderNo;
    }

    public void setOriginalOrderNo(String originalOrderNo) {
        this.originalOrderNo = originalOrderNo;
    }

    public String getOriginPlatform() {
        return originPlatform;
    }

    public void setOriginPlatform(String originPlatform) {
        this.originPlatform = originPlatform;
    }

    public Integer getSendStoreType() {
        return sendStoreType;
    }

    public void setSendStoreType(Integer sendStoreType) {
        this.sendStoreType = sendStoreType;
    }

    public String getSendStoreNo() {
        return sendStoreNo;
    }

    public void setSendStoreNo(String sendStoreNo) {
        this.sendStoreNo = sendStoreNo;
    }

    public String getSendStoreName() {
        return sendStoreName;
    }

    public void setSendStoreName(String sendStoreName) {
        this.sendStoreName = sendStoreName;
    }

    public String getOrderUnitNo() {
        return orderUnitNo;
    }

    public void setOrderUnitNo(String orderUnitNo) {
        this.orderUnitNo = orderUnitNo;
    }

    public String getOrderUnitName() {
        return orderUnitName;
    }

    public void setOrderUnitName(String orderUnitName) {
        this.orderUnitName = orderUnitName;
    }

    public Date getNoticeDate() {
        return noticeDate;
    }

    public void setNoticeDate(Date noticeDate) {
        this.noticeDate = noticeDate;
    }

    public Integer getSendDetailTotal() {
        return sendDetailTotal;
    }

    public void setSendDetailTotal(Integer sendDetailTotal) {
        this.sendDetailTotal = sendDetailTotal;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getShardingFlag() {
        return shardingFlag;
    }

    public void setShardingFlag(String shardingFlag) {
        this.shardingFlag = shardingFlag;
    }

    public String getRetailFlag() {
        return retailFlag;
    }

    public void setRetailFlag(String retailFlag) {
        this.retailFlag = retailFlag;
    }

    public BigDecimal getOrderPayTotalAmount() {
        return orderPayTotalAmount;
    }

    public void setOrderPayTotalAmount(BigDecimal orderPayTotalAmount) {
        this.orderPayTotalAmount = orderPayTotalAmount;
    }

    public String getOriginPlatformName() {
        return originPlatformName;
    }

    public void setOriginPlatformName(String originPlatformName) {
        this.originPlatformName = originPlatformName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopNo() {
        return shopNo;
    }

    public void setShopNo(String shopNo) {
        this.shopNo = shopNo;
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public String getBuyerStoreid() {
        return buyerStoreid;
    }

    public void setBuyerStoreid(String buyerStoreid) {
        this.buyerStoreid = buyerStoreid;
    }

    public String getInterfacePlatform() {
        return interfacePlatform;
    }

    public void setInterfacePlatform(String interfacePlatform) {
        this.interfacePlatform = interfacePlatform;
    }

    public Integer getBasescore() {
        return basescore;
    }

    public void setBasescore(Integer basescore) {
        this.basescore = basescore;
    }

    public Integer getIsVip() {
        return isVip;
    }

    public void setIsVip(Integer isVip) {
        this.isVip = isVip;
    }

    public String getSaleCompanyNo() {
        return saleCompanyNo;
    }

    public void setSaleCompanyNo(String saleCompanyNo) {
        this.saleCompanyNo = saleCompanyNo;
    }

    public String getTransportType() {
        return transportType;
    }

    public void setTransportType(String transportType) {
        this.transportType = transportType;
    }

    public Date getArriveTime() {
        return arriveTime;
    }

    public void setArriveTime(Date arriveTime) {
        this.arriveTime = arriveTime;
    }

    public Date getOutStoreTime() {
        return outStoreTime;
    }

    public void setOutStoreTime(Date outStoreTime) {
        this.outStoreTime = outStoreTime;
    }

    public String getCloseCode() {
        return closeCode;
    }

    public void setCloseCode(String closeCode) {
        this.closeCode = closeCode;
    }

    public String getChannelOccupiedNo() {
        return channelOccupiedNo;
    }

    public void setChannelOccupiedNo(String channelOccupiedNo) {
        this.channelOccupiedNo = channelOccupiedNo;
    }


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

}
