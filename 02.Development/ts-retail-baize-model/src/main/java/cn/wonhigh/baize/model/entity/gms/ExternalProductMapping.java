/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 商家商品映射表
**/
public class ExternalProductMapping  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1723987834754L;
    
    //商家编码
    @Label("商家编码") 
    private String merchantsCode;
    
    //商品条码
    @Label("商品条码") 
    private String barcode;
    
    //品牌编码
    @Label("品牌编码") 
    private String brandCode;
    
    //商品编码
    @Label("商品编码") 
    private String productCode;
    
    //商品尺码
    @Label("商品尺码") 
    private String sizeCode;

    private String skuNo;


    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getMerchantsCode(){
        return  merchantsCode;
    }
    public void setMerchantsCode(String val ){
        merchantsCode = val;
    }
    
    public String getBarcode(){
        return  barcode;
    }
    public void setBarcode(String val ){
        barcode = val;
    }
    
    public String getBrandCode(){
        return  brandCode;
    }
    public void setBrandCode(String val ){
        brandCode = val;
    }
    
    public String getProductCode(){
        return  productCode;
    }
    public void setProductCode(String val ){
        productCode = val;
    }
    
    public String getSizeCode(){
        return  sizeCode;
    }
    public void setSizeCode(String val ){
        sizeCode = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public ExternalProductMappingBuilder build(){
        return new ExternalProductMappingBuilder(this);
    }

    public static class ExternalProductMappingBuilder extends AbstractEntryBuilder<ExternalProductMapping>{

        private ExternalProductMappingBuilder(ExternalProductMapping entry){
            this.obj = entry;
        }

       @Override
		public ExternalProductMapping object() {
			return this.obj;
		}

        
        public ExternalProductMappingBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public ExternalProductMappingBuilder merchantsCode(String value ){
            this.obj.merchantsCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("merchantsCode", value);
            return this;
        }
        
        public ExternalProductMappingBuilder barcode(String value ){
            this.obj.barcode = value;
            if( query == null  )
                query = new Query();
            this.query.where("barcode", value);
            return this;
        }
        
        public ExternalProductMappingBuilder brandCode(String value ){
            this.obj.brandCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("brandCode", value);
            return this;
        }
        
        public ExternalProductMappingBuilder productCode(String value ){
            this.obj.productCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("productCode", value);
            return this;
        }
        
        public ExternalProductMappingBuilder sizeCode(String value ){
            this.obj.sizeCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("sizeCode", value);
            return this;
        }
        
        public ExternalProductMappingBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public ExternalProductMappingBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public ExternalProductMappingBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public ExternalProductMappingBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}