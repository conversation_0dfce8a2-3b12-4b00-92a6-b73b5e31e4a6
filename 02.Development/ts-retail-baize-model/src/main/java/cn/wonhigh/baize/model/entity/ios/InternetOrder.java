/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 网销订单表头
**/
public class InternetOrder  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778662L;
    
    /**
    *仓中仓订单指定发货仓
    **/ 
    @Label("仓中仓订单指定发货仓") 
    private String sendStore;
    

    public String getSendStore(){
        return  sendStore;
    }
    public void setSendStore(String val ){
        sendStore = val;
    }
    /**
    *实际运费
    **/ 
    @Label("实际运费") 
    private BigDecimal actualPostage;
    

    public BigDecimal getActualPostage(){
        return  actualPostage;
    }
    public void setActualPostage(BigDecimal val ){
        actualPostage = val;
    }
    /**
    *原始订单编码
    **/ 
    @Label("原始订单编码") 
    private String originalOrderNo;
    

    public String getOriginalOrderNo(){
        return  originalOrderNo;
    }
    public void setOriginalOrderNo(String val ){
        originalOrderNo = val;
    }
    /**
    *子订单号
    **/ 
    @Label("子订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    /**
    *来源平台
    **/ 
    @Label("来源平台") 
    private String originPlatform;
    

    public String getOriginPlatform(){
        return  originPlatform;
    }
    public void setOriginPlatform(String val ){
        originPlatform = val;
    }
    /**
    *来源平台名称(ADSFS存放店铺编码名称)
    **/ 
    @Label("来源平台名称") 
    private String originPlatformName;
    

    public String getOriginPlatformName(){
        return  originPlatformName;
    }
    public void setOriginPlatformName(String val ){
        originPlatformName = val;
    }
    /**
    *分销
    **/ 
    @Label("分销") 
    private Integer guideOrderFlag;
    

    public Integer getGuideOrderFlag(){
        return  guideOrderFlag;
    }
    public void setGuideOrderFlag(Integer val ){
        guideOrderFlag = val;
    }
    /**
    *系统来源
    **/ 
    @Label("系统来源") 
    private Integer systemSource;
    

    public Integer getSystemSource(){
        return  systemSource;
    }
    public void setSystemSource(Integer val ){
        systemSource = val;
    }
    /**
    *
    **/ 
    private String closeCode;
    

    public String getCloseCode(){
        return  closeCode;
    }
    public void setCloseCode(String val ){
        closeCode = val;
    }
    /**
    *订单时间戳
    **/ 
    @Label("订单时间戳") 
    private String orderTimestamp;
    

    public String getOrderTimestamp(){
        return  orderTimestamp;
    }
    public void setOrderTimestamp(String val ){
        orderTimestamp = val;
    }
    /**
    *原始外部单号
    **/ 
    @Label("原始外部单号") 
    private String originalOutId;
    

    public String getOriginalOutId(){
        return  originalOutId;
    }
    public void setOriginalOutId(String val ){
        originalOutId = val;
    }
    /**
    *瑕疵品发货门店
    **/ 
    @Label("瑕疵品发货门店") 
    private String defectiveShop;
    

    public String getDefectiveShop(){
        return  defectiveShop;
    }
    public void setDefectiveShop(String val ){
        defectiveShop = val;
    }
    /**
    *耐克POP定制  1 是,0否
    **/ 
    @Label("耐克") 
    private Integer customSign;
    

    public Integer getCustomSign(){
        return  customSign;
    }
    public void setCustomSign(Integer val ){
        customSign = val;
    }
    /**
    *唯品po单号,对应优购poNo
    **/ 
    @Label("唯品") 
    private String poNo;
    

    public String getPoNo(){
        return  poNo;
    }
    public void setPoNo(String val ){
        poNo = val;
    }
    /**
    *支付尾款时间
    **/ 
    @Label("支付尾款时间") 
    private Date balanceDueDate;
    

    public Date getBalanceDueDate(){
        return  balanceDueDate;
    }
    public void setBalanceDueDate(Date val ){
        balanceDueDate = val;
    }
    /**
    *仓中仓订单核销状态，0：未核销，1：已核销
    **/ 
    @Label("仓中仓订单核销状态") 
    private Integer verificationStatus;
    

    public Integer getVerificationStatus(){
        return  verificationStatus;
    }
    public void setVerificationStatus(Integer val ){
        verificationStatus = val;
    }
    /**
    *用于派单验证
    **/ 
    @Label("用于派单验证") 
    private String validateBatch;
    

    public String getValidateBatch(){
        return  validateBatch;
    }
    public void setValidateBatch(String val ){
        validateBatch = val;
    }
    /**
    *商家秘钥
    **/ 
    @Label("商家秘钥") 
    private String appSecret;
    

    public String getAppSecret(){
        return  appSecret;
    }
    public void setAppSecret(String val ){
        appSecret = val;
    }
    /**
    *商家key
    **/ 
    @Label("商家") 
    private String appKey;
    

    public String getAppKey(){
        return  appKey;
    }
    public void setAppKey(String val ){
        appKey = val;
    }
    /**
    *来源平台店
    **/ 
    @Label("来源平台店") 
    private String orderSourceNo;
    

    public String getOrderSourceNo(){
        return  orderSourceNo;
    }
    public void setOrderSourceNo(String val ){
        orderSourceNo = val;
    }
    /**
    *接口平台
    **/ 
    @Label("接口平台") 
    private String interfacePlatform;
    

    public String getInterfacePlatform(){
        return  interfacePlatform;
    }
    public void setInterfacePlatform(String val ){
        interfacePlatform = val;
    }
    /**
    *优购订单状态名称
    **/ 
    @Label("优购订单状态名称") 
    private String outOrderStatusName;
    

    public String getOutOrderStatusName(){
        return  outOrderStatusName;
    }
    public void setOutOrderStatusName(String val ){
        outOrderStatusName = val;
    }
    /**
    *优购订单状态
    **/ 
    @Label("优购订单状态") 
    private Integer outOrderStatus;
    

    public Integer getOutOrderStatus(){
        return  outOrderStatus;
    }
    public void setOutOrderStatus(Integer val ){
        outOrderStatus = val;
    }
    /**
    *ios订单状态名称
    **/ 
    @Label("订单状态名称") 
    private String orderStatusName;
    

    public String getOrderStatusName(){
        return  orderStatusName;
    }
    public void setOrderStatusName(String val ){
        orderStatusName = val;
    }
    /**
    *ios订单状态
    **/ 
    @Label("订单状态") 
    private Integer orderStatus;
    

    public Integer getOrderStatus(){
        return  orderStatus;
    }
    public void setOrderStatus(Integer val ){
        orderStatus = val;
    }
    /**
    *业务类型 0:正常单 1:云店通意礴定制 2:bata订单
    **/ 
    @Label("业务类型") 
    private Integer businessType;
    

    public Integer getBusinessType(){
        return  businessType;
    }
    public void setBusinessType(Integer val ){
        businessType = val;
    }
    /**
    *制单类型：1“优购，2非优购，5大宗订单，12直销，13唯品会jit鞋靴，14唯品会jit
    **/ 
    @Label("制单类型") 
    private Integer sourceType;
    

    public Integer getSourceType(){
        return  sourceType;
    }
    public void setSourceType(Integer val ){
        sourceType = val;
    }
    /**
    *订单类型：默认1：正常单，2:预售单，3:换货单
    **/ 
    @Label("订单类型") 
    private Integer orderStyle;
    

    public Integer getOrderStyle(){
        return  orderStyle;
    }
    public void setOrderStyle(Integer val ){
        orderStyle = val;
    }
    /**
    *是否唯品会JIT单据 0-否 1-是
    **/ 
    @Label("是否唯品会") 
    private Integer isVip;
    

    public Integer getIsVip(){
        return  isVip;
    }
    public void setIsVip(Integer val ){
        isVip = val;
    }
    /**
    *是否已转单:0否,1是
    **/ 
    @Label("是否已转单") 
    private Integer isConverted;
    

    public Integer getIsConverted(){
        return  isConverted;
    }
    public void setIsConverted(Integer val ){
        isConverted = val;
    }
    /**
    *转单后的零售订单号
    **/ 
    @Label("转单后的零售订单号") 
    private String orderMainNo;
    

    public String getOrderMainNo(){
        return  orderMainNo;
    }
    public void setOrderMainNo(String val ){
        orderMainNo = val;
    }
    /**
    *销售店铺编码
    **/ 
    @Label("销售店铺编码") 
    private String saleShopNo;
    

    public String getSaleShopNo(){
        return  saleShopNo;
    }
    public void setSaleShopNo(String val ){
        saleShopNo = val;
    }
    /**
    *优购结算公司名称
    **/ 
    @Label("优购结算公司名称") 
    private String companyName;
    

    public String getCompanyName(){
        return  companyName;
    }
    public void setCompanyName(String val ){
        companyName = val;
    }
    /**
    *优购结算公司编码
    **/ 
    @Label("优购结算公司编码") 
    private String companyCode;
    

    public String getCompanyCode(){
        return  companyCode;
    }
    public void setCompanyCode(String val ){
        companyCode = val;
    }
    /**
    *零售省
    **/ 
    @Label("零售省") 
    private String retailProvince;
    

    public String getRetailProvince(){
        return  retailProvince;
    }
    public void setRetailProvince(String val ){
        retailProvince = val;
    }
    /**
    *零售省(中文)
    **/ 
    @Label("零售省") 
    private String retailProvinceName;
    

    public String getRetailProvinceName(){
        return  retailProvinceName;
    }
    public void setRetailProvinceName(String val ){
        retailProvinceName = val;
    }
    /**
    *零售城市编码
    **/ 
    @Label("零售城市编码") 
    private String retailCity;
    

    public String getRetailCity(){
        return  retailCity;
    }
    public void setRetailCity(String val ){
        retailCity = val;
    }
    /**
    *零售城市名
    **/ 
    @Label("零售城市名") 
    private String retailCityName;
    

    public String getRetailCityName(){
        return  retailCityName;
    }
    public void setRetailCityName(String val ){
        retailCityName = val;
    }
    /**
    *零售地区
    **/ 
    @Label("零售地区") 
    private String retailArea;
    

    public String getRetailArea(){
        return  retailArea;
    }
    public void setRetailArea(String val ){
        retailArea = val;
    }
    /**
    *零售地区名
    **/ 
    @Label("零售地区名") 
    private String retailAreaName;
    

    public String getRetailAreaName(){
        return  retailAreaName;
    }
    public void setRetailAreaName(String val ){
        retailAreaName = val;
    }
    /**
    *取消类型，1缺货取消,2超区取消
    **/ 
    @Label("取消类型") 
    private Integer stockType;
    

    public Integer getStockType(){
        return  stockType;
    }
    public void setStockType(Integer val ){
        stockType = val;
    }
    /**
    *是否可发货，0否 1是
    **/ 
    @Label("是否可发货") 
    private Integer undeliverable;
    

    public Integer getUndeliverable(){
        return  undeliverable;
    }
    public void setUndeliverable(Integer val ){
        undeliverable = val;
    }
    /**
    *天猫脱敏
    **/ 
    @Label("天猫脱敏") 
    private String oaId;
    

    public String getOaId(){
        return  oaId;
    }
    public void setOaId(String val ){
        oaId = val;
    }
    /**
    *商品总数
    **/ 
    @Label("商品总数") 
    private Integer productTotalQuantity;
    

    public Integer getProductTotalQuantity(){
        return  productTotalQuantity;
    }
    public void setProductTotalQuantity(Integer val ){
        productTotalQuantity = val;
    }
    /**
    *订单总金额
    **/ 
    @Label("订单总金额") 
    private BigDecimal orderAmount;
    

    public BigDecimal getOrderAmount(){
        return  orderAmount;
    }
    public void setOrderAmount(BigDecimal val ){
        orderAmount = val;
    }
    /**
    *订单支付金额
    **/ 
    @Label("订单支付金额") 
    private BigDecimal orderPayTotalAmont;
    

    public BigDecimal getOrderPayTotalAmont(){
        return  orderPayTotalAmont;
    }
    public void setOrderPayTotalAmont(BigDecimal val ){
        orderPayTotalAmont = val;
    }
    /**
    *发货时间
    **/ 
    @Label("发货时间") 
    private Date shipTime;
    

    public Date getShipTime(){
        return  shipTime;
    }
    public void setShipTime(Date val ){
        shipTime = val;
    }
    /**
    *在线支付时间
    **/ 
    @Label("在线支付时间") 
    private Date onlinePayTime;
    

    public Date getOnlinePayTime(){
        return  onlinePayTime;
    }
    public void setOnlinePayTime(Date val ){
        onlinePayTime = val;
    }
    /**
    *要求到货时间
    **/ 
    @Label("要求到货时间") 
    private Date arriveTime;
    

    public Date getArriveTime(){
        return  arriveTime;
    }
    public void setArriveTime(Date val ){
        arriveTime = val;
    }
    /**
    *同步时间戳, 格式：mmddHHmmss
    **/ 
    @Label("同步时间戳") 
    private Long syncTimeStamp;
    

    public Long getSyncTimeStamp(){
        return  syncTimeStamp;
    }
    public void setSyncTimeStamp(Long val ){
        syncTimeStamp = val;
    }
    /**
    *分库字段:本部+序列
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *零售分库字段:本部+大区
    **/ 
    @Label("零售分库字段") 
    private String retailFlag;
    

    public String getRetailFlag(){
        return  retailFlag;
    }
    public void setRetailFlag(String val ){
        retailFlag = val;
    }
    /**
    *虚仓编码
    **/ 
    @Label("虚仓编码") 
    private String vstoreCode;
    

    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }
    /**
    *子仓序号(null则为总仓)
    **/ 
    @Label("子仓序号") 
    private Integer vstoreType;
    

    public Integer getVstoreType(){
        return  vstoreType;
    }
    public void setVstoreType(Integer val ){
        vstoreType = val;
    }
    /**
    *商家编码
    **/ 
    @Label("商家编码") 
    private String merchantCode;
    

    public String getMerchantCode(){
        return  merchantCode;
    }
    public void setMerchantCode(String val ){
        merchantCode = val;
    }
    /**
    *唯品订单是否匹配质检
    **/ 
    @Label("唯品订单是否匹配质检") 
    private Integer isMatch;
    

    public Integer getIsMatch(){
        return  isMatch;
    }
    public void setIsMatch(Integer val ){
        isMatch = val;
    }
    /**
    *外部单号,对应优购outOrderId, JITx订单则为So单号
    **/ 
    @Label("外部单号") 
    private String outOrderId;
    

    public String getOutOrderId(){
        return  outOrderId;
    }
    public void setOutOrderId(String val ){
        outOrderId = val;
    }
    /**
    *是否导购订单，0=否，1=是
    **/ 
    @Label("是否导购订单") 
    private Integer isGuideOrder;
    

    public Integer getIsGuideOrder(){
        return  isGuideOrder;
    }
    public void setIsGuideOrder(Integer val ){
        isGuideOrder = val;
    }
    /**
    *收货地址对应的纬度
    **/ 
    @Label("收货地址对应的纬度") 
    private String consigneeAddressLatitude;
    

    public String getConsigneeAddressLatitude(){
        return  consigneeAddressLatitude;
    }
    public void setConsigneeAddressLatitude(String val ){
        consigneeAddressLatitude = val;
    }
    /**
    *收货地址对应的经度
    **/ 
    @Label("收货地址对应的经度") 
    private String consigneeAddressLongitude;
    

    public String getConsigneeAddressLongitude(){
        return  consigneeAddressLongitude;
    }
    public void setConsigneeAddressLongitude(String val ){
        consigneeAddressLongitude = val;
    }
    /**
    *订单修改日期
    **/ 
    @Label("订单修改日期") 
    private Date orderModifyTime;
    

    public Date getOrderModifyTime(){
        return  orderModifyTime;
    }
    public void setOrderModifyTime(Date val ){
        orderModifyTime = val;
    }
    /**
    *订单创建日期
    **/ 
    @Label("订单创建日期") 
    private Date orderCreateTime;
    

    public Date getOrderCreateTime(){
        return  orderCreateTime;
    }
    public void setOrderCreateTime(Date val ){
        orderCreateTime = val;
    }
    /**
    *本部编码
    **/ 
    @Label("本部编码") 
    private String organTypeNo;
    

    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetOrderBuilder build(){
        return new InternetOrderBuilder(this);
    }

    public static class InternetOrderBuilder extends AbstractEntryBuilder<InternetOrder>{

        private InternetOrderBuilder(InternetOrder entry){
            this.obj = entry;
        }

       @Override
		public InternetOrder object() {
			return this.obj;
		}

        
        public InternetOrderBuilder sendStore(String value ){
            
            this.obj.sendStore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendStore", value);
            return this;
        }
        
        public InternetOrderBuilder actualPostage(BigDecimal value ){
            
            this.obj.actualPostage = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("actualPostage", value);
            return this;
        }
        
        public InternetOrderBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetOrderBuilder originalOrderNo(String value ){
            
            this.obj.originalOrderNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originalOrderNo", value);
            return this;
        }
        
        public InternetOrderBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
        public InternetOrderBuilder originPlatform(String value ){
            
            this.obj.originPlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatform", value);
            return this;
        }
        
        public InternetOrderBuilder originPlatformName(String value ){
            
            this.obj.originPlatformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatformName", value);
            return this;
        }
        
        public InternetOrderBuilder guideOrderFlag(Integer value ){
            
            this.obj.guideOrderFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("guideOrderFlag", value);
            return this;
        }
        
        public InternetOrderBuilder systemSource(Integer value ){
            
            this.obj.systemSource = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("systemSource", value);
            return this;
        }
        
        public InternetOrderBuilder closeCode(String value ){
            
            this.obj.closeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("closeCode", value);
            return this;
        }
        
        public InternetOrderBuilder orderTimestamp(String value ){
            
            this.obj.orderTimestamp = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderTimestamp", value);
            return this;
        }
        
        public InternetOrderBuilder originalOutId(String value ){
            
            this.obj.originalOutId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originalOutId", value);
            return this;
        }
        
        public InternetOrderBuilder defectiveShop(String value ){
            
            this.obj.defectiveShop = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("defectiveShop", value);
            return this;
        }
        
        public InternetOrderBuilder customSign(Integer value ){
            
            this.obj.customSign = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customSign", value);
            return this;
        }
        
        public InternetOrderBuilder poNo(String value ){
            
            this.obj.poNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("poNo", value);
            return this;
        }
        
        public InternetOrderBuilder balanceDueDate(Date value ){
            
            this.obj.balanceDueDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("balanceDueDate", value);
            return this;
        }
        
        public InternetOrderBuilder verificationStatus(Integer value ){
            
            this.obj.verificationStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("verificationStatus", value);
            return this;
        }
        
        public InternetOrderBuilder validateBatch(String value ){
            
            this.obj.validateBatch = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("validateBatch", value);
            return this;
        }
        
        public InternetOrderBuilder appSecret(String value ){
            
            this.obj.appSecret = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("appSecret", value);
            return this;
        }
        
        public InternetOrderBuilder appKey(String value ){
            
            this.obj.appKey = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("appKey", value);
            return this;
        }
        
        public InternetOrderBuilder orderSourceNo(String value ){
            
            this.obj.orderSourceNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSourceNo", value);
            return this;
        }
        
        public InternetOrderBuilder interfacePlatform(String value ){
            
            this.obj.interfacePlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("interfacePlatform", value);
            return this;
        }
        
        public InternetOrderBuilder outOrderStatusName(String value ){
            
            this.obj.outOrderStatusName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outOrderStatusName", value);
            return this;
        }
        
        public InternetOrderBuilder outOrderStatus(Integer value ){
            
            this.obj.outOrderStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outOrderStatus", value);
            return this;
        }
        
        public InternetOrderBuilder orderStatusName(String value ){
            
            this.obj.orderStatusName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStatusName", value);
            return this;
        }
        
        public InternetOrderBuilder orderStatus(Integer value ){
            
            this.obj.orderStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStatus", value);
            return this;
        }
        
        public InternetOrderBuilder businessType(Integer value ){
            
            this.obj.businessType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("businessType", value);
            return this;
        }
        
        public InternetOrderBuilder sourceType(Integer value ){
            
            this.obj.sourceType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sourceType", value);
            return this;
        }
        
        public InternetOrderBuilder orderStyle(Integer value ){
            
            this.obj.orderStyle = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStyle", value);
            return this;
        }
        
        public InternetOrderBuilder isVip(Integer value ){
            
            this.obj.isVip = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isVip", value);
            return this;
        }
        
        public InternetOrderBuilder isConverted(Integer value ){
            
            this.obj.isConverted = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isConverted", value);
            return this;
        }
        
        public InternetOrderBuilder orderMainNo(String value ){
            
            this.obj.orderMainNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderMainNo", value);
            return this;
        }
        
        public InternetOrderBuilder saleShopNo(String value ){
            
            this.obj.saleShopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleShopNo", value);
            return this;
        }
        
        public InternetOrderBuilder companyName(String value ){
            
            this.obj.companyName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyName", value);
            return this;
        }
        
        public InternetOrderBuilder companyCode(String value ){
            
            this.obj.companyCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyCode", value);
            return this;
        }
        
        public InternetOrderBuilder retailProvince(String value ){
            
            this.obj.retailProvince = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailProvince", value);
            return this;
        }
        
        public InternetOrderBuilder retailProvinceName(String value ){
            
            this.obj.retailProvinceName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailProvinceName", value);
            return this;
        }
        
        public InternetOrderBuilder retailCity(String value ){
            
            this.obj.retailCity = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailCity", value);
            return this;
        }
        
        public InternetOrderBuilder retailCityName(String value ){
            
            this.obj.retailCityName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailCityName", value);
            return this;
        }
        
        public InternetOrderBuilder retailArea(String value ){
            
            this.obj.retailArea = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailArea", value);
            return this;
        }
        
        public InternetOrderBuilder retailAreaName(String value ){
            
            this.obj.retailAreaName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailAreaName", value);
            return this;
        }
        
        public InternetOrderBuilder stockType(Integer value ){
            
            this.obj.stockType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("stockType", value);
            return this;
        }
        
        public InternetOrderBuilder undeliverable(Integer value ){
            
            this.obj.undeliverable = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("undeliverable", value);
            return this;
        }
        
        public InternetOrderBuilder oaId(String value ){
            
            this.obj.oaId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("oaId", value);
            return this;
        }
        
        public InternetOrderBuilder productTotalQuantity(Integer value ){
            
            this.obj.productTotalQuantity = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("productTotalQuantity", value);
            return this;
        }
        
        public InternetOrderBuilder orderAmount(BigDecimal value ){
            
            this.obj.orderAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderAmount", value);
            return this;
        }
        
        public InternetOrderBuilder orderPayTotalAmont(BigDecimal value ){
            
            this.obj.orderPayTotalAmont = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderPayTotalAmont", value);
            return this;
        }
        
        public InternetOrderBuilder shipTime(Date value ){
            
            this.obj.shipTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shipTime", value);
            return this;
        }
        
        public InternetOrderBuilder onlinePayTime(Date value ){
            
            this.obj.onlinePayTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("onlinePayTime", value);
            return this;
        }
        
        public InternetOrderBuilder arriveTime(Date value ){
            
            this.obj.arriveTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("arriveTime", value);
            return this;
        }
        
        public InternetOrderBuilder syncTimeStamp(Long value ){
            
            this.obj.syncTimeStamp = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("syncTimeStamp", value);
            return this;
        }
        
        public InternetOrderBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public InternetOrderBuilder retailFlag(String value ){
            
            this.obj.retailFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailFlag", value);
            return this;
        }
        
        public InternetOrderBuilder vstoreCode(String value ){
            
            this.obj.vstoreCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public InternetOrderBuilder vstoreType(Integer value ){
            
            this.obj.vstoreType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreType", value);
            return this;
        }
        
        public InternetOrderBuilder merchantCode(String value ){
            
            this.obj.merchantCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("merchantCode", value);
            return this;
        }
        
        public InternetOrderBuilder isMatch(Integer value ){
            
            this.obj.isMatch = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isMatch", value);
            return this;
        }
        
        public InternetOrderBuilder outOrderId(String value ){
            
            this.obj.outOrderId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outOrderId", value);
            return this;
        }
        
        public InternetOrderBuilder isGuideOrder(Integer value ){
            
            this.obj.isGuideOrder = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isGuideOrder", value);
            return this;
        }
        
        public InternetOrderBuilder consigneeAddressLatitude(String value ){
            
            this.obj.consigneeAddressLatitude = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("consigneeAddressLatitude", value);
            return this;
        }
        
        public InternetOrderBuilder consigneeAddressLongitude(String value ){
            
            this.obj.consigneeAddressLongitude = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("consigneeAddressLongitude", value);
            return this;
        }
        
        public InternetOrderBuilder orderModifyTime(Date value ){
            
            this.obj.orderModifyTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderModifyTime", value);
            return this;
        }
        
        public InternetOrderBuilder orderCreateTime(Date value ){
            
            this.obj.orderCreateTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderCreateTime", value);
            return this;
        }
        
        public InternetOrderBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public InternetOrderBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetOrderBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetOrderBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetOrderBuilder organTypeNo(String value ){
            
            this.obj.organTypeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }