/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 虚拟仓范围
**/
public class InternetVirtualWarehouseScope  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1693893007542L;
    
    /**
    *库存类型，1:共享，2：独享，3：电商
    **/ 
    @Label("库存类型") 
    private Integer inventoryType;
    

    public Integer getInventoryType(){
        return  inventoryType;
    }
    public void setInventoryType(Integer val ){
        inventoryType = val;
    }
    /**
    *虚拟仓名称
    **/ 
    @Label("虚拟仓名称") 
    private String vstoreName;
    

    public String getVstoreName(){
        return  vstoreName;
    }
    public void setVstoreName(String val ){
        vstoreName = val;
    }
    /**
    *状态，1：启用，0：禁用，禁用表示删除
    **/ 
    @Label("状态") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *订货单位名称
    **/ 
    @Label("订货单位名称") 
    private String orderUnitName;
    

    public String getOrderUnitName(){
        return  orderUnitName;
    }
    public void setOrderUnitName(String val ){
        orderUnitName = val;
    }
    /**
    *订货单位编号
    **/ 
    @Label("订货单位编号") 
    private String orderUnitNo;
    

    public String getOrderUnitNo(){
        return  orderUnitNo;
    }
    public void setOrderUnitNo(String val ){
        orderUnitNo = val;
    }
    /**
    *机构名称
    **/ 
    @Label("机构名称") 
    private String storeName;
    

    public String getStoreName(){
        return  storeName;
    }
    public void setStoreName(String val ){
        storeName = val;
    }
    /**
    *机构编码
    **/ 
    @Label("机构编码") 
    private String storeNo;
    

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    /**
    *多机构标识,0标识单机构，1表示多机构（货管下所有店）
    **/ 
    @Label("多机构标识") 
    private Integer moreStoreFlag;
    

    public Integer getMoreStoreFlag(){
        return  moreStoreFlag;
    }
    public void setMoreStoreFlag(Integer val ){
        moreStoreFlag = val;
    }
    /**
    *机构类型(21:店仓  22:仓库)
    **/ 
    @Label("机构类型")
    private Integer storeType;
    

    public Integer getStoreType(){
        return  storeType;
    }
    public void setStoreType(Integer val ){
        storeType = val;
    }
    /**
    *虚拟仓类型; 1 总仓、2 子仓
    **/ 
    @Label("虚拟仓类型") 
    private Integer vstoreType;
    

    public Integer getVstoreType(){
        return  vstoreType;
    }
    public void setVstoreType(Integer val ){
        vstoreType = val;
    }
    /**
    *虚拟仓编码
    **/ 
    @Label("虚拟仓编码") 
    private String vstoreCode;
    

    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }

    @Label("原所属虚拟仓编码")
    private String originalVstoreCode;

    @Label("原所属虚拟仓名称")
    private String originalVstoreName;

    //聚合仓分类
    private Integer vstoreMold;

    //聚合仓所属父仓编码
    private String parentVstoreCode;

    public Integer getVstoreMold() {
        return vstoreMold;
    }

    public void setVstoreMold(Integer vstoreMold) {
        this.vstoreMold = vstoreMold;
    }

    public String getParentVstoreCode() {
        return parentVstoreCode;
    }

    public void setParentVstoreCode(String parentVstoreCode) {
        this.parentVstoreCode = parentVstoreCode;
    }

    public String getOriginalVstoreCode() {
        return originalVstoreCode;
    }

    public void setOriginalVstoreCode(String originalVstoreCode) {
        this.originalVstoreCode = originalVstoreCode;
    }

    public String getOriginalVstoreName() {
        return originalVstoreName;
    }

    public void setOriginalVstoreName(String originalVstoreName) {
        this.originalVstoreName = originalVstoreName;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public static InternetVirtualWarehouseScopeBuilder build(){
        return new InternetVirtualWarehouseScopeBuilder(new InternetVirtualWarehouseScope());
    }

    public static InternetVirtualWarehouseScopeBuilder build(InternetVirtualWarehouseScope scope){
        return new InternetVirtualWarehouseScopeBuilder(scope);
    }

    public static class InternetVirtualWarehouseScopeBuilder extends AbstractEntryBuilder<InternetVirtualWarehouseScope>{

        private InternetVirtualWarehouseScopeBuilder(InternetVirtualWarehouseScope entry){
            this.obj = entry;
        }

       @Override
		public InternetVirtualWarehouseScope object() {
			return this.obj;
		}

        
        public InternetVirtualWarehouseScopeBuilder inventoryType(Integer value ){
            
            this.obj.inventoryType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("inventoryType", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder vstoreName(String value ){
            
            this.obj.vstoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreName", value);
            return this;
        }

        public InternetVirtualWarehouseScopeBuilder originalVstoreName(String value ){

            this.obj.originalVstoreName = value;

            if( query == null  )
                query = new Query();
            this.query.where("originalVstoreName", value);
            return this;
        }

        public InternetVirtualWarehouseScopeBuilder originalVstoreCode(String value ){

            this.obj.originalVstoreCode = value;

            if( query == null  )
                query = new Query();
            this.query.where("originalVstoreCode", value);
            return this;
        }


        public InternetVirtualWarehouseScopeBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder orderUnitName(String value ){
            
            this.obj.orderUnitName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitName", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder orderUnitNo(String value ){
            
            this.obj.orderUnitNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder storeName(String value ){
            
            this.obj.storeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder storeNo(String value ){
            
            this.obj.storeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder moreStoreFlag(Integer value ){
            
            this.obj.moreStoreFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("moreStoreFlag", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder storeType(Integer value ){
            
            this.obj.storeType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeType", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder vstoreType(Integer value ){
            
            this.obj.vstoreType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreType", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder vstoreCode(String value ){
            
            this.obj.vstoreCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public InternetVirtualWarehouseScopeBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }