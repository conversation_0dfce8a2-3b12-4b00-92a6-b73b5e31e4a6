/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 三级来源配置表
**/
public class OcsOrderSourceConfig  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778671L;
    
    /**
    *销售店铺名称
    **/ 
    @Label("销售店铺名称") 
    private String shopName;
    

    public String getShopName(){
        return  shopName;
    }
    public void setShopName(String val ){
        shopName = val;
    }
    /**
    *状态(0 未生效;1 正常;)
    **/ 
    @Label("状态") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *负责人
    **/ 
    @Label("负责人") 
    private String charge;
    

    public String getCharge(){
        return  charge;
    }
    public void setCharge(String val ){
        charge = val;
    }
    /**
    *负责人电话
    **/ 
    @Label("负责人电话") 
    private String chargePhone;
    

    public String getChargePhone(){
        return  chargePhone;
    }
    public void setChargePhone(String val ){
        chargePhone = val;
    }
    /**
    *售后电话
    **/ 
    @Label("售后电话") 
    private String returnConstactPhone;
    

    public String getReturnConstactPhone(){
        return  returnConstactPhone;
    }
    public void setReturnConstactPhone(String val ){
        returnConstactPhone = val;
    }
    /**
    *开启导入订单：1开启0关闭
    **/ 
    @Label("开启导入订单") 
    private Integer openExport;
    

    public Integer getOpenExport(){
        return  openExport;
    }
    public void setOpenExport(Integer val ){
        openExport = val;
    }
    /**
    *开启修改地址：1开启0关闭
    **/ 
    @Label("开启修改地址") 
    private Integer openUpdateAddress;
    

    public Integer getOpenUpdateAddress(){
        return  openUpdateAddress;
    }
    public void setOpenUpdateAddress(Integer val ){
        openUpdateAddress = val;
    }
    /**
    *开启拦截：1开启0关闭
    **/ 
    @Label("开启拦截") 
    private Integer openIntercepter;
    

    public Integer getOpenIntercepter(){
        return  openIntercepter;
    }
    public void setOpenIntercepter(Integer val ){
        openIntercepter = val;
    }
    /**
    *销售店铺编码：三级来源编码/店铺编码
    **/ 
    @Label("销售店铺编码") 
    private String shopNo;
    

    public String getShopNo(){
        return  shopNo;
    }
    public void setShopNo(String val ){
        shopNo = val;
    }
    /**
    *商家编码:TOP/belle
    **/ 
    @Label("商家编码") 
    private String merchantCode;
    

    public String getMerchantCode(){
        return  merchantCode;
    }
    public void setMerchantCode(String val ){
        merchantCode = val;
    }
    /**
    *业务类型
    **/ 
    @Label("业务类型") 
    private String channelType;
    

    public String getChannelType(){
        return  channelType;
    }
    public void setChannelType(String val ){
        channelType = val;
    }
    /**
    *接口平台
    **/ 
    @Label("接口平台") 
    private String interfacePlatform;
    

    public String getInterfacePlatform(){
        return  interfacePlatform;
    }
    public void setInterfacePlatform(String val ){
        interfacePlatform = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public OcsOrderSourceConfigBuilder build(){
        return new OcsOrderSourceConfigBuilder(this);
    }

    public static class OcsOrderSourceConfigBuilder extends AbstractEntryBuilder<OcsOrderSourceConfig>{

        private OcsOrderSourceConfigBuilder(OcsOrderSourceConfig entry){
            this.obj = entry;
        }

       @Override
		public OcsOrderSourceConfig object() {
			return this.obj;
		}

        
        public OcsOrderSourceConfigBuilder shopName(String value ){
            
            this.obj.shopName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopName", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder charge(String value ){
            
            this.obj.charge = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("charge", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder chargePhone(String value ){
            
            this.obj.chargePhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("chargePhone", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder returnConstactPhone(String value ){
            
            this.obj.returnConstactPhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnConstactPhone", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder openExport(Integer value ){
            
            this.obj.openExport = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("openExport", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder openUpdateAddress(Integer value ){
            
            this.obj.openUpdateAddress = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("openUpdateAddress", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder openIntercepter(Integer value ){
            
            this.obj.openIntercepter = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("openIntercepter", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder shopNo(String value ){
            
            this.obj.shopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopNo", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder merchantCode(String value ){
            
            this.obj.merchantCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("merchantCode", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder channelType(String value ){
            
            this.obj.channelType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("channelType", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder interfacePlatform(String value ){
            
            this.obj.interfacePlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("interfacePlatform", value);
            return this;
        }
        
        public OcsOrderSourceConfigBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }