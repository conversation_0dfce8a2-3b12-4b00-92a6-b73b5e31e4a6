/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 唯品退供单,收入调整单,退货质检单表
**/
public class InternetOrderReturn  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778669L;
    
    /**
    *售后原因
    **/ 
    @Label("售后原因") 
    private String saleReason;
    

    public String getSaleReason(){
        return  saleReason;
    }
    public void setSaleReason(String val ){
        saleReason = val;
    }
    /**
    *退供单号/调整单号/质检单号
    **/ 
    @Label("退供单号") 
    private String sellReturnCode;
    

    public String getSellReturnCode(){
        return  sellReturnCode;
    }
    public void setSellReturnCode(String val ){
        sellReturnCode = val;
    }
    /**
    *订单状态名称
    **/ 
    @Label("订单状态名称") 
    private String orderStatusName;
    

    public String getOrderStatusName(){
        return  orderStatusName;
    }
    public void setOrderStatusName(String val ){
        orderStatusName = val;
    }
    /**
    *订单状态
    **/ 
    @Label("订单状态") 
    private Integer orderStatus;
    

    public Integer getOrderStatus(){
        return  orderStatus;
    }
    public void setOrderStatus(Integer val ){
        orderStatus = val;
    }
    /**
    *是否提前退款 0-否 1-是
    **/ 
    @Label("是否提前退款") 
    private Integer beforeRefundFlag;
    

    public Integer getBeforeRefundFlag(){
        return  beforeRefundFlag;
    }
    public void setBeforeRefundFlag(Integer val ){
        beforeRefundFlag = val;
    }
    /**
    *城市
    **/ 
    @Label("城市") 
    private String city;
    

    public String getCity(){
        return  city;
    }
    public void setCity(String val ){
        city = val;
    }
    /**
    *城市名
    **/ 
    @Label("城市名") 
    private String cityName;
    

    public String getCityName(){
        return  cityName;
    }
    public void setCityName(String val ){
        cityName = val;
    }
    /**
    *地区
    **/ 
    @Label("地区") 
    private String area;
    

    public String getArea(){
        return  area;
    }
    public void setArea(String val ){
        area = val;
    }
    /**
    *地区名
    **/ 
    @Label("地区名") 
    private String areaName;
    

    public String getAreaName(){
        return  areaName;
    }
    public void setAreaName(String val ){
        areaName = val;
    }
    /**
    *收货人姓名
    **/ 
    @Label("收货人姓名") 
    private String returnName;
    

    public String getReturnName(){
        return  returnName;
    }
    public void setReturnName(String val ){
        returnName = val;
    }
    /**
    *省
    **/ 
    @Label("省") 
    private String province;
    

    public String getProvince(){
        return  province;
    }
    public void setProvince(String val ){
        province = val;
    }
    /**
    *省(中文)
    **/ 
    @Label("省") 
    private String provinceName;
    

    public String getProvinceName(){
        return  provinceName;
    }
    public void setProvinceName(String val ){
        provinceName = val;
    }
    /**
    *买家承担运费金额
    **/ 
    @Label("买家承担运费金额") 
    private BigDecimal buyerExpressMoney;
    

    public BigDecimal getBuyerExpressMoney(){
        return  buyerExpressMoney;
    }
    public void setBuyerExpressMoney(BigDecimal val ){
        buyerExpressMoney = val;
    }
    /**
    *卖家承担运费金额
    **/ 
    @Label("卖家承担运费金额") 
    private BigDecimal sellerExpressMoney;
    

    public BigDecimal getSellerExpressMoney(){
        return  sellerExpressMoney;
    }
    public void setSellerExpressMoney(BigDecimal val ){
        sellerExpressMoney = val;
    }
    /**
    *物流公司名称
    **/ 
    @Label("物流公司名称") 
    private String logisticsName;
    

    public String getLogisticsName(){
        return  logisticsName;
    }
    public void setLogisticsName(String val ){
        logisticsName = val;
    }
    /**
    *收货时间
    **/ 
    @Label("收货时间") 
    private Date receiveTime;
    

    public Date getReceiveTime(){
        return  receiveTime;
    }
    public void setReceiveTime(Date val ){
        receiveTime = val;
    }
    /**
    *快递公司编号
    **/ 
    @Label("快递公司编号") 
    private String logisticsCode;
    

    public String getLogisticsCode(){
        return  logisticsCode;
    }
    public void setLogisticsCode(String val ){
        logisticsCode = val;
    }
    /**
    *收货人邮编
    **/ 
    @Label("收货人邮编") 
    private String zipcode;
    

    public String getZipcode(){
        return  zipcode;
    }
    public void setZipcode(String val ){
        zipcode = val;
    }
    /**
    *退货电话
    **/ 
    @Label("退货电话") 
    private String constactPhone;
    

    public String getConstactPhone(){
        return  constactPhone;
    }
    public void setConstactPhone(String val ){
        constactPhone = val;
    }
    /**
    *购买人
    **/ 
    @Label("购买人") 
    private String buyerName;
    

    public String getBuyerName(){
        return  buyerName;
    }
    public void setBuyerName(String val ){
        buyerName = val;
    }
    /**
    *实际退款总金额
    **/ 
    @Label("实际退款总金额") 
    private BigDecimal refundTotalFee;
    

    public BigDecimal getRefundTotalFee(){
        return  refundTotalFee;
    }
    public void setRefundTotalFee(BigDecimal val ){
        refundTotalFee = val;
    }
    /**
    *退货备注
    **/ 
    @Label("退货备注") 
    private String message;
    

    public String getMessage(){
        return  message;
    }
    public void setMessage(String val ){
        message = val;
    }
    /**
    *退货邮箱
    **/ 
    @Label("退货邮箱") 
    private String email;
    

    public String getEmail(){
        return  email;
    }
    public void setEmail(String val ){
        email = val;
    }
    /**
    *收货人地址
    **/ 
    @Label("收货人地址") 
    private String consigneeAddress;
    

    public String getConsigneeAddress(){
        return  consigneeAddress;
    }
    public void setConsigneeAddress(String val ){
        consigneeAddress = val;
    }
    /**
    *会员号
    **/ 
    @Label("会员号") 
    private String memberName;
    

    public String getMemberName(){
        return  memberName;
    }
    public void setMemberName(String val ){
        memberName = val;
    }
    /**
    *退货手机号码
    **/ 
    @Label("退货手机号码") 
    private String mobilePhone;
    

    public String getMobilePhone(){
        return  mobilePhone;
    }
    public void setMobilePhone(String val ){
        mobilePhone = val;
    }
    /**
    *来源平台
    **/ 
    @Label("来源平台") 
    private String originPlatform;
    

    public String getOriginPlatform(){
        return  originPlatform;
    }
    public void setOriginPlatform(String val ){
        originPlatform = val;
    }
    /**
    *来源平台名
    **/ 
    @Label("来源平台名") 
    private String originPlatformName;
    

    public String getOriginPlatformName(){
        return  originPlatformName;
    }
    public void setOriginPlatformName(String val ){
        originPlatformName = val;
    }
    /**
    *来源店
    **/ 
    @Label("来源店") 
    private String orderSourceNo;
    

    public String getOrderSourceNo(){
        return  orderSourceNo;
    }
    public void setOrderSourceNo(String val ){
        orderSourceNo = val;
    }
    /**
    *接口平台
    **/ 
    @Label("接口平台") 
    private String interfacePlatform;
    

    public String getInterfacePlatform(){
        return  interfacePlatform;
    }
    public void setInterfacePlatform(String val ){
        interfacePlatform = val;
    }
    /**
    *仓库编码
    **/ 
    @Label("仓库编码") 
    private String storeCode;
    

    public String getStoreCode(){
        return  storeCode;
    }
    public void setStoreCode(String val ){
        storeCode = val;
    }
    /**
    *订单号
    **/ 
    @Label("订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    /**
    *退货单号
    **/ 
    @Label("退货单号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    /**
    *业务类型:唯品退供:VIP-001,作废质检:IQI-001,拒收/质检:QC-xxx,差异调整:AD-xxx,异常调整:EX-xxx,仅退款:RF-xxx
    **/ 
    @Label("业务类型") 
    private String businessType;
    

    public String getBusinessType(){
        return  businessType;
    }
    public void setBusinessType(String val ){
        businessType = val;
    }
    /**
    *单据类型(1：默认正常), 0-唯品退供,2-作废质检,3-退货质检,4-退货质检-错发,5-仓库拒收质检,6-仅退款,7-差异调整,8-异常调整,9-店铺拒收质检
    **/ 
    @Label("单据类型") 
    private Integer returnType;
    

    public Integer getReturnType(){
        return  returnType;
    }
    public void setReturnType(Integer val ){
        returnType = val;
    }
    /**
    *商品总数
    **/ 
    @Label("商品总数") 
    private Integer productTotalQuantity;
    

    public Integer getProductTotalQuantity(){
        return  productTotalQuantity;
    }
    public void setProductTotalQuantity(Integer val ){
        productTotalQuantity = val;
    }
    /**
    *退供虚仓编码名称
    **/ 
    @Label("退供虚仓编码名称") 
    private String returnShopName;
    

    public String getReturnShopName(){
        return  returnShopName;
    }
    public void setReturnShopName(String val ){
        returnShopName = val;
    }
    /**
    *分库字段:本部+序列
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *退货类型,1:一退；2:二退；3：三退
    **/ 
    @Label("退货类型") 
    private Integer ygReturnType;
    

    public Integer getYgReturnType(){
        return  ygReturnType;
    }
    public void setYgReturnType(Integer val ){
        ygReturnType = val;
    }
    /**
    *质检单号
    **/ 
    @Label("质检单号") 
    private String qaReturnCode;
    

    public String getQaReturnCode(){
        return  qaReturnCode;
    }
    public void setQaReturnCode(String val ){
        qaReturnCode = val;
    }
    /**
    *完结时间
    **/ 
    @Label("完结时间") 
    private Date outStoreDate;
    

    public Date getOutStoreDate(){
        return  outStoreDate;
    }
    public void setOutStoreDate(Date val ){
        outStoreDate = val;
    }
    /**
    *1:正常完成  2:异常完成
    **/ 
    @Label("正常完成") 
    private Integer qaStatus;
    

    public Integer getQaStatus(){
        return  qaStatus;
    }
    public void setQaStatus(Integer val ){
        qaStatus = val;
    }
    /**
    *优购二级仓库编码
    **/ 
    @Label("优购二级仓库编码") 
    private String ygSecondWarehouse;
    

    public String getYgSecondWarehouse(){
        return  ygSecondWarehouse;
    }
    public void setYgSecondWarehouse(String val ){
        ygSecondWarehouse = val;
    }
    /**
    *优购仓库编码
    **/ 
    @Label("优购仓库编码") 
    private String ygWarehouse;
    

    public String getYgWarehouse(){
        return  ygWarehouse;
    }
    public void setYgWarehouse(String val ){
        ygWarehouse = val;
    }
    /**
    *异常类型
    **/ 
    @Label("异常类型") 
    private String exceptionType;
    

    public String getExceptionType(){
        return  exceptionType;
    }
    public void setExceptionType(String val ){
        exceptionType = val;
    }
    /**
    *是否质检通过 0 否，1是
    **/ 
    @Label("是否质检通过") 
    private Integer isquality;
    

    public Integer getIsquality(){
        return  isquality;
    }
    public void setIsquality(Integer val ){
        isquality = val;
    }
    /**
    *唯品会仓库
    **/ 
    @Label("唯品会仓库") 
    private String warehouseName;
    

    public String getWarehouseName(){
        return  warehouseName;
    }
    public void setWarehouseName(String val ){
        warehouseName = val;
    }
    /**
    *唯品会档期
    **/ 
    @Label("唯品会档期") 
    private String theaterSchedule;
    

    public String getTheaterSchedule(){
        return  theaterSchedule;
    }
    public void setTheaterSchedule(String val ){
        theaterSchedule = val;
    }
    /**
    *唯品会PO单号
    **/ 
    @Label("唯品会") 
    private String poNo;
    

    public String getPoNo(){
        return  poNo;
    }
    public void setPoNo(String val ){
        poNo = val;
    }
    /**
    *是否唯品会JIT单据 0-否 1-是
    **/ 
    @Label("是否唯品会") 
    private Integer isVip;
    

    public Integer getIsVip(){
        return  isVip;
    }
    public void setIsVip(Integer val ){
        isVip = val;
    }
    /**
    *子订单运费
    **/ 
    @Label("子订单运费") 
    private BigDecimal orderFreight;
    

    public BigDecimal getOrderFreight(){
        return  orderFreight;
    }
    public void setOrderFreight(BigDecimal val ){
        orderFreight = val;
    }
    /**
    *子订单优惠券分摊金额
    **/ 
    @Label("子订单优惠券分摊金额") 
    private BigDecimal orderCouponAmount;
    

    public BigDecimal getOrderCouponAmount(){
        return  orderCouponAmount;
    }
    public void setOrderCouponAmount(BigDecimal val ){
        orderCouponAmount = val;
    }
    /**
    *子订单礼品卡分摊金额
    **/ 
    @Label("子订单礼品卡分摊金额") 
    private BigDecimal orderGiftCardAmount;
    

    public BigDecimal getOrderGiftCardAmount(){
        return  orderGiftCardAmount;
    }
    public void setOrderGiftCardAmount(BigDecimal val ){
        orderGiftCardAmount = val;
    }
    /**
    *子订单促销优惠金额
    **/ 
    @Label("子订单促销优惠金额") 
    private BigDecimal orderPromotionAmount;
    

    public BigDecimal getOrderPromotionAmount(){
        return  orderPromotionAmount;
    }
    public void setOrderPromotionAmount(BigDecimal val ){
        orderPromotionAmount = val;
    }
    /**
    *批次识别ID
    **/ 
    @Label("批次识别") 
    private String markId;
    

    public String getMarkId(){
        return  markId;
    }
    public void setMarkId(String val ){
        markId = val;
    }
    /**
    *退款时间
    **/ 
    @Label("退款时间") 
    private Date refundTime;
    

    public Date getRefundTime(){
        return  refundTime;
    }
    public void setRefundTime(Date val ){
        refundTime = val;
    }
    /**
    *订单总金额
    **/ 
    @Label("订单总金额") 
    private BigDecimal orderamount;
    

    public BigDecimal getOrderamount(){
        return  orderamount;
    }
    public void setOrderamount(BigDecimal val ){
        orderamount = val;
    }
    /**
    *退款类型,10-丢件, 20-漏发 30-补差价
    **/ 
    @Label("退款类型") 
    private Integer refundtypeoption;
    

    public Integer getRefundtypeoption(){
        return  refundtypeoption;
    }
    public void setRefundtypeoption(Integer val ){
        refundtypeoption = val;
    }
    /**
    *基本积分
    **/ 
    @Label("基本积分") 
    private Integer basescore;
    

    public Integer getBasescore(){
        return  basescore;
    }
    public void setBasescore(Integer val ){
        basescore = val;
    }
    /**
    *质检确认时间
    **/ 
    @Label("质检确认时间") 
    private Date approverDate;
    

    public Date getApproverDate(){
        return  approverDate;
    }
    public void setApproverDate(Date val ){
        approverDate = val;
    }
    /**
    *业绩所属店铺（初次注册店铺）
    **/ 
    @Label("业绩所属店铺") 
    private String buyerStoreid;
    

    public String getBuyerStoreid(){
        return  buyerStoreid;
    }
    public void setBuyerStoreid(String val ){
        buyerStoreid = val;
    }
    /**
    *购买人上上级会员号
    **/ 
    @Label("购买人上上级会员号") 
    private String cardno2;
    

    public String getCardno2(){
        return  cardno2;
    }
    public void setCardno2(String val ){
        cardno2 = val;
    }
    /**
    *购买人上级会员号
    **/ 
    @Label("购买人上级会员号") 
    private String shopcardno;
    

    public String getShopcardno(){
        return  shopcardno;
    }
    public void setShopcardno(String val ){
        shopcardno = val;
    }
    /**
    *退单类型
    **/ 
    @Label("退单类型") 
    private Integer orderType;
    

    public Integer getOrderType(){
        return  orderType;
    }
    public void setOrderType(Integer val ){
        orderType = val;
    }
    /**
    *快递单号
    **/ 
    @Label("快递单号") 
    private String expressNo;
    

    public String getExpressNo(){
        return  expressNo;
    }
    public void setExpressNo(String val ){
        expressNo = val;
    }
    /**
    *退货原因
    **/ 
    @Label("退货原因") 
    private String returnReasonName;
    

    public String getReturnReasonName(){
        return  returnReasonName;
    }
    public void setReturnReasonName(String val ){
        returnReasonName = val;
    }
    /**
    *是否结算物流费 1是 2否
    **/ 
    @Label("是否结算物流费") 
    private Integer logisticsFee;
    

    public Integer getLogisticsFee(){
        return  logisticsFee;
    }
    public void setLogisticsFee(Integer val ){
        logisticsFee = val;
    }
    /**
    *索赔金额
    **/ 
    @Label("索赔金额") 
    private BigDecimal claimAmount;
    

    public BigDecimal getClaimAmount(){
        return  claimAmount;
    }
    public void setClaimAmount(BigDecimal val ){
        claimAmount = val;
    }
    /**
    *提报结果 0：追回实物，1:自认损失，4：物流公司索赔
    **/ 
    @Label("提报结果") 
    private Integer followResultType;
    

    public Integer getFollowResultType(){
        return  followResultType;
    }
    public void setFollowResultType(Integer val ){
        followResultType = val;
    }
    /**
    *关键类型
    **/ 
    @Label("关键类型") 
    private String returnStoreType;
    

    public String getReturnStoreType(){
        return  returnStoreType;
    }
    public void setReturnStoreType(String val ){
        returnStoreType = val;
    }
    /**
    *0无内销,1待处理,2已导入,3处理中,4已经完结
    **/ 
    @Label("无内销") 
    private Integer operationState;
    

    public Integer getOperationState(){
        return  operationState;
    }
    public void setOperationState(Integer val ){
        operationState = val;
    }
    /**
    *调货店铺，值传递用
    **/ 
    @Label("调货店铺") 
    private String transferShop;
    

    public String getTransferShop(){
        return  transferShop;
    }
    public void setTransferShop(String val ){
        transferShop = val;
    }
    /**
    *修改人
    **/ 
    @Label("修改人") 
    private String updateName;
    

    public String getUpdateName(){
        return  updateName;
    }
    public void setUpdateName(String val ){
        updateName = val;
    }
    /**
    *虚仓编码
    **/ 
    @Label("虚仓编码") 
    private String vstoreCode;
    

    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }
    /**
    *三级来源编码
    **/ 
    @Label("三级来源编码") 
    private String tertiarySource;
    

    public String getTertiarySource(){
        return  tertiarySource;
    }
    public void setTertiarySource(String val ){
        tertiarySource = val;
    }
    /**
    *三级来源名称
    **/ 
    @Label("三级来源名称") 
    private String tertiarySourceName;
    

    public String getTertiarySourceName(){
        return  tertiarySourceName;
    }
    public void setTertiarySourceName(String val ){
        tertiarySourceName = val;
    }
    /**
    *人工确认记账时间
    **/ 
    @Label("人工确认记账时间") 
    private Date accountsDate;
    

    public Date getAccountsDate(){
        return  accountsDate;
    }
    public void setAccountsDate(Date val ){
        accountsDate = val;
    }
    /**
    *oms
    **/ 
    private String entryOrderCode;
    

    public String getEntryOrderCode(){
        return  entryOrderCode;
    }
    public void setEntryOrderCode(String val ){
        entryOrderCode = val;
    }
    /**
    *退供虚仓编码
    **/ 
    @Label("退供虚仓编码") 
    private String returnShopNo;
    

    public String getReturnShopNo(){
        return  returnShopNo;
    }
    public void setReturnShopNo(String val ){
        returnShopNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetOrderReturnBuilder build(){
        return new InternetOrderReturnBuilder(this);
    }

    public static class InternetOrderReturnBuilder extends AbstractEntryBuilder<InternetOrderReturn>{

        private InternetOrderReturnBuilder(InternetOrderReturn entry){
            this.obj = entry;
        }

       @Override
		public InternetOrderReturn object() {
			return this.obj;
		}

        
        public InternetOrderReturnBuilder saleReason(String value ){
            
            this.obj.saleReason = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleReason", value);
            return this;
        }
        
        public InternetOrderReturnBuilder sellReturnCode(String value ){
            
            this.obj.sellReturnCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sellReturnCode", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderStatusName(String value ){
            
            this.obj.orderStatusName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStatusName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderStatus(Integer value ){
            
            this.obj.orderStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStatus", value);
            return this;
        }
        
        public InternetOrderReturnBuilder beforeRefundFlag(Integer value ){
            
            this.obj.beforeRefundFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("beforeRefundFlag", value);
            return this;
        }
        
        public InternetOrderReturnBuilder city(String value ){
            
            this.obj.city = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("city", value);
            return this;
        }
        
        public InternetOrderReturnBuilder cityName(String value ){
            
            this.obj.cityName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cityName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder area(String value ){
            
            this.obj.area = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("area", value);
            return this;
        }
        
        public InternetOrderReturnBuilder areaName(String value ){
            
            this.obj.areaName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("areaName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder returnName(String value ){
            
            this.obj.returnName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder province(String value ){
            
            this.obj.province = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("province", value);
            return this;
        }
        
        public InternetOrderReturnBuilder provinceName(String value ){
            
            this.obj.provinceName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("provinceName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder buyerExpressMoney(BigDecimal value ){
            
            this.obj.buyerExpressMoney = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("buyerExpressMoney", value);
            return this;
        }
        
        public InternetOrderReturnBuilder sellerExpressMoney(BigDecimal value ){
            
            this.obj.sellerExpressMoney = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sellerExpressMoney", value);
            return this;
        }
        
        public InternetOrderReturnBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetOrderReturnBuilder logisticsName(String value ){
            
            this.obj.logisticsName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder receiveTime(Date value ){
            
            this.obj.receiveTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("receiveTime", value);
            return this;
        }
        
        public InternetOrderReturnBuilder logisticsCode(String value ){
            
            this.obj.logisticsCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsCode", value);
            return this;
        }
        
        public InternetOrderReturnBuilder zipcode(String value ){
            
            this.obj.zipcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("zipcode", value);
            return this;
        }
        
        public InternetOrderReturnBuilder constactPhone(String value ){
            
            this.obj.constactPhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("constactPhone", value);
            return this;
        }
        
        public InternetOrderReturnBuilder buyerName(String value ){
            
            this.obj.buyerName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("buyerName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder refundTotalFee(BigDecimal value ){
            
            this.obj.refundTotalFee = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refundTotalFee", value);
            return this;
        }
        
        public InternetOrderReturnBuilder message(String value ){
            
            this.obj.message = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("message", value);
            return this;
        }
        
        public InternetOrderReturnBuilder email(String value ){
            
            this.obj.email = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("email", value);
            return this;
        }
        
        public InternetOrderReturnBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetOrderReturnBuilder consigneeAddress(String value ){
            
            this.obj.consigneeAddress = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("consigneeAddress", value);
            return this;
        }
        
        public InternetOrderReturnBuilder memberName(String value ){
            
            this.obj.memberName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("memberName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder mobilePhone(String value ){
            
            this.obj.mobilePhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("mobilePhone", value);
            return this;
        }
        
        public InternetOrderReturnBuilder originPlatform(String value ){
            
            this.obj.originPlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatform", value);
            return this;
        }
        
        public InternetOrderReturnBuilder originPlatformName(String value ){
            
            this.obj.originPlatformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatformName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderSourceNo(String value ){
            
            this.obj.orderSourceNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSourceNo", value);
            return this;
        }
        
        public InternetOrderReturnBuilder interfacePlatform(String value ){
            
            this.obj.interfacePlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("interfacePlatform", value);
            return this;
        }
        
        public InternetOrderReturnBuilder storeCode(String value ){
            
            this.obj.storeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeCode", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
        public InternetOrderReturnBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public InternetOrderReturnBuilder businessType(String value ){
            
            this.obj.businessType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("businessType", value);
            return this;
        }
        
        public InternetOrderReturnBuilder returnType(Integer value ){
            
            this.obj.returnType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnType", value);
            return this;
        }
        
        public InternetOrderReturnBuilder productTotalQuantity(Integer value ){
            
            this.obj.productTotalQuantity = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("productTotalQuantity", value);
            return this;
        }
        
        public InternetOrderReturnBuilder returnShopName(String value ){
            
            this.obj.returnShopName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnShopName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public InternetOrderReturnBuilder ygReturnType(Integer value ){
            
            this.obj.ygReturnType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("ygReturnType", value);
            return this;
        }
        
        public InternetOrderReturnBuilder qaReturnCode(String value ){
            
            this.obj.qaReturnCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("qaReturnCode", value);
            return this;
        }
        
        public InternetOrderReturnBuilder outStoreDate(Date value ){
            
            this.obj.outStoreDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outStoreDate", value);
            return this;
        }
        
        public InternetOrderReturnBuilder qaStatus(Integer value ){
            
            this.obj.qaStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("qaStatus", value);
            return this;
        }
        
        public InternetOrderReturnBuilder ygSecondWarehouse(String value ){
            
            this.obj.ygSecondWarehouse = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("ygSecondWarehouse", value);
            return this;
        }
        
        public InternetOrderReturnBuilder ygWarehouse(String value ){
            
            this.obj.ygWarehouse = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("ygWarehouse", value);
            return this;
        }
        
        public InternetOrderReturnBuilder exceptionType(String value ){
            
            this.obj.exceptionType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("exceptionType", value);
            return this;
        }
        
        public InternetOrderReturnBuilder isquality(Integer value ){
            
            this.obj.isquality = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isquality", value);
            return this;
        }
        
        public InternetOrderReturnBuilder warehouseName(String value ){
            
            this.obj.warehouseName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("warehouseName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder theaterSchedule(String value ){
            
            this.obj.theaterSchedule = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("theaterSchedule", value);
            return this;
        }
        
        public InternetOrderReturnBuilder poNo(String value ){
            
            this.obj.poNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("poNo", value);
            return this;
        }
        
        public InternetOrderReturnBuilder isVip(Integer value ){
            
            this.obj.isVip = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isVip", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderFreight(BigDecimal value ){
            
            this.obj.orderFreight = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderFreight", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderCouponAmount(BigDecimal value ){
            
            this.obj.orderCouponAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderCouponAmount", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderGiftCardAmount(BigDecimal value ){
            
            this.obj.orderGiftCardAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderGiftCardAmount", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderPromotionAmount(BigDecimal value ){
            
            this.obj.orderPromotionAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderPromotionAmount", value);
            return this;
        }
        
        public InternetOrderReturnBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetOrderReturnBuilder markId(String value ){
            
            this.obj.markId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("markId", value);
            return this;
        }
        
        public InternetOrderReturnBuilder refundTime(Date value ){
            
            this.obj.refundTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refundTime", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderamount(BigDecimal value ){
            
            this.obj.orderamount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderamount", value);
            return this;
        }
        
        public InternetOrderReturnBuilder refundtypeoption(Integer value ){
            
            this.obj.refundtypeoption = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refundtypeoption", value);
            return this;
        }
        
        public InternetOrderReturnBuilder basescore(Integer value ){
            
            this.obj.basescore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("basescore", value);
            return this;
        }
        
        public InternetOrderReturnBuilder approverDate(Date value ){
            
            this.obj.approverDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("approverDate", value);
            return this;
        }
        
        public InternetOrderReturnBuilder buyerStoreid(String value ){
            
            this.obj.buyerStoreid = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("buyerStoreid", value);
            return this;
        }
        
        public InternetOrderReturnBuilder cardno2(String value ){
            
            this.obj.cardno2 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cardno2", value);
            return this;
        }
        
        public InternetOrderReturnBuilder shopcardno(String value ){
            
            this.obj.shopcardno = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopcardno", value);
            return this;
        }
        
        public InternetOrderReturnBuilder orderType(Integer value ){
            
            this.obj.orderType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderType", value);
            return this;
        }
        
        public InternetOrderReturnBuilder expressNo(String value ){
            
            this.obj.expressNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressNo", value);
            return this;
        }
        
        public InternetOrderReturnBuilder returnReasonName(String value ){
            
            this.obj.returnReasonName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnReasonName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder logisticsFee(Integer value ){
            
            this.obj.logisticsFee = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsFee", value);
            return this;
        }
        
        public InternetOrderReturnBuilder claimAmount(BigDecimal value ){
            
            this.obj.claimAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("claimAmount", value);
            return this;
        }
        
        public InternetOrderReturnBuilder followResultType(Integer value ){
            
            this.obj.followResultType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("followResultType", value);
            return this;
        }
        
        public InternetOrderReturnBuilder returnStoreType(String value ){
            
            this.obj.returnStoreType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnStoreType", value);
            return this;
        }
        
        public InternetOrderReturnBuilder operationState(Integer value ){
            
            this.obj.operationState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("operationState", value);
            return this;
        }
        
        public InternetOrderReturnBuilder transferShop(String value ){
            
            this.obj.transferShop = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("transferShop", value);
            return this;
        }
        
        public InternetOrderReturnBuilder updateName(String value ){
            
            this.obj.updateName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("updateName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder vstoreCode(String value ){
            
            this.obj.vstoreCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public InternetOrderReturnBuilder tertiarySource(String value ){
            
            this.obj.tertiarySource = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("tertiarySource", value);
            return this;
        }
        
        public InternetOrderReturnBuilder tertiarySourceName(String value ){
            
            this.obj.tertiarySourceName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("tertiarySourceName", value);
            return this;
        }
        
        public InternetOrderReturnBuilder accountsDate(Date value ){
            
            this.obj.accountsDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("accountsDate", value);
            return this;
        }
        
        public InternetOrderReturnBuilder entryOrderCode(String value ){
            
            this.obj.entryOrderCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("entryOrderCode", value);
            return this;
        }
        
        public InternetOrderReturnBuilder returnShopNo(String value ){
            
            this.obj.returnShopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnShopNo", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }