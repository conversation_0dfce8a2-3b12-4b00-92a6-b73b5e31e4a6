package cn.wonhigh.baize.model.dto.sellOutConfig;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SellOutConfigItemDto extends SellOutConfigDto {
    private static final long serialVersionUID = -9018560860977050446L;


    /**
     *
     itemCode
     :
     "AR5005-101"
     itemNo
     :
     "20190103000750"
     sizeNo
     :
     "XS"
     */

    private String itemCode;
    private String itemNo;
    private String sizeNo;

    @JsonProperty
    public String getUk() {
        return getBrandNo() + "_" + itemNo + "_" + sizeNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getSizeNo() {
        return sizeNo;
    }

    public void setSizeNo(String sizeNo) {
        this.sizeNo = sizeNo;
    }
}
