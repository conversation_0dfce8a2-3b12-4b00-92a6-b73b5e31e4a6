package cn.wonhigh.baize.model.enums;

import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 调整单同步状态枚举
 * @date 2025/6/13 15:57
 */
public enum AdjustSyncStatusEnums {
    NOT_SYNC(0, "未同步"),
    PART_SYNC(1, "部分同步"),
    ALL_SYNC(2, "全部同步"),
    ALL_FAIL_SYNC(3, "全部失败");

    private Integer value;
    private String desc;

    AdjustSyncStatusEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AdjustSyncStatusEnums getAdjustSyncStatusEnums(Integer status) {
        for (AdjustSyncStatusEnums adjustSyncStatusEnums : AdjustSyncStatusEnums.values()) {
            if (adjustSyncStatusEnums.getValue().equals(status)) {
                return adjustSyncStatusEnums;
            }
        }
        return null;
    }

    public static List<Map<String, Object>> getAdjustSyncStatusList() {
        return Stream.of(AdjustSyncStatusEnums.values())
                .map(adjustSyncStatusEnums -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("value", adjustSyncStatusEnums.getValue());
                    map.put("desc", adjustSyncStatusEnums.getDesc());
                    return map;
                }).collect(Collectors.toList());
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}