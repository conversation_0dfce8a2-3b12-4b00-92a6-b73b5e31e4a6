package cn.wonhigh.baize.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 一级来源枚举
 * @create 2024/8/5 10:21
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PlatformEnum {

    WBPT("外部平台", "WBPT"),
    ZHLS("智慧零售", "ZHLS")
    ;


    private final String name;

    private final String type;


    PlatformEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }


    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getName(String type){
        PlatformEnum[] enums = PlatformEnum.values();
        for (PlatformEnum anEnum : enums) {
            if(anEnum.type.equals(type)){
                return anEnum.getName();
            }
        }
        return type;
    }
}
