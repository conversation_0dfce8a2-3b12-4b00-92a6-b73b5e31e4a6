package cn.wonhigh.baize.model.entity.oms;

import cn.mercury.annotation.Label;
import cn.mercury.basic.query.Query;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;


/**
 * (OmsStockUploadStrategy)实体类
 *
 * <AUTHOR>
 * @since 2024-10-29 23:06:12
 */
public class OmsStockUploadStrategy extends BaseEntity<Long> implements Serializable {
    private static final long serialVersionUID = -87582856221182799L;
    /**
     * 库存上传策略id
     */
    @Label("库存上传策略id")
    private Long stockUploadStrategyId;
    /**
     * 更新时间
     */
    @Label("更新时间")
    private Date modifiedTime;
    /**
     * 创建时间
     */
    @Label("创建时间")
    private Date createdTime;
    /**
     * 库存上传策略名称
     */
    @Label("库存上传策略名称")
    private String stockUploadStrategyName;
    /**
     * 店铺id
     */
    @Label("店铺id")
    private Long storeId;
    /**
     * 店铺名称
     */
    @Label("店铺名称")
    private String storeName;
    /**
     * 自动上传
     */
    @Label("自动上传")
    private Integer isAutoUpload;
    /**
     * 手工上传
     */
    @Label("手工上传")
    private Integer isManualUpload;
    /**
     * 商城仓库
     */
    @Label("商城仓库")
    private String mallWarehouse;
    /**
     * 策略设置
     */
    @Label("策略设置")
    private String settingJson;


    public Long getStockUploadStrategyId() {
        return stockUploadStrategyId;
    }

    public void setStockUploadStrategyId(Long stockUploadStrategyId) {
        this.stockUploadStrategyId = stockUploadStrategyId;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public String getStockUploadStrategyName() {
        return stockUploadStrategyName;
    }

    public void setStockUploadStrategyName(String stockUploadStrategyName) {
        this.stockUploadStrategyName = stockUploadStrategyName;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getIsAutoUpload() {
        return isAutoUpload;
    }

    public void setIsAutoUpload(Integer isAutoUpload) {
        this.isAutoUpload = isAutoUpload;
    }

    public Integer getIsManualUpload() {
        return isManualUpload;
    }

    public void setIsManualUpload(Integer isManualUpload) {
        this.isManualUpload = isManualUpload;
    }

    public String getMallWarehouse() {
        return mallWarehouse;
    }

    public void setMallWarehouse(String mallWarehouse) {
        this.mallWarehouse = mallWarehouse;
    }

    public String getSettingJson() {
        return settingJson;
    }

    public void setSettingJson(String settingJson) {
        this.settingJson = settingJson;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static OmsStockUploadStrategyBuilder build() {
        return new OmsStockUploadStrategyBuilder(new OmsStockUploadStrategy());
    }

    public static OmsStockUploadStrategyBuilder build(OmsStockUploadStrategy value) {
        return new OmsStockUploadStrategyBuilder(value);
    }

    public static class OmsStockUploadStrategyBuilder extends AbstractEntryBuilder<OmsStockUploadStrategy> {

        private OmsStockUploadStrategyBuilder(OmsStockUploadStrategy entry) {
            this.obj = entry;
        }

        @Override
        public OmsStockUploadStrategy object() {
            return this.obj;
        }

        public OmsStockUploadStrategyBuilder stockUploadStrategyId(Long value) {

            this.obj.setStockUploadStrategyId(value);

            if (query == null)
                query = new Query();
            this.query.where("stockUploadStrategyId", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder modifiedTime(Date value) {

            this.obj.setModifiedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("modifiedTime", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder createdTime(Date value) {

            this.obj.setCreatedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("createdTime", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder stockUploadStrategyName(String value) {

            this.obj.setStockUploadStrategyName(value);

            if (query == null)
                query = new Query();
            this.query.where("stockUploadStrategyName", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder storeId(Long value) {

            this.obj.setStoreId(value);

            if (query == null)
                query = new Query();
            this.query.where("storeId", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder storeName(String value) {

            this.obj.setStoreName(value);

            if (query == null)
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder isAutoUpload(Integer value) {

            this.obj.setIsAutoUpload(value);

            if (query == null)
                query = new Query();
            this.query.where("isAutoUpload", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder isManualUpload(Integer value) {

            this.obj.setIsManualUpload(value);

            if (query == null)
                query = new Query();
            this.query.where("isManualUpload", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder mallWarehouse(String value) {

            this.obj.setMallWarehouse(value);

            if (query == null)
                query = new Query();
            this.query.where("mallWarehouse", value);
            return this;
        }

        public OmsStockUploadStrategyBuilder settingJson(String value) {

            this.obj.setSettingJson(value);

            if (query == null)
                query = new Query();
            this.query.where("settingJson", value);
            return this;
        }
    }
}
