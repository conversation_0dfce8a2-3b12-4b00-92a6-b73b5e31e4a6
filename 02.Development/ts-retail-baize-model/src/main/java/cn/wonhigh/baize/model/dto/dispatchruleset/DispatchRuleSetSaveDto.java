package cn.wonhigh.baize.model.dto.dispatchruleset;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Valid
public class DispatchRuleSetSaveDto implements Serializable {


    private Long id;

    private String ruleNo;

    private Integer type;


    @NotBlank(message = "规则名称不能为空")
    private String ruleName;

    //@NotBlank(message = "品牌不能为空")
    private String brandNo;

    private String brandName;


    /**
     * 需要删除的数据
     */
    private String deleteIds;

    private String platformNo;
    private String platformName;

    /**
     * 策略数据集合
     */
    @NotNull
    @NotEmpty(message = "派单规则策略没有配置")
    private List<InternetDispatchRulePriorityDto> ruleList;

    @Valid
    private List<InternetDispatchRuleSetDtlSaveDto> dtlList;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleNo() {
        return ruleNo;
    }

    public void setRuleNo(String ruleNo) {
        this.ruleNo = ruleNo;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public List<InternetDispatchRulePriorityDto> getRuleList() {
        return ruleList;
    }

    public void setRuleList(List<InternetDispatchRulePriorityDto> ruleList) {
        this.ruleList = ruleList;
    }

    public List<InternetDispatchRuleSetDtlSaveDto> getDtlList() {
        return dtlList;
    }

    public void setDtlList(List<InternetDispatchRuleSetDtlSaveDto> dtlList) {
        this.dtlList = dtlList;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDeleteIds() {
        return deleteIds;
    }

    public void setDeleteIds(String deleteIds) {
        this.deleteIds = deleteIds;
    }


    public String getPlatformNo() {
        return platformNo;
    }

    public void setPlatformNo(String platformNo) {
        this.platformNo = platformNo;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }
}
