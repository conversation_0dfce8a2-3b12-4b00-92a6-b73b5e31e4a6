/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.IEntryBuildable;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 门店信息表
**/
public class Shop  extends cn.mercury.domain.BaseEntity<Integer>  
{

    private static final long serialVersionUID = 1694401292654L;
    
    /**
    *传真号
    **/ 
    @Label("传真号") 
    private String fax;
    

    public String getFax(){
        return  fax;
    }
    public void setFax(String val ){
        fax = val;
    }
    /**
    *品柜形式
    **/ 
    @Label("品柜形式") 
    private String location;
    

    public String getLocation(){
        return  location;
    }
    public void setLocation(String val ){
        location = val;
    }
    /**
    *父级卖场编码
    **/ 
    @Label("父级卖场编码") 
    private String parentSaleNo;
    

    public String getParentSaleNo(){
        return  parentSaleNo;
    }
    public void setParentSaleNo(String val ){
        parentSaleNo = val;
    }
    /**
    *店铺全称
    **/ 
    @Label("店铺全称") 
    private String fullName;
    

    public String getFullName(){
        return  fullName;
    }
    public void setFullName(String val ){
        fullName = val;
    }
    /**
    *店铺简称
    **/ 
    @Label("店铺简称") 
    private String shortName;
    

    public String getShortName(){
        return  shortName;
    }
    public void setShortName(String val ){
        shortName = val;
    }
    /**
    *检索码
    **/ 
    @Label("检索码") 
    private String searchCode;
    

    public String getSearchCode(){
        return  searchCode;
    }
    public void setSearchCode(String val ){
        searchCode = val;
    }
    /**
    *结算公司编码
    **/ 
    @Label("结算公司编码") 
    private String companyNo;
    

    public String getCompanyNo(){
        return  companyNo;
    }
    public void setCompanyNo(String val ){
        companyNo = val;
    }
    /**
    *店铺所属仓库编码
    **/ 
    @Label("店铺所属仓库编码") 
    private String storeNo;
    

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    /**
    *店铺模式编码
    **/ 
    @Label("店铺模式编码") 
    private String shopModeNo;
    

    public String getShopModeNo(){
        return  shopModeNo;
    }
    public void setShopModeNo(String val ){
        shopModeNo = val;
    }
    /**
    *时间序列
    **/ 
    @Label("时间序列") 
    private Long timeSeq;
    

    public Long getTimeSeq(){
        return  timeSeq;
    }
    public void setTimeSeq(Long val ){
        timeSeq = val;
    }
    /**
    *备注
    **/ 
    @Label("备注") 
    private String remark;
    

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    /**
    *组织编号(历史原因从集团接入的数据为经营城市)
    **/ 
    @Label("组织编号") 
    private String organNo;
    

    public String getOrganNo(){
        return  organNo;
    }
    public void setOrganNo(String val ){
        organNo = val;
    }
    /**
    *集团品牌
    **/ 
    @Label("集团品牌") 
    private String groupBrand;
    

    public String getGroupBrand(){
        return  groupBrand;
    }
    public void setGroupBrand(String val ){
        groupBrand = val;
    }
    /**
    *客户编码
    **/ 
    @Label("客户编码") 
    private String customerNo;
    

    public String getCustomerNo(){
        return  customerNo;
    }
    public void setCustomerNo(String val ){
        customerNo = val;
    }
    /**
    *物流费用承担方
    **/ 
    @Label("物流费用承担方") 
    private String logisticsCostPayer;
    

    public String getLogisticsCostPayer(){
        return  logisticsCostPayer;
    }
    public void setLogisticsCostPayer(String val ){
        logisticsCostPayer = val;
    }
    /**
    *经度
    **/ 
    @Label("经度") 
    private String shopLongitude;
    

    public String getShopLongitude(){
        return  shopLongitude;
    }
    public void setShopLongitude(String val ){
        shopLongitude = val;
    }
    /**
    *店铺原代号
    **/ 
    @Label("店铺原代号") 
    private String shopLcode;
    

    public String getShopLcode(){
        return  shopLcode;
    }
    public void setShopLcode(String val ){
        shopLcode = val;
    }
    /**
    *店铺外码
    **/ 
    @Label("店铺外码") 
    private String code;
    

    public String getCode(){
        return  code;
    }
    public void setCode(String val ){
        code = val;
    }
    /**
    *店铺编码
    **/ 
    @Label("店铺编码") 
    private String shopNo;
    

    public String getShopNo(){
        return  shopNo;
    }
    public void setShopNo(String val ){
        shopNo = val;
    }
    /**
    *纬度
    **/ 
    @Label("纬度") 
    private String shopLatitude;
    

    public String getShopLatitude(){
        return  shopLatitude;
    }
    public void setShopLatitude(String val ){
        shopLatitude = val;
    }
    /**
    *经营城市编号
    **/ 
    @Label("经营城市编号") 
    private String bizCityNo;
    

    public String getBizCityNo(){
        return  bizCityNo;
    }
    public void setBizCityNo(String val ){
        bizCityNo = val;
    }
    /**
    *所属业务单元
    **/ 
    @Label("所属业务单元") 
    private String sysNo;
    

    public String getSysNo(){
        return  sysNo;
    }
    public void setSysNo(String val ){
        sysNo = val;
    }
    /**
    *在线标志:1在线，0离线
    **/ 
    @Label("在线标志") 
    private Integer onLineFlag;
    

    public Integer getOnLineFlag(){
        return  onLineFlag;
    }
    public void setOnLineFlag(Integer val ){
        onLineFlag = val;
    }
    /**
    *调价级别
    **/ 
    @Label("调价级别") 
    private String priceAdjustLevel;
    

    public String getPriceAdjustLevel(){
        return  priceAdjustLevel;
    }
    public void setPriceAdjustLevel(String val ){
        priceAdjustLevel = val;
    }
    /**
    *店铺类别
    **/ 
    @Label("店铺类别") 
    private String shopClassify;
    

    public String getShopClassify(){
        return  shopClassify;
    }
    public void setShopClassify(String val ){
        shopClassify = val;
    }
    /**
    *经营类型（男鞋，女鞋，综合）
    **/ 
    @Label("经营类型") 
    private String categoryCode;
    

    public String getCategoryCode(){
        return  categoryCode;
    }
    public void setCategoryCode(String val ){
        categoryCode = val;
    }
    /**
    *成立日期(店铺正式营业的日期)
    **/ 
    @Label("成立日期") 
    private Date openDate;
    

    public Date getOpenDate(){
        return  openDate;
    }
    public void setOpenDate(Date val ){
        openDate = val;
    }
    /**
    *撤销日期(店铺停止营运的日期)
    **/ 
    @Label("撤销日期") 
    private Date closeDate;
    

    public Date getCloseDate(){
        return  closeDate;
    }
    public void setCloseDate(Date val ){
        closeDate = val;
    }
    /**
    *店铺状态( 0:冻结,1:正常,9:撤销)
    **/ 
    @Label("店铺状态") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *卖场面积
    **/ 
    @Label("卖场面积") 
    private BigDecimal area;
    

    public BigDecimal getArea(){
        return  area;
    }
    public void setArea(BigDecimal val ){
        area = val;
    }
    /**
    *仓库面积
    **/ 
    @Label("仓库面积") 
    private BigDecimal areaLeft;
    

    public BigDecimal getAreaLeft(){
        return  areaLeft;
    }
    public void setAreaLeft(BigDecimal val ){
        areaLeft = val;
    }
    /**
    *总面积
    **/ 
    @Label("总面积") 
    private BigDecimal areaTotal;
    

    public BigDecimal getAreaTotal(){
        return  areaTotal;
    }
    public void setAreaTotal(BigDecimal val ){
        areaTotal = val;
    }
    /**
    *面积单位(1:㎡)
    **/ 
    @Label("面积单位") 
    private String areaUnit;
    

    public String getAreaUnit(){
        return  areaUnit;
    }
    public void setAreaUnit(String val ){
        areaUnit = val;
    }
    /**
    *行政省编码
    **/ 
    @Label("行政省编码") 
    private String provinceNo;
    

    public String getProvinceNo(){
        return  provinceNo;
    }
    public void setProvinceNo(String val ){
        provinceNo = val;
    }
    /**
    *行政市编码
    **/ 
    @Label("行政市编码") 
    private String cityNo;
    

    public String getCityNo(){
        return  cityNo;
    }
    public void setCityNo(String val ){
        cityNo = val;
    }
    /**
    *行政县编码
    **/ 
    @Label("行政县编码") 
    private String countyNo;
    

    public String getCountyNo(){
        return  countyNo;
    }
    public void setCountyNo(String val ){
        countyNo = val;
    }
    /**
    *地址(填写时不用包含省、市、县)
    **/ 
    @Label("地址") 
    private String address;
    

    public String getAddress(){
        return  address;
    }
    public void setAddress(String val ){
        address = val;
    }
    /**
    *邮编
    **/ 
    @Label("邮编") 
    private String zipCode;
    

    public String getZipCode(){
        return  zipCode;
    }
    public void setZipCode(String val ){
        zipCode = val;
    }
    /**
    *联系人
    **/ 
    @Label("联系人") 
    private String contactName;
    

    public String getContactName(){
        return  contactName;
    }
    public void setContactName(String val ){
        contactName = val;
    }
    /**
    *电话号码
    **/ 
    @Label("电话号码") 
    private String tel;
    

    public String getTel(){
        return  tel;
    }
    public void setTel(String val ){
        tel = val;
    }
    /**
    *电子邮箱
    **/ 
    @Label("电子邮箱") 
    private String email;
    

    public String getEmail(){
        return  email;
    }
    public void setEmail(String val ){
        email = val;
    }
    /**
    *销售渠道编码
    **/ 
    @Label("销售渠道编码") 
    private String channelNo;
    

    public String getChannelNo(){
        return  channelNo;
    }
    public void setChannelNo(String val ){
        channelNo = val;
    }
    /**
    *商圈编码
    **/ 
    @Label("商圈编码") 
    private String cmcdistNo;
    

    public String getCmcdistNo(){
        return  cmcdistNo;
    }
    public void setCmcdistNo(String val ){
        cmcdistNo = val;
    }
    /**
    *店员配备数(门店必填,指标准的店员配备数量)
    **/ 
    @Label("店员配备数") 
    private Integer employeAmount;
    

    public Integer getEmployeAmount(){
        return  employeAmount;
    }
    public void setEmployeAmount(Integer val ){
        employeAmount = val;
    }
    /**
    *结算方式(门店必填, 1:扣费店 2:租金店 3:不结算)
    **/ 
    @Label("结算方式") 
    private String payType;
    

    public String getPayType(){
        return  payType;
    }
    public void setPayType(String val ){
        payType = val;
    }
    /**
    *收银位数(门店必填, 0:元 1:角 2:分
    **/ 
    @Label("收银位数") 
    private String digits;
    

    public String getDigits(){
        return  digits;
    }
    public void setDigits(String val ){
        digits = val;
    }
    /**
    *每天营业开始时间
    **/ 
    @Label("每天营业开始时间") 
    private String startupTime;
    

    public String getStartupTime(){
        return  startupTime;
    }
    public void setStartupTime(String val ){
        startupTime = val;
    }
    /**
    *每天营业关闭时间
    **/ 
    @Label("每天营业关闭时间") 
    private String shutdownTime;
    

    public String getShutdownTime(){
        return  shutdownTime;
    }
    public void setShutdownTime(String val ){
        shutdownTime = val;
    }
    /**
    *门店级别( A、B、C、D、E)
    **/ 
    @Label("门店级别") 
    private String shopLevel;
    

    public String getShopLevel(){
        return  shopLevel;
    }
    public void setShopLevel(String val ){
        shopLevel = val;
    }
    /**
    *主营品类(门店必填, 1:男鞋 2:女鞋 3:童鞋 4:综合)
    **/ 
    @Label("主营品类") 
    private String major;
    

    public String getMajor(){
        return  major;
    }
    public void setMajor(String val ){
        major = val;
    }
    /**
    *店铺细类 单品多品(门店必填,C:多品店 D:单品店)
    **/ 
    @Label("店铺细类") 
    private String multi;
    

    public String getMulti(){
        return  multi;
    }
    public void setMulti(String val ){
        multi = val;
    }
    /**
    *店铺大类 批发零售(门店必填,1:零售；2:批发)
    **/ 
    @Label("店铺大类") 
    private String saleMode;
    

    public String getSaleMode(){
        return  saleMode;
    }
    public void setSaleMode(String val ){
        saleMode = val;
    }
    /**
    *店铺小类 (销售类型(门店必填, A0:商场店中店 A1:商场独立店 A2:商场

特卖店 A3:商场寄卖店 BJ:独立街边店 BM:MALL B3:独立寄卖店, D0:批发加盟店 D1:批发团购店 D2:批发员购店 D3:批发调货店)
    **/ 
    @Label("店铺小类") 
    private String retailType;
    

    public String getRetailType(){
        return  retailType;
    }
    public void setRetailType(String val ){
        retailType = val;
    }
    /**
    *商场编码
    **/ 
    @Label("商场编码") 
    private String mallNo;
    

    public String getMallNo(){
        return  mallNo;
    }
    public void setMallNo(String val ){
        mallNo = val;
    }
    /**
    *MAP店标志,1是;0否
    **/ 
    @Label("店标志") 
    private Integer mapFlag;
    

    public Integer getMapFlag(){
        return  mapFlag;
    }
    public void setMapFlag(Integer val ){
        mapFlag = val;
    }
    /**
    *法人公司编码
    **/ 
    @Label("法人公司编码") 
    private String corporationNo;
    

    public String getCorporationNo(){
        return  corporationNo;
    }
    public void setCorporationNo(String val ){
        corporationNo = val;
    }
    /**
    *本部编码
    **/ 
    @Label("本部编码") 
    private String organTypeNo;
    

    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    /**
    *专柜/卖场 1 专柜 2 卖场
    **/ 
    @Label("专柜") 
    private Integer saleType;
    

    public Integer getSaleType(){
        return  saleType;
    }
    public void setSaleType(Integer val ){
        saleType = val;
    }
    /**
    *卖场编码
    **/ 
    @Label("卖场编码") 
    private String saleNo;
    

    public String getSaleNo(){
        return  saleNo;
    }
    public void setSaleNo(String val ){
        saleNo = val;
    }
    /**
    *片区编码
    **/ 
    @Label("片区编码") 
    private String regionNo;
    

    public String getRegionNo(){
        return  regionNo;
    }
    public void setRegionNo(String val ){
        regionNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public ShopBuilder build(){
        return new ShopBuilder(this);
    }

    public static class ShopBuilder extends AbstractEntryBuilder<Shop>{

        private ShopBuilder(Shop entry){
            this.obj = entry;
        }

       @Override
		public Shop object() {
			return this.obj;
		}

        
        public ShopBuilder fax(String value ){
            
            this.obj.fax = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("fax", value);
            return this;
        }
        
        public ShopBuilder location(String value ){
            
            this.obj.location = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("location", value);
            return this;
        }
        
        public ShopBuilder parentSaleNo(String value ){
            
            this.obj.parentSaleNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parentSaleNo", value);
            return this;
        }
        
        public ShopBuilder fullName(String value ){
            
            this.obj.fullName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("fullName", value);
            return this;
        }
        
        public ShopBuilder shortName(String value ){
            
            this.obj.shortName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shortName", value);
            return this;
        }
        
        public ShopBuilder searchCode(String value ){
            
            this.obj.searchCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("searchCode", value);
            return this;
        }
        
        public ShopBuilder companyNo(String value ){
            
            this.obj.companyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyNo", value);
            return this;
        }
        
        public ShopBuilder storeNo(String value ){
            
            this.obj.storeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public ShopBuilder shopModeNo(String value ){
            
            this.obj.shopModeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopModeNo", value);
            return this;
        }
        
        public ShopBuilder timeSeq(Long value ){
            
            this.obj.timeSeq = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("timeSeq", value);
            return this;
        }
        
        public ShopBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public ShopBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public ShopBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public ShopBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public ShopBuilder organNo(String value ){
            
            this.obj.organNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organNo", value);
            return this;
        }
        
        public ShopBuilder groupBrand(String value ){
            
            this.obj.groupBrand = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("groupBrand", value);
            return this;
        }
        
        public ShopBuilder customerNo(String value ){
            
            this.obj.customerNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customerNo", value);
            return this;
        }
        
        public ShopBuilder logisticsCostPayer(String value ){
            
            this.obj.logisticsCostPayer = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsCostPayer", value);
            return this;
        }
        
        public ShopBuilder shopLongitude(String value ){
            
            this.obj.shopLongitude = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopLongitude", value);
            return this;
        }
        
        public ShopBuilder shopLcode(String value ){
            
            this.obj.shopLcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopLcode", value);
            return this;
        }
        
        public ShopBuilder code(String value ){
            
            this.obj.code = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("code", value);
            return this;
        }
        
        public ShopBuilder shopNo(String value ){
            
            this.obj.shopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopNo", value);
            return this;
        }
        
        public ShopBuilder id(Integer value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public ShopBuilder shopLatitude(String value ){
            
            this.obj.shopLatitude = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopLatitude", value);
            return this;
        }
        
        public ShopBuilder bizCityNo(String value ){
            
            this.obj.bizCityNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("bizCityNo", value);
            return this;
        }
        
        public ShopBuilder sysNo(String value ){
            
            this.obj.sysNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sysNo", value);
            return this;
        }
        
        public ShopBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public ShopBuilder onLineFlag(Integer value ){
            
            this.obj.onLineFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("onLineFlag", value);
            return this;
        }
        
        public ShopBuilder priceAdjustLevel(String value ){
            
            this.obj.priceAdjustLevel = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("priceAdjustLevel", value);
            return this;
        }
        
        public ShopBuilder shopClassify(String value ){
            
            this.obj.shopClassify = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopClassify", value);
            return this;
        }
        
        public ShopBuilder categoryCode(String value ){
            
            this.obj.categoryCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("categoryCode", value);
            return this;
        }
        
        public ShopBuilder openDate(Date value ){
            
            this.obj.openDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("openDate", value);
            return this;
        }
        
        public ShopBuilder closeDate(Date value ){
            
            this.obj.closeDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("closeDate", value);
            return this;
        }
        
        public ShopBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public ShopBuilder area(BigDecimal value ){
            
            this.obj.area = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("area", value);
            return this;
        }
        
        public ShopBuilder areaLeft(BigDecimal value ){
            
            this.obj.areaLeft = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("areaLeft", value);
            return this;
        }
        
        public ShopBuilder areaTotal(BigDecimal value ){
            
            this.obj.areaTotal = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("areaTotal", value);
            return this;
        }
        
        public ShopBuilder areaUnit(String value ){
            
            this.obj.areaUnit = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("areaUnit", value);
            return this;
        }
        
        public ShopBuilder provinceNo(String value ){
            
            this.obj.provinceNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("provinceNo", value);
            return this;
        }
        
        public ShopBuilder cityNo(String value ){
            
            this.obj.cityNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cityNo", value);
            return this;
        }
        
        public ShopBuilder countyNo(String value ){
            
            this.obj.countyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("countyNo", value);
            return this;
        }
        
        public ShopBuilder address(String value ){
            
            this.obj.address = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("address", value);
            return this;
        }
        
        public ShopBuilder zipCode(String value ){
            
            this.obj.zipCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("zipCode", value);
            return this;
        }
        
        public ShopBuilder contactName(String value ){
            
            this.obj.contactName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("contactName", value);
            return this;
        }
        
        public ShopBuilder tel(String value ){
            
            this.obj.tel = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("tel", value);
            return this;
        }
        
        public ShopBuilder email(String value ){
            
            this.obj.email = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("email", value);
            return this;
        }
        
        public ShopBuilder channelNo(String value ){
            
            this.obj.channelNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("channelNo", value);
            return this;
        }
        
        public ShopBuilder cmcdistNo(String value ){
            
            this.obj.cmcdistNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cmcdistNo", value);
            return this;
        }
        
        public ShopBuilder employeAmount(Integer value ){
            
            this.obj.employeAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("employeAmount", value);
            return this;
        }
        
        public ShopBuilder payType(String value ){
            
            this.obj.payType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("payType", value);
            return this;
        }
        
        public ShopBuilder digits(String value ){
            
            this.obj.digits = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("digits", value);
            return this;
        }
        
        public ShopBuilder startupTime(String value ){
            
            this.obj.startupTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("startupTime", value);
            return this;
        }
        
        public ShopBuilder shutdownTime(String value ){
            
            this.obj.shutdownTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shutdownTime", value);
            return this;
        }
        
        public ShopBuilder shopLevel(String value ){
            
            this.obj.shopLevel = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopLevel", value);
            return this;
        }
        
        public ShopBuilder major(String value ){
            
            this.obj.major = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("major", value);
            return this;
        }
        
        public ShopBuilder multi(String value ){
            
            this.obj.multi = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("multi", value);
            return this;
        }
        
        public ShopBuilder saleMode(String value ){
            
            this.obj.saleMode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleMode", value);
            return this;
        }
        
        public ShopBuilder retailType(String value ){
            
            this.obj.retailType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailType", value);
            return this;
        }
        
        public ShopBuilder mallNo(String value ){
            
            this.obj.mallNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("mallNo", value);
            return this;
        }
        
        public ShopBuilder mapFlag(Integer value ){
            
            this.obj.mapFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("mapFlag", value);
            return this;
        }
        
        public ShopBuilder corporationNo(String value ){
            
            this.obj.corporationNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("corporationNo", value);
            return this;
        }
        
        public ShopBuilder organTypeNo(String value ){
            
            this.obj.organTypeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
        public ShopBuilder saleType(Integer value ){
            
            this.obj.saleType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleType", value);
            return this;
        }
        
        public ShopBuilder saleNo(String value ){
            
            this.obj.saleNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleNo", value);
            return this;
        }
        
        public ShopBuilder regionNo(String value ){
            
            this.obj.regionNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("regionNo", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }