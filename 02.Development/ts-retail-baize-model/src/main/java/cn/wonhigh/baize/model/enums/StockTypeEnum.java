package cn.wonhigh.baize.model.enums;

/**
 * @author: wudong
 * @create: 2025-06-05 14:32
 **/
public enum StockTypeEnum {

    NORMAL(0, "正品"),
    B(17, "B品"),
    C(18, "C品"),
    ;

    private int value;

    private String desc;

    private static final StockTypeEnum[] VALUES = StockTypeEnum.values();

    StockTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static StockTypeEnum of(int value) {
        for (StockTypeEnum item : VALUES) {
            if (item.value == value) {
                return item;
            }
        }
        return null;
    }
}
