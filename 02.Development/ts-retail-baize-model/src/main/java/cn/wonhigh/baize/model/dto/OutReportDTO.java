package cn.wonhigh.baize.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @created 2023/2/27 9:51
 */
public class OutReportDTO implements Serializable {
	private static final long serialVersionUID = 718243995460933759L;


	private String id;

	/**
	 * 出库单号
	 */
	@ExcelProperty("出库单")
	private String billNo;

	/**
	 * 单据状态
	 */
	private String statusStr;

	/**
	 * 来源单号
	 */
	private String refBillNo;

	/**
	 * 网销订单号
	 */
	@ExcelProperty("网销订单号")
	private String orderSubNo;

	/**
	 * 外部交易号
	 */
	@ExcelProperty("外部交易号")
	private String outOrderId;

	/**
	 * 三级来源
	 */
	@ExcelProperty("三级来源")
	private String orderSourceNo;
	/**
	 * 来源平台
	 */
	@ExcelProperty("来源平台")
	private String originPlatformName;

	/**
	 * 发货方
	 */
	@ExcelProperty("发货方")
	private String sendStoreName;

	/**
	 * 发方货管单位
	 */
	@ExcelProperty("发方货管单位")
	private String orderUnitName;

	/**
	 * 发货日期
	 */
	@ExcelProperty("发货日期")
	private String sendOutDate;

	/**
	 * 发货数量
	 */
	@ExcelProperty("发货数量")
	private String sendDetailTotal;

	/**
	 * 金额
	 */
	@ExcelProperty("金额")
	private String totalPrice;

	/**
	 * 快递公司
	 */
	private String logisticsCompanyName;

	@ExcelProperty("品牌")
	private String sendBrandNo;
	private String sendBrandName;

	@ExcelProperty("条码")
	private String sendBarcode;

	@ExcelProperty("尺码")
	private String sendSizeNo;

	@ExcelProperty("商品编号")
	private String sendItemCode;

	@ExcelProperty("快递单号")
	private String expressCode;



	/*退货*/
	@ExcelProperty("退货商品品牌")
	private String returnBrandNo;
	private String returnBrandName;

	@ExcelProperty("退货商品条码")
	private String returnBarcode;

	@ExcelProperty("退货商品尺码")
	private String returnSizeNo;

	@ExcelProperty("退货商品编号")
	private String returnItemCode;


	/**
	 * 取消时间, 为上游发起的拦截时间
	 */
	@ExcelProperty("取消时间")
	private String cancelTime;

	/**
	 * 货损类型
	 */
	@ExcelProperty("货损类型")
	private String damageOfCargoName;



	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getBillNo() {
		return billNo;
	}

	public void setBillNo(String billNo) {
		this.billNo = billNo;
	}

	public String getStatusStr() {
		return statusStr;
	}

	public void setStatusStr(String statusStr) {
		this.statusStr = statusStr;
	}

	public String getRefBillNo() {
		return refBillNo;
	}

	public void setRefBillNo(String refBillNo) {
		this.refBillNo = refBillNo;
	}

	public String getOrderSubNo() {
		return orderSubNo;
	}

	public void setOrderSubNo(String orderSubNo) {
		this.orderSubNo = orderSubNo;
	}

	public String getOutOrderId() {
		return outOrderId;
	}

	public void setOutOrderId(String outOrderId) {
		this.outOrderId = outOrderId;
	}

	public String getOrderSourceNo() {
		return orderSourceNo;
	}

	public void setOrderSourceNo(String orderSourceNo) {
		this.orderSourceNo = orderSourceNo;
	}

	public String getOriginPlatformName() {
		return originPlatformName;
	}

	public void setOriginPlatformName(String originPlatformName) {
		this.originPlatformName = originPlatformName;
	}

	public String getSendStoreName() {
		return sendStoreName;
	}

	public void setSendStoreName(String sendStoreName) {
		this.sendStoreName = sendStoreName;
	}

	public String getOrderUnitName() {
		return orderUnitName;
	}

	public void setOrderUnitName(String orderUnitName) {
		this.orderUnitName = orderUnitName;
	}

	public String getSendOutDate() {
		return sendOutDate;
	}

	public void setSendOutDate(String sendOutDate) {
		this.sendOutDate = sendOutDate;
	}

	public String getSendDetailTotal() {
		return sendDetailTotal;
	}

	public void setSendDetailTotal(String sendDetailTotal) {
		this.sendDetailTotal = sendDetailTotal;
	}

	public String getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(String totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getLogisticsCompanyName() {
		return logisticsCompanyName;
	}

	public void setLogisticsCompanyName(String logisticsCompanyName) {
		this.logisticsCompanyName = logisticsCompanyName;
	}


	public String getSendBrandNo() {
		return sendBrandNo;
	}

	public void setSendBrandNo(String sendBrandNo) {
		this.sendBrandNo = sendBrandNo;
	}

	public String getSendBrandName() {
		return sendBrandName;
	}

	public void setSendBrandName(String sendBrandName) {
		this.sendBrandName = sendBrandName;
	}

	public String getSendBarcode() {
		return sendBarcode;
	}

	public void setSendBarcode(String sendBarcode) {
		this.sendBarcode = sendBarcode;
	}

	public String getSendSizeNo() {
		return sendSizeNo;
	}

	public void setSendSizeNo(String sendSizeNo) {
		this.sendSizeNo = sendSizeNo;
	}

	public String getSendItemCode() {
		return sendItemCode;
	}

	public void setSendItemCode(String sendItemCode) {
		this.sendItemCode = sendItemCode;
	}

	public String getExpressCode() {
		return expressCode;
	}

	public void setExpressCode(String expressCode) {
		this.expressCode = expressCode;
	}

	public String getReturnBrandNo() {
		return returnBrandNo;
	}

	public void setReturnBrandNo(String returnBrandNo) {
		this.returnBrandNo = returnBrandNo;
	}

	public String getReturnBrandName() {
		return returnBrandName;
	}

	public void setReturnBrandName(String returnBrandName) {
		this.returnBrandName = returnBrandName;
	}

	public String getReturnBarcode() {
		return returnBarcode;
	}

	public void setReturnBarcode(String returnBarcode) {
		this.returnBarcode = returnBarcode;
	}

	public String getReturnSizeNo() {
		return returnSizeNo;
	}

	public void setReturnSizeNo(String returnSizeNo) {
		this.returnSizeNo = returnSizeNo;
	}

	public String getReturnItemCode() {
		return returnItemCode;
	}

	public void setReturnItemCode(String returnItemCode) {
		this.returnItemCode = returnItemCode;
	}

	public String getCancelTime() {
		return cancelTime;
	}

	public void setCancelTime(String cancelTime) {
		this.cancelTime = cancelTime;
	}


	public String getDamageOfCargoName() {
		return damageOfCargoName;
	}

	public void setDamageOfCargoName(String damageOfCargoName) {
		this.damageOfCargoName = damageOfCargoName;
	}
}
