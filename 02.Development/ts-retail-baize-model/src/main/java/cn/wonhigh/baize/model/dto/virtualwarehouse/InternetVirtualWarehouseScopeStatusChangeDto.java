package cn.wonhigh.baize.model.dto.virtualwarehouse;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


public class InternetVirtualWarehouseScopeStatusChangeDto implements Serializable {

    /**
     * 1 启用 0 禁用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    @NotBlank(message = "聚合仓编码不能为空")
    private String vstoreCode;

    @NotEmpty(message = "详细信息不能为空")
    @Valid
    private List<Dtl> dtlData;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getVstoreCode() {
        return vstoreCode;
    }

    public void setVstoreCode(String vstoreCode) {
        this.vstoreCode = vstoreCode;
    }


    public List<Dtl> getDtlData() {
        return dtlData;
    }

    public void setDtlData(List<Dtl> dtlData) {
        this.dtlData = dtlData;
    }

    public static class Dtl {

        @NotBlank(message = "ID不能为空")
        private String id;

        @NotNull
        private Integer storeType;

        @NotNull
        private Integer moreStoreFlag;
        @NotBlank
        private String storeNo;
        @NotBlank
        private String orderUnitNo;

        @NotNull
        private Integer inventoryType;

        public Integer getStoreType() {
            return storeType;
        }

        public void setStoreType(Integer storeType) {
            this.storeType = storeType;
        }

        public Integer getMoreStoreFlag() {
            return moreStoreFlag;
        }

        public void setMoreStoreFlag(Integer moreStoreFlag) {
            this.moreStoreFlag = moreStoreFlag;
        }

        public String getStoreNo() {
            return storeNo;
        }

        public void setStoreNo(String storeNo) {
            this.storeNo = storeNo;
        }

        public String getOrderUnitNo() {
            return orderUnitNo;
        }

        public void setOrderUnitNo(String orderUnitNo) {
            this.orderUnitNo = orderUnitNo;
        }

        public Integer getInventoryType() {
            return inventoryType;
        }

        public void setInventoryType(Integer inventoryType) {
            this.inventoryType = inventoryType;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }
}
