package cn.wonhigh.baize.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Objects;

public class WarehouseRatioDetailDto {
    private String shopCode;
    private String shopName;
    private String platformAmplifyRatio;
    @JsonIgnore
    private Integer platformAmplifyRatioNum;
    private String omsAmplifyRatio;
    @JsonIgnore
    private Integer omsAmplifyRatioNum;
    private String shopAmplifyRatio;
    @JsonIgnore
    private Integer shopAmplifyRatioNum;
    @JsonIgnore
    private String brandName;

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getShopAmplifyRatio() {
        if(Objects.isNull(shopAmplifyRatioNum)){
            return "";
        }
        return shopAmplifyRatioNum.toString().concat("%");
    }

    public Integer getShopAmplifyRatioNum() {
        return shopAmplifyRatioNum;
    }

    public void setShopAmplifyRatioNum(Integer shopAmplifyRatioNum) {
        this.shopAmplifyRatioNum = shopAmplifyRatioNum;
    }

    public String getShopCode() {
        return shopCode;
    }

    public void setShopCode(String shopCode) {
        this.shopCode = shopCode;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getPlatformAmplifyRatio() {
        if(Objects.isNull(platformAmplifyRatioNum)){
            return "";
        }
        return platformAmplifyRatioNum.toString().concat("%");
    }


    public Integer getPlatformAmplifyRatioNum() {
        return platformAmplifyRatioNum;
    }

    public void setPlatformAmplifyRatioNum(Integer platformAmplifyRatioNum) {
        this.platformAmplifyRatioNum = platformAmplifyRatioNum;
    }

    public String getOmsAmplifyRatio() {
        if(Objects.isNull(omsAmplifyRatioNum)){
            return "";
        }
        return omsAmplifyRatioNum.toString().concat("%");
    }


    public Integer getOmsAmplifyRatioNum() {
        return omsAmplifyRatioNum;
    }

    public void setOmsAmplifyRatioNum(Integer omsAmplifyRatioNum) {
        this.omsAmplifyRatioNum = omsAmplifyRatioNum;
    }
}
