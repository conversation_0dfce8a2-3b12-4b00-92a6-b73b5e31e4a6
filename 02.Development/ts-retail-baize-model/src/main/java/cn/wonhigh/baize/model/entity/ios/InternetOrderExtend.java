/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 网销订单表头扩展表
**/
public class InternetOrderExtend  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778668L;
    
    /**
    *礼品卡优惠金额
    **/ 
    @Label("礼品卡优惠金额") 
    private BigDecimal couponPrefAmount5;
    

    public BigDecimal getCouponPrefAmount5(){
        return  couponPrefAmount5;
    }
    public void setCouponPrefAmount5(BigDecimal val ){
        couponPrefAmount5 = val;
    }
    /**
    *订单应付金额
    **/ 
    @Label("订单应付金额") 
    private BigDecimal payableMoney;
    

    public BigDecimal getPayableMoney(){
        return  payableMoney;
    }
    public void setPayableMoney(BigDecimal val ){
        payableMoney = val;
    }
    /**
    *第三方会员账号
    **/ 
    @Label("第三方会员账号") 
    private String thirdPartyMember;
    

    public String getThirdPartyMember(){
        return  thirdPartyMember;
    }
    public void setThirdPartyMember(String val ){
        thirdPartyMember = val;
    }
    /**
    *小程序销售门店
    **/ 
    @Label("小程序销售门店") 
    private String saleStore;
    

    public String getSaleStore(){
        return  saleStore;
    }
    public void setSaleStore(String val ){
        saleStore = val;
    }
    /**
    *销售店铺名称
    **/ 
    @Label("销售店铺名称") 
    private String saleStoreName;
    

    public String getSaleStoreName(){
        return  saleStoreName;
    }
    public void setSaleStoreName(String val ){
        saleStoreName = val;
    }
    /**
    *快递单号
    **/ 
    @Label("快递单号") 
    private String expressCode;
    

    public String getExpressCode(){
        return  expressCode;
    }
    public void setExpressCode(String val ){
        expressCode = val;
    }
    /**
    *网店订单号(分销)
    **/ 
    @Label("网店订单号") 
    private String chainOrderNo;
    

    public String getChainOrderNo(){
        return  chainOrderNo;
    }
    public void setChainOrderNo(String val ){
        chainOrderNo = val;
    }
    /**
    *赠品(发货)要求
    **/ 
    @Label("赠品") 
    private String deliveryDemand;
    

    public String getDeliveryDemand(){
        return  deliveryDemand;
    }
    public void setDeliveryDemand(String val ){
        deliveryDemand = val;
    }
    /**
    *快递产品类型 1：顺丰次日 2：顺丰隔日 3：顺丰次晨 4：顺丰即日
    **/ 
    @Label("快递产品类型") 
    private Integer expressProType;
    

    public Integer getExpressProType(){
        return  expressProType;
    }
    public void setExpressProType(Integer val ){
        expressProType = val;
    }
    /**
    *取件码
    **/ 
    @Label("取件码") 
    private String fetchCode;
    

    public String getFetchCode(){
        return  fetchCode;
    }
    public void setFetchCode(String val ){
        fetchCode = val;
    }
    /**
    *分库字段:本部+序列
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *仓库地址编码
    **/ 
    @Label("仓库地址编码") 
    private String jitWarehouse;
    

    public String getJitWarehouse(){
        return  jitWarehouse;
    }
    public void setJitWarehouse(String val ){
        jitWarehouse = val;
    }
    /**
    *唯品会收货仓库名称
    **/ 
    @Label("唯品会收货仓库名称") 
    private String jitWarehouseName;
    

    public String getJitWarehouseName(){
        return  jitWarehouseName;
    }
    public void setJitWarehouseName(String val ){
        jitWarehouseName = val;
    }
    /**
    *优购仓编码
    **/ 
    @Label("优购仓编码") 
    private String warehouseCode;
    

    public String getWarehouseCode(){
        return  warehouseCode;
    }
    public void setWarehouseCode(String val ){
        warehouseCode = val;
    }
    /**
    *是否可发,1普通发货 2预占库存 3预占更新 4预占发货
    **/ 
    @Label("是否可发") 
    private Integer optType;
    

    public Integer getOptType(){
        return  optType;
    }
    public void setOptType(Integer val ){
        optType = val;
    }
    /**
    *换货申请ID
    **/ 
    @Label("换货申请") 
    private String exchangeOrderId;
    

    public String getExchangeOrderId(){
        return  exchangeOrderId;
    }
    public void setExchangeOrderId(String val ){
        exchangeOrderId = val;
    }
    /**
    *AD卖方组织代码
    **/ 
    @Label("卖方组织代码") 
    private String salesOrganization;
    

    public String getSalesOrganization(){
        return  salesOrganization;
    }
    public void setSalesOrganization(String val ){
        salesOrganization = val;
    }
    /**
    *脱敏收货人
    **/ 
    @Label("脱敏收货人") 
    private String extraConsigneeName;
    

    public String getExtraConsigneeName(){
        return  extraConsigneeName;
    }
    public void setExtraConsigneeName(String val ){
        extraConsigneeName = val;
    }
    /**
    *脱敏联系电话
    **/ 
    @Label("脱敏联系电话") 
    private String extraMobilePhone;
    

    public String getExtraMobilePhone(){
        return  extraMobilePhone;
    }
    public void setExtraMobilePhone(String val ){
        extraMobilePhone = val;
    }
    /**
    *脱敏收货地址
    **/ 
    @Label("脱敏收货地址") 
    private String extraConsigneeAddress;
    

    public String getExtraConsigneeAddress(){
        return  extraConsigneeAddress;
    }
    public void setExtraConsigneeAddress(String val ){
        extraConsigneeAddress = val;
    }
    /**
    *处理人
    **/ 
    @Label("处理人") 
    private String handle;
    

    public String getHandle(){
        return  handle;
    }
    public void setHandle(String val ){
        handle = val;
    }
    /**
    *唯品会档期
    **/ 
    @Label("唯品会档期") 
    private String theaterSchedule;
    

    public String getTheaterSchedule(){
        return  theaterSchedule;
    }
    public void setTheaterSchedule(String val ){
        theaterSchedule = val;
    }
    /**
    *唯品会入库单号
    **/ 
    @Label("唯品会入库单号") 
    private String deliveryId;
    

    public String getDeliveryId(){
        return  deliveryId;
    }
    public void setDeliveryId(String val ){
        deliveryId = val;
    }
    /**
    *付款方式 4：到付(到付现金) 2：寄付（寄付月结）
    **/ 
    @Label("付款方式") 
    private Integer payMethod;
    

    public Integer getPayMethod(){
        return  payMethod;
    }
    public void setPayMethod(Integer val ){
        payMethod = val;
    }
    /**
    *消费积分
    **/ 
    @Label("消费积分") 
    private Integer costScore;
    

    public Integer getCostScore(){
        return  costScore;
    }
    public void setCostScore(Integer val ){
        costScore = val;
    }
    /**
    *活动赠送积分
    **/ 
    @Label("活动赠送积分") 
    private Integer proScore;
    

    public Integer getProScore(){
        return  proScore;
    }
    public void setProScore(Integer val ){
        proScore = val;
    }
    /**
    *顾客成交价总金额
    **/ 
    @Label("顾客成交价总金额") 
    private BigDecimal customerSettlePriceAmount;
    

    public BigDecimal getCustomerSettlePriceAmount(){
        return  customerSettlePriceAmount;
    }
    public void setCustomerSettlePriceAmount(BigDecimal val ){
        customerSettlePriceAmount = val;
    }
    /**
    *VIP优惠金额
    **/ 
    @Label("优惠金额") 
    private BigDecimal prefAmountOfVip;
    

    public BigDecimal getPrefAmountOfVip(){
        return  prefAmountOfVip;
    }
    public void setPrefAmountOfVip(BigDecimal val ){
        prefAmountOfVip = val;
    }
    /**
    *商品总金额 现价总金额
    **/ 
    @Label("商品总金额") 
    private BigDecimal prodTotalAmount;
    

    public BigDecimal getProdTotalAmount(){
        return  prodTotalAmount;
    }
    public void setProdTotalAmount(BigDecimal val ){
        prodTotalAmount = val;
    }
    /**
    *牌价总金额
    **/ 
    @Label("牌价总金额") 
    private BigDecimal prodTotalTagAmount;
    

    public BigDecimal getProdTotalTagAmount(){
        return  prodTotalTagAmount;
    }
    public void setProdTotalTagAmount(BigDecimal val ){
        prodTotalTagAmount = val;
    }
    /**
    *活动优惠总金额
    **/ 
    @Label("活动优惠总金额") 
    private BigDecimal totalPrefAmountOfPro;
    

    public BigDecimal getTotalPrefAmountOfPro(){
        return  totalPrefAmountOfPro;
    }
    public void setTotalPrefAmountOfPro(BigDecimal val ){
        totalPrefAmountOfPro = val;
    }
    /**
    *订单运费险
    **/ 
    @Label("订单运费险") 
    private BigDecimal orderPostageInsurance;
    

    public BigDecimal getOrderPostageInsurance(){
        return  orderPostageInsurance;
    }
    public void setOrderPostageInsurance(BigDecimal val ){
        orderPostageInsurance = val;
    }
    /**
    *订单商品均摊总额
    **/ 
    @Label("订单商品均摊总额") 
    private BigDecimal goodAvgAmount;
    

    public BigDecimal getGoodAvgAmount(){
        return  goodAvgAmount;
    }
    public void setGoodAvgAmount(BigDecimal val ){
        goodAvgAmount = val;
    }
    /**
    *订单商品总额
    **/ 
    @Label("订单商品总额") 
    private BigDecimal goodAmount;
    

    public BigDecimal getGoodAmount(){
        return  goodAmount;
    }
    public void setGoodAmount(BigDecimal val ){
        goodAmount = val;
    }
    /**
    *订单重量
    **/ 
    @Label("订单重量") 
    private BigDecimal orderWeight;
    

    public BigDecimal getOrderWeight(){
        return  orderWeight;
    }
    public void setOrderWeight(BigDecimal val ){
        orderWeight = val;
    }
    /**
    *有没有发票 0 没有 1有
    **/ 
    @Label("有没有发票") 
    private Integer hasInvoice;
    

    public Integer getHasInvoice(){
        return  hasInvoice;
    }
    public void setHasInvoice(Integer val ){
        hasInvoice = val;
    }
    /**
    *基本积分
    **/ 
    @Label("基本积分") 
    private Integer baseScore;
    

    public Integer getBaseScore(){
        return  baseScore;
    }
    public void setBaseScore(Integer val ){
        baseScore = val;
    }
    /**
    *大宗原始订单号
    **/ 
    @Label("大宗原始订单号") 
    private String originalOrderCode;
    

    public String getOriginalOrderCode(){
        return  originalOrderCode;
    }
    public void setOriginalOrderCode(String val ){
        originalOrderCode = val;
    }
    /**
    *优惠券优惠金额
    **/ 
    @Label("优惠券优惠金额") 
    private BigDecimal couponPrefAmount;
    

    public BigDecimal getCouponPrefAmount(){
        return  couponPrefAmount;
    }
    public void setCouponPrefAmount(BigDecimal val ){
        couponPrefAmount = val;
    }
    /**
    *支付方式(中文)
    **/ 
    @Label("支付方式") 
    private String paymentName;
    

    public String getPaymentName(){
        return  paymentName;
    }
    public void setPaymentName(String val ){
        paymentName = val;
    }
    /**
    *总优惠金额
    **/ 
    @Label("总优惠金额") 
    private BigDecimal discountAmount;
    

    public BigDecimal getDiscountAmount(){
        return  discountAmount;
    }
    public void setDiscountAmount(BigDecimal val ){
        discountAmount = val;
    }
    /**
    *支付方式
    **/ 
    @Label("支付方式") 
    private String payment;
    

    public String getPayment(){
        return  payment;
    }
    public void setPayment(String val ){
        payment = val;
    }
    /**
    *同步优惠:0-未同步，1-已同步
    **/ 
    @Label("同步优惠") 
    private Integer syncDiscount;
    

    public Integer getSyncDiscount(){
        return  syncDiscount;
    }
    public void setSyncDiscount(Integer val ){
        syncDiscount = val;
    }
    /**
    *是否包邮 0 - 是 1- 否。默认值为 1
    **/ 
    @Label("是否包邮") 
    private Integer isPostageFree;
    

    public Integer getIsPostageFree(){
        return  isPostageFree;
    }
    public void setIsPostageFree(Integer val ){
        isPostageFree = val;
    }
    /**
    *店铺留言
    **/ 
    @Label("店铺留言") 
    private String sellerMessage;
    

    public String getSellerMessage(){
        return  sellerMessage;
    }
    public void setSellerMessage(String val ){
        sellerMessage = val;
    }
    /**
    *买家留言
    **/ 
    @Label("买家留言") 
    private String buyerMessage;
    

    public String getBuyerMessage(){
        return  buyerMessage;
    }
    public void setBuyerMessage(String val ){
        buyerMessage = val;
    }
    /**
    *业绩所属店铺（初次注册店铺）
    **/ 
    @Label("业绩所属店铺") 
    private String buyerStoreid;
    

    public String getBuyerStoreid(){
        return  buyerStoreid;
    }
    public void setBuyerStoreid(String val ){
        buyerStoreid = val;
    }
    /**
    *平台交易号
    **/ 
    @Label("平台交易号") 
    private String dealCodeList;
    

    public String getDealCodeList(){
        return  dealCodeList;
    }
    public void setDealCodeList(String val ){
        dealCodeList = val;
    }
    /**
    *仓库编码
    **/ 
    @Label("仓库编码") 
    private String storeCode;
    

    public String getStoreCode(){
        return  storeCode;
    }
    public void setStoreCode(String val ){
        storeCode = val;
    }
    /**
    *收货人姓名
    **/ 
    @Label("收货人姓名") 
    private String consigneeName;
    

    public String getConsigneeName(){
        return  consigneeName;
    }
    public void setConsigneeName(String val ){
        consigneeName = val;
    }
    /**
    *捡货单号
    **/ 
    @Label("捡货单号") 
    private String packageNo;
    

    public String getPackageNo(){
        return  packageNo;
    }
    public void setPackageNo(String val ){
        packageNo = val;
    }
    /**
    *售后电话
    **/ 
    @Label("售后电话") 
    private String returnConstactPhone;
    

    public String getReturnConstactPhone(){
        return  returnConstactPhone;
    }
    public void setReturnConstactPhone(String val ){
        returnConstactPhone = val;
    }
    /**
    *退货地址
    **/ 
    @Label("退货地址") 
    private String returnAddress;
    

    public String getReturnAddress(){
        return  returnAddress;
    }
    public void setReturnAddress(String val ){
        returnAddress = val;
    }
    /**
    *退货收货人邮编
    **/ 
    @Label("退货收货人邮编") 
    private String returnZipcode;
    

    public String getReturnZipcode(){
        return  returnZipcode;
    }
    public void setReturnZipcode(String val ){
        returnZipcode = val;
    }
    /**
    *员工内码
    **/ 
    @Label("员工内码") 
    private String employeeCode;
    

    public String getEmployeeCode(){
        return  employeeCode;
    }
    public void setEmployeeCode(String val ){
        employeeCode = val;
    }
    /**
    *店员姓名
    **/ 
    @Label("店员姓名") 
    private String clerkName;
    

    public String getClerkName(){
        return  clerkName;
    }
    public void setClerkName(String val ){
        clerkName = val;
    }
    /**
    *店员工号
    **/ 
    @Label("店员工号") 
    private String clerkNumber;
    

    public String getClerkNumber(){
        return  clerkNumber;
    }
    public void setClerkNumber(String val ){
        clerkNumber = val;
    }
    /**
    *会员手机号
    **/ 
    @Label("会员手机号") 
    private String mobileNumber;
    

    public String getMobileNumber(){
        return  mobileNumber;
    }
    public void setMobileNumber(String val ){
        mobileNumber = val;
    }
    /**
    *会员ID
    **/ 
    @Label("会员") 
    private String memberId;
    

    public String getMemberId(){
        return  memberId;
    }
    public void setMemberId(String val ){
        memberId = val;
    }
    /**
    *购买人上上级会员号
    **/ 
    @Label("购买人上上级会员号") 
    private String cardNo2;
    

    public String getCardNo2(){
        return  cardNo2;
    }
    public void setCardNo2(String val ){
        cardNo2 = val;
    }
    /**
    *购买人上级会员号
    **/ 
    @Label("购买人上级会员号") 
    private String shopCardNo;
    

    public String getShopCardNo(){
        return  shopCardNo;
    }
    public void setShopCardNo(String val ){
        shopCardNo = val;
    }
    /**
    *收货人手机号码
    **/ 
    @Label("收货人手机号码") 
    private String mobilePhone;
    

    public String getMobilePhone(){
        return  mobilePhone;
    }
    public void setMobilePhone(String val ){
        mobilePhone = val;
    }
    /**
    *会员号
    **/ 
    @Label("会员号") 
    private String memberName;
    

    public String getMemberName(){
        return  memberName;
    }
    public void setMemberName(String val ){
        memberName = val;
    }
    /**
    *收货人地址
    **/ 
    @Label("收货人地址") 
    private String consigneeAddress;
    

    public String getConsigneeAddress(){
        return  consigneeAddress;
    }
    public void setConsigneeAddress(String val ){
        consigneeAddress = val;
    }
    /**
    *收货人邮箱
    **/ 
    @Label("收货人邮箱") 
    private String email;
    

    public String getEmail(){
        return  email;
    }
    public void setEmail(String val ){
        email = val;
    }
    /**
    *购买人
    **/ 
    @Label("购买人") 
    private String buyerName;
    

    public String getBuyerName(){
        return  buyerName;
    }
    public void setBuyerName(String val ){
        buyerName = val;
    }
    /**
    *收货人电话
    **/ 
    @Label("收货人电话") 
    private String constactPhone;
    

    public String getConstactPhone(){
        return  constactPhone;
    }
    public void setConstactPhone(String val ){
        constactPhone = val;
    }
    /**
    *物流公司名称
    **/ 
    @Label("物流公司名称") 
    private String logisticsName;
    

    public String getLogisticsName(){
        return  logisticsName;
    }
    public void setLogisticsName(String val ){
        logisticsName = val;
    }
    /**
    *快递公司编号
    **/ 
    @Label("快递公司编号") 
    private String logisticsCode;
    

    public String getLogisticsCode(){
        return  logisticsCode;
    }
    public void setLogisticsCode(String val ){
        logisticsCode = val;
    }
    /**
    *退货收货人姓名
    **/ 
    @Label("退货收货人姓名") 
    private String returnConsigneeName;
    

    public String getReturnConsigneeName(){
        return  returnConsigneeName;
    }
    public void setReturnConsigneeName(String val ){
        returnConsigneeName = val;
    }
    /**
    *子订单号
    **/ 
    @Label("子订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    /**
    *城市编码
    **/ 
    @Label("城市编码") 
    private String city;
    

    public String getCity(){
        return  city;
    }
    public void setCity(String val ){
        city = val;
    }
    /**
    *城市名
    **/ 
    @Label("城市名") 
    private String cityName;
    

    public String getCityName(){
        return  cityName;
    }
    public void setCityName(String val ){
        cityName = val;
    }
    /**
    *地区
    **/ 
    @Label("地区") 
    private String area;
    

    public String getArea(){
        return  area;
    }
    public void setArea(String val ){
        area = val;
    }
    /**
    *地区名
    **/ 
    @Label("地区名") 
    private String areaName;
    

    public String getAreaName(){
        return  areaName;
    }
    public void setAreaName(String val ){
        areaName = val;
    }
    /**
    *省
    **/ 
    @Label("省") 
    private String province;
    

    public String getProvince(){
        return  province;
    }
    public void setProvince(String val ){
        province = val;
    }
    /**
    *省(中文)
    **/ 
    @Label("省") 
    private String provinceName;
    

    public String getProvinceName(){
        return  provinceName;
    }
    public void setProvinceName(String val ){
        provinceName = val;
    }
    /**
    *收货人邮编
    **/ 
    @Label("收货人邮编") 
    private String zipcode;
    

    public String getZipcode(){
        return  zipcode;
    }
    public void setZipcode(String val ){
        zipcode = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetOrderExtendBuilder build(){
        return new InternetOrderExtendBuilder(this);
    }

    public static class InternetOrderExtendBuilder extends AbstractEntryBuilder<InternetOrderExtend>{

        private InternetOrderExtendBuilder(InternetOrderExtend entry){
            this.obj = entry;
        }

       @Override
		public InternetOrderExtend object() {
			return this.obj;
		}

        
        public InternetOrderExtendBuilder couponPrefAmount5(BigDecimal value ){
            
            this.obj.couponPrefAmount5 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("couponPrefAmount5", value);
            return this;
        }
        
        public InternetOrderExtendBuilder payableMoney(BigDecimal value ){
            
            this.obj.payableMoney = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("payableMoney", value);
            return this;
        }
        
        public InternetOrderExtendBuilder thirdPartyMember(String value ){
            
            this.obj.thirdPartyMember = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("thirdPartyMember", value);
            return this;
        }
        
        public InternetOrderExtendBuilder saleStore(String value ){
            
            this.obj.saleStore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleStore", value);
            return this;
        }
        
        public InternetOrderExtendBuilder saleStoreName(String value ){
            
            this.obj.saleStoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleStoreName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder expressCode(String value ){
            
            this.obj.expressCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressCode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder chainOrderNo(String value ){
            
            this.obj.chainOrderNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("chainOrderNo", value);
            return this;
        }
        
        public InternetOrderExtendBuilder deliveryDemand(String value ){
            
            this.obj.deliveryDemand = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("deliveryDemand", value);
            return this;
        }
        
        public InternetOrderExtendBuilder expressProType(Integer value ){
            
            this.obj.expressProType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressProType", value);
            return this;
        }
        
        public InternetOrderExtendBuilder fetchCode(String value ){
            
            this.obj.fetchCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("fetchCode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public InternetOrderExtendBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public InternetOrderExtendBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetOrderExtendBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetOrderExtendBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetOrderExtendBuilder jitWarehouse(String value ){
            
            this.obj.jitWarehouse = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("jitWarehouse", value);
            return this;
        }
        
        public InternetOrderExtendBuilder jitWarehouseName(String value ){
            
            this.obj.jitWarehouseName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("jitWarehouseName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder warehouseCode(String value ){
            
            this.obj.warehouseCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("warehouseCode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder optType(Integer value ){
            
            this.obj.optType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("optType", value);
            return this;
        }
        
        public InternetOrderExtendBuilder exchangeOrderId(String value ){
            
            this.obj.exchangeOrderId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("exchangeOrderId", value);
            return this;
        }
        
        public InternetOrderExtendBuilder salesOrganization(String value ){
            
            this.obj.salesOrganization = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("salesOrganization", value);
            return this;
        }
        
        public InternetOrderExtendBuilder extraConsigneeName(String value ){
            
            this.obj.extraConsigneeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("extraConsigneeName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder extraMobilePhone(String value ){
            
            this.obj.extraMobilePhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("extraMobilePhone", value);
            return this;
        }
        
        public InternetOrderExtendBuilder extraConsigneeAddress(String value ){
            
            this.obj.extraConsigneeAddress = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("extraConsigneeAddress", value);
            return this;
        }
        
        public InternetOrderExtendBuilder handle(String value ){
            
            this.obj.handle = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("handle", value);
            return this;
        }
        
        public InternetOrderExtendBuilder theaterSchedule(String value ){
            
            this.obj.theaterSchedule = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("theaterSchedule", value);
            return this;
        }
        
        public InternetOrderExtendBuilder deliveryId(String value ){
            
            this.obj.deliveryId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("deliveryId", value);
            return this;
        }
        
        public InternetOrderExtendBuilder payMethod(Integer value ){
            
            this.obj.payMethod = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("payMethod", value);
            return this;
        }
        
        public InternetOrderExtendBuilder costScore(Integer value ){
            
            this.obj.costScore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("costScore", value);
            return this;
        }
        
        public InternetOrderExtendBuilder proScore(Integer value ){
            
            this.obj.proScore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("proScore", value);
            return this;
        }
        
        public InternetOrderExtendBuilder customerSettlePriceAmount(BigDecimal value ){
            
            this.obj.customerSettlePriceAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customerSettlePriceAmount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder prefAmountOfVip(BigDecimal value ){
            
            this.obj.prefAmountOfVip = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prefAmountOfVip", value);
            return this;
        }
        
        public InternetOrderExtendBuilder prodTotalAmount(BigDecimal value ){
            
            this.obj.prodTotalAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodTotalAmount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder prodTotalTagAmount(BigDecimal value ){
            
            this.obj.prodTotalTagAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodTotalTagAmount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder totalPrefAmountOfPro(BigDecimal value ){
            
            this.obj.totalPrefAmountOfPro = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("totalPrefAmountOfPro", value);
            return this;
        }
        
        public InternetOrderExtendBuilder orderPostageInsurance(BigDecimal value ){
            
            this.obj.orderPostageInsurance = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderPostageInsurance", value);
            return this;
        }
        
        public InternetOrderExtendBuilder goodAvgAmount(BigDecimal value ){
            
            this.obj.goodAvgAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("goodAvgAmount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder goodAmount(BigDecimal value ){
            
            this.obj.goodAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("goodAmount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder orderWeight(BigDecimal value ){
            
            this.obj.orderWeight = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderWeight", value);
            return this;
        }
        
        public InternetOrderExtendBuilder hasInvoice(Integer value ){
            
            this.obj.hasInvoice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("hasInvoice", value);
            return this;
        }
        
        public InternetOrderExtendBuilder baseScore(Integer value ){
            
            this.obj.baseScore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("baseScore", value);
            return this;
        }
        
        public InternetOrderExtendBuilder originalOrderCode(String value ){
            
            this.obj.originalOrderCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originalOrderCode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder couponPrefAmount(BigDecimal value ){
            
            this.obj.couponPrefAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("couponPrefAmount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder paymentName(String value ){
            
            this.obj.paymentName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("paymentName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder discountAmount(BigDecimal value ){
            
            this.obj.discountAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("discountAmount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder payment(String value ){
            
            this.obj.payment = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("payment", value);
            return this;
        }
        
        public InternetOrderExtendBuilder syncDiscount(Integer value ){
            
            this.obj.syncDiscount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("syncDiscount", value);
            return this;
        }
        
        public InternetOrderExtendBuilder isPostageFree(Integer value ){
            
            this.obj.isPostageFree = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isPostageFree", value);
            return this;
        }
        
        public InternetOrderExtendBuilder sellerMessage(String value ){
            
            this.obj.sellerMessage = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sellerMessage", value);
            return this;
        }
        
        public InternetOrderExtendBuilder buyerMessage(String value ){
            
            this.obj.buyerMessage = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("buyerMessage", value);
            return this;
        }
        
        public InternetOrderExtendBuilder buyerStoreid(String value ){
            
            this.obj.buyerStoreid = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("buyerStoreid", value);
            return this;
        }
        
        public InternetOrderExtendBuilder dealCodeList(String value ){
            
            this.obj.dealCodeList = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("dealCodeList", value);
            return this;
        }
        
        public InternetOrderExtendBuilder storeCode(String value ){
            
            this.obj.storeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeCode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder consigneeName(String value ){
            
            this.obj.consigneeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("consigneeName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder packageNo(String value ){
            
            this.obj.packageNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("packageNo", value);
            return this;
        }
        
        public InternetOrderExtendBuilder returnConstactPhone(String value ){
            
            this.obj.returnConstactPhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnConstactPhone", value);
            return this;
        }
        
        public InternetOrderExtendBuilder returnAddress(String value ){
            
            this.obj.returnAddress = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnAddress", value);
            return this;
        }
        
        public InternetOrderExtendBuilder returnZipcode(String value ){
            
            this.obj.returnZipcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnZipcode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder employeeCode(String value ){
            
            this.obj.employeeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("employeeCode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder clerkName(String value ){
            
            this.obj.clerkName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("clerkName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder clerkNumber(String value ){
            
            this.obj.clerkNumber = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("clerkNumber", value);
            return this;
        }
        
        public InternetOrderExtendBuilder mobileNumber(String value ){
            
            this.obj.mobileNumber = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("mobileNumber", value);
            return this;
        }
        
        public InternetOrderExtendBuilder memberId(String value ){
            
            this.obj.memberId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("memberId", value);
            return this;
        }
        
        public InternetOrderExtendBuilder cardNo2(String value ){
            
            this.obj.cardNo2 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cardNo2", value);
            return this;
        }
        
        public InternetOrderExtendBuilder shopCardNo(String value ){
            
            this.obj.shopCardNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopCardNo", value);
            return this;
        }
        
        public InternetOrderExtendBuilder mobilePhone(String value ){
            
            this.obj.mobilePhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("mobilePhone", value);
            return this;
        }
        
        public InternetOrderExtendBuilder memberName(String value ){
            
            this.obj.memberName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("memberName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder consigneeAddress(String value ){
            
            this.obj.consigneeAddress = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("consigneeAddress", value);
            return this;
        }
        
        public InternetOrderExtendBuilder email(String value ){
            
            this.obj.email = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("email", value);
            return this;
        }
        
        public InternetOrderExtendBuilder buyerName(String value ){
            
            this.obj.buyerName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("buyerName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder constactPhone(String value ){
            
            this.obj.constactPhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("constactPhone", value);
            return this;
        }
        
        public InternetOrderExtendBuilder logisticsName(String value ){
            
            this.obj.logisticsName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder logisticsCode(String value ){
            
            this.obj.logisticsCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsCode", value);
            return this;
        }
        
        public InternetOrderExtendBuilder returnConsigneeName(String value ){
            
            this.obj.returnConsigneeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnConsigneeName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetOrderExtendBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
        public InternetOrderExtendBuilder city(String value ){
            
            this.obj.city = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("city", value);
            return this;
        }
        
        public InternetOrderExtendBuilder cityName(String value ){
            
            this.obj.cityName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cityName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder area(String value ){
            
            this.obj.area = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("area", value);
            return this;
        }
        
        public InternetOrderExtendBuilder areaName(String value ){
            
            this.obj.areaName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("areaName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder province(String value ){
            
            this.obj.province = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("province", value);
            return this;
        }
        
        public InternetOrderExtendBuilder provinceName(String value ){
            
            this.obj.provinceName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("provinceName", value);
            return this;
        }
        
        public InternetOrderExtendBuilder zipcode(String value ){
            
            this.obj.zipcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("zipcode", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }