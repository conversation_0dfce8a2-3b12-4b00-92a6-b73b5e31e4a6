/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 
**/
public class OrderUnitPriorityConfig  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1722824985353L;
    
    //虚拟仓名称
    @Label("虚拟仓名称") 
    private String vstoreName;
    
    //货管
    @Label("货管") 
    private String orderUnitNo;
    
    //优先级
    @Label(value = "优先级", defaultVal = "0") 
    private Integer orderUnitLevel;
    
    //货管名称
    @Label("货管名称") 
    private String orderUnitName;
    
    //虚拟仓编码
    @Label("虚拟仓编码") 
    private String vstoreCode;
    
    //备注
    @Label("备注") 
    private String remark;
    
    //状态:0关闭1开启
    @Label(value = "状态", defaultVal = "0") 
    private Integer status;

    private Integer rowNum;
    
    
    public String getVstoreName(){
        return  vstoreName;
    }
    public void setVstoreName(String val ){
        vstoreName = val;
    }
    
    public String getOrderUnitNo(){
        return  orderUnitNo;
    }
    public void setOrderUnitNo(String val ){
        orderUnitNo = val;
    }
    
    public Integer getOrderUnitLevel(){
        return  orderUnitLevel;
    }
    public void setOrderUnitLevel(Integer val ){
        orderUnitLevel = val;
    }
    
    public String getOrderUnitName(){
        return  orderUnitName;
    }
    public void setOrderUnitName(String val ){
        orderUnitName = val;
    }
    
    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }

    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public OrderUnitPriorityConfigBuilder build(){
        return new OrderUnitPriorityConfigBuilder(this);
    }

    public static class OrderUnitPriorityConfigBuilder extends AbstractEntryBuilder<OrderUnitPriorityConfig>{

        private OrderUnitPriorityConfigBuilder(OrderUnitPriorityConfig entry){
            this.obj = entry;
        }

       @Override
		public OrderUnitPriorityConfig object() {
			return this.obj;
		}

        
        public OrderUnitPriorityConfigBuilder vstoreName(String value ){
            this.obj.vstoreName = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreName", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder orderUnitNo(String value ){
            this.obj.orderUnitNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder orderUnitLevel(Integer value ){
            this.obj.orderUnitLevel = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitLevel", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder orderUnitName(String value ){
            this.obj.orderUnitName = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitName", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder vstoreCode(String value ){
            this.obj.vstoreCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public OrderUnitPriorityConfigBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}