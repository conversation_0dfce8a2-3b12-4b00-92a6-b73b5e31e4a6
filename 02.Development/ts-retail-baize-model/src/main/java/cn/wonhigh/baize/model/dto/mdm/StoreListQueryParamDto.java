package cn.wonhigh.baize.model.dto.mdm;

import cn.hutool.core.net.url.UrlQuery;

import java.io.Serializable;
import java.nio.charset.Charset;

/**
 * 店铺列表查询参数
 */
public class StoreListQueryParamDto implements Serializable {



    private String accessToken;
    private Integer sourceLevel;
    private String storeNo;
    private String updateStartTime;
    private String updateEndTime;
    private Integer page =1;
    private Integer pageSize = 20;
    private String validateSourceNo;


    @Override
    public String toString() {
        return new UrlQuery()
                .add("access_token", accessToken)
                .add("sourceLevel", sourceLevel)
                .add("storeNo", storeNo)
                .add("updateStartTime", updateStartTime)
                .add("updateEndTime", updateEndTime)
                .add("page", page)
                .add("pageSize", pageSize)
                .add("validateSourceNo", validateSourceNo)
                .build(Charset.defaultCharset());
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Integer getSourceLevel() {
        return sourceLevel;
    }


    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setSourceLevel(Integer sourceLevel) {
        this.sourceLevel = sourceLevel;
    }

    public String getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(String storeNo) {
        this.storeNo = storeNo;
    }

    public String getUpdateEndTime() {
        return updateEndTime;
    }

    public void setUpdateEndTime(String updateEndTime) {
        this.updateEndTime = updateEndTime;
    }

    public String getUpdateStartTime() {
        return updateStartTime;
    }

    public void setUpdateStartTime(String updateStartTime) {
        this.updateStartTime = updateStartTime;
    }

    public String getValidateSourceNo() {
        return validateSourceNo;
    }

    public void setValidateSourceNo(String validateSourceNo) {
        this.validateSourceNo = validateSourceNo;
    }
}
