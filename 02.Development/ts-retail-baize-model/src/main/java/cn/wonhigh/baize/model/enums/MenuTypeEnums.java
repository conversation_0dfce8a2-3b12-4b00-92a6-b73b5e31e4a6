package cn.wonhigh.baize.model.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2024/10/21 14:21
 */
public enum MenuTypeEnums {

    VWS(1040,"聚合仓货管范围"),
    OSTC(1041,"渠道店铺"),
    VWPP(1042,"聚合仓省属优先级"),
    OUPC(1043,"货管优先级"),
    ACTIVELOCK(1044,"活动锁库"),
    ACTIVELOCKDTL(1045,"活动锁库明细"),
    BRANDINVENTORYRANG(1046,"库存同步商品设置-品季年"),
    ITSI(1047,"自提门店"),
    SDC(1048,"虚店配货规则"),
    ISC(1049,"库存同步配置"),
    SHOP_SETTING(1050,"小程序店铺库存模式配置表"),
    ACTIVELOCKADJUST(1051,"活动锁库调整")
    ;


    private int type;//编码
    private String desc;//菜单

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    MenuTypeEnums(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getTypeDesc(int type) {
        MenuTypeEnums[] typeArr = MenuTypeEnums.values();
        for (MenuTypeEnums bte : typeArr) {
            if (bte.getType() == type) {
                return bte.getDesc();
            }
        }
        return String.valueOf(type);
    }
}
