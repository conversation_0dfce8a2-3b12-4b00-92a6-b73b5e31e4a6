package cn.wonhigh.baize.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 二级来源枚举
 * @create 2024/8/5 10:26
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SecondPlatformEnum {

    DY("抖音虚店", "DY"),
    TSXCXXD("小程序虚店", "TSXCXXD"),
    TSAPPXD("小程序APP虚店", "TSAPPXD"),
    XCX("小程序总部虚店", "XCX")
    ;


    private final String name;

    private final String type;


    SecondPlatformEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }


    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getName(String type){
        SecondPlatformEnum[] enums = SecondPlatformEnum.values();
        for (SecondPlatformEnum anEnum : enums) {
            if(anEnum.type.equals(type)){
                return anEnum.getName();
            }
        }
        return type;
    }
}
