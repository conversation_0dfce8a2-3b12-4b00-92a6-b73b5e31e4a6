/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;
import java.util.Objects;


/** auto generate start ,don't modify */

/**
* 虚拟仓表
**/
public class InternetVirtualWarehouseInfo  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1693878759250L;
    
    /**
    *库存接口所传的对应字段
    **/ 
    @Label("库存接口所传的对应字段") 
    private String qtyStr;
    

    public String getQtyStr(){
        return  qtyStr;
    }
    public void setQtyStr(String val ){
        qtyStr = val;
    }
    /**
    *虚拟仓名称
    **/ 
    @Label("虚拟仓名称") 
    private String vstoreName;
    

    public String getVstoreName(){
        return  vstoreName;
    }
    public void setVstoreName(String val ){
        vstoreName = val;
    }
    /**
    *创建人
    **/ 
    @Label("创建人") 
    private String createName;
    

    public String getCreateName(){
        return  createName;
    }
    public void setCreateName(String val ){
        createName = val;
    }
    /**
    *接口平台
    **/ 
    @Label("接口平台") 
    private String interfacePlatform;
    

    public String getInterfacePlatform(){
        return  interfacePlatform;
    }
    public void setInterfacePlatform(String val ){
        interfacePlatform = val;
    }
    /**
    *是否同步库存到中台 0 否 1 是
    **/ 
    @Label("是否同步库存到中台") 
    private Integer isSync;
    

    public Integer getIsSync(){
        return  isSync;
    }
    public void setIsSync(Integer val ){
        isSync = val;
    }
    /**
    *虚拟仓编码
    **/ 
    @Label("虚拟仓编码") 
    private String vstoreCode;
    

    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }
    /**
    *所属虚仓名称
    **/ 
    @Label("所属虚仓名称") 
    private String parentVstoreName;
    

    public String getParentVstoreName(){
        return  this.vstoreType != null && this.vstoreType == 2 ? parentVstoreName : "-";
    }
    public void setParentVstoreName(String val ){
        parentVstoreName = val;
    }
    /**
    *虚拟仓类型; 1 总仓、2 子仓
    **/ 
    @Label("虚拟仓类型") 
    private Integer vstoreType;
    

    public Integer getVstoreType(){
        return  vstoreType;
    }
    public void setVstoreType(Integer val ){
        vstoreType = val;
    }
    /**
    *业务类型 1-网销(对应要排除线上不派单店) 0-特殊(对应排除线下不派单店)2-跨店
    **/ 
    @Label("业务类型") 
    private Integer businessType;
    

    public Integer getBusinessType(){
        return  businessType;
    }
    public void setBusinessType(Integer val ){
        businessType = val;
    }
    /**
    *子仓序号 可选值1-5 同总仓内不可重复
    **/ 
    @Label("子仓序号") 
    private Integer subIndex;
    

    public Integer getSubIndex(){
        return  subIndex;
    }
    public void setSubIndex(Integer val ){
        subIndex = val;
    }
    /**
    *所属虚仓编码
    **/ 
    @Label("所属虚仓编码") 
    private String parentVstoreCode;
    

    public String getParentVstoreCode(){
        return  parentVstoreCode;
    }
    public void setParentVstoreCode(String val ){
        parentVstoreCode = val;
    }
    /**
    *所属本部
    **/ 
    @Label("所属本部") 
    private String organTypeNo;
    

    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    /**
    *虚仓类型，区分线上，电商，0线下，1电商
    **/ 
    @Label("虚仓类型") 
    private Integer onlineType;
    

    public Integer getOnlineType(){
        return  onlineType;
    }
    public void setOnlineType(Integer val ){
        onlineType = val;
    }
    /**
    *是否计算库存 0 否 1 是
    **/ 
    @Label("是否计算库存") 
    private Integer isCalc;
    

    public Integer getIsCalc(){
        return  isCalc;
    }
    public void setIsCalc(Integer val ){
        isCalc = val;
    }
    /**
    *最后修改人
    **/ 
    @Label("最后修改人") 
    private String updateName;
    

    public String getUpdateName(){
        return  updateName;
    }
    public void setUpdateName(String val ){
        updateName = val;
    }

    /**
     * 聚合仓类型：0 非标准 , 1 标准
     */
    private Integer vstoreMold;

    public Integer getVstoreMold() {
        return vstoreMold;
    }

    public void setVstoreMold(Integer vstoreMold) {
        this.vstoreMold = vstoreMold;
    }

    /**
     * 是否是标准仓
     * @return
     */
    public boolean standardVStore(){
        return Objects.equals(vstoreMold,1);
    }


    /**
     * 是否是冗余仓
     * @return
     */
    public boolean ryVStore(){
        // 标准+子仓  就是冗余仓
        if (!standardVStore()) {
            return false;
        }
        if(Objects.equals(vstoreType,2)){
            return true;
        }
        return false;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetVirtualWarehouseInfoBuilder build(){
        return new InternetVirtualWarehouseInfoBuilder(this);
    }

    public static class InternetVirtualWarehouseInfoBuilder extends AbstractEntryBuilder<InternetVirtualWarehouseInfo>{

        private InternetVirtualWarehouseInfoBuilder(InternetVirtualWarehouseInfo entry){
            this.obj = entry;
        }

       @Override
		public InternetVirtualWarehouseInfo object() {
			return this.obj;
		}

        
        public InternetVirtualWarehouseInfoBuilder qtyStr(String value ){
            
            this.obj.qtyStr = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("qtyStr", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder vstoreName(String value ){
            
            this.obj.vstoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreName", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder createName(String value ){
            
            this.obj.createName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("createName", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder interfacePlatform(String value ){
            
            this.obj.interfacePlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("interfacePlatform", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder isSync(Integer value ){
            
            this.obj.isSync = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isSync", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder vstoreCode(String value ){
            
            this.obj.vstoreCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder parentVstoreName(String value ){
            
            this.obj.parentVstoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parentVstoreName", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder vstoreType(Integer value ){
            
            this.obj.vstoreType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("vstoreType", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder businessType(Integer value ){
            
            this.obj.businessType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("businessType", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder subIndex(Integer value ){
            
            this.obj.subIndex = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("subIndex", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder parentVstoreCode(String value ){
            
            this.obj.parentVstoreCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parentVstoreCode", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder organTypeNo(String value ){
            
            this.obj.organTypeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder onlineType(Integer value ){
            
            this.obj.onlineType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("onlineType", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder isCalc(Integer value ){
            
            this.obj.isCalc = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isCalc", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetVirtualWarehouseInfoBuilder updateName(String value ){
            
            this.obj.updateName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("updateName", value);
            return this;
        }

        public InternetVirtualWarehouseInfoBuilder vstoreMold(Integer value ){

            this.obj.vstoreMold = value;

            if( query == null  )
                query = new Query();
            this.query.where("vstoreMold", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }