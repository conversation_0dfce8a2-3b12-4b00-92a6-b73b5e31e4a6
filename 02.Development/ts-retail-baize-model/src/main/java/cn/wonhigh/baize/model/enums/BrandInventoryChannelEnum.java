package cn.wonhigh.baize.model.enums;

import org.apache.commons.lang.StringUtils;

public enum BrandInventoryChannelEnum {
    OMS_CHANNEL(1, "OMS渠道", "OMS"),
    TIANHONG_CHANNEL(2, "天虹渠道", "TIA<PERSON>HONG"),
    ADSFS_CHANNEL(3, "ADSFS渠道", "ADSFS"),
    XCX_CHANNEL(4, "小程序渠道", "XCX"),
    NKCI_CHANNEL(5, "小程序渠道", "NKCI");
	
    private Integer channel;
    private String desc;
    private String interfacePlatform;

    private BrandInventoryChannelEnum(Integer channel, String desc, String interfacePlatform) {
        this.channel = channel;
        this.desc = desc;
        this.interfacePlatform = interfacePlatform;
    }

	public Integer getChannel() {
		return channel;
	}

	public void setChannel(Integer channel) {
		this.channel = channel;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getInterfacePlatform() {
		return interfacePlatform;
	}

	public void setInterfacePlatform(String interfacePlatform) {
		this.interfacePlatform = interfacePlatform;
	}

	public static int getChannelByInterfacePlatform(String interfacePlatform) {
		if (StringUtils.isNotBlank(interfacePlatform)) {
			for (BrandInventoryChannelEnum channelEnum : values()) {
				if (interfacePlatform.equals(channelEnum.getInterfacePlatform())) {
					return channelEnum.getChannel();
				}
			}
		}
		return BrandInventoryChannelEnum.OMS_CHANNEL.getChannel();
	}
}
