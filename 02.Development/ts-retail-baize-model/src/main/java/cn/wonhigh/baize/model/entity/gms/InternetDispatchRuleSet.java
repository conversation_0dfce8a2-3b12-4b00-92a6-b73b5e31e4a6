/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.IEntryBuildable;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;
import java.util.List;


/** auto generate start ,don't modify */

/**
* 派单规则配置表
**/
public class InternetDispatchRuleSet  extends cn.mercury.domain.BaseEntity<Long>
{

    private static final long serialVersionUID = 1685599549189L;

    /**
    *仓优先派单策略状态，0关闭，1开户
    **/ 
    @Label("仓优先派单策略状态") 
    private Integer storeState;
    

    public Integer getStoreState(){
        return  storeState;
    }
    public void setStoreState(Integer val ){
        storeState = val;
    }
    /**
    *独占仓派单策略状态，0关闭，1开户
    **/ 
    @Label("独占仓派单策略状态") 
    private Integer nosharedState;
    

    public Integer getNosharedState(){
        return  nosharedState;
    }
    public void setNosharedState(Integer val ){
        nosharedState = val;
    }
    /**
    *规则名称
    **/ 
    @Label("规则名称") 
    private String ruleName;
    

    public String getRuleName(){
        return  ruleName;
    }
    public void setRuleName(String val ){
        ruleName = val;
    }
    /**
    *规则编码
    **/ 
    @Label("规则编码") 
    private String ruleNo;
    

    public String getRuleNo(){
        return  ruleNo;
    }
    public void setRuleNo(String val ){
        ruleNo = val;
    }
    /**
    *关闭时间
    **/ 
    @Label("关闭时间") 
    private Date endTime;
    

    public Date getEndTime(){
        return  endTime;
    }
    public void setEndTime(Date val ){
        endTime = val;
    }
    /**
    *开始时间
    **/ 
    @Label("开始时间") 
    private Date beginTime;
    

    public Date getBeginTime(){
        return  beginTime;
    }
    public void setBeginTime(Date val ){
        beginTime = val;
    }
    /**
    *类型 1-线上 2-线下
    **/ 
    @Label("类型") 
    private Integer type;
    

    public Integer getType(){
        return  type;
    }
    public void setType(Integer val ){
        type = val;
    }
    /**
    *开关状态，0：关，1：开
    **/ 
    @Label("开关状态") 
    private Integer state;
    

    public Integer getState(){
        return  state;
    }
    public void setState(Integer val ){
        state = val;
    }
    /**
    *派单规则类型，1：均衡
    **/ 
    @Label("派单规则类型") 
    private Integer ruletype;
    

    public Integer getRuletype(){
        return  ruletype;
    }
    public void setRuletype(Integer val ){
        ruletype = val;
    }
    /**
    *派单上限策略状态，0关闭，1开户
    **/ 
    @Label("派单上限策略状态") 
    private Integer dispatchNumState;
    

    public Integer getDispatchNumState(){
        return  dispatchNumState;
    }
    public void setDispatchNumState(Integer val ){
        dispatchNumState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer dispatchNumPriority;
    

    public Integer getDispatchNumPriority(){
        return  dispatchNumPriority;
    }
    public void setDispatchNumPriority(Integer val ){
        dispatchNumPriority = val;
    }
    /**
    *同结算/跨结算策略状态，0关闭，1开户
    **/ 
    @Label("同结算") 
    private Integer companyState;
    

    public Integer getCompanyState(){
        return  companyState;
    }
    public void setCompanyState(Integer val ){
        companyState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer companyPriority;
    

    public Integer getCompanyPriority(){
        return  companyPriority;
    }
    public void setCompanyPriority(Integer val ){
        companyPriority = val;
    }
    /**
    *非A类/A类策略状态，0关闭，1开户
    **/ 
    @Label("非") 
    private Integer shopTypeState;
    

    public Integer getShopTypeState(){
        return  shopTypeState;
    }
    public void setShopTypeState(Integer val ){
        shopTypeState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer shopTypePriority;
    

    public Integer getShopTypePriority(){
        return  shopTypePriority;
    }
    public void setShopTypePriority(Integer val ){
        shopTypePriority = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer nosharedPriority;
    

    public Integer getNosharedPriority(){
        return  nosharedPriority;
    }
    public void setNosharedPriority(Integer val ){
        nosharedPriority = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer bagPriority;
    

    public Integer getBagPriority(){
        return  bagPriority;
    }
    public void setBagPriority(Integer val ){
        bagPriority = val;
    }
    /**
    *最少包裹派单策略状态，0关闭，1开户
    **/ 
    @Label("最少包裹派单策略状态") 
    private Integer bagState;
    

    public Integer getBagState(){
        return  bagState;
    }
    public void setBagState(Integer val ){
        bagState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer shopPriority;
    

    public Integer getShopPriority(){
        return  shopPriority;
    }
    public void setShopPriority(Integer val ){
        shopPriority = val;
    }
    /**
    *店优先派单策略状态，0关闭，1开户
    **/ 
    @Label("店优先派单策略状态") 
    private Integer shopState;
    

    public Integer getShopState(){
        return  shopState;
    }
    public void setShopState(Integer val ){
        shopState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer storePriority;
    

    public Integer getStorePriority(){
        return  storePriority;
    }
    public void setStorePriority(Integer val ){
        storePriority = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer inventoryPriority;
    

    public Integer getInventoryPriority(){
        return  inventoryPriority;
    }
    public void setInventoryPriority(Integer val ){
        inventoryPriority = val;
    }
    /**
    *最大库存派单策略状态，0关闭，1开户
    **/ 
    @Label("最大库存派单策略状态") 
    private Integer inventoryState;
    

    public Integer getInventoryState(){
        return  inventoryState;
    }
    public void setInventoryState(Integer val ){
        inventoryState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer actionPriority;
    

    public Integer getActionPriority(){
        return  actionPriority;
    }
    public void setActionPriority(Integer val ){
        actionPriority = val;
    }
    /**
    *动销派单策略状态，0关闭，1开户
    **/ 
    @Label("动销派单策略状态") 
    private Integer actionState;
    

    public Integer getActionState(){
        return  actionState;
    }
    public void setActionState(Integer val ){
        actionState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer nearPriority;
    

    public Integer getNearPriority(){
        return  nearPriority;
    }
    public void setNearPriority(Integer val ){
        nearPriority = val;
    }
    /**
    *就近派单策略状态，0关闭，1开户
    **/ 
    @Label("就近派单策略状态") 
    private Integer nearState;
    

    public Integer getNearState(){
        return  nearState;
    }
    public void setNearState(Integer val ){
        nearState = val;
    }
    /**
    *策略优先级，99为最优先，数字真大越优先
    **/ 
    @Label("策略优先级") 
    private Integer avgPriority;
    

    public Integer getAvgPriority(){
        return  avgPriority;
    }
    public void setAvgPriority(Integer val ){
        avgPriority = val;
    }
    /**
    *均匀派单策略状态，0关闭，1开户
    **/ 
    @Label("均匀派单策略状态") 
    private Integer avgState;
    

    public Integer getAvgState(){
        return  avgState;
    }
    public void setAvgState(Integer val ){
        avgState = val;
    }
    /**
    *品牌名称(支持多个品牌，逗号分隔）
    **/ 
    @Label("品牌名称") 
    private String brandName;
    

    public String getBrandName(){
        return  brandName;
    }
    public void setBrandName(String val ){
        brandName = val;
    }
    /**
    *品牌编码,(支持多个品牌，逗号分隔）
    **/ 
    @Label("品牌编码") 
    private String brandNo;
    

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    /**
    *platform平台名称(-&#62;业务模式)
    **/ 
    @Label("平台名称") 
    private String platformName;
    

    public String getPlatformName(){
        return  platformName;
    }
    public void setPlatformName(String val ){
        platformName = val;
    }
    /**
    *platform平台编码(-&#62;业务模式)
    **/ 
    @Label("平台编码") 
    private String platformNo;
    

    public String getPlatformNo(){
        return  platformNo;
    }
    public void setPlatformNo(String val ){
        platformNo = val;
    }
    /**
    *本部名称
    **/ 
    @Label("本部名称") 
    private String organTypeName;
    

    public String getOrganTypeName(){
        return  organTypeName;
    }
    public void setOrganTypeName(String val ){
        organTypeName = val;
    }
    /**
    *本部编码
    **/ 
    @Label("本部编码") 
    private String organTypeNo;
    

    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }

    @Label("货管策略状态")
    private Integer orderUnitState;

    @Label("货管策略优先级")
    private Integer orderUnitPriority;

    @Label("店铺信用分策略优先级")
    private Integer shopCreditPriority;

    @Label("店铺信用分策略状态")
    private Integer shopCreditState;


    public Integer getShopCreditPriority() {
        return shopCreditPriority;
    }

    public void setShopCreditPriority(Integer shopCreditPriority) {
        this.shopCreditPriority = shopCreditPriority;
    }

    public Integer getShopCreditState() {
        return shopCreditState;
    }

    public void setShopCreditState(Integer shopCreditState) {
        this.shopCreditState = shopCreditState;
    }

    public Integer getOrderUnitState() {
        return orderUnitState;
    }

    public void setOrderUnitState(Integer orderUnitState) {
        this.orderUnitState = orderUnitState;
    }

    public Integer getOrderUnitPriority() {
        return orderUnitPriority;
    }

    public void setOrderUnitPriority(Integer orderUnitPriority) {
        this.orderUnitPriority = orderUnitPriority;
    }

    private List<InternetDispatchRuleSetDtl> dtlList;

    public List<InternetDispatchRuleSetDtl> getDtlList() {
        return dtlList;
    }

    public void setDtlList(List<InternetDispatchRuleSetDtl> dtlList) {
        this.dtlList = dtlList;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetDispatchRuleSetBuilder build(){
        return new InternetDispatchRuleSetBuilder(this);
    }

    public static class InternetDispatchRuleSetBuilder extends AbstractEntryBuilder<InternetDispatchRuleSet>{

        private InternetDispatchRuleSetBuilder(InternetDispatchRuleSet entry){
            this.obj = entry;
        }

       @Override
		public InternetDispatchRuleSet object() {
			return this.obj;
		}

        
        public InternetDispatchRuleSetBuilder storeState(Integer value ){
            
            this.obj.storeState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder nosharedState(Integer value ){
            
            this.obj.nosharedState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("nosharedState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder ruleName(String value ){
            
            this.obj.ruleName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("ruleName", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder ruleNo(String value ){
            
            this.obj.ruleNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("ruleNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder endTime(Date value ){
            
            this.obj.endTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("endTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder beginTime(Date value ){
            
            this.obj.beginTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("beginTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder type(Integer value ){
            
            this.obj.type = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("type", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder state(Integer value ){
            
            this.obj.state = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("state", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder ruletype(Integer value ){
            
            this.obj.ruletype = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("ruletype", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder dispatchNumState(Integer value ){
            
            this.obj.dispatchNumState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("dispatchNumState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder dispatchNumPriority(Integer value ){
            
            this.obj.dispatchNumPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("dispatchNumPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder companyState(Integer value ){
            
            this.obj.companyState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder companyPriority(Integer value ){
            
            this.obj.companyPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder shopTypeState(Integer value ){
            
            this.obj.shopTypeState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopTypeState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder shopTypePriority(Integer value ){
            
            this.obj.shopTypePriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopTypePriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder nosharedPriority(Integer value ){
            
            this.obj.nosharedPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("nosharedPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder bagPriority(Integer value ){
            
            this.obj.bagPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("bagPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder bagState(Integer value ){
            
            this.obj.bagState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("bagState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder shopPriority(Integer value ){
            
            this.obj.shopPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder shopState(Integer value ){
            
            this.obj.shopState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder storePriority(Integer value ){
            
            this.obj.storePriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storePriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder inventoryPriority(Integer value ){
            
            this.obj.inventoryPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("inventoryPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder inventoryState(Integer value ){
            
            this.obj.inventoryState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("inventoryState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder actionPriority(Integer value ){
            
            this.obj.actionPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("actionPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder actionState(Integer value ){
            
            this.obj.actionState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("actionState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder nearPriority(Integer value ){
            
            this.obj.nearPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("nearPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder nearState(Integer value ){
            
            this.obj.nearState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("nearState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder avgPriority(Integer value ){
            
            this.obj.avgPriority = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("avgPriority", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder avgState(Integer value ){
            
            this.obj.avgState = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("avgState", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder brandName(String value ){
            
            this.obj.brandName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandName", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder brandNo(String value ){
            
            this.obj.brandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder platformName(String value ){
            
            this.obj.platformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("platformName", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder platformNo(String value ){
            
            this.obj.platformNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("platformNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder organTypeName(String value ){
            
            this.obj.organTypeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeName", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder id(Long value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetDispatchRuleSetBuilder organTypeNo(String value ){
            
            this.obj.organTypeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }


        public InternetDispatchRuleSetBuilder orderUnitState(Integer value ){

            this.obj.orderUnitState = value;

            if( query == null  )
                query = new Query();
            this.query.where("orderUnitState", value);
            return this;
        }


        public InternetDispatchRuleSetBuilder orderUnitPriority(Integer value ){

            this.obj.orderUnitPriority = value;

            if( query == null  )
                query = new Query();
            this.query.where("orderUnitPriority", value);
            return this;
        }

        public InternetDispatchRuleSetBuilder shopCreditPriority(Integer value ){

            this.obj.shopCreditPriority = value;

            if( query == null  )
                query = new Query();
            this.query.where("shopCreditPriority", value);
            return this;
        }

        public InternetDispatchRuleSetBuilder shopCreditState(Integer value ){

            this.obj.shopCreditState = value;

            if( query == null  )
                query = new Query();
            this.query.where("shopCreditState", value);
            return this;
        }

    }
     /** auto generate end,don't modify */
    }