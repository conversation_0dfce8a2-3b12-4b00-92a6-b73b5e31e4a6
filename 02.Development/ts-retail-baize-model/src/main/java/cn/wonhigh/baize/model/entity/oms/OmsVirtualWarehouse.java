package cn.wonhigh.baize.model.entity.oms;

import java.io.Serializable;

import cn.mercury.domain.BaseEntity;

import java.util.Date;

import cn.mercury.annotation.Label;

import cn.mercury.basic.query.Query;
import cn.mercury.domain.AbstractEntryBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * (OmsVirtualWarehouse)实体类
 *
 * <AUTHOR>
 * @since 2025-01-08 14:40:55
 */
public class OmsVirtualWarehouse extends BaseEntity<Long> implements Serializable {
    private static final long serialVersionUID = -31229176922316645L;
    /**
     * 虚拟仓id
     */
    @Label("虚拟仓id")
    private Long virtualWarehouseId;
    /**
     * 更新时间
     */
    @Label("更新时间")
    private Date modifiedTime;
    /**
     * 创建时间
     */
    @Label("创建时间")
    private Date createdTime;
    /**
     * 是否启用
     */
    @Label("是否启用")
    private Integer isEnable;
    /**
     * 虚拟仓编码
     */
    @Label("虚拟仓编码")
    private String virtualWarehouseCode;
    /**
     * 虚拟仓名称
     */
    @Label("虚拟仓名称")
    private String virtualWarehouseName;
    /**
     * 虚拟仓类型
     */
    @Label("虚拟仓类型")
    private Integer virtualWarehouseType;
    /**
     * 物理仓id
     */
    @Label("物理仓id")
    private Long warehouseId;
    /**
     * 仓库名称
     */
    @Label("仓库名称")
    private String warehouseName;


    public Long getVirtualWarehouseId() {
        return virtualWarehouseId;
    }

    public void setVirtualWarehouseId(Long virtualWarehouseId) {
        this.virtualWarehouseId = virtualWarehouseId;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public String getVirtualWarehouseCode() {
        return virtualWarehouseCode;
    }

    public void setVirtualWarehouseCode(String virtualWarehouseCode) {
        this.virtualWarehouseCode = virtualWarehouseCode;
    }

    public String getVirtualWarehouseName() {
        return virtualWarehouseName;
    }

    public void setVirtualWarehouseName(String virtualWarehouseName) {
        this.virtualWarehouseName = virtualWarehouseName;
    }

    public Integer getVirtualWarehouseType() {
        return virtualWarehouseType;
    }

    public void setVirtualWarehouseType(Integer virtualWarehouseType) {
        this.virtualWarehouseType = virtualWarehouseType;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static OmsVirtualWarehouseBuilder build() {
        return new OmsVirtualWarehouseBuilder(new OmsVirtualWarehouse());
    }

    public static OmsVirtualWarehouseBuilder build(OmsVirtualWarehouse value) {
        return new OmsVirtualWarehouseBuilder(value);
    }

    public static class OmsVirtualWarehouseBuilder extends AbstractEntryBuilder<OmsVirtualWarehouse> {

        private OmsVirtualWarehouseBuilder(OmsVirtualWarehouse entry) {
            this.obj = entry;
        }

        @Override
        public OmsVirtualWarehouse object() {
            return this.obj;
        }

        public OmsVirtualWarehouseBuilder virtualWarehouseId(Long value) {

            this.obj.setVirtualWarehouseId(value);

            if (query == null)
                query = new Query();
            this.query.where("virtualWarehouseId", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder modifiedTime(Date value) {

            this.obj.setModifiedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("modifiedTime", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder createdTime(Date value) {

            this.obj.setCreatedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("createdTime", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder isEnable(Integer value) {

            this.obj.setIsEnable(value);

            if (query == null)
                query = new Query();
            this.query.where("isEnable", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder virtualWarehouseCode(String value) {

            this.obj.setVirtualWarehouseCode(value);

            if (query == null)
                query = new Query();
            this.query.where("virtualWarehouseCode", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder virtualWarehouseName(String value) {

            this.obj.setVirtualWarehouseName(value);

            if (query == null)
                query = new Query();
            this.query.where("virtualWarehouseName", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder virtualWarehouseType(Integer value) {

            this.obj.setVirtualWarehouseType(value);

            if (query == null)
                query = new Query();
            this.query.where("virtualWarehouseType", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder warehouseId(Long value) {

            this.obj.setWarehouseId(value);

            if (query == null)
                query = new Query();
            this.query.where("warehouseId", value);
            return this;
        }

        public OmsVirtualWarehouseBuilder warehouseName(String value) {

            this.obj.setWarehouseName(value);

            if (query == null)
                query = new Query();
            this.query.where("warehouseName", value);
            return this;
        }
    }
}
