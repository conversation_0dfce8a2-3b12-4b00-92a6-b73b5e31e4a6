/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.IEntryBuildable;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 订货单位表
**/
public class OrderUnit  extends cn.mercury.domain.BaseEntity<Integer>  
{

    private static final long serialVersionUID = 1694396820461L;
    
    /**
    *本部编码
    **/ 
    @Label("本部编码") 
    private String organTypeNo;
    

    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    /**
    *1有效，0无效
    **/ 
    @Label("有效") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *备注
    **/ 
    @Label("备注") 
    private String remark;
    

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    /**
    *时间序列
    **/ 
    @Label("时间序列") 
    private Long timeSeq;
    

    public Long getTimeSeq(){
        return  timeSeq;
    }
    public void setTimeSeq(Long val ){
        timeSeq = val;
    }
    /**
    *检索码
    **/ 
    @Label("检索码") 
    private String searchCode;
    

    public String getSearchCode(){
        return  searchCode;
    }
    public void setSearchCode(String val ){
        searchCode = val;
    }
    /**
    *订货单位类型(0 品牌部;1 批发客户)
    **/ 
    @Label("订货单位类型") 
    private Integer type;
    

    public Integer getType(){
        return  type;
    }
    public void setType(Integer val ){
        type = val;
    }
    /**
    *结算公司编码
    **/ 
    @Label("结算公司编码") 
    private String companyNo;
    

    public String getCompanyNo(){
        return  companyNo;
    }
    public void setCompanyNo(String val ){
        companyNo = val;
    }
    /**
    *序列号
    **/ 
    @Label("序列号") 
    private Integer orderNo;
    

    public Integer getOrderNo(){
        return  orderNo;
    }
    public void setOrderNo(Integer val ){
        orderNo = val;
    }
    /**
    *是否MAP货管(0:否,1:是)
    **/ 
    @Label("是否") 
    private Integer mapFlag;
    

    public Integer getMapFlag(){
        return  mapFlag;
    }
    public void setMapFlag(Integer val ){
        mapFlag = val;
    }
    /**
    *货管品牌类型(0:单品 1:多品 2:FOSS)
    **/ 
    @Label("货管品牌类型") 
    private Integer cargoTubeBrandType;
    

    public Integer getCargoTubeBrandType(){
        return  cargoTubeBrandType;
    }
    public void setCargoTubeBrandType(Integer val ){
        cargoTubeBrandType = val;
    }
    /**
    *订货单位编号
    **/ 
    @Label("订货单位编号") 
    private String orderUnitNo;
    

    public String getOrderUnitNo(){
        return  orderUnitNo;
    }
    public void setOrderUnitNo(String val ){
        orderUnitNo = val;
    }
    /**
    *订货单位编码
    **/ 
    @Label("订货单位编码") 
    private String orderUnitCode;
    

    public String getOrderUnitCode(){
        return  orderUnitCode;
    }
    public void setOrderUnitCode(String val ){
        orderUnitCode = val;
    }
    /**
    *管理城市编号
    **/ 
    @Label("管理城市编号") 
    private String organNo;
    

    public String getOrganNo(){
        return  organNo;
    }
    public void setOrganNo(String val ){
        organNo = val;
    }
    /**
    *订货单位名称
    **/ 
    @Label("订货单位名称") 
    private String name;
    

    public String getName(){
        return  name;
    }
    public void setName(String val ){
        name = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public OrderUnitBuilder build(){
        return new OrderUnitBuilder(this);
    }

    public static class OrderUnitBuilder extends AbstractEntryBuilder<OrderUnit>{

        private OrderUnitBuilder(OrderUnit entry){
            this.obj = entry;
        }

       @Override
		public OrderUnit object() {
			return this.obj;
		}

        
        public OrderUnitBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public OrderUnitBuilder organTypeNo(String value ){
            
            this.obj.organTypeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
        public OrderUnitBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public OrderUnitBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public OrderUnitBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public OrderUnitBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public OrderUnitBuilder timeSeq(Long value ){
            
            this.obj.timeSeq = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("timeSeq", value);
            return this;
        }
        
        public OrderUnitBuilder searchCode(String value ){
            
            this.obj.searchCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("searchCode", value);
            return this;
        }
        
        public OrderUnitBuilder type(Integer value ){
            
            this.obj.type = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("type", value);
            return this;
        }
        
        public OrderUnitBuilder companyNo(String value ){
            
            this.obj.companyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyNo", value);
            return this;
        }
        
        public OrderUnitBuilder orderNo(Integer value ){
            
            this.obj.orderNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderNo", value);
            return this;
        }
        
        public OrderUnitBuilder mapFlag(Integer value ){
            
            this.obj.mapFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("mapFlag", value);
            return this;
        }
        
        public OrderUnitBuilder cargoTubeBrandType(Integer value ){
            
            this.obj.cargoTubeBrandType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cargoTubeBrandType", value);
            return this;
        }
        
        public OrderUnitBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public OrderUnitBuilder id(Integer value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public OrderUnitBuilder orderUnitNo(String value ){
            
            this.obj.orderUnitNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }
        
        public OrderUnitBuilder orderUnitCode(String value ){
            
            this.obj.orderUnitCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitCode", value);
            return this;
        }
        
        public OrderUnitBuilder organNo(String value ){
            
            this.obj.organNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organNo", value);
            return this;
        }
        
        public OrderUnitBuilder name(String value ){
            
            this.obj.name = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("name", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }