package cn.wonhigh.baize.model.dto.activelock;

import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;

import java.io.Serializable;
import java.util.ArrayList;

public class InventoryActiveLockDtlQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    private String billNo;

    /**
     * 是否包含预占数量
     */
    private Boolean isContainerOccupiedQty = false;

    private String channelNo;
    private ArrayList<Integer> syncStatus;


    public InventoryActiveLockDtlQuery() {
    }

    public static InventoryActiveLockDtlQueryBuilder builder() {
        return new InventoryActiveLockDtlQueryBuilder();
    }


    public static class InventoryActiveLockDtlQueryBuilder {
        private String billNo;
        private Boolean isContainerOccupiedQty = false;
        private String channelNo;
        private ArrayList<Integer> syncStatus;

        public InventoryActiveLockDtlQueryBuilder() {
        }

        public InventoryActiveLockDtlQueryBuilder billNo(String billNo) {
            this.billNo = billNo;
            return this;
        }

        public InventoryActiveLockDtlQueryBuilder isContainerOccupiedQty(Boolean isContainerOccupiedQty) {
            this.isContainerOccupiedQty = isContainerOccupiedQty;
            return this;
        }

        public InventoryActiveLockDtlQueryBuilder channelNo(String channelNo) {
            this.channelNo = channelNo;
            return this;
        }

        public InventoryActiveLockDtlQuery build() {
            InventoryActiveLockDtlQuery query = new InventoryActiveLockDtlQuery();
            query.setBillNo(billNo);
            query.setContainerOccupiedQty(isContainerOccupiedQty);
            query.setChannelNo(channelNo);
            query.setSyncStatus(syncStatus);
            return query;
        }

        public InventoryActiveLockDtlQueryBuilder syncStatus(ArrayList<Integer> integers) {
            this.syncStatus = integers;
            return this;
        }
    }

    private void setSyncStatus(ArrayList<Integer> syncStatus) {
        this.syncStatus = syncStatus;
    }

    public ArrayList<Integer> getSyncStatus() {
        return syncStatus;
    }


    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }


    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public Boolean getContainerOccupiedQty() {
        return isContainerOccupiedQty;
    }

    public void setContainerOccupiedQty(Boolean containerOccupiedQty) {
        isContainerOccupiedQty = containerOccupiedQty;
    }
}
