package cn.wonhigh.baize.model.entity.oms;

import cn.mercury.annotation.Label;
import cn.mercury.basic.query.Query;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;


/**
 * (OmsStockOccupancy)实体类
 *
 * <AUTHOR>
 * @since 2024-12-27 18:32:40
 */
public class OmsStockOccupancy extends BaseEntity<Long> implements Serializable {
    private static final long serialVersionUID = 785154540067663715L;
    /**
     * 占用id
     */
    @Label("占用id")
    private Long stockOccupancyId;
    /**
     * 更新时间
     */
    @Label("更新时间")
    private Date modifiedTime;
    /**
     * 创建时间
     */
    @Label("创建时间")
    private Date createdTime;
    /**
     * 排序时间
     */
    @Label("排序时间")
    private Date sortTime;
    /**
     * 数量
     */
    @Label("数量")
    private Integer quantity;
    /**
     * 占用类型
     */
    @Label("占用类型")
    private Integer stockOccupancyType;
    /**
     * 商品规格id
     */
    @Label("商品规格id")
    private Long skuId;
    /**
     * 商品规格编码
     */
    @Label("商品规格编码")
    private String skuCode;
    /**
     * 占用主表id
     */
    @Label("占用主表id")
    private Long mainId;
    /**
     * 占用明细id
     */
    @Label("占用明细id")
    private Long detailId;
    /**
     * 实体仓id
     */
    @Label("实体仓id")
    private Long warehouseId;
    /**
     * 仓库名称
     */
    @Label("仓库名称")
    private String warehouseName;
    /**
     * 虚拟仓id
     */
    @Label("虚拟仓id")
    private Long virtualWarehouseId;
    /**
     * 虚拟仓名称
     */
    @Label("虚拟仓名称")
    private String virtualWarehouseName;
    /**
     * 占用状态
     */
    @Label("占用状态")
    private Integer status;
    /**
     * 备注
     */
    @Label("备注")
    private String remark;


    public Long getStockOccupancyId() {
        return stockOccupancyId;
    }

    public void setStockOccupancyId(Long stockOccupancyId) {
        this.stockOccupancyId = stockOccupancyId;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getSortTime() {
        return sortTime;
    }

    public void setSortTime(Date sortTime) {
        this.sortTime = sortTime;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getStockOccupancyType() {
        return stockOccupancyType;
    }

    public void setStockOccupancyType(Integer stockOccupancyType) {
        this.stockOccupancyType = stockOccupancyType;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public Long getMainId() {
        return mainId;
    }

    public void setMainId(Long mainId) {
        this.mainId = mainId;
    }

    public Long getDetailId() {
        return detailId;
    }

    public void setDetailId(Long detailId) {
        this.detailId = detailId;
    }

    public Long getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Long warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Long getVirtualWarehouseId() {
        return virtualWarehouseId;
    }

    public void setVirtualWarehouseId(Long virtualWarehouseId) {
        this.virtualWarehouseId = virtualWarehouseId;
    }

    public String getVirtualWarehouseName() {
        return virtualWarehouseName;
    }

    public void setVirtualWarehouseName(String virtualWarehouseName) {
        this.virtualWarehouseName = virtualWarehouseName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static OmsStockOccupancyBuilder build() {
        return new OmsStockOccupancyBuilder(new OmsStockOccupancy());
    }

    public static OmsStockOccupancyBuilder build(OmsStockOccupancy value) {
        return new OmsStockOccupancyBuilder(value);
    }

    public static class OmsStockOccupancyBuilder extends AbstractEntryBuilder<OmsStockOccupancy> {

        private OmsStockOccupancyBuilder(OmsStockOccupancy entry) {
            this.obj = entry;
        }

        @Override
        public OmsStockOccupancy object() {
            return this.obj;
        }

        public OmsStockOccupancyBuilder stockOccupancyId(Long value) {

            this.obj.setStockOccupancyId(value);

            if (query == null)
                query = new Query();
            this.query.where("stockOccupancyId", value);
            return this;
        }

        public OmsStockOccupancyBuilder modifiedTime(Date value) {

            this.obj.setModifiedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("modifiedTime", value);
            return this;
        }

        public OmsStockOccupancyBuilder createdTime(Date value) {

            this.obj.setCreatedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("createdTime", value);
            return this;
        }

        public OmsStockOccupancyBuilder sortTime(Date value) {

            this.obj.setSortTime(value);

            if (query == null)
                query = new Query();
            this.query.where("sortTime", value);
            return this;
        }

        public OmsStockOccupancyBuilder quantity(Integer value) {

            this.obj.setQuantity(value);

            if (query == null)
                query = new Query();
            this.query.where("quantity", value);
            return this;
        }

        public OmsStockOccupancyBuilder stockOccupancyType(Integer value) {

            this.obj.setStockOccupancyType(value);

            if (query == null)
                query = new Query();
            this.query.where("stockOccupancyType", value);
            return this;
        }

        public OmsStockOccupancyBuilder skuId(Long value) {

            this.obj.setSkuId(value);

            if (query == null)
                query = new Query();
            this.query.where("skuId", value);
            return this;
        }

        public OmsStockOccupancyBuilder skuCode(String value) {

            this.obj.setSkuCode(value);

            if (query == null)
                query = new Query();
            this.query.where("skuCode", value);
            return this;
        }

        public OmsStockOccupancyBuilder mainId(Long value) {

            this.obj.setMainId(value);

            if (query == null)
                query = new Query();
            this.query.where("mainId", value);
            return this;
        }

        public OmsStockOccupancyBuilder detailId(Long value) {

            this.obj.setDetailId(value);

            if (query == null)
                query = new Query();
            this.query.where("detailId", value);
            return this;
        }

        public OmsStockOccupancyBuilder warehouseId(Long value) {

            this.obj.setWarehouseId(value);

            if (query == null)
                query = new Query();
            this.query.where("warehouseId", value);
            return this;
        }

        public OmsStockOccupancyBuilder warehouseName(String value) {

            this.obj.setWarehouseName(value);

            if (query == null)
                query = new Query();
            this.query.where("warehouseName", value);
            return this;
        }

        public OmsStockOccupancyBuilder virtualWarehouseId(Long value) {

            this.obj.setVirtualWarehouseId(value);

            if (query == null)
                query = new Query();
            this.query.where("virtualWarehouseId", value);
            return this;
        }

        public OmsStockOccupancyBuilder virtualWarehouseName(String value) {

            this.obj.setVirtualWarehouseName(value);

            if (query == null)
                query = new Query();
            this.query.where("virtualWarehouseName", value);
            return this;
        }

        public OmsStockOccupancyBuilder status(Integer value) {

            this.obj.setStatus(value);

            if (query == null)
                query = new Query();
            this.query.where("status", value);
            return this;
        }

        public OmsStockOccupancyBuilder remark(String value) {

            this.obj.setRemark(value);

            if (query == null)
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
    }
}
