package cn.wonhigh.baize.model.entity.oms;

import cn.mercury.annotation.Label;
import cn.mercury.domain.IEntry;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

public class OmsWarehouse implements IEntry {
    private static final long serialVersionUID = 1L;
    /**
     * 仓库id
     */
    @NotNull(message="[仓库id]不能为空")
    @Label("仓库id")
    private Long warehouseId;
    /**
     * 市id
     */
    @Label("市id")
    private Long cityId;
    /**
     * 国家id
     */
    @Label("国家id")
    private Long countryId;
    /**
     * 区id
     */
    @Label("区id")
    private Long districtId;
    /**
     * 省id
     */
    @Label("省id")
    private Long provinceId;
    /**
     * 市
     */
    @Size(max= 50,message="编码长度不能超过50")
    @Label("市")
    @Length(max= 50,message="编码长度不能超过50")
    private String cityName;
    /**
     * 区
     */
    @Size(max= 50,message="编码长度不能超过50")
    @Label("区")
    @Length(max= 50,message="编码长度不能超过50")
    private String districtName;
    /**
     * 省
     */
    @Size(max= 50,message="编码长度不能超过50")
    @Label("省")
    @Length(max= 50,message="编码长度不能超过50")
    private String provinceName;
    /**
     * 国家
     */
    @Size(max= 50,message="编码长度不能超过50")
    @Label("国家")
    @Length(max= 50,message="编码长度不能超过50")
    private String countryName;
    /**
     * 更新时间
     */
    @NotNull(message="[更新时间]不能为空")
    @Label("更新时间")
    private Date modifiedTime;
    /**
     * 创建时间
     */
    @NotNull(message="[创建时间]不能为空")
    @Label("创建时间")
    private Date createdTime;
    /**
     * 是否启用
     */
    @NotNull(message="[是否启用]不能为空")
    @Label("是否启用")
    private Integer isEnable;
    /**
     * 仓库代码
     */
    @NotBlank(message="[仓库代码]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @Label("仓库代码")
    @Length(max= 50,message="编码长度不能超过50")
    private String warehouseCode;
    /**
     * 仓库名称
     */
    @NotBlank(message="[仓库名称]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @Label("仓库名称")
    @Length(max= 50,message="编码长度不能超过50")
    private String warehouseName;
    /**
     * 仓库类型
     */
    @NotNull(message="[仓库类型]不能为空")
    @Label("仓库类型")
    private Integer warehouseType;
    /**
     * 品牌编码
     */
    @Size(max= 200,message="编码长度不能超过200")
    @Label("品牌编码")
    @Length(max= 200,message="编码长度不能超过200")
    private String brandCodes;
    /**
     * 品牌名称
     */
    @Size(max= 200,message="编码长度不能超过200")
    @Label("品牌名称")
    @Length(max= 200,message="编码长度不能超过200")
    private String brandNames;
    /**
     * 仓库应用id
     */
    @Label("仓库应用id")
    private Long wmsAppId;
    /**
     * 联系人
     */
    @Size(max= 1500,message="编码长度不能超过1500")
    @Label("联系人")
    @Length(max= 1500,message="编码长度不能超过1500")
    private String contact;
    /**
     * 地址
     */
    @Size(max= 1500,message="编码长度不能超过1500")
    @Label("地址")
    @Length(max= 1500,message="编码长度不能超过1500")
    private String address;
    /**
     * 手机
     */
    @Size(max= 500,message="编码长度不能超过500")
    @Label("手机")
    @Length(max= 500,message="编码长度不能超过500")
    private String mobile;
    /**
     * 电话
     */
    @Size(max= 500,message="编码长度不能超过500")
    @Label("电话")
    @Length(max= 500,message="编码长度不能超过500")
    private String telephone;
    /**
     * 货主编码
     */
    @Size(max= 25,message="编码长度不能超过25")
    @Label("货主编码")
    @Length(max= 25,message="编码长度不能超过25")
    private String ownerCode;
    /**
     * 外部编码
     */
    @Size(max= 25,message="编码长度不能超过25")
    @Label("外部编码")
    @Length(max= 25,message="编码长度不能超过25")
    private String outerCode;
    /**
     * 是否匹配区域
     */
    @NotNull(message="[是否匹配区域]不能为空")
    @Label("是否匹配区域")
    private Integer isMatchRegion;
    /**
     * 唯品平台仓库代码
     */
    @Size(max= 25,message="编码长度不能超过25")
    @Label("唯品平台仓库代码")
    @Length(max= 25,message="编码长度不能超过25")
    private String vipPlatformCode;
    /**
     * 是否允许配货
     */
    @NotNull(message="[是否允许配货]不能为空")
    @Label("是否允许配货")
    private Integer isStockAvailable;
    /**
     * 是否允许上传库存
     */
    @NotNull(message="[是否允许上传库存]不能为空")
    @Label("是否允许上传库存")
    private Integer isCanStockUpload;
    /**
     * 是否已关联门店组
     */
    @NotNull(message="[是否已关联门店组]不能为空")
    @Label("是否已关联门店组")
    private Integer isHasGroupConnected;
    /**
     * 公司id
     */
    @Label("公司id")
    private Long companyId;
    /**
     * 公司名称
     */
    @Size(max= 50,message="编码长度不能超过50")
    @Label("公司名称")
    @Length(max= 50,message="编码长度不能超过50")
    private String companyName;
    /**
     * 是否按快递成本替快递排序
     */
    @NotNull(message="[是否按快递成本替快递排序]不能为空")
    @Label("是否按快递成本替快递排序")
    private Integer isSortExpressByCost;
    /**
     * 是否截单
     */
    @NotNull(message="[是否截单]不能为空")
    @Label("是否截单")
    private Integer isOrderIntercepted;
    /**
     * 截单时间类型
     */
    @Label("截单时间类型")
    private Integer interceptTimeType;
    /**
     * 截单时间点
     */
    @Label("截单时间点")
    private Date interceptTime;
    /**
     * 截单开始时间
     */
    @Label("截单开始时间")
    private Date interceptBeginTime;
    /**
     * 截单结束时间
     */
    @Label("截单结束时间")
    private Date interceptEndTime;
    /**
     * 前置派送类型
     */
    @Label("前置派送类型")
    private Integer advanceDeliveryType;
    /**
     * 是否全渠道
     */
    @NotNull(message="[是否全渠道]不能为空")
    @Label("是否全渠道")
    private Integer isO2o;
    /**
     * 是否开启天猫翱象配置
     */
    @Label("是否开启天猫翱象配置")
    private Integer isEnableAoxiang;
    /**
     * 邮编
     */
    @Size(max= 10,message="编码长度不能超过10")
    @Label("邮编")
    @Length(max= 10,message="编码长度不能超过10")
    private String zipcode;
    /**
     * 是否按品牌拆单
     */
    @Label("是否按品牌拆单")
    private Integer isSplitByBrand;

    /**
     * 仓库id
     */
    public void setWarehouseId(Long warehouseId){
        this.warehouseId = warehouseId;
    }

    /**
     * 市id
     */
    public void setCityId(Long cityId){
        this.cityId = cityId;
    }

    /**
     * 国家id
     */
    public void setCountryId(Long countryId){
        this.countryId = countryId;
    }

    /**
     * 区id
     */
    public void setDistrictId(Long districtId){
        this.districtId = districtId;
    }

    /**
     * 省id
     */
    public void setProvinceId(Long provinceId){
        this.provinceId = provinceId;
    }

    /**
     * 市
     */
    public void setCityName(String cityName){
        this.cityName = cityName;
    }

    /**
     * 区
     */
    public void setDistrictName(String districtName){
        this.districtName = districtName;
    }

    /**
     * 省
     */
    public void setProvinceName(String provinceName){
        this.provinceName = provinceName;
    }

    /**
     * 国家
     */
    public void setCountryName(String countryName){
        this.countryName = countryName;
    }

    /**
     * 更新时间
     */
    public void setModifiedTime(Date modifiedTime){
        this.modifiedTime = modifiedTime;
    }

    /**
     * 创建时间
     */
    public void setCreatedTime(Date createdTime){
        this.createdTime = createdTime;
    }

    /**
     * 是否启用
     */
    public void setIsEnable(Integer isEnable){
        this.isEnable = isEnable;
    }

    /**
     * 仓库代码
     */
    public void setWarehouseCode(String warehouseCode){
        this.warehouseCode = warehouseCode;
    }

    /**
     * 仓库名称
     */
    public void setWarehouseName(String warehouseName){
        this.warehouseName = warehouseName;
    }

    /**
     * 仓库类型
     */
    public void setWarehouseType(Integer warehouseType){
        this.warehouseType = warehouseType;
    }

    /**
     * 品牌编码
     */
    public void setBrandCodes(String brandCodes){
        this.brandCodes = brandCodes;
    }

    /**
     * 品牌名称
     */
    public void setBrandNames(String brandNames){
        this.brandNames = brandNames;
    }

    /**
     * 仓库应用id
     */
    public void setWmsAppId(Long wmsAppId){
        this.wmsAppId = wmsAppId;
    }

    /**
     * 联系人
     */
    public void setContact(String contact){
        this.contact = contact;
    }

    /**
     * 地址
     */
    public void setAddress(String address){
        this.address = address;
    }

    /**
     * 手机
     */
    public void setMobile(String mobile){
        this.mobile = mobile;
    }

    /**
     * 电话
     */
    public void setTelephone(String telephone){
        this.telephone = telephone;
    }

    /**
     * 货主编码
     */
    public void setOwnerCode(String ownerCode){
        this.ownerCode = ownerCode;
    }

    /**
     * 外部编码
     */
    public void setOuterCode(String outerCode){
        this.outerCode = outerCode;
    }

    /**
     * 是否匹配区域
     */
    public void setIsMatchRegion(Integer isMatchRegion){
        this.isMatchRegion = isMatchRegion;
    }

    /**
     * 唯品平台仓库代码
     */
    public void setVipPlatformCode(String vipPlatformCode){
        this.vipPlatformCode = vipPlatformCode;
    }

    /**
     * 是否允许配货
     */
    public void setIsStockAvailable(Integer isStockAvailable){
        this.isStockAvailable = isStockAvailable;
    }

    /**
     * 是否允许上传库存
     */
    public void setIsCanStockUpload(Integer isCanStockUpload){
        this.isCanStockUpload = isCanStockUpload;
    }

    /**
     * 是否已关联门店组
     */
    public void setIsHasGroupConnected(Integer isHasGroupConnected){
        this.isHasGroupConnected = isHasGroupConnected;
    }

    /**
     * 公司id
     */
    public void setCompanyId(Long companyId){
        this.companyId = companyId;
    }

    /**
     * 公司名称
     */
    public void setCompanyName(String companyName){
        this.companyName = companyName;
    }

    /**
     * 是否按快递成本替快递排序
     */
    public void setIsSortExpressByCost(Integer isSortExpressByCost){
        this.isSortExpressByCost = isSortExpressByCost;
    }

    /**
     * 是否截单
     */
    public void setIsOrderIntercepted(Integer isOrderIntercepted){
        this.isOrderIntercepted = isOrderIntercepted;
    }

    /**
     * 截单时间类型
     */
    public void setInterceptTimeType(Integer interceptTimeType){
        this.interceptTimeType = interceptTimeType;
    }

    /**
     * 截单时间点
     */
    public void setInterceptTime(Date interceptTime){
        this.interceptTime = interceptTime;
    }

    /**
     * 截单开始时间
     */
    public void setInterceptBeginTime(Date interceptBeginTime){
        this.interceptBeginTime = interceptBeginTime;
    }

    /**
     * 截单结束时间
     */
    public void setInterceptEndTime(Date interceptEndTime){
        this.interceptEndTime = interceptEndTime;
    }

    /**
     * 前置派送类型
     */
    public void setAdvanceDeliveryType(Integer advanceDeliveryType){
        this.advanceDeliveryType = advanceDeliveryType;
    }

    /**
     * 是否全渠道
     */
    public void setIsO2o(Integer isO2o){
        this.isO2o = isO2o;
    }

    /**
     * 是否开启天猫翱象配置
     */
    public void setIsEnableAoxiang(Integer isEnableAoxiang){
        this.isEnableAoxiang = isEnableAoxiang;
    }

    /**
     * 邮编
     */
    public void setZipcode(String zipcode){
        this.zipcode = zipcode;
    }

    /**
     * 是否按品牌拆单
     */
    public void setIsSplitByBrand(Integer isSplitByBrand){
        this.isSplitByBrand = isSplitByBrand;
    }


    /**
     * 仓库id
     */
    public Long getWarehouseId(){
        return this.warehouseId;
    }

    /**
     * 市id
     */
    public Long getCityId(){
        return this.cityId;
    }

    /**
     * 国家id
     */
    public Long getCountryId(){
        return this.countryId;
    }

    /**
     * 区id
     */
    public Long getDistrictId(){
        return this.districtId;
    }

    /**
     * 省id
     */
    public Long getProvinceId(){
        return this.provinceId;
    }

    /**
     * 市
     */
    public String getCityName(){
        return this.cityName;
    }

    /**
     * 区
     */
    public String getDistrictName(){
        return this.districtName;
    }

    /**
     * 省
     */
    public String getProvinceName(){
        return this.provinceName;
    }

    /**
     * 国家
     */
    public String getCountryName(){
        return this.countryName;
    }

    /**
     * 更新时间
     */
    public Date getModifiedTime(){
        return this.modifiedTime;
    }

    /**
     * 创建时间
     */
    public Date getCreatedTime(){
        return this.createdTime;
    }

    /**
     * 是否启用
     */
    public Integer getIsEnable(){
        return this.isEnable;
    }

    /**
     * 仓库代码
     */
    public String getWarehouseCode(){
        return this.warehouseCode;
    }

    /**
     * 仓库名称
     */
    public String getWarehouseName(){
        return this.warehouseName;
    }

    /**
     * 仓库类型
     */
    public Integer getWarehouseType(){
        return this.warehouseType;
    }

    /**
     * 品牌编码
     */
    public String getBrandCodes(){
        return this.brandCodes;
    }

    /**
     * 品牌名称
     */
    public String getBrandNames(){
        return this.brandNames;
    }

    /**
     * 仓库应用id
     */
    public Long getWmsAppId(){
        return this.wmsAppId;
    }

    /**
     * 联系人
     */
    public String getContact(){
        return this.contact;
    }

    /**
     * 地址
     */
    public String getAddress(){
        return this.address;
    }

    /**
     * 手机
     */
    public String getMobile(){
        return this.mobile;
    }

    /**
     * 电话
     */
    public String getTelephone(){
        return this.telephone;
    }

    /**
     * 货主编码
     */
    public String getOwnerCode(){
        return this.ownerCode;
    }

    /**
     * 外部编码
     */
    public String getOuterCode(){
        return this.outerCode;
    }

    /**
     * 是否匹配区域
     */
    public Integer getIsMatchRegion(){
        return this.isMatchRegion;
    }

    /**
     * 唯品平台仓库代码
     */
    public String getVipPlatformCode(){
        return this.vipPlatformCode;
    }

    /**
     * 是否允许配货
     */
    public Integer getIsStockAvailable(){
        return this.isStockAvailable;
    }

    /**
     * 是否允许上传库存
     */
    public Integer getIsCanStockUpload(){
        return this.isCanStockUpload;
    }

    /**
     * 是否已关联门店组
     */
    public Integer getIsHasGroupConnected(){
        return this.isHasGroupConnected;
    }

    /**
     * 公司id
     */
    public Long getCompanyId(){
        return this.companyId;
    }

    /**
     * 公司名称
     */
    public String getCompanyName(){
        return this.companyName;
    }

    /**
     * 是否按快递成本替快递排序
     */
    public Integer getIsSortExpressByCost(){
        return this.isSortExpressByCost;
    }

    /**
     * 是否截单
     */
    public Integer getIsOrderIntercepted(){
        return this.isOrderIntercepted;
    }

    /**
     * 截单时间类型
     */
    public Integer getInterceptTimeType(){
        return this.interceptTimeType;
    }

    /**
     * 截单时间点
     */
    public Date getInterceptTime(){
        return this.interceptTime;
    }

    /**
     * 截单开始时间
     */
    public Date getInterceptBeginTime(){
        return this.interceptBeginTime;
    }

    /**
     * 截单结束时间
     */
    public Date getInterceptEndTime(){
        return this.interceptEndTime;
    }

    /**
     * 前置派送类型
     */
    public Integer getAdvanceDeliveryType(){
        return this.advanceDeliveryType;
    }

    /**
     * 是否全渠道
     */
    public Integer getIsO2o(){
        return this.isO2o;
    }

    /**
     * 是否开启天猫翱象配置
     */
    public Integer getIsEnableAoxiang(){
        return this.isEnableAoxiang;
    }

    /**
     * 邮编
     */
    public String getZipcode(){
        return this.zipcode;
    }

    /**
     * 是否按品牌拆单
     */
    public Integer getIsSplitByBrand(){
        return this.isSplitByBrand;
    }
}
