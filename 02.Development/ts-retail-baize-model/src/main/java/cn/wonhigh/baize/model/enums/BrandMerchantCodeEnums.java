package cn.wonhigh.baize.model.enums;

import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 品牌方商家编码枚举
 * @date 2025/6/11 10:27
 */
public enum BrandMerchantCodeEnums {
    PUSFS("PUSFS", "PU01", "彪马"),
    ADSFS("ADSFS", "AD01",  "阿迪达斯");

    private String code;
    private String brandNo;
    private String name;

    BrandMerchantCodeEnums(String code, String brandNo, String name) {
        this.code = code;
        this.brandNo = brandNo;
        this.name = name;
    }

    public static List<Map<String, Object>> getBrandMerchantList() {
        return Stream.of(BrandMerchantCodeEnums.values())
                .map(brandMerchantCodeEnums -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("value", brandMerchantCodeEnums.getCode());
                    map.put("desc", brandMerchantCodeEnums.getName());
                    return map;
                }).collect(Collectors.toList());
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static BrandMerchantCodeEnums getNameByCode(String code) {
        for (BrandMerchantCodeEnums value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
