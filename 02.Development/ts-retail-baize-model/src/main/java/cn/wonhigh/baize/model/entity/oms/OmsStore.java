package cn.wonhigh.baize.model.entity.oms;

import cn.mercury.annotation.Label;
import cn.mercury.basic.query.Query;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.Date;


/**
 * (OmsStore)实体类
 *
 * <AUTHOR>
 * @since 2024-10-29 22:19:20
 */
public class OmsStore extends BaseEntity<Long> implements Serializable {
    private static final long serialVersionUID = 472647205609965822L;
    /**
     * 店铺id
     */
    @Label("店铺id")
    private Long storeId;
    /**
     * 更新时间
     */
    @Label("更新时间")
    private Date modifiedTime;
    /**
     * 创建时间
     */
    @Label("创建时间")
    private Date createdTime;
    /**
     * 是否启用
     */
    @Label("是否启用")
    private Integer isEnable;
    /**
     * 店铺名称
     */
    @Label("店铺名称")
    private String storeName;
    /**
     * 店铺编码
     */
    @Label("店铺编码")
    private String storeCode;
    /**
     * 昵称
     */
    @Label("昵称")
    private String nickname;
    /**
     * 手机
     */
    @Label("手机")
    private String mobile;
    /**
     * 电话
     */
    @Label("电话")
    private String telephone;
    /**
     * 地址
     */
    @Label("地址")
    private String address;
    /**
     * 授权码
     */
    @Label("授权码")
    private String accessToken;
    /**
     * 刷新码
     */
    @Label("刷新码")
    private String refreshToken;
    /**
     * 授权码过期时间
     */
    @Label("授权码过期时间")
    private Date accessTokenExpirationTime;
    /**
     * 刷新码过期时间
     */
    @Label("刷新码过期时间")
    private Date refreshTokenExpirationTime;
    /**
     * 备注
     */
    @Label("备注")
    private String remark;
    /**
     * 店铺配置
     */
    @Label("店铺配置")
    private String settingJson;
    /**
     * 商城应用id
     */
    @Label("商城应用id")
    private Long mallAppId;
    /**
     * 商城类型
     */
    @Label("商城类型")
    private Integer mallType;
    /**
     * 公司id
     */
    @Label("公司id")
    private Long companyId;
    /**
     * 公司名称
     */
    @Label("公司名称")
    private String companyName;
    /**
     * 发票模板id
     */
    @Label("发票模板id")
    private Long invoiceTemplateId;
    /**
     * 短信签名id
     */
    @Label("短信签名id")
    private Long smsSignId;
    /**
     * 授权是否过期
     */
    @Label("授权是否过期")
    private Integer isAccessInvalid;
    /**
     * 关联店铺id
     */
    @Label("关联店铺id")
    private Long relateStoreId;


    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Integer getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public Date getAccessTokenExpirationTime() {
        return accessTokenExpirationTime;
    }

    public void setAccessTokenExpirationTime(Date accessTokenExpirationTime) {
        this.accessTokenExpirationTime = accessTokenExpirationTime;
    }

    public Date getRefreshTokenExpirationTime() {
        return refreshTokenExpirationTime;
    }

    public void setRefreshTokenExpirationTime(Date refreshTokenExpirationTime) {
        this.refreshTokenExpirationTime = refreshTokenExpirationTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSettingJson() {
        return settingJson;
    }

    public void setSettingJson(String settingJson) {
        this.settingJson = settingJson;
    }

    public Long getMallAppId() {
        return mallAppId;
    }

    public void setMallAppId(Long mallAppId) {
        this.mallAppId = mallAppId;
    }

    public Integer getMallType() {
        return mallType;
    }

    public void setMallType(Integer mallType) {
        this.mallType = mallType;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getInvoiceTemplateId() {
        return invoiceTemplateId;
    }

    public void setInvoiceTemplateId(Long invoiceTemplateId) {
        this.invoiceTemplateId = invoiceTemplateId;
    }

    public Long getSmsSignId() {
        return smsSignId;
    }

    public void setSmsSignId(Long smsSignId) {
        this.smsSignId = smsSignId;
    }

    public Integer getIsAccessInvalid() {
        return isAccessInvalid;
    }

    public void setIsAccessInvalid(Integer isAccessInvalid) {
        this.isAccessInvalid = isAccessInvalid;
    }

    public Long getRelateStoreId() {
        return relateStoreId;
    }

    public void setRelateStoreId(Long relateStoreId) {
        this.relateStoreId = relateStoreId;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static OmsStoreBuilder build() {
        return new OmsStoreBuilder(new OmsStore());
    }

    public static OmsStoreBuilder build(OmsStore value) {
        return new OmsStoreBuilder(value);
    }

    public static class OmsStoreBuilder extends AbstractEntryBuilder<OmsStore> {

        private OmsStoreBuilder(OmsStore entry) {
            this.obj = entry;
        }

        @Override
        public OmsStore object() {
            return this.obj;
        }

        public OmsStoreBuilder storeId(Long value) {

            this.obj.setStoreId(value);

            if (query == null)
                query = new Query();
            this.query.where("storeId", value);
            return this;
        }

        public OmsStoreBuilder modifiedTime(Date value) {

            this.obj.setModifiedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("modifiedTime", value);
            return this;
        }

        public OmsStoreBuilder createdTime(Date value) {

            this.obj.setCreatedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("createdTime", value);
            return this;
        }

        public OmsStoreBuilder isEnable(Integer value) {

            this.obj.setIsEnable(value);

            if (query == null)
                query = new Query();
            this.query.where("isEnable", value);
            return this;
        }

        public OmsStoreBuilder storeName(String value) {

            this.obj.setStoreName(value);

            if (query == null)
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }

        public OmsStoreBuilder storeCode(String value) {

            this.obj.setStoreCode(value);

            if (query == null)
                query = new Query();
            this.query.where("storeCode", value);
            return this;
        }

        public OmsStoreBuilder nickname(String value) {

            this.obj.setNickname(value);

            if (query == null)
                query = new Query();
            this.query.where("nickname", value);
            return this;
        }

        public OmsStoreBuilder mobile(String value) {

            this.obj.setMobile(value);

            if (query == null)
                query = new Query();
            this.query.where("mobile", value);
            return this;
        }

        public OmsStoreBuilder telephone(String value) {

            this.obj.setTelephone(value);

            if (query == null)
                query = new Query();
            this.query.where("telephone", value);
            return this;
        }

        public OmsStoreBuilder address(String value) {

            this.obj.setAddress(value);

            if (query == null)
                query = new Query();
            this.query.where("address", value);
            return this;
        }

        public OmsStoreBuilder accessToken(String value) {

            this.obj.setAccessToken(value);

            if (query == null)
                query = new Query();
            this.query.where("accessToken", value);
            return this;
        }

        public OmsStoreBuilder refreshToken(String value) {

            this.obj.setRefreshToken(value);

            if (query == null)
                query = new Query();
            this.query.where("refreshToken", value);
            return this;
        }

        public OmsStoreBuilder accessTokenExpirationTime(Date value) {

            this.obj.setAccessTokenExpirationTime(value);

            if (query == null)
                query = new Query();
            this.query.where("accessTokenExpirationTime", value);
            return this;
        }

        public OmsStoreBuilder refreshTokenExpirationTime(Date value) {

            this.obj.setRefreshTokenExpirationTime(value);

            if (query == null)
                query = new Query();
            this.query.where("refreshTokenExpirationTime", value);
            return this;
        }

        public OmsStoreBuilder remark(String value) {

            this.obj.setRemark(value);

            if (query == null)
                query = new Query();
            this.query.where("remark", value);
            return this;
        }

        public OmsStoreBuilder settingJson(String value) {

            this.obj.setSettingJson(value);

            if (query == null)
                query = new Query();
            this.query.where("settingJson", value);
            return this;
        }

        public OmsStoreBuilder mallAppId(Long value) {

            this.obj.setMallAppId(value);

            if (query == null)
                query = new Query();
            this.query.where("mallAppId", value);
            return this;
        }

        public OmsStoreBuilder mallType(Integer value) {

            this.obj.setMallType(value);

            if (query == null)
                query = new Query();
            this.query.where("mallType", value);
            return this;
        }

        public OmsStoreBuilder companyId(Long value) {

            this.obj.setCompanyId(value);

            if (query == null)
                query = new Query();
            this.query.where("companyId", value);
            return this;
        }

        public OmsStoreBuilder companyName(String value) {

            this.obj.setCompanyName(value);

            if (query == null)
                query = new Query();
            this.query.where("companyName", value);
            return this;
        }

        public OmsStoreBuilder invoiceTemplateId(Long value) {

            this.obj.setInvoiceTemplateId(value);

            if (query == null)
                query = new Query();
            this.query.where("invoiceTemplateId", value);
            return this;
        }

        public OmsStoreBuilder smsSignId(Long value) {

            this.obj.setSmsSignId(value);

            if (query == null)
                query = new Query();
            this.query.where("smsSignId", value);
            return this;
        }

        public OmsStoreBuilder isAccessInvalid(Integer value) {

            this.obj.setIsAccessInvalid(value);

            if (query == null)
                query = new Query();
            this.query.where("isAccessInvalid", value);
            return this;
        }

        public OmsStoreBuilder relateStoreId(Long value) {

            this.obj.setRelateStoreId(value);

            if (query == null)
                query = new Query();
            this.query.where("relateStoreId", value);
            return this;
        }
    }
}
