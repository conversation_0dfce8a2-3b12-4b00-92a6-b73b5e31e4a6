/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 品牌默认库存比例配置
**/
public class BrandInventoryRange  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1716428931300L;
    
    //渠道(1-OMS 2-天虹 3-ADSFS)
    @Label(value = "渠道", defaultVal = "1") 
    private Integer channel;
    
    //类型：1-鞋,2-体育,3-新业务,4-小程序,5-大众点评,6-童鞋.修改类型为varchar(32)便于支持虚仓编码
    @Label(value = "虚仓编码", defaultVal = "1")
    private String bussinessType;

    @Label(value = "虚仓名称", defaultVal = "1")
    private String bussinessTypeName;

    
    //安全库存
    @Label(value = "安全库存", defaultVal = "10") 
    private Integer safetyStock;
    
    //共享比例
    @Label(value = "共享比例", defaultVal = "10") 
    private Integer sharingRatio;
    
    //季节
    @Label("季节") 
    private String purchaseSeason;
    
    //年份(指上市的年份,下拉框选择,值: 2006~2026,默认当年)
    @Label("年份") 
    private String years;
    
    //品牌编码
    @Label("品牌编码") 
    private String brandNo;
    
    //品牌名称
    @Label("品牌名称") 
    private String brandName;


    //年份名称
    @Label("年份名称")
    private String yearsName;

    //季节名称
    @Label("季节名称")
    private String purchaseSeasonName;



    
    public Integer getChannel(){
        return  channel;
    }
    public void setChannel(Integer val ){
        channel = val;
    }
    
    public String getBussinessType(){
        return  bussinessType;
    }
    public void setBussinessType(String val ){
        bussinessType = val;
    }
    
    public Integer getSafetyStock(){
        return  safetyStock;
    }
    public void setSafetyStock(Integer val ){
        safetyStock = val;
    }
    
    public Integer getSharingRatio(){
        return  sharingRatio;
    }
    public void setSharingRatio(Integer val ){
        sharingRatio = val;
    }
    
    public String getPurchaseSeason(){
        return  purchaseSeason;
    }
    public void setPurchaseSeason(String val ){
        purchaseSeason = val;
    }
    
    public String getYears(){
        return  years;
    }
    public void setYears(String val ){
        years = val;
    }
    
    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    
    public String getBrandName(){
        return  brandName;
    }
    public void setBrandName(String val ){
        brandName = val;
    }

    public String getBussinessTypeName() {
        return bussinessTypeName;
    }

    public void setBussinessTypeName(String bussinessTypeName) {
        this.bussinessTypeName = bussinessTypeName;
    }

    public String getYearsName() {
        return yearsName;
    }

    public void setYearsName(String yearsName) {
        this.yearsName = yearsName;
    }

    public String getPurchaseSeasonName() {
        return purchaseSeasonName;
    }

    public void setPurchaseSeasonName(String purchaseSeasonName) {
        this.purchaseSeasonName = purchaseSeasonName;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public BrandInventoryRangeBuilder build(){
        return new BrandInventoryRangeBuilder(this);
    }

    public static class BrandInventoryRangeBuilder extends AbstractEntryBuilder<BrandInventoryRange>{

        private BrandInventoryRangeBuilder(BrandInventoryRange entry){
            this.obj = entry;
        }

       @Override
		public BrandInventoryRange object() {
			return this.obj;
		}

        
        public BrandInventoryRangeBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder channel(Integer value ){
            this.obj.channel = value;
            if( query == null  )
                query = new Query();
            this.query.where("channel", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder bussinessType(String value ){
            this.obj.bussinessType = value;
            if( query == null  )
                query = new Query();
            this.query.where("bussinessType", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder safetyStock(Integer value ){
            this.obj.safetyStock = value;
            if( query == null  )
                query = new Query();
            this.query.where("safetyStock", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder sharingRatio(Integer value ){
            this.obj.sharingRatio = value;
            if( query == null  )
                query = new Query();
            this.query.where("sharingRatio", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder purchaseSeason(String value ){
            this.obj.purchaseSeason = value;
            if( query == null  )
                query = new Query();
            this.query.where("purchaseSeason", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder years(String value ){
            this.obj.years = value;
            if( query == null  )
                query = new Query();
            this.query.where("years", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder brandNo(String value ){
            this.obj.brandNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public BrandInventoryRangeBuilder brandName(String value ){
            this.obj.brandName = value;
            if( query == null  )
                query = new Query();
            this.query.where("brandName", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}