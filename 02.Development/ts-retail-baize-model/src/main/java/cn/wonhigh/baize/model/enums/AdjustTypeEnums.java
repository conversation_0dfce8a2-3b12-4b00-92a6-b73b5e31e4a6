package cn.wonhigh.baize.model.enums;

import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 调整单类型枚举
 * @date 2025/6/13 15:57
 */
public enum AdjustTypeEnums {
    ADD(1, "增加数量"),
    AUDIT_SUCCESS(-1, "减少数量");

    private Integer value;
    private String desc;

    AdjustTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AdjustTypeEnums getAdjustStatusEnums(Integer status) {
        for (AdjustTypeEnums adjustStatusEnums : AdjustTypeEnums.values()) {
            if (adjustStatusEnums.getValue().equals(status)) {
                return adjustStatusEnums;
            }
        }
        return null;
    }

    public static List<Map<String, Object>> getAdjustStatusList() {
        return Stream.of(AdjustTypeEnums.values())
                .map(adjustStatusEnums -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("value", adjustStatusEnums.getValue());
                    map.put("desc", adjustStatusEnums.getDesc());
                    return map;
                }).collect(Collectors.toList());
    }

    public Integer getValue() {
        return value;
    }
    public void setValue(Integer value) {
        this.value = value;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
}
