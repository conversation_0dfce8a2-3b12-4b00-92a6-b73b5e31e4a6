package cn.wonhigh.baize.model.dto;


/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.model.dto
 * @Project：ts-retail-baize
 * @name：IcsInventoryOmsLockDto
 * @Date：2024/12/28 10:48
 * @Filename：IcsInventoryOmsLockDto
 * @description：
 */
public class IcsInventoryOmsLockDto {

    /**
     * 虚仓编码
     */
    private String vstoreCode;
    /**
     * 虚拟仓名称
     */
    private String vstoreName;
    /**
     * sku编码
     */
    private String skuNo;
    /**
     * 锁存数量
     */
    private Integer lockQty;

    public String getVstoreCode() {
        return vstoreCode;
    }

    public void setVstoreCode(String vstoreCode) {
        this.vstoreCode = vstoreCode;
    }

    public String getVstoreName() {
        return vstoreName;
    }

    public void setVstoreName(String vstoreName) {
        this.vstoreName = vstoreName;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public Integer getLockQty() {
        return lockQty;
    }

    public void setLockQty(Integer lockQty) {
        this.lockQty = lockQty;
    }
}
