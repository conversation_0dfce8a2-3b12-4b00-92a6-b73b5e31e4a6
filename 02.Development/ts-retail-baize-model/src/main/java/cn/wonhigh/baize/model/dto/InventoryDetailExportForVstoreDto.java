package cn.wonhigh.baize.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

public class InventoryDetailExportForVstoreDto implements Serializable {
    private static final long serialVersionUID = -4294177484881847233L;

    @ExcelProperty("聚合仓编码")
    private String vstoreCode;
    @ExcelProperty("品牌编码")
    private String brandNo;
    @ExcelProperty("SKU")
    private String skuNo;
    @ExcelProperty("商品编码")
    private String itemCode;
    @ExcelProperty("尺寸")
    private String sizeNo;
    @ExcelProperty("可派库存")
    private Integer availableQty;
    @ExcelProperty("可共享库存")
    private Integer shareQty;
    @ExcelProperty("SKU安全库存")
    private Integer safeStockQty;
    @ExcelProperty("共享比例%")
    private Double shareRatio;
    @ExcelProperty("库存同步比例%")
    private Double channelShareRatio;
    @ExcelProperty("冗余安全库存")
    private Integer ryQty;
    @ExcelProperty("OMS锁库量")
    private Integer omsLockQty;
    @ExcelProperty("活动锁库量")
    private Integer lockQty;

    public Integer getAvailableQty() {
        return availableQty;
    }

    public void setAvailableQty(Integer availableQty) {
        this.availableQty = availableQty;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public Double getChannelShareRatio() {
        return channelShareRatio;
    }

    public void setChannelShareRatio(Double channelShareRatio) {
        this.channelShareRatio = channelShareRatio;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public Integer getLockQty() {
        return lockQty;
    }

    public void setLockQty(Integer lockQty) {
        this.lockQty = lockQty;
    }

    public Integer getOmsLockQty() {
        return omsLockQty;
    }

    public void setOmsLockQty(Integer omsLockQty) {
        this.omsLockQty = omsLockQty;
    }

    public Integer getRyQty() {
        return ryQty;
    }

    public void setRyQty(Integer ryQty) {
        this.ryQty = ryQty;
    }

    public Integer getSafeStockQty() {
        return safeStockQty;
    }

    public void setSafeStockQty(Integer safeStockQty) {
        this.safeStockQty = safeStockQty;
    }

    public Integer getShareQty() {
        return shareQty;
    }

    public void setShareQty(Integer shareQty) {
        this.shareQty = shareQty;
    }

    public Double getShareRatio() {
        return shareRatio;
    }

    public void setShareRatio(Double shareRatio) {
        this.shareRatio = shareRatio;
    }

    public String getSizeNo() {
        return sizeNo;
    }

    public void setSizeNo(String sizeNo) {
        this.sizeNo = sizeNo;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getVstoreCode() {
        return vstoreCode;
    }

    public void setVstoreCode(String vstoreCode) {
        this.vstoreCode = vstoreCode;
    }
}
