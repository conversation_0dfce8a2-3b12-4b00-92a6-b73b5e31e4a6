package cn.wonhigh.baize.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class WarehouseRatioDto {
    private String warehouseCode;
    private String warehouseName;
    private String amplifyRatio;
    @JsonIgnore
    private Integer amplifyRatioNum;
    private String brandName;
    private String brandCode;
    private String key;

    public String getKey() {
        return warehouseCode+brandCode;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public void setAmplifyRatio(String amplifyRatio) {
        this.amplifyRatio = amplifyRatio;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getAmplifyRatioNum() {
        return amplifyRatioNum;
    }

    public void setAmplifyRatioNum(Integer amplifyRatioNum) {
        this.amplifyRatioNum = amplifyRatioNum;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public String getAmplifyRatio() {
        if (amplifyRatioNum == null) {
            return "";
        }
        return amplifyRatioNum.toString().concat("%");
    }

}
