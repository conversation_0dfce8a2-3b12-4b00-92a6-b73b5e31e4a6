/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 转单后订单扩展表
**/
public class RetailOrder  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778671L;
    
    /**
    *业务类型 0:正常单 1:云店通意礴定制
    **/ 
    @Label("业务类型") 
    private Integer businessType;
    

    public Integer getBusinessType(){
        return  businessType;
    }
    public void setBusinessType(Integer val ){
        businessType = val;
    }
    /**
    *打标name
    **/ 
    @Label("打标") 
    private String orderMarkName;
    

    public String getOrderMarkName(){
        return  orderMarkName;
    }
    public void setOrderMarkName(String val ){
        orderMarkName = val;
    }
    /**
    *同步时间戳 格式：mmddhhHHmmss
    **/ 
    @Label("同步时间戳") 
    private Integer syncTimeStamp;
    

    public Integer getSyncTimeStamp(){
        return  syncTimeStamp;
    }
    public void setSyncTimeStamp(Integer val ){
        syncTimeStamp = val;
    }
    /**
    *分库字段：订单hash
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *是否唯品会JIT单据 0-否 1-是
    **/ 
    @Label("是否唯品会") 
    private Integer isVip;
    

    public Integer getIsVip(){
        return  isVip;
    }
    public void setIsVip(Integer val ){
        isVip = val;
    }
    /**
    *订单状态名称
    **/ 
    @Label("订单状态名称") 
    private String orderStatusName;
    

    public String getOrderStatusName(){
        return  orderStatusName;
    }
    public void setOrderStatusName(String val ){
        orderStatusName = val;
    }
    /**
    *相关退单号
    **/ 
    @Label("相关退单号") 
    private String returnBillNo;
    

    public String getReturnBillNo(){
        return  returnBillNo;
    }
    public void setReturnBillNo(String val ){
        returnBillNo = val;
    }
    /**
    *发货时间
    **/ 
    @Label("发货时间") 
    private Date shipTime;
    

    public Date getShipTime(){
        return  shipTime;
    }
    public void setShipTime(Date val ){
        shipTime = val;
    }
    /**
    *提报结果 1:自认损失，2：追回实物，3：物流公司索赔
    **/ 
    @Label("提报结果") 
    private Integer followResultType;
    

    public Integer getFollowResultType(){
        return  followResultType;
    }
    public void setFollowResultType(Integer val ){
        followResultType = val;
    }
    /**
    *支付尾款时间
    **/ 
    @Label("支付尾款时间") 
    private Date balanceDueDate;
    

    public Date getBalanceDueDate(){
        return  balanceDueDate;
    }
    public void setBalanceDueDate(Date val ){
        balanceDueDate = val;
    }
    /**
    *唯品po单号,对应优购poNo
    **/ 
    @Label("唯品") 
    private String poNo;
    

    public String getPoNo(){
        return  poNo;
    }
    public void setPoNo(String val ){
        poNo = val;
    }
    /**
    *
    **/ 
    private String closeCode;
    

    public String getCloseCode(){
        return  closeCode;
    }
    public void setCloseCode(String val ){
        closeCode = val;
    }
    /**
    *订单支付金额
    **/ 
    @Label("订单支付金额") 
    private BigDecimal orderPayTotalAmont;
    

    public BigDecimal getOrderPayTotalAmont(){
        return  orderPayTotalAmont;
    }
    public void setOrderPayTotalAmont(BigDecimal val ){
        orderPayTotalAmont = val;
    }
    /**
    *订单总额
    **/ 
    @Label("订单总额") 
    private BigDecimal orderAmount;
    

    public BigDecimal getOrderAmount(){
        return  orderAmount;
    }
    public void setOrderAmount(BigDecimal val ){
        orderAmount = val;
    }
    /**
    *商品总数
    **/ 
    @Label("商品总数") 
    private Integer sendDetailTotal;
    

    public Integer getSendDetailTotal(){
        return  sendDetailTotal;
    }
    public void setSendDetailTotal(Integer val ){
        sendDetailTotal = val;
    }
    /**
    *实际运费
    **/ 
    @Label("实际运费") 
    private BigDecimal actualPostage;
    

    public BigDecimal getActualPostage(){
        return  actualPostage;
    }
    public void setActualPostage(BigDecimal val ){
        actualPostage = val;
    }
    /**
    *物流公司名称
    **/ 
    @Label("物流公司名称") 
    private String logisticsName;
    

    public String getLogisticsName(){
        return  logisticsName;
    }
    public void setLogisticsName(String val ){
        logisticsName = val;
    }
    /**
    *快递公司编号
    **/ 
    @Label("快递公司编号") 
    private String logisticsCode;
    

    public String getLogisticsCode(){
        return  logisticsCode;
    }
    public void setLogisticsCode(String val ){
        logisticsCode = val;
    }
    /**
    *退货收货人姓名
    **/ 
    @Label("退货收货人姓名") 
    private String returnConsigneeName;
    

    public String getReturnConsigneeName(){
        return  returnConsigneeName;
    }
    public void setReturnConsigneeName(String val ){
        returnConsigneeName = val;
    }
    /**
    *订单类型：0-正常 1-锁定订单 2-挂起订单 3-问题订单...详情参考码表
    **/ 
    @Label("订单类型") 
    private Integer orderType;
    

    public Integer getOrderType(){
        return  orderType;
    }
    public void setOrderType(Integer val ){
        orderType = val;
    }
    /**
    *退货收货人邮编
    **/ 
    @Label("退货收货人邮编") 
    private String returnZipcode;
    

    public String getReturnZipcode(){
        return  returnZipcode;
    }
    public void setReturnZipcode(String val ){
        returnZipcode = val;
    }
    /**
    *打标id
    **/ 
    @Label("打标") 
    private String orderMarkId;
    

    public String getOrderMarkId(){
        return  orderMarkId;
    }
    public void setOrderMarkId(String val ){
        orderMarkId = val;
    }
    /**
    *退货地址
    **/ 
    @Label("退货地址") 
    private String returnAddress;
    

    public String getReturnAddress(){
        return  returnAddress;
    }
    public void setReturnAddress(String val ){
        returnAddress = val;
    }
    /**
    *售后电话
    **/ 
    @Label("售后电话") 
    private String returnConstactPhone;
    

    public String getReturnConstactPhone(){
        return  returnConstactPhone;
    }
    public void setReturnConstactPhone(String val ){
        returnConstactPhone = val;
    }
    /**
    *是否需要进入抢单 0 不需要 1 需要 2 抢单失效
    **/ 
    @Label("是否需要进入抢单") 
    private Integer needGrab;
    

    public Integer getNeedGrab(){
        return  needGrab;
    }
    public void setNeedGrab(Integer val ){
        needGrab = val;
    }
    /**
    *在线支付时间
    **/ 
    @Label("在线支付时间") 
    private Date onlinePayTime;
    

    public Date getOnlinePayTime(){
        return  onlinePayTime;
    }
    public void setOnlinePayTime(Date val ){
        onlinePayTime = val;
    }
    /**
    *问题类型id
    **/ 
    @Label("问题类型") 
    private String problemTypeId;
    

    public String getProblemTypeId(){
        return  problemTypeId;
    }
    public void setProblemTypeId(String val ){
        problemTypeId = val;
    }
    /**
    *问题类型name
    **/ 
    @Label("问题类型") 
    private String problemTypeName;
    

    public String getProblemTypeName(){
        return  problemTypeName;
    }
    public void setProblemTypeName(String val ){
        problemTypeName = val;
    }
    /**
    *是否使用仓库发货.1 使用仓库发货  2 不使用
    **/ 
    @Label("是否使用仓库发货") 
    private Integer userStore;
    

    public Integer getUserStore(){
        return  userStore;
    }
    public void setUserStore(Integer val ){
        userStore = val;
    }
    /**
    *销售店铺结算公司
    **/ 
    @Label("销售店铺结算公司") 
    private String saleCompanyNo;
    

    public String getSaleCompanyNo(){
        return  saleCompanyNo;
    }
    public void setSaleCompanyNo(String val ){
        saleCompanyNo = val;
    }
    /**
    *订单创建时间
    **/ 
    @Label("订单创建时间") 
    private Date orderCreateTime;
    

    public Date getOrderCreateTime(){
        return  orderCreateTime;
    }
    public void setOrderCreateTime(Date val ){
        orderCreateTime = val;
    }
    /**
    *制单类型：1“优购，2非优购，5大宗订单，12直销，13唯品会jit鞋靴，14唯品会jit
    **/ 
    @Label("制单类型") 
    private Integer sourceType;
    

    public Integer getSourceType(){
        return  sourceType;
    }
    public void setSourceType(Integer val ){
        sourceType = val;
    }
    /**
    *订单类型：默认1：正常单，2:预售单，3:换货单
    **/ 
    @Label("订单类型") 
    private Integer orderStyle;
    

    public Integer getOrderStyle(){
        return  orderStyle;
    }
    public void setOrderStyle(Integer val ){
        orderStyle = val;
    }
    /**
    *单据编号
    **/ 
    @Label("单据编号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    /**
    *质检单号
    **/ 
    @Label("质检单号") 
    private String refBillNo;
    

    public String getRefBillNo(){
        return  refBillNo;
    }
    public void setRefBillNo(String val ){
        refBillNo = val;
    }
    /**
    *原始订单号
    **/ 
    @Label("原始订单号") 
    private String originalOrderNo;
    

    public String getOriginalOrderNo(){
        return  originalOrderNo;
    }
    public void setOriginalOrderNo(String val ){
        originalOrderNo = val;
    }
    /**
    *平台订单号
    **/ 
    @Label("平台订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    /**
    *优购PO单号
    **/ 
    @Label("优购") 
    private String outOrderId;
    

    public String getOutOrderId(){
        return  outOrderId;
    }
    public void setOutOrderId(String val ){
        outOrderId = val;
    }
    /**
    *订单状态 0 未确认 10 已确认 30 已作废 50 已完成
    **/ 
    @Label("订单状态") 
    private Integer orderStatus;
    

    public Integer getOrderStatus(){
        return  orderStatus;
    }
    public void setOrderStatus(Integer val ){
        orderStatus = val;
    }
    /**
    *支付状态 0 未支付 1已支付
    **/ 
    @Label("支付状态") 
    private Integer payStatus;
    

    public Integer getPayStatus(){
        return  payStatus;
    }
    public void setPayStatus(Integer val ){
        payStatus = val;
    }
    /**
    *支付状态名
    **/ 
    @Label("支付状态名") 
    private String payName;
    

    public String getPayName(){
        return  payName;
    }
    public void setPayName(String val ){
        payName = val;
    }
    /**
    *支付方式
    **/ 
    @Label("支付方式") 
    private String paymentCode;
    

    public String getPaymentCode(){
        return  paymentCode;
    }
    public void setPaymentCode(String val ){
        paymentCode = val;
    }
    /**
    *物流状态 0 未发货 10 已通知配货 11 已接单 12 已拒单
    **/ 
    @Label("物流状态") 
    private Integer deliverStatus;
    

    public Integer getDeliverStatus(){
        return  deliverStatus;
    }
    public void setDeliverStatus(Integer val ){
        deliverStatus = val;
    }
    /**
    *物流状态(中文)
    **/ 
    @Label("物流状态") 
    private String deliverStatusName;
    

    public String getDeliverStatusName(){
        return  deliverStatusName;
    }
    public void setDeliverStatusName(String val ){
        deliverStatusName = val;
    }
    /**
    *接口平台
    **/ 
    @Label("接口平台") 
    private String interfacePlatform;
    

    public String getInterfacePlatform(){
        return  interfacePlatform;
    }
    public void setInterfacePlatform(String val ){
        interfacePlatform = val;
    }
    /**
    *来源平台
    **/ 
    @Label("来源平台") 
    private String originPlatform;
    

    public String getOriginPlatform(){
        return  originPlatform;
    }
    public void setOriginPlatform(String val ){
        originPlatform = val;
    }
    /**
    *来源平台名
    **/ 
    @Label("来源平台名") 
    private String originPlatformName;
    

    public String getOriginPlatformName(){
        return  originPlatformName;
    }
    public void setOriginPlatformName(String val ){
        originPlatformName = val;
    }
    /**
    *店铺编码
    **/ 
    @Label("店铺编码") 
    private String shopNo;
    

    public String getShopNo(){
        return  shopNo;
    }
    public void setShopNo(String val ){
        shopNo = val;
    }
    /**
    *用于派单验证
    **/ 
    @Label("用于派单验证") 
    private String validateBatch;
    

    public String getValidateBatch(){
        return  validateBatch;
    }
    public void setValidateBatch(String val ){
        validateBatch = val;
    }
    /**
    *销售店铺名称
    **/ 
    @Label("销售店铺名称") 
    private String shopName;
    

    public String getShopName(){
        return  shopName;
    }
    public void setShopName(String val ){
        shopName = val;
    }
    /**
    *渠道号
    **/ 
    @Label("渠道号") 
    private String channelNo;
    

    public String getChannelNo(){
        return  channelNo;
    }
    public void setChannelNo(String val ){
        channelNo = val;
    }
    /**
    *仓库编码
    **/ 
    @Label("仓库编码") 
    private String storeNo;
    

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public RetailOrderBuilder build(){
        return new RetailOrderBuilder(this);
    }

    public static class RetailOrderBuilder extends AbstractEntryBuilder<RetailOrder>{

        private RetailOrderBuilder(RetailOrder entry){
            this.obj = entry;
        }

       @Override
		public RetailOrder object() {
			return this.obj;
		}

        
        public RetailOrderBuilder businessType(Integer value ){
            
            this.obj.businessType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("businessType", value);
            return this;
        }
        
        public RetailOrderBuilder orderMarkName(String value ){
            
            this.obj.orderMarkName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderMarkName", value);
            return this;
        }
        
        public RetailOrderBuilder syncTimeStamp(Integer value ){
            
            this.obj.syncTimeStamp = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("syncTimeStamp", value);
            return this;
        }
        
        public RetailOrderBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public RetailOrderBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public RetailOrderBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public RetailOrderBuilder isVip(Integer value ){
            
            this.obj.isVip = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isVip", value);
            return this;
        }
        
        public RetailOrderBuilder orderStatusName(String value ){
            
            this.obj.orderStatusName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStatusName", value);
            return this;
        }
        
        public RetailOrderBuilder returnBillNo(String value ){
            
            this.obj.returnBillNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnBillNo", value);
            return this;
        }
        
        public RetailOrderBuilder shipTime(Date value ){
            
            this.obj.shipTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shipTime", value);
            return this;
        }
        
        public RetailOrderBuilder followResultType(Integer value ){
            
            this.obj.followResultType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("followResultType", value);
            return this;
        }
        
        public RetailOrderBuilder balanceDueDate(Date value ){
            
            this.obj.balanceDueDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("balanceDueDate", value);
            return this;
        }
        
        public RetailOrderBuilder poNo(String value ){
            
            this.obj.poNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("poNo", value);
            return this;
        }
        
        public RetailOrderBuilder closeCode(String value ){
            
            this.obj.closeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("closeCode", value);
            return this;
        }
        
        public RetailOrderBuilder orderPayTotalAmont(BigDecimal value ){
            
            this.obj.orderPayTotalAmont = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderPayTotalAmont", value);
            return this;
        }
        
        public RetailOrderBuilder orderAmount(BigDecimal value ){
            
            this.obj.orderAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderAmount", value);
            return this;
        }
        
        public RetailOrderBuilder sendDetailTotal(Integer value ){
            
            this.obj.sendDetailTotal = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendDetailTotal", value);
            return this;
        }
        
        public RetailOrderBuilder actualPostage(BigDecimal value ){
            
            this.obj.actualPostage = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("actualPostage", value);
            return this;
        }
        
        public RetailOrderBuilder logisticsName(String value ){
            
            this.obj.logisticsName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsName", value);
            return this;
        }
        
        public RetailOrderBuilder logisticsCode(String value ){
            
            this.obj.logisticsCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsCode", value);
            return this;
        }
        
        public RetailOrderBuilder returnConsigneeName(String value ){
            
            this.obj.returnConsigneeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnConsigneeName", value);
            return this;
        }
        
        public RetailOrderBuilder orderType(Integer value ){
            
            this.obj.orderType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderType", value);
            return this;
        }
        
        public RetailOrderBuilder returnZipcode(String value ){
            
            this.obj.returnZipcode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnZipcode", value);
            return this;
        }
        
        public RetailOrderBuilder orderMarkId(String value ){
            
            this.obj.orderMarkId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderMarkId", value);
            return this;
        }
        
        public RetailOrderBuilder returnAddress(String value ){
            
            this.obj.returnAddress = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnAddress", value);
            return this;
        }
        
        public RetailOrderBuilder returnConstactPhone(String value ){
            
            this.obj.returnConstactPhone = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnConstactPhone", value);
            return this;
        }
        
        public RetailOrderBuilder needGrab(Integer value ){
            
            this.obj.needGrab = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("needGrab", value);
            return this;
        }
        
        public RetailOrderBuilder onlinePayTime(Date value ){
            
            this.obj.onlinePayTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("onlinePayTime", value);
            return this;
        }
        
        public RetailOrderBuilder problemTypeId(String value ){
            
            this.obj.problemTypeId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("problemTypeId", value);
            return this;
        }
        
        public RetailOrderBuilder problemTypeName(String value ){
            
            this.obj.problemTypeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("problemTypeName", value);
            return this;
        }
        
        public RetailOrderBuilder userStore(Integer value ){
            
            this.obj.userStore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("userStore", value);
            return this;
        }
        
        public RetailOrderBuilder saleCompanyNo(String value ){
            
            this.obj.saleCompanyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleCompanyNo", value);
            return this;
        }
        
        public RetailOrderBuilder orderCreateTime(Date value ){
            
            this.obj.orderCreateTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderCreateTime", value);
            return this;
        }
        
        public RetailOrderBuilder sourceType(Integer value ){
            
            this.obj.sourceType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sourceType", value);
            return this;
        }
        
        public RetailOrderBuilder orderStyle(Integer value ){
            
            this.obj.orderStyle = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStyle", value);
            return this;
        }
        
        public RetailOrderBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public RetailOrderBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public RetailOrderBuilder refBillNo(String value ){
            
            this.obj.refBillNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refBillNo", value);
            return this;
        }
        
        public RetailOrderBuilder originalOrderNo(String value ){
            
            this.obj.originalOrderNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originalOrderNo", value);
            return this;
        }
        
        public RetailOrderBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
        public RetailOrderBuilder outOrderId(String value ){
            
            this.obj.outOrderId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outOrderId", value);
            return this;
        }
        
        public RetailOrderBuilder orderStatus(Integer value ){
            
            this.obj.orderStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderStatus", value);
            return this;
        }
        
        public RetailOrderBuilder payStatus(Integer value ){
            
            this.obj.payStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("payStatus", value);
            return this;
        }
        
        public RetailOrderBuilder payName(String value ){
            
            this.obj.payName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("payName", value);
            return this;
        }
        
        public RetailOrderBuilder paymentCode(String value ){
            
            this.obj.paymentCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("paymentCode", value);
            return this;
        }
        
        public RetailOrderBuilder deliverStatus(Integer value ){
            
            this.obj.deliverStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("deliverStatus", value);
            return this;
        }
        
        public RetailOrderBuilder deliverStatusName(String value ){
            
            this.obj.deliverStatusName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("deliverStatusName", value);
            return this;
        }
        
        public RetailOrderBuilder interfacePlatform(String value ){
            
            this.obj.interfacePlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("interfacePlatform", value);
            return this;
        }
        
        public RetailOrderBuilder originPlatform(String value ){
            
            this.obj.originPlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatform", value);
            return this;
        }
        
        public RetailOrderBuilder originPlatformName(String value ){
            
            this.obj.originPlatformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatformName", value);
            return this;
        }
        
        public RetailOrderBuilder shopNo(String value ){
            
            this.obj.shopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopNo", value);
            return this;
        }
        
        public RetailOrderBuilder validateBatch(String value ){
            
            this.obj.validateBatch = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("validateBatch", value);
            return this;
        }
        
        public RetailOrderBuilder shopName(String value ){
            
            this.obj.shopName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopName", value);
            return this;
        }
        
        public RetailOrderBuilder channelNo(String value ){
            
            this.obj.channelNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("channelNo", value);
            return this;
        }
        
        public RetailOrderBuilder storeNo(String value ){
            
            this.obj.storeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }