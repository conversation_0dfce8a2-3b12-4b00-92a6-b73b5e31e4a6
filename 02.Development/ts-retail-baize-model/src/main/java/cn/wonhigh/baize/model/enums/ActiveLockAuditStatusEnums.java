package cn.wonhigh.baize.model.enums;

/**
 * TODO: 审核状态
 * 
 * <AUTHOR>
 * @date 2022年7月29日 下午4:08:48
 * @version 0.1.0 
 * @copyright Wonhigh Information Technology (Shenzhen) Co.,Ltd.
 */
public enum ActiveLockAuditStatusEnums {

	NOT_AUDIT(0, "未审核"),
	AUDIT_SUCCESS(1, "审核成功"),
	AUDIT_FAILURE(2, "审核失败");
	
	private Integer value;
	private String desc;
	
	private ActiveLockAuditStatusEnums(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public static ActiveLockAuditStatusEnums getActiveLockAuditStatusEnums(Integer status) {
		ActiveLockAuditStatusEnums[] values = ActiveLockAuditStatusEnums.values();
		for (ActiveLockAuditStatusEnums activeLockAuditStatusEnums : values) {
			if (activeLockAuditStatusEnums.getValue().equals(status)) {
				return activeLockAuditStatusEnums;
			}
		}
		return null;
	}
	
	public Integer getValue() {
		return value;
	}
	public void setValue(Integer value) {
		this.value = value;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
}
