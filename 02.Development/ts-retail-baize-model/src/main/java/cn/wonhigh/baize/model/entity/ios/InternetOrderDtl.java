/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 网销订单明细表
**/
public class InternetOrderDtl  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778666L;
    
    /**
    *尺寸编码
    **/ 
    @Label("尺寸编码") 
    private String sizeNo;
    

    public String getSizeNo(){
        return  sizeNo;
    }
    public void setSizeNo(String val ){
        sizeNo = val;
    }
    /**
    *优惠券优惠金额
    **/ 
    @Label("优惠券优惠金额") 
    private BigDecimal couponPrefAmount;
    

    public BigDecimal getCouponPrefAmount(){
        return  couponPrefAmount;
    }
    public void setCouponPrefAmount(BigDecimal val ){
        couponPrefAmount = val;
    }
    /**
    *会员优惠金额
    **/ 
    @Label("会员优惠金额") 
    private BigDecimal memberPrefAmount;
    

    public BigDecimal getMemberPrefAmount(){
        return  memberPrefAmount;
    }
    public void setMemberPrefAmount(BigDecimal val ){
        memberPrefAmount = val;
    }
    /**
    *应付运费
    **/ 
    @Label("应付运费") 
    private BigDecimal shouldPostage;
    

    public BigDecimal getShouldPostage(){
        return  shouldPostage;
    }
    public void setShouldPostage(BigDecimal val ){
        shouldPostage = val;
    }
    /**
    *下单立减优惠金额
    **/ 
    @Label("下单立减优惠金额") 
    private BigDecimal buyReductionPrefAmount;
    

    public BigDecimal getBuyReductionPrefAmount(){
        return  buyReductionPrefAmount;
    }
    public void setBuyReductionPrefAmount(BigDecimal val ){
        buyReductionPrefAmount = val;
    }
    /**
    *实际运费
    **/ 
    @Label("实际运费") 
    private BigDecimal postageCost;
    

    public BigDecimal getPostageCost(){
        return  postageCost;
    }
    public void setPostageCost(BigDecimal val ){
        postageCost = val;
    }
    /**
    *支付方式优惠金额
    **/ 
    @Label("支付方式优惠金额") 
    private BigDecimal paymentPrefAmount;
    

    public BigDecimal getPaymentPrefAmount(){
        return  paymentPrefAmount;
    }
    public void setPaymentPrefAmount(BigDecimal val ){
        paymentPrefAmount = val;
    }
    /**
    *活动优惠金额
    **/ 
    @Label("活动优惠金额") 
    private BigDecimal activePrefAmount;
    

    public BigDecimal getActivePrefAmount(){
        return  activePrefAmount;
    }
    public void setActivePrefAmount(BigDecimal val ){
        activePrefAmount = val;
    }
    /**
    *上级会员提成率
    **/ 
    @Label("上级会员提成率") 
    private BigDecimal cpercent1;
    

    public BigDecimal getCpercent1(){
        return  cpercent1;
    }
    public void setCpercent1(BigDecimal val ){
        cpercent1 = val;
    }
    /**
    *上上级会员提成率
    **/ 
    @Label("上上级会员提成率") 
    private BigDecimal cpercent2;
    

    public BigDecimal getCpercent2(){
        return  cpercent2;
    }
    public void setCpercent2(BigDecimal val ){
        cpercent2 = val;
    }
    /**
    *pos订单详情id, 回调pos接口用
    **/ 
    @Label("订单详情") 
    private String posDtlId;
    

    public String getPosDtlId(){
        return  posDtlId;
    }
    public void setPosDtlId(String val ){
        posDtlId = val;
    }
    /**
    *商品sku
    **/ 
    @Label("商品") 
    private String skuNo;
    

    public String getSkuNo(){
        return  skuNo;
    }
    public void setSkuNo(String val ){
        skuNo = val;
    }
    /**
    *发货店铺编码
    **/ 
    @Label("发货店铺编码") 
    private String sendShopNo;
    

    public String getSendShopNo(){
        return  sendShopNo;
    }
    public void setSendShopNo(String val ){
        sendShopNo = val;
    }
    /**
    *基本积分
    **/ 
    @Label("基本积分") 
    private Integer basescore;
    

    public Integer getBasescore(){
        return  basescore;
    }
    public void setBasescore(Integer val ){
        basescore = val;
    }
    /**
    *VIP优惠金额
    **/ 
    @Label("优惠金额") 
    private BigDecimal prefamountofvip;
    

    public BigDecimal getPrefamountofvip(){
        return  prefamountofvip;
    }
    public void setPrefamountofvip(BigDecimal val ){
        prefamountofvip = val;
    }
    /**
    *商品实际结算单价
    **/ 
    @Label("商品实际结算单价") 
    private BigDecimal itemactualpayprice;
    

    public BigDecimal getItemactualpayprice(){
        return  itemactualpayprice;
    }
    public void setItemactualpayprice(BigDecimal val ){
        itemactualpayprice = val;
    }
    /**
    *商品实际结算总金额
    **/ 
    @Label("商品实际结算总金额") 
    private BigDecimal itemactualpayamount;
    

    public BigDecimal getItemactualpayamount(){
        return  itemactualpayamount;
    }
    public void setItemactualpayamount(BigDecimal val ){
        itemactualpayamount = val;
    }
    /**
    *平台交易号
    **/ 
    @Label("平台交易号") 
    private String dealCode;
    

    public String getDealCode(){
        return  dealCode;
    }
    public void setDealCode(String val ){
        dealCode = val;
    }
    /**
    *品牌编码
    **/ 
    @Label("品牌编码") 
    private String brandNo;
    

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    /**
    *礼品卡分摊金额
    **/ 
    @Label("礼品卡分摊金额") 
    private BigDecimal giftCardAmount;
    

    public BigDecimal getGiftCardAmount(){
        return  giftCardAmount;
    }
    public void setGiftCardAmount(BigDecimal val ){
        giftCardAmount = val;
    }
    /**
    *顾客成交价
    **/ 
    @Label("顾客成交价") 
    private BigDecimal customerSettlePrice;
    

    public BigDecimal getCustomerSettlePrice(){
        return  customerSettlePrice;
    }
    public void setCustomerSettlePrice(BigDecimal val ){
        customerSettlePrice = val;
    }
    /**
    *活动赠送积分
    **/ 
    @Label("活动赠送积分") 
    private Integer proScore;
    

    public Integer getProScore(){
        return  proScore;
    }
    public void setProScore(Integer val ){
        proScore = val;
    }
    /**
    *消费积分
    **/ 
    @Label("消费积分") 
    private Integer costScore;
    

    public Integer getCostScore(){
        return  costScore;
    }
    public void setCostScore(Integer val ){
        costScore = val;
    }
    /**
    *优购明细ID
    **/ 
    @Label("优购明细") 
    private String yougouOrderDtlId;
    

    public String getYougouOrderDtlId(){
        return  yougouOrderDtlId;
    }
    public void setYougouOrderDtlId(String val ){
        yougouOrderDtlId = val;
    }
    /**
    *分库字段：订单hash
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *零售商品itemNo
    **/ 
    @Label("零售商品") 
    private String itemNo;
    

    public String getItemNo(){
        return  itemNo;
    }
    public void setItemNo(String val ){
        itemNo = val;
    }
    /**
    *零售商品code
    **/ 
    @Label("零售商品") 
    private String itemCode;
    

    public String getItemCode(){
        return  itemCode;
    }
    public void setItemCode(String val ){
        itemCode = val;
    }
    /**
    *商品款号
    **/ 
    @Label("商品款号") 
    private String styleNo;
    

    public String getStyleNo(){
        return  styleNo;
    }
    public void setStyleNo(String val ){
        styleNo = val;
    }
    /**
    *退货商品-红冲金额
    **/ 
    @Label("退货商品") 
    private BigDecimal redInkAmount;
    

    public BigDecimal getRedInkAmount(){
        return  redInkAmount;
    }
    public void setRedInkAmount(BigDecimal val ){
        redInkAmount = val;
    }
    /**
    *退货商品-红冲单价
    **/ 
    @Label("退货商品") 
    private BigDecimal redInkPrice;
    

    public BigDecimal getRedInkPrice(){
        return  redInkPrice;
    }
    public void setRedInkPrice(BigDecimal val ){
        redInkPrice = val;
    }
    /**
    *结算单价 结算总额/条码商品数量=结算单价
    **/ 
    @Label("结算单价") 
    private BigDecimal settlePrice;
    

    public BigDecimal getSettlePrice(){
        return  settlePrice;
    }
    public void setSettlePrice(BigDecimal val ){
        settlePrice = val;
    }
    /**
    *结算总额 含运费，含平台承担优惠
    **/ 
    @Label("结算总额") 
    private BigDecimal settleAmount;
    

    public BigDecimal getSettleAmount(){
        return  settleAmount;
    }
    public void setSettleAmount(BigDecimal val ){
        settleAmount = val;
    }
    /**
    *平台承担优惠金额
    **/ 
    @Label("平台承担优惠金额") 
    private BigDecimal platformBearAmount;
    

    public BigDecimal getPlatformBearAmount(){
        return  platformBearAmount;
    }
    public void setPlatformBearAmount(BigDecimal val ){
        platformBearAmount = val;
    }
    /**
    *优购价
    **/ 
    @Label("优购价") 
    private BigDecimal yougouPrice;
    

    public BigDecimal getYougouPrice(){
        return  yougouPrice;
    }
    public void setYougouPrice(BigDecimal val ){
        yougouPrice = val;
    }
    /**
    *是否瑕疵品可发( 1是 2否) 
    **/ 
    @Label("是否瑕疵品可发") 
    private Integer isDefectiveSent;
    

    public Integer getIsDefectiveSent(){
        return  isDefectiveSent;
    }
    public void setIsDefectiveSent(Integer val ){
        isDefectiveSent = val;
    }
    /**
    *是否禁航,0:否,1:是
    **/ 
    @Label("是否禁航") 
    private Integer flightFlag;
    

    public Integer getFlightFlag(){
        return  flightFlag;
    }
    public void setFlightFlag(Integer val ){
        flightFlag = val;
    }
    /**
    *牌价
    **/ 
    @Label("牌价") 
    private BigDecimal marketPrice;
    

    public BigDecimal getMarketPrice(){
        return  marketPrice;
    }
    public void setMarketPrice(BigDecimal val ){
        marketPrice = val;
    }
    /**
    *AD参数预发货使用
    **/ 
    @Label("参数预发货使用") 
    private Integer deliveryLine;
    

    public Integer getDeliveryLine(){
        return  deliveryLine;
    }
    public void setDeliveryLine(Integer val ){
        deliveryLine = val;
    }
    /**
    *AD物品ID
    **/ 
    @Label("物品") 
    private String articleNo;
    

    public String getArticleNo(){
        return  articleNo;
    }
    public void setArticleNo(String val ){
        articleNo = val;
    }
    /**
    *AD换货单号打印面单呼叫快递
    **/ 
    @Label("换货单号打印面单呼叫快递") 
    private String exchangeDisputeId;
    

    public String getExchangeDisputeId(){
        return  exchangeDisputeId;
    }
    public void setExchangeDisputeId(String val ){
        exchangeDisputeId = val;
    }
    /**
    *0,未退款;1,已退款
    **/ 
    @Label("未退款") 
    private Integer refundStatus;
    

    public Integer getRefundStatus(){
        return  refundStatus;
    }
    public void setRefundStatus(Integer val ){
        refundStatus = val;
    }
    /**
    *主播ID
    **/ 
    @Label("主播") 
    private String streamerId;
    

    public String getStreamerId(){
        return  streamerId;
    }
    public void setStreamerId(String val ){
        streamerId = val;
    }
    /**
    *主播名称
    **/ 
    @Label("主播名称") 
    private String streamerName;
    

    public String getStreamerName(){
        return  streamerName;
    }
    public void setStreamerName(String val ){
        streamerName = val;
    }
    /**
    *商品类型(0：普通商品 1：赠品 2：换购 3：从商品--针对加价购的促销)
    **/ 
    @Label("商品类型") 
    private Integer commoditytype;
    

    public Integer getCommoditytype(){
        return  commoditytype;
    }
    public void setCommoditytype(Integer val ){
        commoditytype = val;
    }
    /**
    *商品款色编码(供应商)
    **/ 
    @Label("商品款色编码") 
    private String supplierCode;
    

    public String getSupplierCode(){
        return  supplierCode;
    }
    public void setSupplierCode(String val ){
        supplierCode = val;
    }
    /**
    *商品颜色尺码，以逗号劈开
    **/ 
    @Label("商品颜色尺码") 
    private String commoditySpecificationStr;
    

    public String getCommoditySpecificationStr(){
        return  commoditySpecificationStr;
    }
    public void setCommoditySpecificationStr(String val ){
        commoditySpecificationStr = val;
    }
    /**
    *商品数量
    **/ 
    @Label("商品数量") 
    private Integer commodityNum;
    

    public Integer getCommodityNum(){
        return  commodityNum;
    }
    public void setCommodityNum(Integer val ){
        commodityNum = val;
    }
    /**
    *商品编码
    **/ 
    @Label("商品编码") 
    private String commodityNo;
    

    public String getCommodityNo(){
        return  commodityNo;
    }
    public void setCommodityNo(String val ){
        commodityNo = val;
    }
    /**
    *商家货品条码
    **/ 
    @Label("商家货品条码") 
    private String levelCode;
    

    public String getLevelCode(){
        return  levelCode;
    }
    public void setLevelCode(String val ){
        levelCode = val;
    }
    /**
    *货品结算总金额
    **/ 
    @Label("货品结算总金额") 
    private BigDecimal prodTotalAmt;
    

    public BigDecimal getProdTotalAmt(){
        return  prodTotalAmt;
    }
    public void setProdTotalAmt(BigDecimal val ){
        prodTotalAmt = val;
    }
    /**
    *货品单价
    **/ 
    @Label("货品单价") 
    private BigDecimal prodUnitPrice;
    

    public BigDecimal getProdUnitPrice(){
        return  prodUnitPrice;
    }
    public void setProdUnitPrice(BigDecimal val ){
        prodUnitPrice = val;
    }
    /**
    *货品名称
    **/ 
    @Label("货品名称") 
    private String prodName;
    

    public String getProdName(){
        return  prodName;
    }
    public void setProdName(String val ){
        prodName = val;
    }
    /**
    *货品编码
    **/ 
    @Label("货品编码") 
    private String prodNo;
    

    public String getProdNo(){
        return  prodNo;
    }
    public void setProdNo(String val ){
        prodNo = val;
    }
    /**
    *货品优惠总价
    **/ 
    @Label("货品优惠总价") 
    private BigDecimal prodDiscountAmount;
    

    public BigDecimal getProdDiscountAmount(){
        return  prodDiscountAmount;
    }
    public void setProdDiscountAmount(BigDecimal val ){
        prodDiscountAmount = val;
    }
    /**
    *商品图片URL
    **/ 
    @Label("商品图片") 
    private String commodityImage;
    

    public String getCommodityImage(){
        return  commodityImage;
    }
    public void setCommodityImage(String val ){
        commodityImage = val;
    }
    /**
    *子订单号
    **/ 
    @Label("子订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetOrderDtlBuilder build(){
        return new InternetOrderDtlBuilder(this);
    }

    public static class InternetOrderDtlBuilder extends AbstractEntryBuilder<InternetOrderDtl>{

        private InternetOrderDtlBuilder(InternetOrderDtl entry){
            this.obj = entry;
        }

       @Override
		public InternetOrderDtl object() {
			return this.obj;
		}

        
        public InternetOrderDtlBuilder sizeNo(String value ){
            
            this.obj.sizeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sizeNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetOrderDtlBuilder couponPrefAmount(BigDecimal value ){
            
            this.obj.couponPrefAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("couponPrefAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder memberPrefAmount(BigDecimal value ){
            
            this.obj.memberPrefAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("memberPrefAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder shouldPostage(BigDecimal value ){
            
            this.obj.shouldPostage = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shouldPostage", value);
            return this;
        }
        
        public InternetOrderDtlBuilder buyReductionPrefAmount(BigDecimal value ){
            
            this.obj.buyReductionPrefAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("buyReductionPrefAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder postageCost(BigDecimal value ){
            
            this.obj.postageCost = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("postageCost", value);
            return this;
        }
        
        public InternetOrderDtlBuilder paymentPrefAmount(BigDecimal value ){
            
            this.obj.paymentPrefAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("paymentPrefAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder activePrefAmount(BigDecimal value ){
            
            this.obj.activePrefAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("activePrefAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder cpercent1(BigDecimal value ){
            
            this.obj.cpercent1 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cpercent1", value);
            return this;
        }
        
        public InternetOrderDtlBuilder cpercent2(BigDecimal value ){
            
            this.obj.cpercent2 = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cpercent2", value);
            return this;
        }
        
        public InternetOrderDtlBuilder posDtlId(String value ){
            
            this.obj.posDtlId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("posDtlId", value);
            return this;
        }
        
        public InternetOrderDtlBuilder skuNo(String value ){
            
            this.obj.skuNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder sendShopNo(String value ){
            
            this.obj.sendShopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendShopNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder basescore(Integer value ){
            
            this.obj.basescore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("basescore", value);
            return this;
        }
        
        public InternetOrderDtlBuilder prefamountofvip(BigDecimal value ){
            
            this.obj.prefamountofvip = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prefamountofvip", value);
            return this;
        }
        
        public InternetOrderDtlBuilder itemactualpayprice(BigDecimal value ){
            
            this.obj.itemactualpayprice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemactualpayprice", value);
            return this;
        }
        
        public InternetOrderDtlBuilder itemactualpayamount(BigDecimal value ){
            
            this.obj.itemactualpayamount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemactualpayamount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder dealCode(String value ){
            
            this.obj.dealCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("dealCode", value);
            return this;
        }
        
        public InternetOrderDtlBuilder brandNo(String value ){
            
            this.obj.brandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder giftCardAmount(BigDecimal value ){
            
            this.obj.giftCardAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("giftCardAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder customerSettlePrice(BigDecimal value ){
            
            this.obj.customerSettlePrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customerSettlePrice", value);
            return this;
        }
        
        public InternetOrderDtlBuilder proScore(Integer value ){
            
            this.obj.proScore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("proScore", value);
            return this;
        }
        
        public InternetOrderDtlBuilder costScore(Integer value ){
            
            this.obj.costScore = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("costScore", value);
            return this;
        }
        
        public InternetOrderDtlBuilder yougouOrderDtlId(String value ){
            
            this.obj.yougouOrderDtlId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("yougouOrderDtlId", value);
            return this;
        }
        
        public InternetOrderDtlBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public InternetOrderDtlBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetOrderDtlBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public InternetOrderDtlBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetOrderDtlBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetOrderDtlBuilder itemNo(String value ){
            
            this.obj.itemNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder itemCode(String value ){
            
            this.obj.itemCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("itemCode", value);
            return this;
        }
        
        public InternetOrderDtlBuilder styleNo(String value ){
            
            this.obj.styleNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("styleNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder redInkAmount(BigDecimal value ){
            
            this.obj.redInkAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("redInkAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder redInkPrice(BigDecimal value ){
            
            this.obj.redInkPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("redInkPrice", value);
            return this;
        }
        
        public InternetOrderDtlBuilder settlePrice(BigDecimal value ){
            
            this.obj.settlePrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("settlePrice", value);
            return this;
        }
        
        public InternetOrderDtlBuilder settleAmount(BigDecimal value ){
            
            this.obj.settleAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("settleAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder platformBearAmount(BigDecimal value ){
            
            this.obj.platformBearAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("platformBearAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder yougouPrice(BigDecimal value ){
            
            this.obj.yougouPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("yougouPrice", value);
            return this;
        }
        
        public InternetOrderDtlBuilder isDefectiveSent(Integer value ){
            
            this.obj.isDefectiveSent = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isDefectiveSent", value);
            return this;
        }
        
        public InternetOrderDtlBuilder flightFlag(Integer value ){
            
            this.obj.flightFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("flightFlag", value);
            return this;
        }
        
        public InternetOrderDtlBuilder marketPrice(BigDecimal value ){
            
            this.obj.marketPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("marketPrice", value);
            return this;
        }
        
        public InternetOrderDtlBuilder deliveryLine(Integer value ){
            
            this.obj.deliveryLine = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("deliveryLine", value);
            return this;
        }
        
        public InternetOrderDtlBuilder articleNo(String value ){
            
            this.obj.articleNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("articleNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder exchangeDisputeId(String value ){
            
            this.obj.exchangeDisputeId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("exchangeDisputeId", value);
            return this;
        }
        
        public InternetOrderDtlBuilder refundStatus(Integer value ){
            
            this.obj.refundStatus = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refundStatus", value);
            return this;
        }
        
        public InternetOrderDtlBuilder streamerId(String value ){
            
            this.obj.streamerId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("streamerId", value);
            return this;
        }
        
        public InternetOrderDtlBuilder streamerName(String value ){
            
            this.obj.streamerName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("streamerName", value);
            return this;
        }
        
        public InternetOrderDtlBuilder commoditytype(Integer value ){
            
            this.obj.commoditytype = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commoditytype", value);
            return this;
        }
        
        public InternetOrderDtlBuilder supplierCode(String value ){
            
            this.obj.supplierCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("supplierCode", value);
            return this;
        }
        
        public InternetOrderDtlBuilder commoditySpecificationStr(String value ){
            
            this.obj.commoditySpecificationStr = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commoditySpecificationStr", value);
            return this;
        }
        
        public InternetOrderDtlBuilder commodityNum(Integer value ){
            
            this.obj.commodityNum = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commodityNum", value);
            return this;
        }
        
        public InternetOrderDtlBuilder commodityNo(String value ){
            
            this.obj.commodityNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commodityNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder levelCode(String value ){
            
            this.obj.levelCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("levelCode", value);
            return this;
        }
        
        public InternetOrderDtlBuilder prodTotalAmt(BigDecimal value ){
            
            this.obj.prodTotalAmt = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodTotalAmt", value);
            return this;
        }
        
        public InternetOrderDtlBuilder prodUnitPrice(BigDecimal value ){
            
            this.obj.prodUnitPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodUnitPrice", value);
            return this;
        }
        
        public InternetOrderDtlBuilder prodName(String value ){
            
            this.obj.prodName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodName", value);
            return this;
        }
        
        public InternetOrderDtlBuilder prodNo(String value ){
            
            this.obj.prodNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodNo", value);
            return this;
        }
        
        public InternetOrderDtlBuilder prodDiscountAmount(BigDecimal value ){
            
            this.obj.prodDiscountAmount = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("prodDiscountAmount", value);
            return this;
        }
        
        public InternetOrderDtlBuilder commodityImage(String value ){
            
            this.obj.commodityImage = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("commodityImage", value);
            return this;
        }
        
        public InternetOrderDtlBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }