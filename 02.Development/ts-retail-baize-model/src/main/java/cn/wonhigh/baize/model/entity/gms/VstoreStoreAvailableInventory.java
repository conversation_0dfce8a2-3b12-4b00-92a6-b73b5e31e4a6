package cn.wonhigh.baize.model.entity.gms;

import java.io.Serializable;

public class VstoreStoreAvailableInventory implements Serializable {

	private static final long serialVersionUID = 8507330494357214925L;

	/**
	 * 聚合仓编码
	 */
	private String vstoreCode;
	/**
	 * 聚合仓名称
	 */
	private String vstoreName;

	/**
	 * 机构编码
	 */
	private String storeNo;
	/**
	 * 机构名称
	 */
	private String storeName;

	private String orderUnitNo;

	private String orderUnitName;

	/**
	 * 品牌编码
	 */
	private String brandNo;
	/**
	 * 商品编码
	 */
	private String itemCode;
	/**
	 * 尺寸编码
	 */
	private String sizeNo;
	/**
	 * sku编码
	 */
	private String skuNo;
	/**
	 * 可售库存
	 */
	private int availableQty;
	/**
	 * 线上不可派数量
	 */
	private int defectiveGoodsQty;
	/**
	 * 门店bc品
	 */
	private int workQty;
	/**
	 * 是否保护店 1是
	 */
	private int unavailableShopFlag;
	/**
	 * 是否拒单 1是
	 */
	private int refuseRecordFlag;

	/**
	 * 门店信用分
	 */
	private Integer creditScore;


	public String getVstoreCode() {
		return vstoreCode;
	}

	public void setVstoreCode(String vstoreCode) {
		this.vstoreCode = vstoreCode;
	}

	public String getVstoreName() {
		return vstoreName;
	}

	public void setVstoreName(String vstoreName) {
		this.vstoreName = vstoreName;
	}

	public String getStoreNo() {
		return storeNo;
	}

	public void setStoreNo(String storeNo) {
		this.storeNo = storeNo;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}


	public String getOrderUnitNo() {
		return orderUnitNo;
	}

	public void setOrderUnitNo(String orderUnitNo) {
		this.orderUnitNo = orderUnitNo;
	}

	public String getOrderUnitName() {
		return orderUnitName;
	}

	public void setOrderUnitName(String orderUnitName) {
		this.orderUnitName = orderUnitName;
	}

	public String getBrandNo() {
		return brandNo;
	}

	public void setBrandNo(String brandNo) {
		this.brandNo = brandNo;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public String getSizeNo() {
		return sizeNo;
	}

	public void setSizeNo(String sizeNo) {
		this.sizeNo = sizeNo;
	}

	public String getSkuNo() {
		return skuNo;
	}

	public void setSkuNo(String skuNo) {
		this.skuNo = skuNo;
	}

	public int getAvailableQty() {
		return availableQty;
	}

	public void setAvailableQty(int availableQty) {
		this.availableQty = availableQty;
	}

	public int getDefectiveGoodsQty() {
		return defectiveGoodsQty;
	}

	public void setDefectiveGoodsQty(int defectiveGoodsQty) {
		this.defectiveGoodsQty = defectiveGoodsQty;
	}

	public int getWorkQty() {
		return workQty;
	}

	public void setWorkQty(int workQty) {
		this.workQty = workQty;
	}

	public int getUnavailableShopFlag() {
		return unavailableShopFlag;
	}

	public void setUnavailableShopFlag(int unavailableShopFlag) {
		this.unavailableShopFlag = unavailableShopFlag;
	}

	public int getRefuseRecordFlag() {
		return refuseRecordFlag;
	}

	public void setRefuseRecordFlag(int refuseRecordFlag) {
		this.refuseRecordFlag = refuseRecordFlag;
	}

	public Integer getCreditScore() {
		return creditScore;
	}

	public void setCreditScore(Integer creditScore) {
		this.creditScore = creditScore;
	}
}
