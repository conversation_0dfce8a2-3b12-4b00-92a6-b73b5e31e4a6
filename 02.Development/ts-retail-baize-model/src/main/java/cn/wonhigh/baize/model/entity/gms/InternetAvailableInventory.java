package cn.wonhigh.baize.model.entity.gms;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang.builder.ReflectionToStringBuilder;

import java.io.Serializable;

/**
 * 请写出类的用途
 * 
 * <AUTHOR>
 * @date 2016-06-04 11:51:34
 * @version 1.0.0
 * @copyright (C) 2013 YouGou Information Technology Co.,Ltd All Rights
 *            Reserved.
 * 
 *            The software for the YouGou technology development, without the
 *            company's written consent, and any other individuals and
 *            organizations shall not be used, Copying, Modify or distribute the
 *            software.
 * 
 */
public class InternetAvailableInventory implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -872396073313007221L;

	private String shardingFlag;
	/** 库存类型 **/
	private Integer inventoryType;
	/** (店铺+仓库)总库存数量 */
	private Integer availableQty;
	/** 店铺库存数量 */
	private Integer availableQtyInShop;
	/** 仓库库存数量 */
	private Integer availableQtyInStore;

	private Integer availableQtyOnlyInStore;

	private Integer level;

	private String storeName;

	private String storeNo;

	private String orderUnitNo;

	private Integer balanceQty;

	private String orderUnitName;
	/**
	 * sku编码
	 */
	private String skuNo;

	/**
	 * 商品内码
	 */
	private String itemNo;

	/**
	 * 品牌编码
	 */
	private String brandNo;

	/** 尺寸编号 */
	private String sizeNo;
	/** 商品编码 */
	private String itemCode;
	/** 商品名称 */
	private String itemName;
	/** 颜色编码 */
	private String colorNo;
	/** 颜色名称 */
	private String colorName;
	/** 品牌编码 */
	private String brandName;
	/** 商品尺寸分类 */
	private String sizeKind;
	/** 商品类别编码 */
	private String categoryNo;

	private String barcode;

	private String companyNo;
	/** 店铺地址 */
	private String shopAddress;
	/** 店铺电话 */
	private String shopTel;
	/** 店铺名称 */
	private String shopName;
	/** 销售虚店铺编码（优购o2o用到 */
	private String saleShopNo;
	/** 销售虚店铺名称（优购o2o用到） */
	private String saleShopName;
	/** 店/仓 的省编号 */
	private String provinceNo;
	/** 店/仓 的市编号 */
	private String cityNo;
	/** skuCount 满足条件sku数量 */
	private Integer skuCount;
	/** 同一个包裹 0-是 1-否 */
	private Integer onlyOnePage;
	/** sku列表 */
	private String skuList;
	/** 商品对应的库存信息 */
	private String skuQtyList;
	/** 不共享库存 */
	private Integer unShareQty;
	/** 发货仓类型, 仓/店类型 */
	private String storeType;
	/** 地区 */
	private String zoneNo;
	/** 地区库存 */
	private Integer zoneQty = 0;

	public String getUniqueKey() {
		return  storeNo +"_"+ orderUnitNo +"_"+ skuNo;
	}

	public Integer getAvailableQtyOnlyInStore() {
		return availableQtyOnlyInStore;
	}

	/**
	 * @return the inventoryType
	 */
	public Integer getInventoryType() {
		return inventoryType;
	}

	/**
	 * @param inventoryType the inventoryType to set
	 */
	public void setInventoryType(Integer inventoryType) {
		this.inventoryType = inventoryType;
	}

	public void setAvailableQtyOnlyInStore(Integer availableQtyOnlyInStore) {
		this.availableQtyOnlyInStore = availableQtyOnlyInStore;
	}

	public Integer getBalanceQty() {
		return balanceQty;
	}

	public void setBalanceQty(Integer balanceQty) {
		this.balanceQty = balanceQty;
	}

	public String getCompanyNo() {
		return companyNo;
	}

	public void setCompanyNo(String companyNo) {
		this.companyNo = companyNo;
	}

	public String getSizeNo() {
		return sizeNo;
	}

	public void setSizeNo(String sizeNo) {
		this.sizeNo = sizeNo;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public Integer getAvailableQty() {
		return availableQty;
	}

	public void setAvailableQty(Integer availableQty) {
		this.availableQty = availableQty;
	}

	public String getSkuNo() {
		return skuNo;
	}

	public void setSkuNo(String skuNo) {
		this.skuNo = skuNo;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public String getStoreNo() {
		return storeNo;
	}

	public void setStoreNo(String storeNo) {
		this.storeNo = storeNo;
	}

	public String getOrderUnitNo() {
		return orderUnitNo;
	}

	public void setOrderUnitNo(String orderUnitNo) {
		this.orderUnitNo = orderUnitNo;
	}

	public String getOrderUnitName() {
		return orderUnitName;
	}

	public void setOrderUnitName(String orderUnitName) {
		this.orderUnitName = orderUnitName;
	}

	private String keyForBillNt;

	@JsonIgnore
	public String getKeyForBillNt() {
		if (keyForBillNt == null) {
			keyForBillNt = storeNo + "_" + orderUnitNo;
		}
		return keyForBillNt;
	}

	/**
	 * 跟BillInternetOrderDtl保持一致
	 * 
	 * @return
	 */
	@JsonIgnore
	public String getSkuKey() {
		return skuNo;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getBrandNo() {
		return brandNo;
	}

	public void setBrandNo(String brandNo) {
		this.brandNo = brandNo;
	}

	public String getColorNo() {
		return colorNo;
	}

	public void setColorNo(String colorNo) {
		this.colorNo = colorNo;
	}

	public String getColorName() {
		return colorName;
	}

	public void setColorName(String colorName) {
		this.colorName = colorName;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getSizeKind() {
		return sizeKind;
	}

	public void setSizeKind(String sizeKind) {
		this.sizeKind = sizeKind;
	}

	public String getCategoryNo() {
		return categoryNo;
	}

	public void setCategoryNo(String categoryNo) {
		this.categoryNo = categoryNo;
	}

	public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	public Integer getAvailableQtyInShop() {
		return availableQtyInShop;
	}

	public void setAvailableQtyInShop(Integer availableQtyInShop) {
		this.availableQtyInShop = availableQtyInShop;
	}

	public Integer getAvailableQtyInStore() {
		return availableQtyInStore;
	}

	public void setAvailableQtyInStore(Integer availableQtyInStore) {
		this.availableQtyInStore = availableQtyInStore;
	}

	public String getShopAddress() {
		return shopAddress;
	}

	public void setShopAddress(String shopAddress) {
		this.shopAddress = shopAddress;
	}

	public String getShopTel() {
		return shopTel;
	}

	public void setShopTel(String shopTel) {
		this.shopTel = shopTel;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public String getSaleShopNo() {
		return saleShopNo;
	}

	public void setSaleShopNo(String saleShopNo) {
		this.saleShopNo = saleShopNo;
	}

	public String getSaleShopName() {
		return saleShopName;
	}

	public void setSaleShopName(String saleShopName) {
		this.saleShopName = saleShopName;
	}

	public String getProvinceNo() {
		return provinceNo;
	}

	public void setProvinceNo(String provinceNo) {
		this.provinceNo = provinceNo;
	}

	public String getCityNo() {
		return cityNo;
	}

	public void setCityNo(String cityNo) {
		this.cityNo = cityNo;
	}

	public Integer getSkuCount() {
		return skuCount;
	}

	public void setSkuCount(Integer skuCount) {
		this.skuCount = skuCount;
	}

	/**
	 * @return the skuList
	 */
	public String getSkuList() {
		return skuList;
	}

	/**
	 * @param skuList the skuList to set
	 */
	public void setSkuList(String skuList) {
		this.skuList = skuList;
	}

	/**
	 * @return the skuQtyList
	 */
	public String getSkuQtyList() {
		return skuQtyList;
	}

	/**
	 * @param skuQtyList the skuQtyList to set
	 */
	public void setSkuQtyList(String skuQtyList) {
		this.skuQtyList = skuQtyList;
	}

	/**
	 * @return the unShareQty
	 */
	public Integer getUnShareQty() {
		return unShareQty;
	}

	/**
	 * @param unShareQty the unShareQty to set
	 */
	public void setUnShareQty(Integer unShareQty) {
		this.unShareQty = unShareQty;
	}

	/**
	 * @return the storeType
	 */
	public String getStoreType() {
		return storeType;
	}

	/**
	 * @param storeType the storeType to set
	 */
	public void setStoreType(String storeType) {
		this.storeType = storeType;
	}

	/**
	 * @return the onlyOnePage
	 */
	public Integer getOnlyOnePage() {
		return onlyOnePage;
	}

	/**
	 * @param onlyOnePage the onlyOnePage to set
	 */
	public void setOnlyOnePage(Integer onlyOnePage) {
		this.onlyOnePage = onlyOnePage;
	}
	
	public String getZoneNo() {
		return zoneNo;
	}

	public void setZoneNo(String zoneNo) {
		this.zoneNo = zoneNo;
	}

	public Integer getZoneQty() {
		return zoneQty;
	}

	public void setZoneQty(Integer zoneQty) {
		this.zoneQty = zoneQty;
	}

	public String getShardingFlag() {
		return shardingFlag;
	}

	public void setShardingFlag(String shardingFlag) {
		this.shardingFlag = shardingFlag;
	}

	public void setKeyForBillNt(String keyForBillNt) {
		this.keyForBillNt = keyForBillNt;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}
}