package cn.wonhigh.baize.model.entity.ios;

import cn.mercury.domain.BasicEntity;

import java.util.Date;

/**
 * @author: wudong
 * @create: 2025-07-14 11:10
 **/
public class AsyncTaskCompleted  extends BasicEntity {
    private Integer taskType;
    private String billNo;
    private Integer billType;
    private String refBillNo;
    private Integer refBillType;
    private Integer preTaskType;
    private Byte preTaskSearchMode;
    private String validateBatch;
    private Integer batch;
    private Integer status;
    private String shardingFlag;
    private String errorMsg;
    private String hostInfo;
    private String remark;
    private String sentBatch;
    private String sentType;
    private Date executeTime;
    private Integer elapsedTime;

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public String getRefBillNo() {
        return refBillNo;
    }

    public void setRefBillNo(String refBillNo) {
        this.refBillNo = refBillNo;
    }

    public Integer getRefBillType() {
        return refBillType;
    }

    public void setRefBillType(Integer refBillType) {
        this.refBillType = refBillType;
    }

    public Integer getPreTaskType() {
        return preTaskType;
    }

    public void setPreTaskType(Integer preTaskType) {
        this.preTaskType = preTaskType;
    }

    public Byte getPreTaskSearchMode() {
        return preTaskSearchMode;
    }

    public void setPreTaskSearchMode(Byte preTaskSearchMode) {
        this.preTaskSearchMode = preTaskSearchMode;
    }

    public String getValidateBatch() {
        return validateBatch;
    }

    public void setValidateBatch(String validateBatch) {
        this.validateBatch = validateBatch;
    }

    public Integer getBatch() {
        return batch;
    }

    public void setBatch(Integer batch) {
        this.batch = batch;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getShardingFlag() {
        return shardingFlag;
    }

    public void setShardingFlag(String shardingFlag) {
        this.shardingFlag = shardingFlag;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getHostInfo() {
        return hostInfo;
    }

    public void setHostInfo(String hostInfo) {
        this.hostInfo = hostInfo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSentBatch() {
        return sentBatch;
    }

    public void setSentBatch(String sentBatch) {
        this.sentBatch = sentBatch;
    }

    public String getSentType() {
        return sentType;
    }

    public void setSentType(String sentType) {
        this.sentType = sentType;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }

    public Integer getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Integer elapsedTime) {
        this.elapsedTime = elapsedTime;
    }
}
