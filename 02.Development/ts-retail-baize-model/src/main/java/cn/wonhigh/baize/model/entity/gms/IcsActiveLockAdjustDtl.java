/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 活动锁库调整单明细
**/
public class IcsActiveLockAdjustDtl  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1749440735281L;

    //尺码
    @Label("尺码") 
    private String sizeNo;
    
    //商品编码
    @Label("商品编码") 
    private String itemCode;
    
    //品牌编码
    @Label("品牌编码") 
    private String brandNo;
    
    //机构名称
    @Label("机构名称") 
    private String storeName;
    
    //锁库调整单号
    @Label("锁库调整单号") 
    private String billNo;
    
    //同步状态 0未同步 1同步成功，2同步失败
    @Label(value = "同步状态", defaultVal = "0") 
    private Integer syncStatus;
    
    //机构编码
    @Label("机构编码") 
    private String storeNo;
    
    //条码
    @Label("条码") 
    private String barcode;
    
    //货管编码
    @Label("货管编码") 
    private String orderUnitNo;
    
    //货管名称
    @Label("货管名称") 
    private String orderUnitName;
    
    //调整数量
    @Label(value = "调整数量", defaultVal = "0") 
    private Integer adjustQty;
    
    //sku编码
    @Label("编码") 
    private String skuNo;

    //剩余库存数量
    private Integer balanceLockQty;

    //锁库数量
    private Integer lockQty;

    //验证结果
    private String verifyResult;

    //错误信息
    private String errorMessage;

    public String getUniqueKey() {
        return  storeNo +"_"+ orderUnitNo +"_"+ skuNo;
    }

    public String getVerifyResult() {
        return verifyResult;
    }

    public void setVerifyResult(String verifyResult) {
        this.verifyResult = verifyResult;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getLockQty() {
        return lockQty;
    }

    public void setLockQty(Integer lockQty) {
        this.lockQty = lockQty;
    }

    public Integer getBalanceLockQty() {
        return balanceLockQty;
    }

    public void setBalanceLockQty(Integer balanceLockQty) {
        this.balanceLockQty = balanceLockQty;
    }

    public String getSizeNo(){
        return  sizeNo;
    }
    public void setSizeNo(String val ){
        sizeNo = val;
    }
    
    public String getItemCode(){
        return  itemCode;
    }
    public void setItemCode(String val ){
        itemCode = val;
    }
    
    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    
    public String getStoreName(){
        return  storeName;
    }
    public void setStoreName(String val ){
        storeName = val;
    }
    
    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    
    public Integer getSyncStatus(){
        return  syncStatus;
    }
    public void setSyncStatus(Integer val ){
        syncStatus = val;
    }
    
    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    
    public String getBarcode(){
        return  barcode;
    }
    public void setBarcode(String val ){
        barcode = val;
    }
    
    public String getOrderUnitNo(){
        return  orderUnitNo;
    }
    public void setOrderUnitNo(String val ){
        orderUnitNo = val;
    }
    
    public String getOrderUnitName(){
        return  orderUnitName;
    }
    public void setOrderUnitName(String val ){
        orderUnitName = val;
    }
    
    public Integer getAdjustQty(){
        return  adjustQty;
    }
    public void setAdjustQty(Integer val ){
        adjustQty = val;
    }
    
    public String getSkuNo(){
        return  skuNo;
    }
    public void setSkuNo(String val ){
        skuNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public IcsActiveLockAdjustDtlBuilder build(){
        return new IcsActiveLockAdjustDtlBuilder(this);
    }

    public static class IcsActiveLockAdjustDtlBuilder extends AbstractEntryBuilder<IcsActiveLockAdjustDtl>{

        private IcsActiveLockAdjustDtlBuilder(IcsActiveLockAdjustDtl entry){
            this.obj = entry;
        }

       @Override
		public IcsActiveLockAdjustDtl object() {
			return this.obj;
		}

        
        public IcsActiveLockAdjustDtlBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder sizeNo(String value ){
            this.obj.sizeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("sizeNo", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder itemCode(String value ){
            this.obj.itemCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("itemCode", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder brandNo(String value ){
            this.obj.brandNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder storeName(String value ){
            this.obj.storeName = value;
            if( query == null  )
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder billNo(String value ){
            this.obj.billNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder syncStatus(Integer value ){
            this.obj.syncStatus = value;
            if( query == null  )
                query = new Query();
            this.query.where("syncStatus", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder storeNo(String value ){
            this.obj.storeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder barcode(String value ){
            this.obj.barcode = value;
            if( query == null  )
                query = new Query();
            this.query.where("barcode", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder orderUnitNo(String value ){
            this.obj.orderUnitNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder orderUnitName(String value ){
            this.obj.orderUnitName = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitName", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder adjustQty(Integer value ){
            this.obj.adjustQty = value;
            if( query == null  )
                query = new Query();
            this.query.where("adjustQty", value);
            return this;
        }
        
        public IcsActiveLockAdjustDtlBuilder skuNo(String value ){
            this.obj.skuNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }

    }
/** auto generate end,don't modify */
}