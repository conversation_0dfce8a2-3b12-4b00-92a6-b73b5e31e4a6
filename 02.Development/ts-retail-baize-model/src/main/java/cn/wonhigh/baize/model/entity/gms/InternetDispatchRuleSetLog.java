/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.IEntryBuildable;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 派单规则操作日志表
**/
public class InternetDispatchRuleSetLog  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1688711695690L;
    
    /**
    *platform平台编码(-&#62;业务模式)
    **/ 
    @Label("平台编码") 
    private String platformNo;
    

    public String getPlatformNo(){
        return  platformNo;
    }
    public void setPlatformNo(String val ){
        platformNo = val;
    }
    /**
    *操作时间
    **/ 
    @Label("操作时间") 
    private Date operationTime;
    

    public Date getOperationTime(){
        return  operationTime;
    }
    public void setOperationTime(Date val ){
        operationTime = val;
    }
    /**
    *派单规则配置的ID
    **/ 
    @Label("派单规则配置的") 
    private Integer parentId;
    

    public Integer getParentId(){
        return  parentId;
    }
    public void setParentId(Integer val ){
        parentId = val;
    }
    /**
    *关闭的派单规则
    **/ 
    @Label("关闭的派单规则") 
    private String closeRuleStr;
    

    public String getCloseRuleStr(){
        return  closeRuleStr;
    }
    public void setCloseRuleStr(String val ){
        closeRuleStr = val;
    }
    /**
    *开启的派单规则
    **/ 
    @Label("开启的派单规则") 
    private String openRuleStr;
    

    public String getOpenRuleStr(){
        return  openRuleStr;
    }
    public void setOpenRuleStr(String val ){
        openRuleStr = val;
    }
    /**
    *操作人
    **/ 
    @Label("操作人") 
    private String operationUser;
    

    public String getOperationUser(){
        return  operationUser;
    }
    public void setOperationUser(String val ){
        operationUser = val;
    }
    /**
    *品牌名称
    **/ 
    @Label("品牌名称") 
    private String brandName;
    

    public String getBrandName(){
        return  brandName;
    }
    public void setBrandName(String val ){
        brandName = val;
    }
    /**
    *品牌编码
    **/ 
    @Label("品牌编码") 
    private String brandNo;
    

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    /**
    *本部名称
    **/ 
    @Label("本部名称") 
    private String organTypeName;
    

    public String getOrganTypeName(){
        return  organTypeName;
    }
    public void setOrganTypeName(String val ){
        organTypeName = val;
    }
    /**
    *本部编码
    **/ 
    @Label("本部编码") 
    private String organTypeNo;
    

    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    /**
    *platform平台名称(-&#62;业务模式)
    **/ 
    @Label("平台名称") 
    private String platformName;
    

    public String getPlatformName(){
        return  platformName;
    }
    public void setPlatformName(String val ){
        platformName = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetDispatchRuleSetLogBuilder build(){
        return new InternetDispatchRuleSetLogBuilder(this);
    }

    public static class InternetDispatchRuleSetLogBuilder extends AbstractEntryBuilder<InternetDispatchRuleSetLog>{

        private InternetDispatchRuleSetLogBuilder(InternetDispatchRuleSetLog entry){
            this.obj = entry;
        }

       @Override
		public InternetDispatchRuleSetLog object() {
			return this.obj;
		}

        
        public InternetDispatchRuleSetLogBuilder platformNo(String value ){
            
            this.obj.platformNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("platformNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder operationTime(Date value ){
            
            this.obj.operationTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("operationTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder parentId(Integer value ){
            
            this.obj.parentId = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parentId", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder closeRuleStr(String value ){
            
            this.obj.closeRuleStr = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("closeRuleStr", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder openRuleStr(String value ){
            
            this.obj.openRuleStr = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("openRuleStr", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder operationUser(String value ){
            
            this.obj.operationUser = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("operationUser", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder brandName(String value ){
            
            this.obj.brandName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandName", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder brandNo(String value ){
            
            this.obj.brandNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder organTypeName(String value ){
            
            this.obj.organTypeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeName", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder organTypeNo(String value ){
            
            this.obj.organTypeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetLogBuilder platformName(String value ){
            
            this.obj.platformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("platformName", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }