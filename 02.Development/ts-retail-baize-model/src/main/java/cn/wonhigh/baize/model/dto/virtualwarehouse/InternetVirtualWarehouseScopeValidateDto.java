package cn.wonhigh.baize.model.dto.virtualwarehouse;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class InternetVirtualWarehouseScopeValidateDto {

    @NotBlank(message = "vstoreCode")
    private String vstoreCode;

    @NotNull(message = "storeType")
    private Integer storeType;

    @NotNull(message = "moreStoreFlag")
    private Integer moreStoreFlag;
    private String storeNo;

    @NotBlank(message = "orderUnitNo")
    private String orderUnitNo;

    @NotNull(message = "inventoryType")
    private Integer inventoryType;

    @NotNull(message = "status")
    private Integer status;


    public InternetVirtualWarehouseScopeValidateDto(String vstoreCode, Integer storeType, Integer moreStoreFlag, String storeNo, String orderUnitNo, Integer inventoryType, Integer status) {
        this.vstoreCode = vstoreCode;
        this.storeType = storeType;
        this.moreStoreFlag = moreStoreFlag;
        this.storeNo = storeNo;
        this.orderUnitNo = orderUnitNo;
        this.inventoryType = inventoryType;
        this.status = status;
    }

    public String getVstoreCode() {
        return vstoreCode;
    }

    public void setVstoreCode(String vstoreCode) {
        this.vstoreCode = vstoreCode;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public Integer getMoreStoreFlag() {
        return moreStoreFlag;
    }

    public void setMoreStoreFlag(Integer moreStoreFlag) {
        this.moreStoreFlag = moreStoreFlag;
    }

    public String getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(String storeNo) {
        this.storeNo = storeNo;
    }

    public String getOrderUnitNo() {
        return orderUnitNo;
    }

    public void setOrderUnitNo(String orderUnitNo) {
        this.orderUnitNo = orderUnitNo;
    }

    public Integer getInventoryType() {
        return inventoryType;
    }

    public void setInventoryType(Integer inventoryType) {
        this.inventoryType = inventoryType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
