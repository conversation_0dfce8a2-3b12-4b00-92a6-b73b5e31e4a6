/** q **/
package cn.wonhigh.baize.model.entity.gms;

import cn.mercury.domain.BasicEntity;
import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 主数据与优购货品对应关系表
**/
public class CommodityCorpMatchProduct  extends BasicEntity {

    private static final long serialVersionUID = 1727246213936L;
    
    //主数据品牌编号
    @Label("主数据品牌编号") 
    private String corpBrandNo;
    
    //款色编码
    @Label("款色编码") 
    private String styleColorCode;
    
    //集团主数据 尺码 code
    @Label("集团主数据") 
    private String corpSizeCode;
    
    //商品条码
    @Label("商品条码") 
    private String insideBarcode;
    
    //商品条码ID
    @Label("商品条码") 
    private String insideBarcodeId;
    
    //是否通库存1-可通0-不可通
    @Label(value = "是否通库存", defaultVal = "1") 
    private Integer isSyncInv;

    private String isSyncInvName;

    public String getIsSyncInvName() {
        return isSyncInvName;
    }

    public void setIsSyncInvName(String isSyncInvName) {
        this.isSyncInvName = isSyncInvName;
    }

    public String getCorpBrandNo(){
        return  corpBrandNo;
    }
    public void setCorpBrandNo(String val ){
        corpBrandNo = val;
    }
    
    public String getStyleColorCode(){
        return  styleColorCode;
    }
    public void setStyleColorCode(String val ){
        styleColorCode = val;
    }
    
    public String getCorpSizeCode(){
        return  corpSizeCode;
    }
    public void setCorpSizeCode(String val ){
        corpSizeCode = val;
    }
    
    public String getInsideBarcode(){
        return  insideBarcode;
    }
    public void setInsideBarcode(String val ){
        insideBarcode = val;
    }
    
    public String getInsideBarcodeId(){
        return  insideBarcodeId;
    }
    public void setInsideBarcodeId(String val ){
        insideBarcodeId = val;
    }
    
    public Integer getIsSyncInv(){
        return  isSyncInv;
    }
    public void setIsSyncInv(Integer val ){
        isSyncInv = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public CommodityCorpMatchProductBuilder build(){
        return new CommodityCorpMatchProductBuilder(this);
    }

    public static class CommodityCorpMatchProductBuilder extends AbstractEntryBuilder<CommodityCorpMatchProduct>{

        private CommodityCorpMatchProductBuilder(CommodityCorpMatchProduct entry){
            this.obj = entry;
        }

       @Override
		public CommodityCorpMatchProduct object() {
			return this.obj;
		}

        
        public CommodityCorpMatchProductBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public CommodityCorpMatchProductBuilder corpBrandNo(String value ){
            this.obj.corpBrandNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("corpBrandNo", value);
            return this;
        }
        
        public CommodityCorpMatchProductBuilder styleColorCode(String value ){
            this.obj.styleColorCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("styleColorCode", value);
            return this;
        }
        
        public CommodityCorpMatchProductBuilder corpSizeCode(String value ){
            this.obj.corpSizeCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("corpSizeCode", value);
            return this;
        }
        
        public CommodityCorpMatchProductBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public CommodityCorpMatchProductBuilder insideBarcode(String value ){
            this.obj.insideBarcode = value;
            if( query == null  )
                query = new Query();
            this.query.where("insideBarcode", value);
            return this;
        }
        
        public CommodityCorpMatchProductBuilder insideBarcodeId(String value ){
            this.obj.insideBarcodeId = value;
            if( query == null  )
                query = new Query();
            this.query.where("insideBarcodeId", value);
            return this;
        }
        
        public CommodityCorpMatchProductBuilder isSyncInv(Integer value ){
            this.obj.isSyncInv = value;
            if( query == null  )
                query = new Query();
            this.query.where("isSyncInv", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}