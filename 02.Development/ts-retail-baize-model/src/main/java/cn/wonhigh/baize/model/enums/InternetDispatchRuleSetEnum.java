package cn.wonhigh.baize.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum InternetDispatchRuleSetEnum implements Serializable {

    AVG_PRIORITY(1,  0,  "avg_state", "avg_priority", "均匀派单"),
    NEAR_PRIORITY(2,  1, "near_state","near_priority", "就近"),
    ACTION_PRIORITY(3,  0, "action_state","action_priority", "动销派单"),
    INVENTORY_PRIORITY(4,  1, "inventory_state","inventory_priority", "最大库存"),
    STORE_PRIORITY(5,  1,"store_state", "store_priority", "仓优先"),
    SHOP_PRIORITY(6,  1, "shop_state" , "shop_priority", "店优先"),
    BAG_PRIORITY(7,  1, "bag_state", "bag_priority", "最少包裹"),
    NOSHARED_PRIORITY(8,  0, "noshared_state", "noshared_priority", "独占仓派单"),
    SHOP_TYPE_PRIORITY(9,  1, "shop_type_state","shop_type_priority", "非A类/A类"),
    COMPANY_PRIORITY(10,  1, "company_state", "company_priority", "同结算/跨结算"),
    DISPATCH_NUM_PRIORITY(11,  1, "dispatch_num_state","dispatch_num_priority", "派单上限"),
    ORDER_UNIT_PRIORITY(12,  1, "order_unit_state","order_unit_priority", "货管优先"),
    SHOP_CREDIT_SCORE_PRIORITY(13,  1, "shop_credit_state","shop_credit_priority", "店铺信用分"),

    ;

    private final int id;

    /**
     * 0 关闭, 1 开启
     */
    private final int state;

    private final String stateCode;

    private final String priorityCode;

    private final String name;

    InternetDispatchRuleSetEnum(int id, int state, String stateCode, String priorityCode, String name) {
        this.id = id;
        this.state = state;
        this.stateCode = stateCode;
        this.priorityCode = priorityCode;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public int getState() {
        return state;
    }

    public String getStateCode() {
        return stateCode;
    }

    public String getPriorityCode() {
        return priorityCode;
    }

    public String getName() {
        return name;
    }
}
