package cn.wonhigh.baize.model.entity.gms;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

public class OrderSourceVstoreConfig extends cn.mercury.domain.BasicEntity {


	/**
	 * 
	 */
	private static final long serialVersionUID = 8205960903151898364L;
	
	/**
	 * id
	 */
	private String id;

	/**
	 * 来源平台店
	 *
	 */
	private String orderSourceNo;

	/**
	 * 虚仓编码
	 *
	 */
	private String vstoreCode;
	
	/**
	 * 虚店编码
	 */
	private String shopNo;
	
	/**
	 * 虚店名称
	 */
	private String shopName;
	
	/**
	 * 渠道预占编码
	 */
	private String channelOccupiedNo;
	
	/**
	 * 虚仓类型0线下1线上
	 */
	private Integer onlineType;

	/**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新用户
     */
    private String updateUser;

	private Integer groups;

	private Integer vstoreScopeType;

	private Integer vstoreLevel;

	private String remark;

	/**
	 * 虚仓名称
	 */
	private String vstoreName;

	/**
	 * 来源平台店名称
	 */
	private String orderSourceName;

	public Integer getGroups() {
		return groups;
	}

	public void setGroups(Integer groups) {
		this.groups = groups;
	}

	public Integer getVstoreScopeType() {
		return vstoreScopeType;
	}

	public void setVstoreScopeType(Integer vstoreScopeType) {
		this.vstoreScopeType = vstoreScopeType;
	}

	public Integer getVstoreLevel() {
		return vstoreLevel;
	}

	public void setVstoreLevel(Integer vstoreLevel) {
		this.vstoreLevel = vstoreLevel;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getVstoreName() {
		return vstoreName;
	}

	public void setVstoreName(String vstoreName) {
		this.vstoreName = vstoreName;
	}

	public String getOrderSourceName() {
		return orderSourceName;
	}

	public void setOrderSourceName(String orderSourceName) {
		this.orderSourceName = orderSourceName;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getChannelOccupiedNo() {
		return channelOccupiedNo;
	}

	public void setChannelOccupiedNo(String channelOccupiedNo) {
		this.channelOccupiedNo = channelOccupiedNo;
	}

	public Integer getOnlineType() {
		return onlineType;
	}

	public void setOnlineType(Integer onlineType) {
		this.onlineType = onlineType;
	}

	public String getOrderSourceNo() {
		return orderSourceNo;
	}

	public void setOrderSourceNo(String orderSourceNo) {
		this.orderSourceNo = orderSourceNo;
	}

	public String getVstoreCode() {
		return vstoreCode;
	}

	public void setVstoreCode(String vstoreCode) {
		this.vstoreCode = vstoreCode;
	}

	public String getShopNo() {
		return shopNo;
	}

	public void setShopNo(String shopNo) {
		this.shopNo = shopNo;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getUpdateUser() {
		return updateUser;
	}

	public void setUpdateUser(String updateUser) {
		this.updateUser = updateUser;
	}
	
}