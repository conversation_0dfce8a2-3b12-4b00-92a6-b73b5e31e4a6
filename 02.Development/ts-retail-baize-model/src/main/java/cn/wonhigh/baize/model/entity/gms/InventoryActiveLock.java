/**  **/
package cn.wonhigh.baize.model.entity.gms;

import cn.mercury.utils.JsonDateDeserializer$19;
import cn.mercury.utils.JsonDateSerializer$19;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 活动锁库表
**/
public class InventoryActiveLock  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1726905667706L;
    
    //机构编码
    @Label("机构编码") 
    private String storeNo;
    
    //机构编码
    @Label("机构编码") 
    private String storeName;
    
    //货管单位编码
    @Label("货管单位编码") 
    private String orderUnitNo;
    
    //货管单位名称
    @Label("货管单位名称") 
    private String orderUnitName;
    
    //销售店铺名称
    @Label("销售店铺名称") 
    private String shopName;
    
    //销售店铺编码：三级来源编码/店铺编码
    @Label("销售店铺编码") 
    private String shopNo;
    
    //虚拟仓名称
    @Label("虚拟仓名称") 
    private String vstoreName;
    
    //虚拟仓编码
    @Label("虚拟仓编码") 
    private String vstoreCode;
    
    //活动名称
    @Label("活动名称") 
    private String activeName;
    
    //活动编码
    @Label("活动编码") 
    private String billNo;
    
    //活动结束时间
    @Label("活动结束时间")
    @JsonSerialize(
            using = JsonDateSerializer$19.class
    )
    @JsonDeserialize(
            using = JsonDateDeserializer$19.class
    )
    private Date endTime;
    
    //0未审核-1审核成功-2审核失败
    @Label(value = "未审核", defaultVal = "0") 
    private Integer isApprove;
    
    //状态(1新建-2已生效-7已结束-8已终止)
    @Label(value = "状态", defaultVal = "1") 
    private Integer status;
    
    //备注
    @Label("备注") 
    private String remark;
    
    //业态 5001=ADSFS,4900=POS,4901=云店通,4902=小程序实店,4903:总部虚店,4904:地区虚店,5000:电商
    @Label("业态") 
    private String channelType;
    
    //业态名称
    @Label("业态名称") 
    private String channelName;
    
    //来源平台
    @Label("来源平台") 
    private String sourcePlatform;
    
    //活动开始时间
    @Label("活动开始时间")
    @JsonSerialize(
            using = JsonDateSerializer$19.class
    )
    @JsonDeserialize(
            using = JsonDateDeserializer$19.class
    )
    private Date startTime;

    @Label("剩余锁库量")
    private Long totalBalanceLockQty;

    @Label("锁库总数")
    private Long totalLockQty;

    @Label("锁库类型")
    private Integer lockType;

    @Label("同步状态")
    private Integer syncStatus;

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public Integer getLockType() {
        return lockType;
    }

    public void setLockType(Integer lockType) {
        this.lockType = lockType;
    }

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    
    public String getStoreName(){
        return  storeName;
    }
    public void setStoreName(String val ){
        storeName = val;
    }
    
    public String getOrderUnitNo(){
        return  orderUnitNo;
    }
    public void setOrderUnitNo(String val ){
        orderUnitNo = val;
    }
    
    public String getOrderUnitName(){
        return  orderUnitName;
    }
    public void setOrderUnitName(String val ){
        orderUnitName = val;
    }
    
    public String getShopName(){
        return  shopName;
    }
    public void setShopName(String val ){
        shopName = val;
    }
    
    public String getShopNo(){
        return  shopNo;
    }
    public void setShopNo(String val ){
        shopNo = val;
    }
    
    public String getVstoreName(){
        return  vstoreName;
    }
    public void setVstoreName(String val ){
        vstoreName = val;
    }
    
    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }
    
    public String getActiveName(){
        return  activeName;
    }
    public void setActiveName(String val ){
        activeName = val;
    }
    
    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    
    public Date getEndTime(){
        return  endTime;
    }
    public void setEndTime(Date val ){
        endTime = val;
    }
    
    public Integer getIsApprove(){
        return  isApprove;
    }
    public void setIsApprove(Integer val ){
        isApprove = val;
    }
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    public String getChannelType(){
        return  channelType;
    }
    public void setChannelType(String val ){
        channelType = val;
    }
    
    public String getChannelName(){
        return  channelName;
    }
    public void setChannelName(String val ){
        channelName = val;
    }
    
    public String getSourcePlatform(){
        return  sourcePlatform;
    }
    public void setSourcePlatform(String val ){
        sourcePlatform = val;
    }
    
    public Date getStartTime(){
        return  startTime;
    }
    public void setStartTime(Date val ){
        startTime = val;
    }

    public Long getTotalBalanceLockQty() {
        return totalBalanceLockQty;
    }

    public void setTotalBalanceLockQty(Long totalBalanceLockQty) {
        this.totalBalanceLockQty = totalBalanceLockQty;
    }

    public Long getTotalLockQty() {
        return totalLockQty;
    }

    public void setTotalLockQty(Long totalLockQty) {
        this.totalLockQty = totalLockQty;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InventoryActiveLockBuilder build(){
        return new InventoryActiveLockBuilder(this);
    }

    public static class InventoryActiveLockBuilder extends AbstractEntryBuilder<InventoryActiveLock>{

        private InventoryActiveLockBuilder(InventoryActiveLock entry){
            this.obj = entry;
        }

       @Override
		public InventoryActiveLock object() {
			return this.obj;
		}

        
        public InventoryActiveLockBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InventoryActiveLockBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InventoryActiveLockBuilder storeNo(String value ){
            this.obj.storeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public InventoryActiveLockBuilder storeName(String value ){
            this.obj.storeName = value;
            if( query == null  )
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }
        
        public InventoryActiveLockBuilder orderUnitNo(String value ){
            this.obj.orderUnitNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }
        
        public InventoryActiveLockBuilder orderUnitName(String value ){
            this.obj.orderUnitName = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitName", value);
            return this;
        }
        
        public InventoryActiveLockBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InventoryActiveLockBuilder shopName(String value ){
            this.obj.shopName = value;
            if( query == null  )
                query = new Query();
            this.query.where("shopName", value);
            return this;
        }
        
        public InventoryActiveLockBuilder shopNo(String value ){
            this.obj.shopNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("shopNo", value);
            return this;
        }
        
        public InventoryActiveLockBuilder vstoreName(String value ){
            this.obj.vstoreName = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreName", value);
            return this;
        }
        
        public InventoryActiveLockBuilder vstoreCode(String value ){
            this.obj.vstoreCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public InventoryActiveLockBuilder activeName(String value ){
            this.obj.activeName = value;
            if( query == null  )
                query = new Query();
            this.query.where("activeName", value);
            return this;
        }
        
        public InventoryActiveLockBuilder billNo(String value ){
            this.obj.billNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public InventoryActiveLockBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InventoryActiveLockBuilder endTime(Date value ){
            this.obj.endTime = value;
            if( query == null  )
                query = new Query();
            this.query.where("endTime", value);
            return this;
        }
        
        public InventoryActiveLockBuilder isApprove(Integer value ){
            this.obj.isApprove = value;
            if( query == null  )
                query = new Query();
            this.query.where("isApprove", value);
            return this;
        }
        
        public InventoryActiveLockBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public InventoryActiveLockBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public InventoryActiveLockBuilder channelType(String value ){
            this.obj.channelType = value;
            if( query == null  )
                query = new Query();
            this.query.where("channelType", value);
            return this;
        }
        
        public InventoryActiveLockBuilder channelName(String value ){
            this.obj.channelName = value;
            if( query == null  )
                query = new Query();
            this.query.where("channelName", value);
            return this;
        }
        
        public InventoryActiveLockBuilder sourcePlatform(String value ){
            this.obj.sourcePlatform = value;
            if( query == null  )
                query = new Query();
            this.query.where("sourcePlatform", value);
            return this;
        }
        
        public InventoryActiveLockBuilder startTime(Date value ){
            this.obj.startTime = value;
            if( query == null  )
                query = new Query();
            this.query.where("startTime", value);
            return this;
        }
        
        public InventoryActiveLockBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }

        public InventoryActiveLockBuilder lockType(Integer value ){
            this.obj.setLockType(value);

            if( query == null  )
                query = new Query();
            this.query.where("lockType", value);
            return this;
        }

        public InventoryActiveLockBuilder asyncStatus(Integer value ){
            this.obj.setSyncStatus(value);

            if( query == null  )
                query = new Query();
            this.query.where("asyncStatus", value);
            return this;
        }
    }
/** auto generate end,don't modify */
}