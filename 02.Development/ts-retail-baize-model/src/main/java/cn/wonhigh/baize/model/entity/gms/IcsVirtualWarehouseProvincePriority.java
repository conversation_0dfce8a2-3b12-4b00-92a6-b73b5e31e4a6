/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 
**/
public class IcsVirtualWarehouseProvincePriority  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1726726567379L;
    
    //状态:0关闭1开启
    @Label(value = "状态", defaultVal = "0") 
    private Integer status;
    
    //聚合仓名称
    @Label("聚合仓名称") 
    private String vstoreName;
    
    //备注
    @Label("备注") 
    private String remark;
    
    //省名称
    @Label("省名称") 
    private String provinceName;
    
    //省编码
    @Label("省编码") 
    private String provinceNo;
    
    //聚合仓编码
    @Label("聚合仓编码") 
    private String vstoreCode;
    
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public String getVstoreName(){
        return  vstoreName;
    }
    public void setVstoreName(String val ){
        vstoreName = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    public String getProvinceName(){
        return  provinceName;
    }
    public void setProvinceName(String val ){
        provinceName = val;
    }
    
    public String getProvinceNo(){
        return  provinceNo;
    }
    public void setProvinceNo(String val ){
        provinceNo = val;
    }
    
    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public IcsVirtualWarehouseProvincePriorityBuilder build(){
        return new IcsVirtualWarehouseProvincePriorityBuilder(this);
    }

    public static class IcsVirtualWarehouseProvincePriorityBuilder extends AbstractEntryBuilder<IcsVirtualWarehouseProvincePriority>{

        private IcsVirtualWarehouseProvincePriorityBuilder(IcsVirtualWarehouseProvincePriority entry){
            this.obj = entry;
        }

       @Override
		public IcsVirtualWarehouseProvincePriority object() {
			return this.obj;
		}

        
        public IcsVirtualWarehouseProvincePriorityBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder vstoreName(String value ){
            this.obj.vstoreName = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreName", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder provinceName(String value ){
            this.obj.provinceName = value;
            if( query == null  )
                query = new Query();
            this.query.where("provinceName", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder provinceNo(String value ){
            this.obj.provinceNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("provinceNo", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder vstoreCode(String value ){
            this.obj.vstoreCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public IcsVirtualWarehouseProvincePriorityBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}