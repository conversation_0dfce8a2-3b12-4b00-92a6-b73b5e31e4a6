/**  **/
package cn.wonhigh.baize.model.entity.gms;

import cn.wonhigh.baize.model.enums.ManagerZoneEnum;
import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 
**/
public class OrderSourceTerminalConfig  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1690177269700L;

    /**
     *终端编码
     **/
    @Label("终端编码")
    private String terminal;

    /**
     *终端名称
     **/
    @Label("终端名称")
    private String terminalName;

    /**
     *商户编码
     **/
    @Label("商家编码")
    private String merchantCode;

    /**
     *平台编码
     **/
    @Label("平台编码")
    private String platform;

    /**
     *平台名称
     **/
    @Label("平台名称")
    private String platformName;

    /**
     *二级平台编码
     **/
    @Label("二级平台编码")
    private String secondPlatform;

    /**
     *二级平台名称
     **/
    @Label("二级平台名称")
    private String secondPlatformName;

    /**
     *三级平台编码
     **/
    @Label("三级平台编码")
    private String thirdPlatform;

    /**
    *三级平台名称
    **/ 
    @Label("三级平台名称") 
    private String thirdPlatformName;

    /**
     *三级来源编码
     **/
    @Label("三级来源编码")
    private String thirdChannelNo;

    /**
     *三级来源名称
     **/
    @Label("三级来源名称")
    private String thirdChannelName;

    @Label("省编码")
    private String provinceNo;

    private String provinceName;

    @Label("市编码")
    private String cityNo;

    private String cityName;

    @Label("地区编码")
    private String zoneNo;

    private String zoneName;

    @Label("管理大区编码")
    private String managerZoneNo;

    private String managerZoneName;

    /**
     *零售结算公司编码
     **/
    @Label("零售结算公司编码")
    private String companyNo;

    /**
     *零售结算公司名称
     **/
    @Label("零售结算公司名称")
    private String companyName;

    /**
     *备注
     **/
    @Label("备注")
    private String remark;

    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public String getThirdPlatformName(){
        return  thirdPlatformName;
    }
    public void setThirdPlatformName(String val ){
        thirdPlatformName = val;
    }

    public String getThirdChannelNo(){
        return  thirdChannelNo;
    }
    public void setThirdChannelNo(String val ){
        thirdChannelNo = val;
    }

    public String getTerminal(){
        return  terminal;
    }
    public void setTerminal(String val ){
        terminal = val;
    }

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }

    public String getThirdPlatform(){
        return  thirdPlatform;
    }
    public void setThirdPlatform(String val ){
        thirdPlatform = val;
    }

    public String getSecondPlatformName(){
        return  secondPlatformName;
    }
    public void setSecondPlatformName(String val ){
        secondPlatformName = val;
    }

    public String getThirdChannelName(){
        return  thirdChannelName;
    }
    public void setThirdChannelName(String val ){
        thirdChannelName = val;
    }

    public String getSecondPlatform(){
        return  secondPlatform;
    }
    public void setSecondPlatform(String val ){
        secondPlatform = val;
    }

    public String getPlatformName(){
        return  platformName;
    }
    public void setPlatformName(String val ){
        platformName = val;
    }

    public String getPlatform(){
        return  platform;
    }
    public void setPlatform(String val ){
        platform = val;
    }

    public String getTerminalName(){
        return  terminalName;
    }
    public void setTerminalName(String val ){
        terminalName = val;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getProvinceNo() {
        return provinceNo;
    }

    public void setProvinceNo(String provinceNo) {
        this.provinceNo = provinceNo;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityNo() {
        return cityNo;
    }

    public void setCityNo(String cityNo) {
        this.cityNo = cityNo;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getZoneNo() {
        return zoneNo;
    }

    public void setZoneNo(String zoneNo) {
        this.zoneNo = zoneNo;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getManagerZoneNo() {
        this.managerZoneName = ManagerZoneEnum.getName(managerZoneNo);
        return managerZoneNo;
    }

    public void setManagerZoneNo(String managerZoneNo) {
        this.managerZoneNo = managerZoneNo;
    }

    public String getManagerZoneName() {
        return managerZoneName;
    }

    public void setManagerZoneName(String managerZoneName) {
        this.managerZoneName = managerZoneName;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public OrderSourceTerminalConfigBuilder build(){
        return new OrderSourceTerminalConfigBuilder(this);
    }

    public static class OrderSourceTerminalConfigBuilder extends AbstractEntryBuilder<OrderSourceTerminalConfig>{

        private OrderSourceTerminalConfigBuilder(OrderSourceTerminalConfig entry){
            this.obj = entry;
        }

       @Override
		public OrderSourceTerminalConfig object() {
			return this.obj;
		}

        
        public OrderSourceTerminalConfigBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder thirdPlatformName(String value ){
            
            this.obj.thirdPlatformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("thirdPlatformName", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder thirdChannelNo(String value ){
            
            this.obj.thirdChannelNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("thirdChannelNo", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder terminal(String value ){
            
            this.obj.terminal = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("terminal", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder thirdPlatform(String value ){
            
            this.obj.thirdPlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("thirdPlatform", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder secondPlatformName(String value ){
            
            this.obj.secondPlatformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("secondPlatformName", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder thirdChannelName(String value ){
            
            this.obj.thirdChannelName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("thirdChannelName", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder secondPlatform(String value ){
            
            this.obj.secondPlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("secondPlatform", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder platformName(String value ){
            
            this.obj.platformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("platformName", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder platform(String value ){
            
            this.obj.platform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("platform", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder terminalName(String value ){
            
            this.obj.terminalName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("terminalName", value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder provinceNo(String value){

            this.obj.setProvinceNo(value);

            if( query == null  )
                query = new Query();
            this.query.where("provinceNo", value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder provinceName(String value){
            this.obj.setProvinceName(value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder cityNo(String value){

            this.obj.setCityNo(value);

            if( query == null  )
                query = new Query();
            this.query.where("cityNo", value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder cityName(String value){
            this.obj.setCityName(value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder zoneNo(String value){

            this.obj.setZoneNo(value);

            if( query == null  )
                query = new Query();
            this.query.where("zoneNo", value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder zoneName(String value){
            this.obj.setZoneName(value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder managerZoneNo(String value){

            this.obj.setManagerZoneNo(value);

            if( query == null  )
                query = new Query();
            this.query.where("managerZoneNo", value);
            return this;
        }
        
        public OrderSourceTerminalConfigBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder companyNo(String value ){

            this.obj.setCompanyNo(value);

            if( query == null  )
                query = new Query();
            this.query.where("companyNo", value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder companyName(String value ){

            this.obj.setCompanyName(value);

            if( query == null  )
                query = new Query();
            this.query.where("companyName", value);
            return this;
        }

        public OrderSourceTerminalConfigBuilder merchantCode(String value ){

            this.obj.setMerchantCode(value);

            if( query == null  )
                query = new Query();
            this.query.where("merchantCode", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }