package cn.wonhigh.baize.model.entity.gms;

import java.math.BigDecimal;
import java.util.Date;

public class Item extends cn.mercury.domain.BasicEntity {

    private static final long serialVersionUID = 1545814831148L;

    /**
     *item.item_no - 商品系统唯一编码(系统编码,对用户不可见)
     *
     * Generated by MyBatis Generator.
     */
    private String itemNo;

    /**
     *item.code - 商品编码(出厂时的商品编码)
     *
     * Generated by MyBatis Generator.
     */
    private String code;

    /**
     *item.name - 商品名称
     *
     * Generated by MyBatis Generator.
     */
    private String name;

    /**
     *item.full_name - 商品全称(默认与商品名称一致)
     *
     * Generated by MyBatis Generator.
     */
    private String fullName;

    /**
     *item.en_name - 商品英文名
     *
     * Generated by MyBatis Generator.
     */
    private String enName;

    /**
     *item.sys_no - 所属业务单元
     *
     * Generated by MyBatis Generator.
     */
    private String sysNo;

    /**
     *item.style_no - 商品款号
     *
     * Generated by MyBatis Generator.
     */
    private String styleNo;

    /**
     *item.brand_no - 品牌编码
     *
     * Generated by MyBatis Generator.
     */
    private String brandNo;

    /**
     *item.shoe_model - 款型(下拉框选择,值: 1.5, 2, 2.5, 无)
     *
     * Generated by MyBatis Generator.
     */
    private String shoeModel;

    /**
     *item.ingredients - 主料(下拉框选择,值: 1:皮 2:布 3:绒 4:PU 5:木 6:弹力 7:

网 8:其它 9:不涉及)
     *
     * Generated by MyBatis Generator.
     */
    private String ingredients;

    /**
     *item.mainqdb - 面料成份
     *
     * Generated by MyBatis Generator.
     */
    private String mainqdb;

    /**
     *item.lining - 内里(D:单里 M:毛里 R:绒里 F:仿毛里)
     *
     * Generated by MyBatis Generator.
     */
    private String lining;

    /**
     *item.main_color - 主色
     *
     * Generated by MyBatis Generator.
     */
    private String mainColor;

    /**
     *item.color_no - 颜色编码
     *
     * Generated by MyBatis Generator.
     */
    private String colorNo;

    /**
     *item.category_no - 类别编码
     *
     * Generated by MyBatis Generator.
     */
    private String categoryNo;

    /**
     *item.root_category_no - 商品大类编码
     *
     * Generated by MyBatis Generator.
     */
    private String rootCategoryNo;

    /**
     *item.repeatlisting - 重复上市(下拉框选择,值: 是, 否)
     *
     * Generated by MyBatis Generator.
     */
    private String repeatlisting;

    /**
     *item.gender - 性别(下拉框选择,值: 男, 女, 童, 无性别)
     *
     * Generated by MyBatis Generator.
     */
    private String gender;

    /**
     *item.heeltype - 跟型(下拉框选择,值: 高, 中, 低, 平, 不涉及)
     *
     * Generated by MyBatis Generator.
     */
    private String heeltype;

    /**
     *item.bottomtype - 底型(下拉框选择,值: 成型底, 片底, 成型片, 不涉及)
     *
     * Generated by MyBatis Generator.
     */
    private String bottomtype;

    /**
     *item.size_kind - 尺寸分类
     *
     * Generated by MyBatis Generator.
     */
    private String sizeKind;

    /**
     *item.status - 状态(0 = 禁用 1 = 正常 2 = 临时)
     *
     * Generated by MyBatis Generator.
     */
    private Integer status;

    /**
     *item.tag_price - 牌价
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal tagPrice;

    /**
     *item.suggest_tag_price - 建议牌价
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal suggestTagPrice;

    /**
     *item.purchase_tax_price - 含税采购价
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal purchaseTaxPrice;

    /**
     *item.costtaxrate - 进项税率
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal costtaxrate;

    /**
     *item.saletaxrate - 销项税率
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal saletaxrate;

    /**
     *item.material_price - 物料价
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal materialPrice;

    /**
     *item.supplier_no - 供应商编码
     *
     * Generated by MyBatis Generator.
     */
    private String supplierNo;

    /**
     *item.supplier_item_no - 供应商货号
     *
     * Generated by MyBatis Generator.
     */
    private String supplierItemNo;

    /**
     *item.supplier_item_name - 供应商商品名称
     *
     * Generated by MyBatis Generator.
     */
    private String supplierItemName;

    /**
     *item.orderfrom - 订货形式(下拉框选择,值: 1:自产 2:外购 3:地区自购)
     *
     * Generated by MyBatis Generator.
     */
    private String orderfrom;

    /**
     *item.years - 年份(指上市的年份,下拉框选择,值: 2006~2026,默认当年)
     *
     * Generated by MyBatis Generator.
     */
    private String years;

    /**
     *item.sell_season - 产品季节(下拉框选择,A:春 B:夏 C:秋 D:冬)
     *
     * Generated by MyBatis Generator.
     */
    private String sellSeason;

    /**
     *item.purchase_season - 季节
     *
     * Generated by MyBatis Generator.
     */
    private String purchaseSeason;

    /**
     *item.sale_date - 建议上柜日
     *
     * Generated by MyBatis Generator.
     */
    private Date saleDate;

    /**
     *item.search_code - 检索码
     *
     * Generated by MyBatis Generator.
     */
    private String searchCode;

    /**
     *item.style - 风格
     *
     * Generated by MyBatis Generator.
     */
    private String style;

    /**
     *item.remark - 备注
     *
     * Generated by MyBatis Generator.
     */
    private String remark;

    /**
     *item.time_seq - 时间序列
     *
     * Generated by MyBatis Generator.
     */
    private Long timeSeq;

    /**
     *item.season - 采购季节
     *
     * Generated by MyBatis Generator.
     */
    private String season;

    /**
     *item.liner - 衬里/內垫
     *
     * Generated by MyBatis Generator.
     */
    private String liner;

    /**
     *item.outsole - 外底
     *
     * Generated by MyBatis Generator.
     */
    private String outsole;

    /**
     *item.pattern - 楦型
     *
     * Generated by MyBatis Generator.
     */
    private String pattern;

    /**
     *item.item_code2 - 货号2
     *
     * Generated by MyBatis Generator.
     */
    private String itemCode2;

    /**
     *item.ext_dev_prop - 开发属性
     *
     * Generated by MyBatis Generator.
     */
    private String extDevProp;

    /**
     *item.ext_brand_style - 品牌风格
     *
     * Generated by MyBatis Generator.
     */
    private String extBrandStyle;

    /**
     *item.ext_style - 风格
     *
     * Generated by MyBatis Generator.
     */
    private String extStyle;

    /**
     *item.ext_series - 系列
     *
     * Generated by MyBatis Generator.
     */
    private String extSeries;

    /**
     *item.ext_spec_prop - 特定属性
     *
     * Generated by MyBatis Generator.
     */
    private String extSpecProp;

    /**
     *item.item_style_no - 款式编码(体育)
     *
     * Generated by MyBatis Generator.
     */
    private String itemStyleNo;

    /**
     *item.item_series_no - 系列编码(体育)
     *
     * Generated by MyBatis Generator.
     */
    private String itemSeriesNo;

    /**
     *item.japan_name - 日本名称
     *
     * Generated by MyBatis Generator.
     */
    private String japanName;

    /**
     *item.designer_name - 设计者
     *
     * Generated by MyBatis Generator.
     */
    private String designerName;

    /**
     *item.origine_country - 原产国
     *
     * Generated by MyBatis Generator.
     */
    private String origineCountry;

    /**
     *item.japan_tag_price - 日本牌价
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal japanTagPrice;

    /**
     *item.japan_cost - 日本成本
     *
     * Generated by MyBatis Generator.
     */
    private BigDecimal japanCost;

    /**
     *item.sale_year - 上柜年
     *
     * Generated by MyBatis Generator.
     */
    private String saleYear;

    /**
     *item.sale_week - 上柜周
     *
     * Generated by MyBatis Generator.
     */
    private String saleWeek;

    /**
     *item.brand_season - 品牌季节
     *
     * Generated by MyBatis Generator.
     */
    private String brandSeason;

    /**
     *item.item_flag - 商品标志
     *
     * Generated by MyBatis Generator.
     */
    private String itemFlag;

    /**
     *item.plate_code - 板单号
     *
     * Generated by MyBatis Generator.
     */
    private String plateCode;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.item_no - 商品系统唯一编码(系统编码,对用户不可见)
     * @return the value of item.item_no
     */
    public String getItemNo() {
        return itemNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.item_no - 商品系统唯一编码(系统编码,对用户不可见)
     * @param itemNo the value for item.item_no
     */
    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.code - 商品编码(出厂时的商品编码)
     * @return the value of item.code
     */
    public String getCode() {
        return code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.code - 商品编码(出厂时的商品编码)
     * @param code the value for item.code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.name - 商品名称
     * @return the value of item.name
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.name - 商品名称
     * @param name the value for item.name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.full_name - 商品全称(默认与商品名称一致)
     * @return the value of item.full_name
     */
    public String getFullName() {
        return fullName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.full_name - 商品全称(默认与商品名称一致)
     * @param fullName the value for item.full_name
     */
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.en_name - 商品英文名
     * @return the value of item.en_name
     */
    public String getEnName() {
        return enName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.en_name - 商品英文名
     * @param enName the value for item.en_name
     */
    public void setEnName(String enName) {
        this.enName = enName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.sys_no - 所属业务单元
     * @return the value of item.sys_no
     */
    public String getSysNo() {
        return sysNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.sys_no - 所属业务单元
     * @param sysNo the value for item.sys_no
     */
    public void setSysNo(String sysNo) {
        this.sysNo = sysNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.style_no - 商品款号
     * @return the value of item.style_no
     */
    public String getStyleNo() {
        return styleNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.style_no - 商品款号
     * @param styleNo the value for item.style_no
     */
    public void setStyleNo(String styleNo) {
        this.styleNo = styleNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.brand_no - 品牌编码
     * @return the value of item.brand_no
     */
    public String getBrandNo() {
        return brandNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.brand_no - 品牌编码
     * @param brandNo the value for item.brand_no
     */
    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.shoe_model - 款型(下拉框选择,值: 1.5, 2, 2.5, 无)
     * @return the value of item.shoe_model
     */
    public String getShoeModel() {
        return shoeModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.shoe_model - 款型(下拉框选择,值: 1.5, 2, 2.5, 无)
     * @param shoeModel the value for item.shoe_model
     */
    public void setShoeModel(String shoeModel) {
        this.shoeModel = shoeModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.ingredients - 主料(下拉框选择,值: 1:皮 2:布 3:绒 4:PU 5:木 6:弹力 7:

网 8:其它 9:不涉及)
     * @return the value of item.ingredients
     */
    public String getIngredients() {
        return ingredients;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.ingredients - 主料(下拉框选择,值: 1:皮 2:布 3:绒 4:PU 5:木 6:弹力 7:

网 8:其它 9:不涉及)
     * @param ingredients the value for item.ingredients
     */
    public void setIngredients(String ingredients) {
        this.ingredients = ingredients;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.mainqdb - 面料成份
     * @return the value of item.mainqdb
     */
    public String getMainqdb() {
        return mainqdb;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.mainqdb - 面料成份
     * @param mainqdb the value for item.mainqdb
     */
    public void setMainqdb(String mainqdb) {
        this.mainqdb = mainqdb;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.lining - 内里(D:单里 M:毛里 R:绒里 F:仿毛里)
     * @return the value of item.lining
     */
    public String getLining() {
        return lining;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.lining - 内里(D:单里 M:毛里 R:绒里 F:仿毛里)
     * @param lining the value for item.lining
     */
    public void setLining(String lining) {
        this.lining = lining;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.main_color - 主色
     * @return the value of item.main_color
     */
    public String getMainColor() {
        return mainColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.main_color - 主色
     * @param mainColor the value for item.main_color
     */
    public void setMainColor(String mainColor) {
        this.mainColor = mainColor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.color_no - 颜色编码
     * @return the value of item.color_no
     */
    public String getColorNo() {
        return colorNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.color_no - 颜色编码
     * @param colorNo the value for item.color_no
     */
    public void setColorNo(String colorNo) {
        this.colorNo = colorNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.category_no - 类别编码
     * @return the value of item.category_no
     */
    public String getCategoryNo() {
        return categoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.category_no - 类别编码
     * @param categoryNo the value for item.category_no
     */
    public void setCategoryNo(String categoryNo) {
        this.categoryNo = categoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.root_category_no - 商品大类编码
     * @return the value of item.root_category_no
     */
    public String getRootCategoryNo() {
        return rootCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.root_category_no - 商品大类编码
     * @param rootCategoryNo the value for item.root_category_no
     */
    public void setRootCategoryNo(String rootCategoryNo) {
        this.rootCategoryNo = rootCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.repeatlisting - 重复上市(下拉框选择,值: 是, 否)
     * @return the value of item.repeatlisting
     */
    public String getRepeatlisting() {
        return repeatlisting;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.repeatlisting - 重复上市(下拉框选择,值: 是, 否)
     * @param repeatlisting the value for item.repeatlisting
     */
    public void setRepeatlisting(String repeatlisting) {
        this.repeatlisting = repeatlisting;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.gender - 性别(下拉框选择,值: 男, 女, 童, 无性别)
     * @return the value of item.gender
     */
    public String getGender() {
        return gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.gender - 性别(下拉框选择,值: 男, 女, 童, 无性别)
     * @param gender the value for item.gender
     */
    public void setGender(String gender) {
        this.gender = gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.heeltype - 跟型(下拉框选择,值: 高, 中, 低, 平, 不涉及)
     * @return the value of item.heeltype
     */
    public String getHeeltype() {
        return heeltype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.heeltype - 跟型(下拉框选择,值: 高, 中, 低, 平, 不涉及)
     * @param heeltype the value for item.heeltype
     */
    public void setHeeltype(String heeltype) {
        this.heeltype = heeltype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.bottomtype - 底型(下拉框选择,值: 成型底, 片底, 成型片, 不涉及)
     * @return the value of item.bottomtype
     */
    public String getBottomtype() {
        return bottomtype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.bottomtype - 底型(下拉框选择,值: 成型底, 片底, 成型片, 不涉及)
     * @param bottomtype the value for item.bottomtype
     */
    public void setBottomtype(String bottomtype) {
        this.bottomtype = bottomtype;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.size_kind - 尺寸分类
     * @return the value of item.size_kind
     */
    public String getSizeKind() {
        return sizeKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.size_kind - 尺寸分类
     * @param sizeKind the value for item.size_kind
     */
    public void setSizeKind(String sizeKind) {
        this.sizeKind = sizeKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.status - 状态(0 = 禁用 1 = 正常 2 = 临时)
     * @return the value of item.status
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.status - 状态(0 = 禁用 1 = 正常 2 = 临时)
     * @param status the value for item.status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.tag_price - 牌价
     * @return the value of item.tag_price
     */
    public BigDecimal getTagPrice() {
        return tagPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.tag_price - 牌价
     * @param tagPrice the value for item.tag_price
     */
    public void setTagPrice(BigDecimal tagPrice) {
        this.tagPrice = tagPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.suggest_tag_price - 建议牌价
     * @return the value of item.suggest_tag_price
     */
    public BigDecimal getSuggestTagPrice() {
        return suggestTagPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.suggest_tag_price - 建议牌价
     * @param suggestTagPrice the value for item.suggest_tag_price
     */
    public void setSuggestTagPrice(BigDecimal suggestTagPrice) {
        this.suggestTagPrice = suggestTagPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.purchase_tax_price - 含税采购价
     * @return the value of item.purchase_tax_price
     */
    public BigDecimal getPurchaseTaxPrice() {
        return purchaseTaxPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.purchase_tax_price - 含税采购价
     * @param purchaseTaxPrice the value for item.purchase_tax_price
     */
    public void setPurchaseTaxPrice(BigDecimal purchaseTaxPrice) {
        this.purchaseTaxPrice = purchaseTaxPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.costtaxrate - 进项税率
     * @return the value of item.costtaxrate
     */
    public BigDecimal getCosttaxrate() {
        return costtaxrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.costtaxrate - 进项税率
     * @param costtaxrate the value for item.costtaxrate
     */
    public void setCosttaxrate(BigDecimal costtaxrate) {
        this.costtaxrate = costtaxrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.saletaxrate - 销项税率
     * @return the value of item.saletaxrate
     */
    public BigDecimal getSaletaxrate() {
        return saletaxrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.saletaxrate - 销项税率
     * @param saletaxrate the value for item.saletaxrate
     */
    public void setSaletaxrate(BigDecimal saletaxrate) {
        this.saletaxrate = saletaxrate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.material_price - 物料价
     * @return the value of item.material_price
     */
    public BigDecimal getMaterialPrice() {
        return materialPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.material_price - 物料价
     * @param materialPrice the value for item.material_price
     */
    public void setMaterialPrice(BigDecimal materialPrice) {
        this.materialPrice = materialPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.supplier_no - 供应商编码
     * @return the value of item.supplier_no
     */
    public String getSupplierNo() {
        return supplierNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.supplier_no - 供应商编码
     * @param supplierNo the value for item.supplier_no
     */
    public void setSupplierNo(String supplierNo) {
        this.supplierNo = supplierNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.supplier_item_no - 供应商货号
     * @return the value of item.supplier_item_no
     */
    public String getSupplierItemNo() {
        return supplierItemNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.supplier_item_no - 供应商货号
     * @param supplierItemNo the value for item.supplier_item_no
     */
    public void setSupplierItemNo(String supplierItemNo) {
        this.supplierItemNo = supplierItemNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.supplier_item_name - 供应商商品名称
     * @return the value of item.supplier_item_name
     */
    public String getSupplierItemName() {
        return supplierItemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.supplier_item_name - 供应商商品名称
     * @param supplierItemName the value for item.supplier_item_name
     */
    public void setSupplierItemName(String supplierItemName) {
        this.supplierItemName = supplierItemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.orderfrom - 订货形式(下拉框选择,值: 1:自产 2:外购 3:地区自购)
     * @return the value of item.orderfrom
     */
    public String getOrderfrom() {
        return orderfrom;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.orderfrom - 订货形式(下拉框选择,值: 1:自产 2:外购 3:地区自购)
     * @param orderfrom the value for item.orderfrom
     */
    public void setOrderfrom(String orderfrom) {
        this.orderfrom = orderfrom;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.years - 年份(指上市的年份,下拉框选择,值: 2006~2026,默认当年)
     * @return the value of item.years
     */
    public String getYears() {
        return years;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.years - 年份(指上市的年份,下拉框选择,值: 2006~2026,默认当年)
     * @param years the value for item.years
     */
    public void setYears(String years) {
        this.years = years;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.sell_season - 产品季节(下拉框选择,A:春 B:夏 C:秋 D:冬)
     * @return the value of item.sell_season
     */
    public String getSellSeason() {
        return sellSeason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.sell_season - 产品季节(下拉框选择,A:春 B:夏 C:秋 D:冬)
     * @param sellSeason the value for item.sell_season
     */
    public void setSellSeason(String sellSeason) {
        this.sellSeason = sellSeason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.purchase_season - 季节
     * @return the value of item.purchase_season
     */
    public String getPurchaseSeason() {
        return purchaseSeason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.purchase_season - 季节
     * @param purchaseSeason the value for item.purchase_season
     */
    public void setPurchaseSeason(String purchaseSeason) {
        this.purchaseSeason = purchaseSeason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.sale_date - 建议上柜日
     * @return the value of item.sale_date
     */
    public Date getSaleDate() {
        return saleDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.sale_date - 建议上柜日
     * @param saleDate the value for item.sale_date
     */
    public void setSaleDate(Date saleDate) {
        this.saleDate = saleDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.search_code - 检索码
     * @return the value of item.search_code
     */
    public String getSearchCode() {
        return searchCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.search_code - 检索码
     * @param searchCode the value for item.search_code
     */
    public void setSearchCode(String searchCode) {
        this.searchCode = searchCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.style - 风格
     * @return the value of item.style
     */
    public String getStyle() {
        return style;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.style - 风格
     * @param style the value for item.style
     */
    public void setStyle(String style) {
        this.style = style;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.remark - 备注
     * @return the value of item.remark
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.remark - 备注
     * @param remark the value for item.remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.time_seq - 时间序列
     * @return the value of item.time_seq
     */
    public Long getTimeSeq() {
        return timeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.time_seq - 时间序列
     * @param timeSeq the value for item.time_seq
     */
    public void setTimeSeq(Long timeSeq) {
        this.timeSeq = timeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.season - 采购季节
     * @return the value of item.season
     */
    public String getSeason() {
        return season;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.season - 采购季节
     * @param season the value for item.season
     */
    public void setSeason(String season) {
        this.season = season;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.liner - 衬里/內垫
     * @return the value of item.liner
     */
    public String getLiner() {
        return liner;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.liner - 衬里/內垫
     * @param liner the value for item.liner
     */
    public void setLiner(String liner) {
        this.liner = liner;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.outsole - 外底
     * @return the value of item.outsole
     */
    public String getOutsole() {
        return outsole;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.outsole - 外底
     * @param outsole the value for item.outsole
     */
    public void setOutsole(String outsole) {
        this.outsole = outsole;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.pattern - 楦型
     * @return the value of item.pattern
     */
    public String getPattern() {
        return pattern;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.pattern - 楦型
     * @param pattern the value for item.pattern
     */
    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.item_code2 - 货号2
     * @return the value of item.item_code2
     */
    public String getItemCode2() {
        return itemCode2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.item_code2 - 货号2
     * @param itemCode2 the value for item.item_code2
     */
    public void setItemCode2(String itemCode2) {
        this.itemCode2 = itemCode2;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.ext_dev_prop - 开发属性
     * @return the value of item.ext_dev_prop
     */
    public String getExtDevProp() {
        return extDevProp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.ext_dev_prop - 开发属性
     * @param extDevProp the value for item.ext_dev_prop
     */
    public void setExtDevProp(String extDevProp) {
        this.extDevProp = extDevProp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.ext_brand_style - 品牌风格
     * @return the value of item.ext_brand_style
     */
    public String getExtBrandStyle() {
        return extBrandStyle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.ext_brand_style - 品牌风格
     * @param extBrandStyle the value for item.ext_brand_style
     */
    public void setExtBrandStyle(String extBrandStyle) {
        this.extBrandStyle = extBrandStyle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.ext_style - 风格
     * @return the value of item.ext_style
     */
    public String getExtStyle() {
        return extStyle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.ext_style - 风格
     * @param extStyle the value for item.ext_style
     */
    public void setExtStyle(String extStyle) {
        this.extStyle = extStyle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.ext_series - 系列
     * @return the value of item.ext_series
     */
    public String getExtSeries() {
        return extSeries;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.ext_series - 系列
     * @param extSeries the value for item.ext_series
     */
    public void setExtSeries(String extSeries) {
        this.extSeries = extSeries;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.ext_spec_prop - 特定属性
     * @return the value of item.ext_spec_prop
     */
    public String getExtSpecProp() {
        return extSpecProp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.ext_spec_prop - 特定属性
     * @param extSpecProp the value for item.ext_spec_prop
     */
    public void setExtSpecProp(String extSpecProp) {
        this.extSpecProp = extSpecProp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.item_style_no - 款式编码(体育)
     * @return the value of item.item_style_no
     */
    public String getItemStyleNo() {
        return itemStyleNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.item_style_no - 款式编码(体育)
     * @param itemStyleNo the value for item.item_style_no
     */
    public void setItemStyleNo(String itemStyleNo) {
        this.itemStyleNo = itemStyleNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.item_series_no - 系列编码(体育)
     * @return the value of item.item_series_no
     */
    public String getItemSeriesNo() {
        return itemSeriesNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.item_series_no - 系列编码(体育)
     * @param itemSeriesNo the value for item.item_series_no
     */
    public void setItemSeriesNo(String itemSeriesNo) {
        this.itemSeriesNo = itemSeriesNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.japan_name - 日本名称
     * @return the value of item.japan_name
     */
    public String getJapanName() {
        return japanName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.japan_name - 日本名称
     * @param japanName the value for item.japan_name
     */
    public void setJapanName(String japanName) {
        this.japanName = japanName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.designer_name - 设计者
     * @return the value of item.designer_name
     */
    public String getDesignerName() {
        return designerName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.designer_name - 设计者
     * @param designerName the value for item.designer_name
     */
    public void setDesignerName(String designerName) {
        this.designerName = designerName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.origine_country - 原产国
     * @return the value of item.origine_country
     */
    public String getOrigineCountry() {
        return origineCountry;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.origine_country - 原产国
     * @param origineCountry the value for item.origine_country
     */
    public void setOrigineCountry(String origineCountry) {
        this.origineCountry = origineCountry;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.japan_tag_price - 日本牌价
     * @return the value of item.japan_tag_price
     */
    public BigDecimal getJapanTagPrice() {
        return japanTagPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.japan_tag_price - 日本牌价
     * @param japanTagPrice the value for item.japan_tag_price
     */
    public void setJapanTagPrice(BigDecimal japanTagPrice) {
        this.japanTagPrice = japanTagPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.japan_cost - 日本成本
     * @return the value of item.japan_cost
     */
    public BigDecimal getJapanCost() {
        return japanCost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.japan_cost - 日本成本
     * @param japanCost the value for item.japan_cost
     */
    public void setJapanCost(BigDecimal japanCost) {
        this.japanCost = japanCost;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.sale_year - 上柜年
     * @return the value of item.sale_year
     */
    public String getSaleYear() {
        return saleYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.sale_year - 上柜年
     * @param saleYear the value for item.sale_year
     */
    public void setSaleYear(String saleYear) {
        this.saleYear = saleYear;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.sale_week - 上柜周
     * @return the value of item.sale_week
     */
    public String getSaleWeek() {
        return saleWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.sale_week - 上柜周
     * @param saleWeek the value for item.sale_week
     */
    public void setSaleWeek(String saleWeek) {
        this.saleWeek = saleWeek;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.brand_season - 品牌季节
     * @return the value of item.brand_season
     */
    public String getBrandSeason() {
        return brandSeason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.brand_season - 品牌季节
     * @param brandSeason the value for item.brand_season
     */
    public void setBrandSeason(String brandSeason) {
        this.brandSeason = brandSeason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.item_flag - 商品标志
     * @return the value of item.item_flag
     */
    public String getItemFlag() {
        return itemFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.item_flag - 商品标志
     * @param itemFlag the value for item.item_flag
     */
    public void setItemFlag(String itemFlag) {
        this.itemFlag = itemFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column item.plate_code - 板单号
     * @return the value of item.plate_code
     */
    public String getPlateCode() {
        return plateCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column item.plate_code - 板单号
     * @param plateCode the value for item.plate_code
     */
    public void setPlateCode(String plateCode) {
        this.plateCode = plateCode;
    }
}