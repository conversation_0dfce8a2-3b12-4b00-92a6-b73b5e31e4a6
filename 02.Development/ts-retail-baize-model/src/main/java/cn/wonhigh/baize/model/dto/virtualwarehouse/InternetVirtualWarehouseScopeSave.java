package cn.wonhigh.baize.model.dto.virtualwarehouse;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


public class InternetVirtualWarehouseScopeSave implements Serializable {

    @NotBlank(message = "聚合仓编码不能为空")
    private String vstoreCode;

    @NotBlank(message = "货管不能为空")
    private String orderUnitNo;
    private String orderUnitName;

    @NotNull(message = "店铺类型不能为空")
    private Integer storeType;

    //@NotNull(message = "多机构标识不能为空")
    private String moreStoreFlag;

    //@NotNull(message = "库存类型不能为空")
    private Integer inventoryType;

    // 默认是启用
    private Integer status = 1;

    private List<SaveDtl> addDtls;

    public static SaveDtl buildDtl(String storeNo, String storeName, Integer storeType, String orderUnitNo, String  orderUnitName, Integer status){
        SaveDtl saveDtl = new SaveDtl();
        saveDtl.setStoreNo(storeNo);
        saveDtl.setStoreName(storeName);
        saveDtl.setStoreType(storeType);
        saveDtl.setOrderUnitNo(orderUnitNo);
        saveDtl.setOrderUnitName(orderUnitName);
        saveDtl.setStatus(status);
        return saveDtl;
    }


    public static class  SaveDtl{

        private String storeNo;

        private String storeName;

        private Integer storeType;

        private String orderUnitNo;
        private String  orderUnitName;

        private String originalVstoreCode;

        private String originalVstoreName;

        // 默认是启用
        private Integer status = 1;

        private String id;
        private Boolean isEdit = false;

        public String getStoreNo() {
            return storeNo;
        }

        public void setStoreNo(String storeNo) {
            this.storeNo = storeNo;
        }

        public String getStoreName() {
            return storeName;
        }

        public void setStoreName(String storeName) {
            this.storeName = storeName;
        }

        public String getOrderUnitNo() {
            return orderUnitNo;
        }

        public void setOrderUnitNo(String orderUnitNo) {
            this.orderUnitNo = orderUnitNo;
        }

        public String getOrderUnitName() {
            return orderUnitName;
        }

        public void setOrderUnitName(String orderUnitName) {
            this.orderUnitName = orderUnitName;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getStoreType() {
            return storeType;
        }

        public void setStoreType(Integer storeType) {
            this.storeType = storeType;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Boolean getIsEdit() {
            return isEdit;
        }

        public void setIsEdit(Boolean isEdit) {
            this.isEdit = isEdit;
        }

        public String getOriginalVstoreCode() {
            return originalVstoreCode;
        }

        public void setOriginalVstoreCode(String originalVstoreCode) {
            this.originalVstoreCode = originalVstoreCode;
        }

        public String getOriginalVstoreName() {
            return originalVstoreName;
        }

        public void setOriginalVstoreName(String originalVstoreName) {
            this.originalVstoreName = originalVstoreName;
        }
    }


    public List<SaveDtl> getAddDtls() {
        if(addDtls == null){
            addDtls = new ArrayList<>();
        }
        return addDtls;
    }

    public void setAddDtls(List<SaveDtl> addDtls) {
        this.addDtls = addDtls;
    }

    public String getVstoreCode() {
        return vstoreCode;
    }

    public void setVstoreCode(String vstoreCode) {
        this.vstoreCode = vstoreCode;
    }

    public String getOrderUnitNo() {
        return orderUnitNo;
    }

    public void setOrderUnitNo(String orderUnitNo) {
        this.orderUnitNo = orderUnitNo;
    }

    public String getOrderUnitName() {
        return orderUnitName;
    }

    public void setOrderUnitName(String orderUnitName) {
        this.orderUnitName = orderUnitName;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public String getMoreStoreFlag() {
        return moreStoreFlag;
    }

    public void setMoreStoreFlag(String moreStoreFlag) {
        this.moreStoreFlag = moreStoreFlag;
    }

    public Integer getInventoryType() {
        return inventoryType;
    }

    public void setInventoryType(Integer inventoryType) {
        this.inventoryType = inventoryType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("vstoreCode", vstoreCode)
                .append("orderUnitNo", orderUnitNo)
                .append("orderUnitName", orderUnitName)
                .append("storeType", storeType)
                .append("moreStoreFlag", moreStoreFlag)
                .append("inventoryType", inventoryType)
                .append("addDtls", addDtls)
                .append("status", status)
                .toString();
    }


}
