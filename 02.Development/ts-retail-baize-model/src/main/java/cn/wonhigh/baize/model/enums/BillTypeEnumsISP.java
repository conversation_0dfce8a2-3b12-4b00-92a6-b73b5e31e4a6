package cn.wonhigh.baize.model.enums;

import java.util.HashSet;
import java.util.Set;


/**
 * 
 * 单据类型(配送/退仓/收货/退货/盘点)
 * 
 * <AUTHOR>
 * @date 2013-11-29 下午2:17:35
 * @version 0.1.0
 * @copyright yougou.com
 */
public enum BillTypeEnumsISP {
	//-------采购入库管理-----
	TRANSPORT(1300, "货运单","bill_transport"),
	ASN(1301, "到货单","bill_asn",new int[]{0,1,2,10,11,12,33,34,5},new String[]{"订货","补货","直接","差异调整（订货）","差异调整（补货）","差异调整（直接）","收购","差异调整（收购）","差异处理"}),
	RECEIPT(1304, "验收单","bill_receipt",new int[]{0,1,2,10,11,12,33,34,5},new String[]{"订货","补货","直接","差异调整（订货）","差异调整（补货）","差异调整（直接）","收购","差异调整（收购）","差异处理"}),


	//---------仓出店管理---------------------	
	DELIVERYNT(1325, "配货单", "bill_delivery_nt",new int[]{0,1},new String[]{"正常配货","备用配货"}),
	DELIVERYNT_OUT(1318, "仓出店单", "bill_delivery", DELIVERYNT,new int[]{0,1},new String[]{"正常配货","备用配货"}),
	RESTOCK_SUGGEST(1357, "补货建议", "bill_restock_suggest"),


	//--------------退厂管理-------------------
	RETURNOWNNT(1386, "原残退厂通知单","bill_return_nt"),
	RETURNCUSTOMERNT(1387, "客残退厂通知单","bill_return_nt"),
	RETURNOWN(1333, "原残退厂发货单","bill_return"),
	RETURNO_CONFIRM(1338, "原残退厂收货单","bill_return"),
	RETURNCUSTOMER(1334, "客残退厂发货单","bill_return"),
	SALEOUT(1335, "客残销售出库单","bill_sale_out",new int[]{2,3,21,22,23,24,10,50,51,52,53,54,55},new String[]{"地区承担","总部承担","批发销售","批发退货","团购销售","团购退货","盘差处理单","正常销售","跨店销售","商场团购","公司团购","员工内购","专柜团购"}),
	REPAIRIN(1340, "返修入库","bill_repair_in"),
	RETURNCUSTOMERCONFIRM(1332, "客残退厂结果鉴定处理","bill_return"),
	RETURNCUSTOMERZONECONFIRM(1331, "地区客残鉴定处理","bill_return"),
	SALEOUTZONE(2005, "地区客残销售出库单","bill_transfer"),
	SALEOUTHQ(2006, "总部客残销售出库单","bill_transfer"),
	//add by dai.xw2016-04-13 18:28:40新加的业务类型区分召回的退货通知单和原来的退外厂通知单
	RECALLNT(2015, "退货通知单","bill_return_nt"), 
	RECALLNTCONFIRM(2016, "鉴定结果单","bill_recall_confirm"),
	BILLRETURNDIFFERENCES(2018, "鉴定差异列表","bill_return_differences"),
//	//add by li.df 2016-04-20 14:56:40 采购退货单
//	RETURN_PURCHASE(2017,"采购退货单","bill_return"),

	//-------------店退残-----------------
	DELIVERY_RETURN_NT(1326, "店退残通知单", "bill_transfer_nt"),
	DELIVERY_RETURN(1324, "店退残单", "bill_delivery_return",DELIVERY_RETURN_NT,new int[]{0,1},new String[]{"原残","客残"}),


	//----------------调货------------------------
	BILL_TRANSFER_NT(1321, "调货通知单", "bill_transfer_nt",new int[]{0,1,2},new String[]{"正常调货","过季调货","原残调货"}),
	BillTransferCargoNt(1323, "跨区调货通知单", "bill_transfer_nt"),

	//从调货通知单生成的仓出店
	DELIVERYNT_OUT_FROM_TRANSFER_NT(1318, "仓出店单", "bill_delivery", BILL_TRANSFER_NT),


	STORE_STORE(1341, "移仓出（入）库单（仓到仓）", "bill_transfer", BILL_TRANSFER_NT),
	SHOP_STORE(1319, "移仓出（入）库单（店到仓）","bill_transfer", BILL_TRANSFER_NT),
	transfer(1317, "店转货单", "bill_transfer", BILL_TRANSFER_NT),
	REGION_SHOP_SHOP(1320, "调货出（入）库单（店到店）", "bill_transfer", BillTransferCargoNt,new int[]{4,5,6},new String[]{"跨区调货","差异调整","店退仓"}),
	REGION_STORE_STORE(1327, "调货出（入）库单（仓到仓）","bill_transfer", BillTransferCargoNt,new int[]{4,5,6},new String[]{"跨区调货","差异调整","店退仓"}),
	REGION_SHOP_STORE(1322, "调货出（入）库单（店到仓）", "bill_transfer",BillTransferCargoNt,new int[]{4,5,6},new String[]{"跨区调货","差异调整","店退仓"}),
	REGION_STORE_SHOP(1328, "调货出（入）库单（仓到店）", "bill_transfer", BillTransferCargoNt),
	
	BATCH_STORE_OUT(1391, "跨区调货分批出库单","bill_transfer_batch_out", BillTransferCargoNt),
	BATCH_STORE_IN(1392, "跨区调货分批入库单","bill_transfer_batch_in", BillTransferCargoNt),
	BATCH_STORE_OUT_BUYOUT(1393, "跨区调货分批出库单O2O销退买断","bill_transfer_batch_out", BillTransferCargoNt),

	
	TRANSFER_OUT(1371, "调货出库单", "bill_transfer", BillTransferCargoNt),
	//用于生成单据编号和记账
	TRANSFER_IN(1372, "调货入库单", "bill_transfer", BillTransferCargoNt),
	//用于生成单据编号和记账
	TRANSFERWAREHOUSE(1373, "移仓单", "bill_transfer", BILL_TRANSFER_NT),
	//用于生成单据编号和记账
	TRANSFER_FROM_DELIVERY_NT(1373, "从配货单生成的移仓单", "bill_transfer", DELIVERYNT),
	//用于生成单据编号和记账
	TRANSFER_FROM_RETURN_NT(1399, "从退货通知单生成的移仓单", "bill_transfer", RECALLNT),
	//用于生成单据编号和记账
	OTHER_WARE_HOUSE(2002, "其它出库单", "bill_sale_out"),
	//用于生成单据编号

	//-------------批发销售管理------------------------------
	SALEQUOTATION(1336, "报价单"),
	SALEORDER(1337, "批发订单", "bill_sale_order"),
	GROUP_ORDER(1378, "团购订单", "bill_sale_order"),
	//TODO 是否有区分biz_type
	WHOLESALENT(1339, "批发通知单", "bill_wholesale_nt", SALEORDER),
	SALEOUTS(1335, "批发/团购出库单", "bill_sale_out", WHOLESALENT),
	GROUP_PURCHASE_OUT_NT(1329, "团购发货通知单", "bill_wholesale_nt"),
	WHOLESALE_PRICE_CHANGE(2007, "批发价变更单", "bill_wholesaleprice_change"),
	GROUP_PRICE_CHANGE(2008, "团购价变更单", "bill_group_price_change"),
	AUTOMATIC_ALLOT_TASK(2009, "自动分货任务单", "bill_automatic_allot_task"),
	AUTOMATIC_ALLOT_PROPOSAL(2010, "自动分货建议单", "bill_automatic_allot_proposal"),


	//----------------报废管理------------------------
	LOSS(1342, "报废单", "bill_loss"),
	LOSSNT(1380, "报废通知单", "bill_loss_nt"),


	//---------------库存管理---------------------------------
	INVENTORYYADJUSTNT(1343, "库存调整通知单","bill_inventory_adjust_nt"),
	//用于生成单据编号
	INVENTORY_ADJUST_CHECK(1370, "区内差异调整审批单"),
	INVENTORY_ADJUST(1379, "库存调整单","bill_inventory_adjust"),
	//用于生成单据编号	
	WHOLESALE_OUT(1376, "批发出库单", "bill_sale_out"),
	GROUP_PURCHASE_OUT(1377, "团购出库单", "bill_sale_out"),
	

	RETAIL_BACKUP(1344, "正品转备货","bill_inventory_adjust_nt"),
	BACKUP_RETAIL(1346, "备货转正品","bill_inventory_adjust_nt"),
	INVENTORYYADJUST(1345, "库存冻结解冻单","bill_inventory_adjust_nt"),
	GUEST_QUALITY(1381,"客残转正品","bill_inventory_adjust_nt"),
	ORIGINAL_QUALITY(1382, "原残转正品","bill_inventory_adjust_nt"),
	QUALITY_ORIGINAL(1383, "正品转原残","bill_inventory_adjust_nt"),
	ORDERUNITADJUST(1347, "货管单位调整单","bill_inventory_adjust_nt"),
	INVENTORY_NUM_ADJUST(1348, "区内差异调整","bill_inventory_adjust"),
	GUEST_ORIGINAL(1384,"客残转原残","bill_inventory_adjust_nt"),
	ORIGINAL_GUEST(1385,"原残转客残","bill_inventory_adjust_nt"),
	QUALITY_GUEST(1388,"正品转客残","bill_inventory_adjust_nt"),


	//--------------盘点管理------------------------------
	CHECK(1349, "盘点单","bill_checkstk"),
	CHECKDIFF(1350, "盘差单"),


	//----------------差异管理--------------------------------
	DiFFRECEIPT(1353, "厂入库差异单","bill_diff_receipt"),
	DIFFTRANSFER(1354, "跨区调货差异单","bill_diff_transfer"),
	CLAIM(1355, "内销单"),
 

	//--------------借用管理-----------------------------------
	BORROW(1360, "借用单","bill_borrow"),
	BORROWOUT(1361, "借用出库单","bill_sale_out",new int[]{7,25},new String[]{"样品销售单","样品销售单(跨结算主体)"}),
	BORROWRETURN(1362, "借用单归还单","bill_borrow"),
	BORROWCONFIRM(1363, "借用单归还处理","bill_borrow"),
	BORROWNT(1364, "借用通知单","bill_borrow_nt"),
	BORROWNTRETURN(1365, "借用归还通知单","bill_borrow_nt"),


	RETURNNT(1312, "退货申请单"),
	RETURN(1313, "退货单"),

	//-----------------pos---------------------------
	salesOrder(1375, "销售预占"),
	salesReturn(1374, "销售退货入库","bill_sale_out"),
	INVENTORY_COST_ADJUST(2000, "库存成本调整单","bill_inv_cost_adjust"),
	SALE_MEND(2001,"维修单"),//用于生成pos 维修单 单据编号
	DIFF_APPLY(1356,"差异申请单"),
	RETURN_EXCHANGE_MAIN(2003,"退换货单"),
    ORDER_MAIN(2004,"退换货单"),
	
	//-----------------储位相关---------------------------
	TRANSFER_STORAGE(1400, "移库单", "bill_transfer_storage"),
	PICK(1401, "拣货单", "bill_pick"),
	DIVIDE(1402, "按箱分货单", "bill_divide"),
	PICKTASK(1498, "拣货单任务单"),//移动gms
	DIVIDETASK(1499, "按箱分货任务单"),//移动gms

	PERIOD_BALANCE(3000, "生成结存"),
	
	PACKING_EMAIL(3001,"解析装箱单附件"),
	PACKING_GENARATE_ASN(3002,"装箱单生成到货单"),
	PACKING_SEND_NEWSKU(3003,"同步新尺码/新条码"),
	PACKING_FORWARD_EMAIL(3004,"装箱单转发邮件"),
	
	
	//---------------供应商变更----------------------
	BILL_CHANGE_SUPPLIER(1450,"供应商变更单"),
	
	//---------------折扣率调整单----------------------
	BILL_DISCOUNT(1451,"折扣率调整单"),
	
	//---------------品牌库存转移单----------------------
	BILL_CHNAGE_BRAND_NT(1452,"品牌库存转移通知单","bill_change_brand"),
	BILL_CHNAGE_BRAND(1453,"品牌库存转移单","bill_change_brand"),
	
	BILL_TRANSFER_TABLE(1454, "调货总单", "bill_transfer_table"),
	
	//共享库存更变
	SHARE_INVENTORY(4444,"共享库存变更","share_inventory_change_record"),
	 
	//4开头,4000~4999 留给ISP使用 其他单据请不要使用
	BILL_INTERNET_OUT_NT(4000,"网销发货通知单","bill_internet_out_nt"),
	BILL_INTERNET_OUT(4001,"网销出库单/退货单","bill_internet_out"),
	YOUGOU_ORDER_COMPLETED_UPDATE(4002,"将网销订单置为已发货"),
	YOUGOU_INVENTORY_UPDATE(4003,"更新网销库存信息"),
	YOUGOU_RETURN_QUALITY_ADD(4004,"退换货质检登记","internet_order_cancel_apply"),//包裹拒收
	BILL_INTERNET_ORDER(4005,"网销订单","bill_internet_order"),
	YOUGOU_IMPORT_DETAIL(4006,"唯品会出库绑定明细"),
	BILL_INTETNET_RETURN_NT(4007,"网销退货通知单","bill_internet_return_nt"),
	INTERNET_ORDER_TO_POS(4008,"通知单同步到POS订单中心"),
	INTERNET_OUT_TO_POS(4009,"退货单同步到POS订单中心"),
	REFUND_CANCEL_ORDER_TO_POS(4010,"退款成功的取消申请单"),
	REFUND_CUSTOMER_ORDER_TO_POS(4011,"退款成功的售后申请单"),
	INTERNET_ORDER_CONVERSION(4012,"平台转换销售订单"),
	YG_INTERNET_TRANSFER_CG_IN(4013,"优购采购入库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_CG_OUT(4014,"优购采购出库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_XT_IN(4015,"优购销退入库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_XT_OUT(4016,"优购销退出库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_DB_IN(4017,"优购调拨入库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_DB_OUT(4018,"优购调拨出库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_XS_IN(4019,"优购销售入库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_XS_OUT(4020,"优购销售出库","bill_internet_transfer"),
	YG_INTERNET_TRANSFER_CGTH(4021,"优购采购退货","bill_internet_transfer"),
	YG_ORDER_CANCEL(4022,"优购问题单取消"),
	YG_SPORTS_INTERNET_ORDER(4023,"优购体育销售订单"),
	YG_SPORTS_INTERNET_ORDER_RETURN(4024,"优购体育销售订单退货单"),
	YG_SPORTS_VIP_DISCOUNT(4025,"唯品会订单优惠信息同步到POS"),
	BILL_INTERNET_OUT_OCP(4026,"网销出库单/退货单传递ocp"),
	YOUGOU_RETURN_REJECTION(4027,"拒收质检回传接口"),
	YOUGOU_CHANGE_COMPANY(4028,"换结算公司"),
	INTERNET_ORDER_BATCH_RETURN_NT(4029,"退回单通知单"),
	INTERNET_ORDER_CHECK_RETURN_MATCH_HANDLED(4030, "无单售后质检差异处理", "internet_order_check_return_matched.handled_batch"),
	INTERNET_TRANSFER_BATCH_OUT(4031, "增加调拨出库单店到店", "internet_transfer_batch_out"),
	INTERNET_TRANSFER_BATCH_OUT_TWO(4032, "增加调拨出库单店到店已收货", "internet_transfer_batch_out"),
	/**
	 * 零售订单类型,用于预派单/派单异步,区分原单派单和拒单派单
	 */
	BILL_INTERNET_ORDER_MAIN(4033, "零售订单", "bill_internet_order_main"),
	INVENTORY_ACTIVE_LOCK(4034, "活动锁库", "inventory_active_lock"),
	//4开头,4000~4999 留给ISP使用 其他单据请不要使用
	WMS_BOX_INFO(5000,"WMS回传箱状态"),
	SHOP_BLANCE(1888,"MS-结算单"),
	;
	
	/**
	 * SERIALNO_CONFIG 表 REQUESTID 的值
	 */
	private Integer requestId;
	/**
	 * 模块描述
	 */
	private String desc;
	/**
	 * 建议枚举名就是表名 ,这样就可以不用tableName
	 */
	private String tableName;
	
	/**
	 * 业务类型数组
	 */
	private int [] bizTypeList;
	
	/**
	 * 业务类型数组
	 */
	private String[] bizTypeStrList;

	/**
	 * 所属通知单的BillType
	 */
	private BillTypeEnumsISP ntBillType;

	private BillTypeEnumsISP(Integer requestId, String desc) {
		this.requestId = requestId;
		this.desc = desc;
	}

	/**
	 * @param requestId
	 * @param desc
	 * @param tableName
	 */
	private BillTypeEnumsISP(Integer requestId, String desc, String tableName) {
		this.requestId = requestId;
		this.desc = desc;
		this.tableName = tableName;
	}
	
	/**
	 * @param requestId
	 * @param desc
	 * @param tableName
	 */
	private BillTypeEnumsISP(Integer requestId, String desc, String tableName, int[] bizTypeList, String[] bizTypeStrList) {
		this.requestId = requestId;
		this.desc = desc;
		this.tableName = tableName;
		this.bizTypeList = bizTypeList;
		this.bizTypeStrList = bizTypeStrList;
	}

	/**
	 * @param requestId
	 * @param desc
	 * @param tableName
	 * @param ntBillType
	 */
	private BillTypeEnumsISP(Integer requestId, String desc, String tableName, BillTypeEnumsISP ntBillType) {
		this.requestId = requestId;
		this.desc = desc;
		this.tableName = tableName;
		this.ntBillType = ntBillType;
	}
	/**
	 * @param requestId
	 * @param desc
	 * @param tableName
	 * @param ntBillType
	 */
	private BillTypeEnumsISP(Integer requestId, String desc, String tableName, BillTypeEnumsISP ntBillType, int[] bizTypeList, String[] bizTypeStrList) {
		this.requestId = requestId;
		this.desc = desc;
		this.tableName = tableName;
		this.ntBillType = ntBillType;
		this.bizTypeList = bizTypeList;
		this.bizTypeStrList = bizTypeStrList;
	}

	/**
	 * 如果没有表明，使用枚举名。建议枚举名就是表名
	 * 
	 * @return the tableName
	 */
	public String getTableName() {
		return tableName == null ? this.toString() : tableName;
	}

	

	public Integer getRequestId() {
		return requestId;
	}

	public String getDesc() {
		return desc;
	}

	/**
	 * @return the ntBillType
	 */
	public BillTypeEnumsISP getNtBillType() {
		return ntBillType;
	}

	public int[] getBizTypeList() {
		return bizTypeList;
	}

	protected void setBizTypeList(int[] bizTypeList) {
		this.bizTypeList = bizTypeList;
	}

	public String[] getBizTypeStrList() {
		return bizTypeStrList;
	}

	/**
	 * 根据requestId得到BillTypeEnumsISP
	 * @param requestId
	 * @return
	 */
	public static BillTypeEnumsISP getBillTypeEnum(Integer requestId) {
		if (requestId == null) {
			return null;
		}
		BillTypeEnumsISP[] values = BillTypeEnumsISP.values();
		for (BillTypeEnumsISP billTypeEnum : values) {
			if (billTypeEnum.getRequestId().intValue() == requestId) {
				return billTypeEnum;
			}
		}
		return null;
	}
	
	/**
	 * 根据requestId得到BillTypeEnumsISP
	 * @param requestId
	 * @return
	 */
	public static BillTypeEnumsISP getBillTypeEnum(String str) {
		if (str == null) {
			return null;
		}
		BillTypeEnumsISP[] values = BillTypeEnumsISP.values();
		for (BillTypeEnumsISP billTypeEnum : values) {
			if (billTypeEnum.getDesc().equals(str)) {
				return billTypeEnum;
			}
		}
		return null;
	}
	
	/**
	 * 
	 * @param tableName
	 * @return
	 */
	public static Integer getRequestIdByTableName(String tableName){
		int retObj = -1;
		if(null == tableName || "".equals(tableName)){
			return retObj;
		}
		for(BillTypeEnumsISP em : BillTypeEnumsISP.values()){
			if(em.getTableName().equals(tableName)){
				retObj = em.getRequestId();
				break;
			}
		}
		
		return retObj;
	}
	
	private static Set<Integer> billTypeFromShop = new HashSet<>();
	
	/**
	 * 是否是从POS过来的单据
	 * @param billType
	 * @return
	 */
	public static boolean isFromShop(Integer billType){
		return billType!=null&&billTypeFromShop.contains(billType);
	}
	/**
	 * 是否是装箱单相关的类型
	 * @return
	 */
	public static boolean isPackingBillType(int billType){
		return PACKING_EMAIL.getRequestId()==billType||PACKING_GENARATE_ASN.getRequestId()==billType
				||PACKING_SEND_NEWSKU.getRequestId()==billType
						||PACKING_FORWARD_EMAIL.getRequestId()==billType;
	}
	
	public static String getStrByBizType(Integer requestId,Integer bizType){
		BillTypeEnumsISP BillTypeEnumsISP = getBillTypeEnum(requestId);
		if(BillTypeEnumsISP==null){
		    return "";
		}
		int [] bizTypeList = BillTypeEnumsISP.getBizTypeList();
        if(bizTypeList!=null && bizTypeList.length>0){
            for(int i=0;i<bizTypeList.length;i++){
                if(bizTypeList[i] == bizType){
                    return  BillTypeEnumsISP.getBizTypeStrList()[i];
                }
            }
        }
		return "";
	}
	
	public static Integer getRequestIdByStr(String str){
		BillTypeEnumsISP BillTypeEnumsISP = getBillTypeEnum(str);
		if(BillTypeEnumsISP!=null){
			return BillTypeEnumsISP.getRequestId();
		}
		return null;
	}
	
	
	private static Set<Integer> internerBillType = new HashSet<>();
	static {
		internerBillType.add(BillTypeEnumsISP.BILL_INTERNET_OUT_NT.getRequestId()); 
		internerBillType.add(BillTypeEnumsISP.BILL_INTERNET_OUT.getRequestId()); 
		internerBillType.add(BillTypeEnumsISP.YOUGOU_ORDER_COMPLETED_UPDATE.getRequestId()); 
		internerBillType.add(BillTypeEnumsISP.YOUGOU_INVENTORY_UPDATE.getRequestId()); 
		internerBillType.add(BillTypeEnumsISP.YOUGOU_RETURN_QUALITY_ADD.getRequestId()); 
		internerBillType.add(BillTypeEnumsISP.BILL_INTERNET_ORDER.getRequestId()); 
		internerBillType.add(BillTypeEnumsISP.YOUGOU_IMPORT_DETAIL.getRequestId());
		internerBillType.add(BillTypeEnumsISP.INTERNET_ORDER_TO_POS.getRequestId());
		internerBillType.add(BillTypeEnumsISP.INTERNET_OUT_TO_POS.getRequestId());
		internerBillType.add(BillTypeEnumsISP.BILL_INTETNET_RETURN_NT.getRequestId());
		internerBillType.add(BillTypeEnumsISP.REFUND_CANCEL_ORDER_TO_POS.getRequestId());
		internerBillType.add(BillTypeEnumsISP.REFUND_CUSTOMER_ORDER_TO_POS.getRequestId());
		internerBillType.add(BillTypeEnumsISP.INTERNET_ORDER_CONVERSION.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_CG_IN.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_CG_OUT.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_XT_IN.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_XT_OUT.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_DB_IN.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_DB_OUT.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_XS_IN.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_XS_OUT.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_INTERNET_TRANSFER_CGTH.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_SPORTS_INTERNET_ORDER.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_SPORTS_INTERNET_ORDER_RETURN.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YG_SPORTS_VIP_DISCOUNT.getRequestId());
		internerBillType.add(BillTypeEnumsISP.BILL_INTERNET_OUT_OCP.getRequestId());
		internerBillType.add(BillTypeEnumsISP.YOUGOU_RETURN_REJECTION.getRequestId());
		
		billTypeFromShop.add(BillTypeEnumsISP.transfer.getRequestId());//区内店到店
        billTypeFromShop.add(BillTypeEnumsISP.SHOP_STORE.getRequestId());// 区内店到仓
        billTypeFromShop.add(BillTypeEnumsISP.REGION_SHOP_SHOP.getRequestId());// 区外的店到店 
        billTypeFromShop.add(BillTypeEnumsISP.REGION_SHOP_STORE.getRequestId());// 区外的 店到仓
        billTypeFromShop.add(BillTypeEnumsISP.REGION_STORE_SHOP.getRequestId());// 区外的仓到店
	}
	
	public static boolean isInternerBillType(Integer billType){
		return billType!=null&&internerBillType.contains(billType);
	}
	
}
