/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 活动锁库调整单
**/
public class IcsActiveLockAdjust  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1749440735279L;
    
    //调整类型 1增加数量 -1减少数量
    @Label("调整类型") 
    private Integer adjustType;
    
    //备注
    @Label("备注") 
    private String remark;
    
    //同步状态 0未同步 1部分同步 2全部同步
    @Label(value = "同步状态", defaultVal = "0") 
    private Integer syncStatus;
    
    //调整单状态 0新建 1审核成功 2审核失败 3已作废
    @Label(value = "调整单状态", defaultVal = "0") 
    private Integer adjustStatus;
    
    //活动锁库单号
    @Label("活动锁库单号") 
    private String refBillNo;
    
    //锁库调整单号
    @Label("锁库调整单号") 
    private String billNo;

    private String warehouseCode;

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public Integer getAdjustType(){
        return  adjustType;
    }
    public void setAdjustType(Integer val ){
        adjustType = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    public Integer getSyncStatus(){
        return  syncStatus;
    }
    public void setSyncStatus(Integer val ){
        syncStatus = val;
    }
    
    public Integer getAdjustStatus(){
        return  adjustStatus;
    }
    public void setAdjustStatus(Integer val ){
        adjustStatus = val;
    }
    
    public String getRefBillNo(){
        return  refBillNo;
    }
    public void setRefBillNo(String val ){
        refBillNo = val;
    }
    
    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public IcsActiveLockAdjustBuilder build(){
        return new IcsActiveLockAdjustBuilder(this);
    }

    public static class IcsActiveLockAdjustBuilder extends AbstractEntryBuilder<IcsActiveLockAdjust>{

        private IcsActiveLockAdjustBuilder(IcsActiveLockAdjust entry){
            this.obj = entry;
        }

       @Override
		public IcsActiveLockAdjust object() {
			return this.obj;
		}

        
        public IcsActiveLockAdjustBuilder adjustType(Integer value ){
            this.obj.adjustType = value;
            if( query == null  )
                query = new Query();
            this.query.where("adjustType", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder syncStatus(Integer value ){
            this.obj.syncStatus = value;
            if( query == null  )
                query = new Query();
            this.query.where("syncStatus", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder adjustStatus(Integer value ){
            this.obj.adjustStatus = value;
            if( query == null  )
                query = new Query();
            this.query.where("adjustStatus", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder refBillNo(String value ){
            this.obj.refBillNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("refBillNo", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder billNo(String value ){
            this.obj.billNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public IcsActiveLockAdjustBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}