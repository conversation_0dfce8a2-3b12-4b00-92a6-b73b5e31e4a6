/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.domain.IEntryBuildable;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


/** auto generate start ,don't modify */

/**
* 派单规则配置明细表
**/
public class InternetDispatchRuleSetDtl  extends cn.mercury.domain.BaseEntity<Long>
{

    private static final long serialVersionUID = 1685599549190L;
    
    /**
    *派单规则编码
    **/ 
    @Label("派单规则编码") 
    private String ruleNo;
    

    public String getRuleNo(){
        return  ruleNo;
    }
    public void setRuleNo(String val ){
        ruleNo = val;
    }
    /**
    *渠道编码
    **/ 
    @Label("渠道编码") 
    private String channelNo;
    

    public String getChannelNo(){
        return  channelNo;
    }
    public void setChannelNo(String val ){
        channelNo = val;
    }
    /**
    *渠道名称
    **/ 
    @Label("渠道名称") 
    private String channelName;
    

    public String getChannelName(){
        return  channelName;
    }
    public void setChannelName(String val ){
        channelName = val;
    }
    /**
    *多店铺标识,0标识单店，1表示多店（渠道所有店）
    **/ 
    @Label("多店铺标识") 
    private Integer moreShopFlag;
    

    public Integer getMoreShopFlag(){
        return  moreShopFlag;
    }
    public void setMoreShopFlag(Integer val ){
        moreShopFlag = val;
    }
    /**
    *店铺编码
    **/ 
    @Label("店铺编码") 
    private String orderSourceNo;
    

    public String getOrderSourceNo(){
        return  orderSourceNo;
    }
    public void setOrderSourceNo(String val ){
        orderSourceNo = val;
    }
    /**
    *店铺名称
    **/ 
    @Label("店铺名称") 
    private String orderSourceName;
    

    public String getOrderSourceName(){
        return  orderSourceName;
    }
    public void setOrderSourceName(String val ){
        orderSourceName = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InternetDispatchRuleSetDtlBuilder build(){
        return new InternetDispatchRuleSetDtlBuilder(this);
    }

    public static class InternetDispatchRuleSetDtlBuilder extends AbstractEntryBuilder<InternetDispatchRuleSetDtl>{

        private InternetDispatchRuleSetDtlBuilder(InternetDispatchRuleSetDtl entry){
            this.obj = entry;
        }

       @Override
		public InternetDispatchRuleSetDtl object() {
			return this.obj;
		}

        
        public InternetDispatchRuleSetDtlBuilder ruleNo(String value ){
            
            this.obj.ruleNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("ruleNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder id(Long value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder channelNo(String value ){
            
            this.obj.channelNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("channelNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder channelName(String value ){
            
            this.obj.channelName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("channelName", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder moreShopFlag(Integer value ){
            
            this.obj.moreShopFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("moreShopFlag", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder orderSourceNo(String value ){
            
            this.obj.orderSourceNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSourceNo", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder orderSourceName(String value ){
            
            this.obj.orderSourceName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSourceName", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public InternetDispatchRuleSetDtlBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }