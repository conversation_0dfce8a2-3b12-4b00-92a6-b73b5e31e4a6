package cn.wonhigh.baize.model.entity.ios;

import cn.mercury.domain.BaseEntity;
import cn.mercury.utils.JsonDateDeserializer$19;
import cn.mercury.utils.JsonDateSerializer$19;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2025/3/28 11:28
 */
public class InternetTakeShopItem extends BaseEntity<String> {

    private static final long serialVersionUID = -1049539811190681305L;
    private String shopNo;
    private String shopName;
    private String channelNo;
    private String channelName;
    private String itemNo;
    private String itemCode;
    private String brandNo;
    private Integer takeSign;

    @JsonSerialize(
            using = JsonDateSerializer$19.class
    )
    @JsonDeserialize(
            using = JsonDateDeserializer$19.class
    )
    private Date stockSyncTime;

    public String getShopNo() {
        return shopNo;
    }

    public void setShopNo(String shopNo) {
        this.shopNo = shopNo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public Integer getTakeSign() {
        return takeSign;
    }

    public void setTakeSign(Integer takeSign) {
        this.takeSign = takeSign;
    }

    public Date getStockSyncTime() {
        return stockSyncTime;
    }

    public void setStockSyncTime(Date stockSyncTime) {
        this.stockSyncTime = stockSyncTime;
    }
}

