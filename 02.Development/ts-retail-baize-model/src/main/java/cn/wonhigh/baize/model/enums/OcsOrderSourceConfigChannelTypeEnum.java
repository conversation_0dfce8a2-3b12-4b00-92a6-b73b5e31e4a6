package cn.wonhigh.baize.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * 三级来源渠道类型枚举
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum OcsOrderSourceConfigChannelTypeEnum {


    PC("门店PC", "PC"),
    MMP("云店通", "MMP"),
    LDKD("离店开单", "LDKD"),
    XCXSD("小程序实店", "XCXSD"),
    XCXDQXD("小程序地区虚店", "XCXDQXD"),
    XCXZBXD("小程序总部虚店", "XCXZBXD"),
    OMSZB("OMS总部", "OMSZB"),
    OMSDQ("OMS地区", "OMSDQ"),
    PPF("品牌方", "PPF");


    private final String name;

    private final String type;


    OcsOrderSourceConfigChannelTypeEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }


    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getName(String type){
        OcsOrderSourceConfigChannelTypeEnum[] enums = OcsOrderSourceConfigChannelTypeEnum.values();
        for (OcsOrderSourceConfigChannelTypeEnum anEnum : enums) {
            if(anEnum.type.equals(type)){
                return anEnum.getName();
            }
        }
        return type;
    }
}
