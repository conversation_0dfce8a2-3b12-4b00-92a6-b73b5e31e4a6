package cn.wonhigh.baize.model.entity.oms;

import java.io.Serializable;

import cn.mercury.domain.BaseEntity;

import java.util.Date;

import cn.mercury.annotation.Label;

import cn.mercury.basic.query.Query;
import cn.mercury.domain.AbstractEntryBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;


/**
 * (OmsProductWmsMapping)实体类
 *
 * <AUTHOR>
 * @since 2024-12-28 11:21:22
 */
public class OmsProductWmsMapping extends BaseEntity<Long> implements Serializable {
    private static final long serialVersionUID = 333355806841580562L;
    /**
     * 商品仓库映射id
     */
    @Label("商品仓库映射id")
    private Long productWmsMappingId;
    /**
     * 更新时间
     */
    @Label("更新时间")
    private Date modifiedTime;
    /**
     * 创建时间
     */
    @Label("创建时间")
    private Date createdTime;
    /**
     * 商品id
     */
    @Label("商品id")
    private Long productId;
    /**
     * 商品规格id
     */
    @Label("商品规格id")
    private Long skuId;
    /**
     * 物流类型
     */
    @Label("物流类型")
    private Integer wmsType;
    /**
     * 仓库规格id
     */
    @Label("仓库规格id")
    private String wmsSkuId;
    /**
     * 客户标识
     */
    @Label("客户标识")
    private String customerId;
    /**
     * 货主编码
     */
    @Label("货主编码")
    private String ownerCode;
    /**
     * 优先级
     */
    @Label("优先级")
    private Integer priorityNo;


    public Long getProductWmsMappingId() {
        return productWmsMappingId;
    }

    public void setProductWmsMappingId(Long productWmsMappingId) {
        this.productWmsMappingId = productWmsMappingId;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Integer getWmsType() {
        return wmsType;
    }

    public void setWmsType(Integer wmsType) {
        this.wmsType = wmsType;
    }

    public String getWmsSkuId() {
        return wmsSkuId;
    }

    public void setWmsSkuId(String wmsSkuId) {
        this.wmsSkuId = wmsSkuId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public Integer getPriorityNo() {
        return priorityNo;
    }

    public void setPriorityNo(Integer priorityNo) {
        this.priorityNo = priorityNo;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public static OmsProductWmsMappingBuilder build() {
        return new OmsProductWmsMappingBuilder(new OmsProductWmsMapping());
    }

    public static OmsProductWmsMappingBuilder build(OmsProductWmsMapping value) {
        return new OmsProductWmsMappingBuilder(value);
    }

    public static class OmsProductWmsMappingBuilder extends AbstractEntryBuilder<OmsProductWmsMapping> {

        private OmsProductWmsMappingBuilder(OmsProductWmsMapping entry) {
            this.obj = entry;
        }

        @Override
        public OmsProductWmsMapping object() {
            return this.obj;
        }

        public OmsProductWmsMappingBuilder productWmsMappingId(Long value) {

            this.obj.setProductWmsMappingId(value);

            if (query == null)
                query = new Query();
            this.query.where("productWmsMappingId", value);
            return this;
        }

        public OmsProductWmsMappingBuilder modifiedTime(Date value) {

            this.obj.setModifiedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("modifiedTime", value);
            return this;
        }

        public OmsProductWmsMappingBuilder createdTime(Date value) {

            this.obj.setCreatedTime(value);

            if (query == null)
                query = new Query();
            this.query.where("createdTime", value);
            return this;
        }

        public OmsProductWmsMappingBuilder productId(Long value) {

            this.obj.setProductId(value);

            if (query == null)
                query = new Query();
            this.query.where("productId", value);
            return this;
        }

        public OmsProductWmsMappingBuilder skuId(Long value) {

            this.obj.setSkuId(value);

            if (query == null)
                query = new Query();
            this.query.where("skuId", value);
            return this;
        }

        public OmsProductWmsMappingBuilder wmsType(Integer value) {

            this.obj.setWmsType(value);

            if (query == null)
                query = new Query();
            this.query.where("wmsType", value);
            return this;
        }

        public OmsProductWmsMappingBuilder wmsSkuId(String value) {

            this.obj.setWmsSkuId(value);

            if (query == null)
                query = new Query();
            this.query.where("wmsSkuId", value);
            return this;
        }

        public OmsProductWmsMappingBuilder customerId(String value) {

            this.obj.setCustomerId(value);

            if (query == null)
                query = new Query();
            this.query.where("customerId", value);
            return this;
        }

        public OmsProductWmsMappingBuilder ownerCode(String value) {

            this.obj.setOwnerCode(value);

            if (query == null)
                query = new Query();
            this.query.where("ownerCode", value);
            return this;
        }

        public OmsProductWmsMappingBuilder priorityNo(Integer value) {

            this.obj.setPriorityNo(value);

            if (query == null)
                query = new Query();
            this.query.where("priorityNo", value);
            return this;
        }
    }
}
