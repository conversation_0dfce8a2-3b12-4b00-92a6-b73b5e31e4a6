package cn.wonhigh.baize.model.entity.gms;

import cn.wonhigh.baize.model.enums.ChannelTypeEnum;

public class StockDistirbutionConfig extends cn.mercury.domain.BaseEntity<String>   {
	private static final long serialVersionUID = 6716533193840346523L;

	/**
	 * 店铺编码
	 */
	private String shopNo;
	
	/**
	 * 店铺名称
	 */
	private String shopName;
	
	/**
	 * 品牌编码
	 */
	private String brandNo;
	
	/**
	 * 虚仓编码
	 */
	private String vstoreCode;
	
	/**
	 * 虚仓类型0线下1线上
	 */
	private Integer onlineType;
	
	/**
	 * 虚店范围类型1虚仓范围2跨店货管范围
	 */
	private Integer vstoreScopeType;
	
	/**
	 * 优先级
	 */
	private Integer vstoreLevel;

	/**
	 * 状态0关闭1开启
	 */
	private Integer status;
	
	/**
	 * 备注
	 */
	private String remark;

	private String vstoreName;

	private String statusName;

	private Integer channelType;

	//三级来源编码
	private String thirdChannelNo;

	public String getThirdChannelNo() {
		return thirdChannelNo;
	}

	public void setThirdChannelNo(String thirdChannelNo) {
		this.thirdChannelNo = thirdChannelNo;
	}

	private String channelTypeName;
	public String getChannelTypeName() {
		return ChannelTypeEnum.getName(this.channelType);
	}

	public void setChannelTypeName(String channelTypeName) {
		this.channelTypeName = channelTypeName;
	}

	public Integer getChannelType() {
		return channelType;
	}

	public void setChannelType(Integer channelType) {
		this.channelType = channelType;
	}

	public String getStatusName() {
		return  (this.status != null && this.status == 1) ? "启用" : "禁用";
	}

	public void setStatusName(String statusName) {
		this.statusName = statusName;
	}

	public String getVstoreName() {
		return vstoreName;
	}

	public void setVstoreName(String vstoreName) {
		this.vstoreName = vstoreName;
	}

	public String getShopNo() {
		return shopNo;
	}

	public void setShopNo(String shopNo) {
		this.shopNo = shopNo;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public String getBrandNo() {
		return brandNo;
	}

	public void setBrandNo(String brandNo) {
		this.brandNo = brandNo;
	}

	public String getVstoreCode() {
		return vstoreCode;
	}

	public void setVstoreCode(String vstoreCode) {
		this.vstoreCode = vstoreCode;
	}

	public Integer getOnlineType() {
		return onlineType;
	}

	public void setOnlineType(Integer onlineType) {
		this.onlineType = onlineType;
	}

	public Integer getVstoreScopeType() {
		return vstoreScopeType;
	}

	public void setVstoreScopeType(Integer vstoreScopeType) {
		this.vstoreScopeType = vstoreScopeType;
	}

	public Integer getVstoreLevel() {
		return vstoreLevel;
	}

	public void setVstoreLevel(Integer vstoreLevel) {
		this.vstoreLevel = vstoreLevel;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Override
	public String toString() {
		return "StockDistirbutionConfig [shopNo=" + shopNo + ", shopName=" + shopName + ", brandNo=" + brandNo
				+ ", vstoreCode=" + vstoreCode + ", onlineType=" + onlineType + ", vstoreScopeType=" + vstoreScopeType
				+ ", vstoreLevel=" + vstoreLevel + ", status=" + status + ", remark=" + remark + "]";
	}
}
