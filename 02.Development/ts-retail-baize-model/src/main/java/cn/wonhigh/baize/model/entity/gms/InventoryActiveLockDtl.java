/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 活动锁库明细表
**/
public class InventoryActiveLockDtl  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1723621830720L;


    //品牌编码
    @Label("机构编码")
    private String storeNo;


    //品牌编码
    @Label("机构名称")
    private String storeName;


    //品牌编码
    @Label("机构类型")
    private Integer storeType;


    //品牌编码
    @Label("货管编码")
    private String orderUnitNo;


    //品牌编码
    @Label("货管名称")
    private String orderUnitName;


    
    //品牌编码
    @Label("品牌编码") 
    private String brandNo;
    
    //活动编码
    @Label("活动编码") 
    private String billNo;
    
    //SKUNO 
    private String skuNo;
    
    //商品编码
    @Label("商品编码") 
    private String itemCode;
    
    //锁库数量
    @Label(value = "锁库数量", defaultVal = "0") 
    private Integer lockQty;
    
    //唯一条码
    @Label("唯一条码") 
    private String barcode;
    
    //商品尺码
    @Label("商品尺码") 
    private String sizeNo;
    
    //剩余锁库数量
    @Label(value = "剩余锁库数量", defaultVal = "0") 
    private Integer balanceLockQty;

    @Label(value = "同步状态")
    private int syncStatus;

    public int getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(int syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getUniqueKey(){
        return storeNo +"_"+ orderUnitNo +"_"+ skuNo;
    }

    public String getUniqueKey2(){
        return storeNo +"_"+ orderUnitNo +"_"+ brandNo + "_" + itemCode +"_"+ sizeNo;
    }

    public String getBrandNo(){
        return  brandNo;
    }
    public void setBrandNo(String val ){
        brandNo = val;
    }
    
    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    
    public String getSkuNo(){
        return  skuNo;
    }
    public void setSkuNo(String val ){
        skuNo = val;
    }
    
    public String getItemCode(){
        return  itemCode;
    }
    public void setItemCode(String val ){
        itemCode = val;
    }
    
    public Integer getLockQty(){
        return  lockQty;
    }
    public void setLockQty(Integer val ){
        lockQty = val;
    }
    
    public String getBarcode(){
        return  barcode;
    }
    public void setBarcode(String val ){
        barcode = val;
    }
    
    public String getSizeNo(){
        return  sizeNo;
    }
    public void setSizeNo(String val ){
        sizeNo = val;
    }
    
    public Integer getBalanceLockQty(){
        return  balanceLockQty;
    }
    public void setBalanceLockQty(Integer val ){
        balanceLockQty = val;
    }


    public String getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(String storeNo) {
        this.storeNo = storeNo;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getStoreType() {
        return storeType;
    }

    public void setStoreType(Integer storeType) {
        this.storeType = storeType;
    }

    public String getOrderUnitNo() {
        return orderUnitNo;
    }

    public void setOrderUnitNo(String orderUnitNo) {
        this.orderUnitNo = orderUnitNo;
    }

    public String getOrderUnitName() {
        return orderUnitName;
    }

    public void setOrderUnitName(String orderUnitName) {
        this.orderUnitName = orderUnitName;
    }

    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public InventoryActiveLockDtlBuilder build(){
        return new InventoryActiveLockDtlBuilder(this);
    }

    public static class InventoryActiveLockDtlBuilder extends AbstractEntryBuilder<InventoryActiveLockDtl>{

        private InventoryActiveLockDtlBuilder(InventoryActiveLockDtl entry){
            this.obj = entry;
        }

       @Override
		public InventoryActiveLockDtl object() {
			return this.obj;
		}


        public InventoryActiveLockDtlBuilder storeNo(String value ){
            this.obj.storeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }

        public InventoryActiveLockDtlBuilder storeName(String value ){
            this.obj.storeName = value;
            if( query == null  )
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }

        public InventoryActiveLockDtlBuilder storeType(Integer value ){
            this.obj.storeType = value;
            if( query == null  )
                query = new Query();
            this.query.where("storeType", value);
            return this;
        }


        public InventoryActiveLockDtlBuilder orderUnitNo(String value ){
            this.obj.orderUnitNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }

        public InventoryActiveLockDtlBuilder orderUnitName(String value ){
            this.obj.orderUnitName = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitName", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder brandNo(String value ){
            this.obj.brandNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("brandNo", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder billNo(String value ){
            this.obj.billNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder skuNo(String value ){
            this.obj.skuNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("skuNo", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder itemCode(String value ){
            this.obj.itemCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("itemCode", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder lockQty(Integer value ){
            this.obj.lockQty = value;
            if( query == null  )
                query = new Query();
            this.query.where("lockQty", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder barcode(String value ){
            this.obj.barcode = value;
            if( query == null  )
                query = new Query();
            this.query.where("barcode", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder sizeNo(String value ){
            this.obj.sizeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("sizeNo", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder balanceLockQty(Integer value ){
            this.obj.balanceLockQty = value;
            if( query == null  )
                query = new Query();
            this.query.where("balanceLockQty", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public InventoryActiveLockDtlBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }

        public InventoryActiveLockDtlBuilder syncStatus(int value ){
            this.obj.setSyncStatus(value);

            if( query == null  )
                query = new Query();
            this.query.where("syncStatus", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}