/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 质检信息
**/
public class RetailOrderReturn  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778674L;
    
    /**
    *是否到付 0-否，1-是
    **/ 
    @Label("是否到付") 
    private Integer isPayment;
    

    public Integer getIsPayment(){
        return  isPayment;
    }
    public void setIsPayment(Integer val ){
        isPayment = val;
    }
    /**
    *外部平台原始销售订单号
    **/ 
    @Label("外部平台原始销售订单号") 
    private String originalOrderNo;
    

    public String getOriginalOrderNo(){
        return  originalOrderNo;
    }
    public void setOriginalOrderNo(String val ){
        originalOrderNo = val;
    }
    /**
    *审核时间
    **/ 
    @Label("审核时间") 
    private Date auditTime;
    

    public Date getAuditTime(){
        return  auditTime;
    }
    public void setAuditTime(Date val ){
        auditTime = val;
    }
    /**
    *审核人
    **/ 
    @Label("审核人") 
    private String auditor;
    

    public String getAuditor(){
        return  auditor;
    }
    public void setAuditor(String val ){
        auditor = val;
    }
    /**
    *质检时间
    **/ 
    @Label("质检时间") 
    private Date qualityDate;
    

    public Date getQualityDate(){
        return  qualityDate;
    }
    public void setQualityDate(Date val ){
        qualityDate = val;
    }
    /**
    *质检描述
    **/ 
    @Label("质检描述") 
    private String qualityInfo;
    

    public String getQualityInfo(){
        return  qualityInfo;
    }
    public void setQualityInfo(String val ){
        qualityInfo = val;
    }
    /**
    *问题类型：0-GOOD,1-BAD
    **/ 
    @Label("问题类型") 
    private String bugType;
    

    public String getBugType(){
        return  bugType;
    }
    public void setBugType(String val ){
        bugType = val;
    }
    /**
    *退货类型：0-拒收退货，1-正常退货
    **/ 
    @Label("退货类型") 
    private String untreadType;
    

    public String getUntreadType(){
        return  untreadType;
    }
    public void setUntreadType(String val ){
        untreadType = val;
    }
    /**
    *拒收类型，0：非拆包拒收，1：拆包拒收
    **/ 
    @Label("拒收类型") 
    private Integer rejectiontype;
    

    public Integer getRejectiontype(){
        return  rejectiontype;
    }
    public void setRejectiontype(Integer val ){
        rejectiontype = val;
    }
    /**
    *渠道号
    **/ 
    @Label("渠道号") 
    private String channelNo;
    

    public String getChannelNo(){
        return  channelNo;
    }
    public void setChannelNo(String val ){
        channelNo = val;
    }
    /**
    *拣货单号
    **/ 
    @Label("拣货单号") 
    private String packageNo;
    

    public String getPackageNo(){
        return  packageNo;
    }
    public void setPackageNo(String val ){
        packageNo = val;
    }
    /**
    *入库单号
    **/ 
    @Label("入库单号") 
    private String storageNo;
    

    public String getStorageNo(){
        return  storageNo;
    }
    public void setStorageNo(String val ){
        storageNo = val;
    }
    /**
    *是否匹配到订单：0-没有，1-有
    **/ 
    @Label("是否匹配到订单") 
    private Integer hasOrder;
    

    public Integer getHasOrder(){
        return  hasOrder;
    }
    public void setHasOrder(Integer val ){
        hasOrder = val;
    }
    /**
    *批发销货单
    **/ 
    @Label("批发销货单") 
    private String recordCode;
    

    public String getRecordCode(){
        return  recordCode;
    }
    public void setRecordCode(String val ){
        recordCode = val;
    }
    /**
    *是否唯品会JIT单据 0-否 1-是
    **/ 
    @Label("是否唯品会") 
    private Integer isVip;
    

    public Integer getIsVip(){
        return  isVip;
    }
    public void setIsVip(Integer val ){
        isVip = val;
    }
    /**
    *唯品会档期
    **/ 
    @Label("唯品会档期") 
    private String theaterSchedule;
    

    public String getTheaterSchedule(){
        return  theaterSchedule;
    }
    public void setTheaterSchedule(String val ){
        theaterSchedule = val;
    }
    /**
    *纸箱型号
    **/ 
    @Label("纸箱型号") 
    private String boxCode;
    

    public String getBoxCode(){
        return  boxCode;
    }
    public void setBoxCode(String val ){
        boxCode = val;
    }
    /**
    *物流仓编码
    **/ 
    @Label("物流仓编码") 
    private String storeNo;
    

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    /**
    *物流仓名称
    **/ 
    @Label("物流仓名称") 
    private String storeName;
    

    public String getStoreName(){
        return  storeName;
    }
    public void setStoreName(String val ){
        storeName = val;
    }
    /**
    *总价
    **/ 
    @Label("总价") 
    private BigDecimal totalPrice;
    

    public BigDecimal getTotalPrice(){
        return  totalPrice;
    }
    public void setTotalPrice(BigDecimal val ){
        totalPrice = val;
    }
    /**
    *总数量
    **/ 
    @Label("总数量") 
    private Integer sendDetailTotal;
    

    public Integer getSendDetailTotal(){
        return  sendDetailTotal;
    }
    public void setSendDetailTotal(Integer val ){
        sendDetailTotal = val;
    }
    /**
    *包裹重量
    **/ 
    @Label("包裹重量") 
    private BigDecimal parcelWeight;
    

    public BigDecimal getParcelWeight(){
        return  parcelWeight;
    }
    public void setParcelWeight(BigDecimal val ){
        parcelWeight = val;
    }
    /**
    *快递费
    **/ 
    @Label("快递费") 
    private BigDecimal expressPrice;
    

    public BigDecimal getExpressPrice(){
        return  expressPrice;
    }
    public void setExpressPrice(BigDecimal val ){
        expressPrice = val;
    }
    /**
    *快递单号
    **/ 
    @Label("快递单号") 
    private String expressCode;
    

    public String getExpressCode(){
        return  expressCode;
    }
    public void setExpressCode(String val ){
        expressCode = val;
    }
    /**
    *快递公司名称
    **/ 
    @Label("快递公司名称") 
    private String logisticsCompanyName;
    

    public String getLogisticsCompanyName(){
        return  logisticsCompanyName;
    }
    public void setLogisticsCompanyName(String val ){
        logisticsCompanyName = val;
    }
    /**
    *快递公司
    **/ 
    @Label("快递公司") 
    private String logisticCompanyCode;
    

    public String getLogisticCompanyCode(){
        return  logisticCompanyCode;
    }
    public void setLogisticCompanyCode(String val ){
        logisticCompanyCode = val;
    }
    /**
    *来源平台名称
    **/ 
    @Label("来源平台名称") 
    private String originPlatformName;
    

    public String getOriginPlatformName(){
        return  originPlatformName;
    }
    public void setOriginPlatformName(String val ){
        originPlatformName = val;
    }
    /**
    *来源平台
    **/ 
    @Label("来源平台") 
    private String originPlatform;
    

    public String getOriginPlatform(){
        return  originPlatform;
    }
    public void setOriginPlatform(String val ){
        originPlatform = val;
    }
    /**
    *客户名称
    **/ 
    @Label("客户名称") 
    private String customerName;
    

    public String getCustomerName(){
        return  customerName;
    }
    public void setCustomerName(String val ){
        customerName = val;
    }
    /**
    *客户编码,目前指优购
    **/ 
    @Label("客户编码") 
    private String customerNo;
    

    public String getCustomerNo(){
        return  customerNo;
    }
    public void setCustomerNo(String val ){
        customerNo = val;
    }
    /**
    *订单留言
    **/ 
    @Label("订单留言") 
    private String message;
    

    public String getMessage(){
        return  message;
    }
    public void setMessage(String val ){
        message = val;
    }
    /**
    *发货日期
    **/ 
    @Label("发货日期") 
    private Date sendOutDate;
    

    public Date getSendOutDate(){
        return  sendOutDate;
    }
    public void setSendOutDate(Date val ){
        sendOutDate = val;
    }
    /**
    *质检方类型:1店，2仓
    **/ 
    @Label("质检方类型") 
    private Integer sendStoreType;
    

    public Integer getSendStoreType(){
        return  sendStoreType;
    }
    public void setSendStoreType(Integer val ){
        sendStoreType = val;
    }
    /**
    *质检方结算公司
    **/ 
    @Label("质检方结算公司") 
    private String sendCompanyNo;
    

    public String getSendCompanyNo(){
        return  sendCompanyNo;
    }
    public void setSendCompanyNo(String val ){
        sendCompanyNo = val;
    }
    /**
    *质检方货单位名
    **/ 
    @Label("质检方货单位名") 
    private String sendOrderUnitName;
    

    public String getSendOrderUnitName(){
        return  sendOrderUnitName;
    }
    public void setSendOrderUnitName(String val ){
        sendOrderUnitName = val;
    }
    /**
    *质检方货管单位
    **/ 
    @Label("质检方货管单位") 
    private String sendOrderUnitNo;
    

    public String getSendOrderUnitNo(){
        return  sendOrderUnitNo;
    }
    public void setSendOrderUnitNo(String val ){
        sendOrderUnitNo = val;
    }
    /**
    *质检仓名称
    **/ 
    @Label("质检仓名称") 
    private String sendStoreName;
    

    public String getSendStoreName(){
        return  sendStoreName;
    }
    public void setSendStoreName(String val ){
        sendStoreName = val;
    }
    /**
    *质检方编码
    **/ 
    @Label("质检方编码") 
    private String sendStoreNo;
    

    public String getSendStoreNo(){
        return  sendStoreNo;
    }
    public void setSendStoreNo(String val ){
        sendStoreNo = val;
    }
    /**
    *销售店铺结算公司
    **/ 
    @Label("销售店铺结算公司") 
    private String saleCompanyNo;
    

    public String getSaleCompanyNo(){
        return  saleCompanyNo;
    }
    public void setSaleCompanyNo(String val ){
        saleCompanyNo = val;
    }
    /**
    *销售店铺货单位名
    **/ 
    @Label("销售店铺货单位名") 
    private String saleOrderUnitName;
    

    public String getSaleOrderUnitName(){
        return  saleOrderUnitName;
    }
    public void setSaleOrderUnitName(String val ){
        saleOrderUnitName = val;
    }
    /**
    *销售货管单位
    **/ 
    @Label("销售货管单位") 
    private String saleOrderUnitNo;
    

    public String getSaleOrderUnitNo(){
        return  saleOrderUnitNo;
    }
    public void setSaleOrderUnitNo(String val ){
        saleOrderUnitNo = val;
    }
    /**
    *销售店铺名称
    **/ 
    @Label("销售店铺名称") 
    private String shopName;
    

    public String getShopName(){
        return  shopName;
    }
    public void setShopName(String val ){
        shopName = val;
    }
    /**
    *销售门店
    **/ 
    @Label("销售门店") 
    private String shopNo;
    

    public String getShopNo(){
        return  shopNo;
    }
    public void setShopNo(String val ){
        shopNo = val;
    }
    /**
    *单据状态 ：5 确认 100完结
    **/ 
    @Label("单据状态") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *单据类型(1：默认正常), 0-唯品退供,2-作废质检,3-退货质检,4-退货质检-错发,5-仓库拒收质检,6-仅退款,7-差异调整,8-异常调整,9-店铺拒收质检
    **/ 
    @Label("单据类型") 
    private Integer returnType;
    

    public Integer getReturnType(){
        return  returnType;
    }
    public void setReturnType(Integer val ){
        returnType = val;
    }
    /**
    *业务类型:唯品退供:VIP-001,作废质检:IQI-001,拒收/质检:QC-xxx,差异调整:AD-xxx,异常调整:EX-xxx,仅退款:RF-xxx
    **/ 
    @Label("业务类型") 
    private String businessType;
    

    public String getBusinessType(){
        return  businessType;
    }
    public void setBusinessType(String val ){
        businessType = val;
    }
    /**
    *备注
    **/ 
    @Label("备注") 
    private String remark;
    

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    /**
    *优购销售订单号
    **/ 
    @Label("优购销售订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    /**
    *唯品退供单号，非唯品为空
    **/ 
    @Label("唯品退供单号") 
    private String sellReturnCode;
    

    public String getSellReturnCode(){
        return  sellReturnCode;
    }
    public void setSellReturnCode(String val ){
        sellReturnCode = val;
    }
    /**
    *退货（发货）通知单号
    **/ 
    @Label("退货") 
    private String refBillNo;
    

    public String getRefBillNo(){
        return  refBillNo;
    }
    public void setRefBillNo(String val ){
        refBillNo = val;
    }
    /**
    *单据编号
    **/ 
    @Label("单据编号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    /**
    *接口平台
    **/ 
    @Label("接口平台") 
    private String interfacePlatform;
    

    public String getInterfacePlatform(){
        return  interfacePlatform;
    }
    public void setInterfacePlatform(String val ){
        interfacePlatform = val;
    }
    /**
    *零售分库字段：本部+大区
    **/ 
    @Label("零售分库字段") 
    private String retailFlag;
    

    public String getRetailFlag(){
        return  retailFlag;
    }
    public void setRetailFlag(String val ){
        retailFlag = val;
    }
    /**
    *分库字段：本部+序号
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *默认NULL, 0.共享可配 1.不共享可配，2.不共享不可配
    **/ 
    @Label("默认") 
    private Integer shareFlag;
    

    public Integer getShareFlag(){
        return  shareFlag;
    }
    public void setShareFlag(Integer val ){
        shareFlag = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public RetailOrderReturnBuilder build(){
        return new RetailOrderReturnBuilder(this);
    }

    public static class RetailOrderReturnBuilder extends AbstractEntryBuilder<RetailOrderReturn>{

        private RetailOrderReturnBuilder(RetailOrderReturn entry){
            this.obj = entry;
        }

       @Override
		public RetailOrderReturn object() {
			return this.obj;
		}

        
        public RetailOrderReturnBuilder isPayment(Integer value ){
            
            this.obj.isPayment = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isPayment", value);
            return this;
        }
        
        public RetailOrderReturnBuilder originalOrderNo(String value ){
            
            this.obj.originalOrderNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originalOrderNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder auditTime(Date value ){
            
            this.obj.auditTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("auditTime", value);
            return this;
        }
        
        public RetailOrderReturnBuilder auditor(String value ){
            
            this.obj.auditor = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("auditor", value);
            return this;
        }
        
        public RetailOrderReturnBuilder qualityDate(Date value ){
            
            this.obj.qualityDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("qualityDate", value);
            return this;
        }
        
        public RetailOrderReturnBuilder qualityInfo(String value ){
            
            this.obj.qualityInfo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("qualityInfo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder bugType(String value ){
            
            this.obj.bugType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("bugType", value);
            return this;
        }
        
        public RetailOrderReturnBuilder untreadType(String value ){
            
            this.obj.untreadType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("untreadType", value);
            return this;
        }
        
        public RetailOrderReturnBuilder rejectiontype(Integer value ){
            
            this.obj.rejectiontype = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("rejectiontype", value);
            return this;
        }
        
        public RetailOrderReturnBuilder channelNo(String value ){
            
            this.obj.channelNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("channelNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder packageNo(String value ){
            
            this.obj.packageNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("packageNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder storageNo(String value ){
            
            this.obj.storageNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storageNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder hasOrder(Integer value ){
            
            this.obj.hasOrder = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("hasOrder", value);
            return this;
        }
        
        public RetailOrderReturnBuilder recordCode(String value ){
            
            this.obj.recordCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("recordCode", value);
            return this;
        }
        
        public RetailOrderReturnBuilder isVip(Integer value ){
            
            this.obj.isVip = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isVip", value);
            return this;
        }
        
        public RetailOrderReturnBuilder theaterSchedule(String value ){
            
            this.obj.theaterSchedule = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("theaterSchedule", value);
            return this;
        }
        
        public RetailOrderReturnBuilder boxCode(String value ){
            
            this.obj.boxCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("boxCode", value);
            return this;
        }
        
        public RetailOrderReturnBuilder storeNo(String value ){
            
            this.obj.storeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder storeName(String value ){
            
            this.obj.storeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public RetailOrderReturnBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public RetailOrderReturnBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public RetailOrderReturnBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public RetailOrderReturnBuilder totalPrice(BigDecimal value ){
            
            this.obj.totalPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("totalPrice", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendDetailTotal(Integer value ){
            
            this.obj.sendDetailTotal = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendDetailTotal", value);
            return this;
        }
        
        public RetailOrderReturnBuilder parcelWeight(BigDecimal value ){
            
            this.obj.parcelWeight = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parcelWeight", value);
            return this;
        }
        
        public RetailOrderReturnBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public RetailOrderReturnBuilder expressPrice(BigDecimal value ){
            
            this.obj.expressPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressPrice", value);
            return this;
        }
        
        public RetailOrderReturnBuilder expressCode(String value ){
            
            this.obj.expressCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressCode", value);
            return this;
        }
        
        public RetailOrderReturnBuilder logisticsCompanyName(String value ){
            
            this.obj.logisticsCompanyName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsCompanyName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder logisticCompanyCode(String value ){
            
            this.obj.logisticCompanyCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticCompanyCode", value);
            return this;
        }
        
        public RetailOrderReturnBuilder originPlatformName(String value ){
            
            this.obj.originPlatformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatformName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder originPlatform(String value ){
            
            this.obj.originPlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatform", value);
            return this;
        }
        
        public RetailOrderReturnBuilder customerName(String value ){
            
            this.obj.customerName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customerName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder customerNo(String value ){
            
            this.obj.customerNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customerNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder message(String value ){
            
            this.obj.message = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("message", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendOutDate(Date value ){
            
            this.obj.sendOutDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendOutDate", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendStoreType(Integer value ){
            
            this.obj.sendStoreType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendStoreType", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendCompanyNo(String value ){
            
            this.obj.sendCompanyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendCompanyNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendOrderUnitName(String value ){
            
            this.obj.sendOrderUnitName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendOrderUnitName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendOrderUnitNo(String value ){
            
            this.obj.sendOrderUnitNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendOrderUnitNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendStoreName(String value ){
            
            this.obj.sendStoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendStoreName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sendStoreNo(String value ){
            
            this.obj.sendStoreNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendStoreNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder saleCompanyNo(String value ){
            
            this.obj.saleCompanyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleCompanyNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder saleOrderUnitName(String value ){
            
            this.obj.saleOrderUnitName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleOrderUnitName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder saleOrderUnitNo(String value ){
            
            this.obj.saleOrderUnitNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleOrderUnitNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder shopName(String value ){
            
            this.obj.shopName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopName", value);
            return this;
        }
        
        public RetailOrderReturnBuilder shopNo(String value ){
            
            this.obj.shopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public RetailOrderReturnBuilder returnType(Integer value ){
            
            this.obj.returnType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("returnType", value);
            return this;
        }
        
        public RetailOrderReturnBuilder businessType(String value ){
            
            this.obj.businessType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("businessType", value);
            return this;
        }
        
        public RetailOrderReturnBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public RetailOrderReturnBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder sellReturnCode(String value ){
            
            this.obj.sellReturnCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sellReturnCode", value);
            return this;
        }
        
        public RetailOrderReturnBuilder refBillNo(String value ){
            
            this.obj.refBillNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refBillNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public RetailOrderReturnBuilder interfacePlatform(String value ){
            
            this.obj.interfacePlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("interfacePlatform", value);
            return this;
        }
        
        public RetailOrderReturnBuilder retailFlag(String value ){
            
            this.obj.retailFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailFlag", value);
            return this;
        }
        
        public RetailOrderReturnBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public RetailOrderReturnBuilder shareFlag(Integer value ){
            
            this.obj.shareFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shareFlag", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }