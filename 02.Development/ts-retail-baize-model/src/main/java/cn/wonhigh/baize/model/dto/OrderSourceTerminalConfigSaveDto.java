package cn.wonhigh.baize.model.dto;

import cn.wonhigh.baize.model.validate.CreateGroup;
import cn.wonhigh.baize.model.validate.EditGroup;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

public class OrderSourceTerminalConfigSaveDto implements Serializable {


    private static final long serialVersionUID = 1L;

      
    @NotBlank(message = "id不能为空", groups = {EditGroup.class})
    private String id;


    @NotBlank(message = "终端编码不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String terminal;

    //@NotBlank(message = "终端名称不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String terminalName;


      
    @NotBlank(message = "一级来源不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String platform;

      
    @NotBlank(message = "一级来源名称不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String platformName;

    @NotBlank(message = "二级来源不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String secondPlatform;

    @NotBlank(message = "二级来源名称不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String secondPlatformName;


    @NotBlank(message = "三级来源不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String thirdPlatform;


    @NotBlank(message = "三级来源名称不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String thirdPlatformName;


    @NotBlank(message = "三级来源编码不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String thirdChannelNo;

    @NotBlank(message = "三级来源名称不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String thirdChannelName;

      
    private String provinceNo;

      
    private String cityNo;

      
    private String zoneNo;

      
    private String managerZoneNo;


    @NotBlank(message = "结算公司编码不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String companyNo;

    //@NotBlank(message = "结算公司名称不能为空", groups = {EditGroup.class, CreateGroup.class})
    private String companyName;


    @Length(max = 200, message = "备注长度不能超过200", groups = {EditGroup.class, CreateGroup.class})
    private String remark;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTerminal() {
        return terminal;
    }

    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getSecondPlatform() {
        return secondPlatform;
    }

    public void setSecondPlatform(String secondPlatform) {
        this.secondPlatform = secondPlatform;
    }

    public String getSecondPlatformName() {
        return secondPlatformName;
    }

    public void setSecondPlatformName(String secondPlatformName) {
        this.secondPlatformName = secondPlatformName;
    }

    public String getThirdPlatform() {
        return thirdPlatform;
    }

    public void setThirdPlatform(String thirdPlatform) {
        this.thirdPlatform = thirdPlatform;
    }

    public String getThirdPlatformName() {
        return thirdPlatformName;
    }

    public void setThirdPlatformName(String thirdPlatformName) {
        this.thirdPlatformName = thirdPlatformName;
    }

    public String getThirdChannelNo() {
        return thirdChannelNo;
    }

    public void setThirdChannelNo(String thirdChannelNo) {
        this.thirdChannelNo = thirdChannelNo;
    }

    public String getThirdChannelName() {
        return thirdChannelName;
    }

    public void setThirdChannelName(String thirdChannelName) {
        this.thirdChannelName = thirdChannelName;
    }

    public String getProvinceNo() {
        return provinceNo;
    }

    public void setProvinceNo(String provinceNo) {
        this.provinceNo = provinceNo;
    }

    public String getCityNo() {
        return cityNo;
    }

    public void setCityNo(String cityNo) {
        this.cityNo = cityNo;
    }

    public String getZoneNo() {
        return zoneNo;
    }

    public void setZoneNo(String zoneNo) {
        this.zoneNo = zoneNo;
    }

    public String getManagerZoneNo() {
        return managerZoneNo;
    }

    public void setManagerZoneNo(String managerZoneNo) {
        this.managerZoneNo = managerZoneNo;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}