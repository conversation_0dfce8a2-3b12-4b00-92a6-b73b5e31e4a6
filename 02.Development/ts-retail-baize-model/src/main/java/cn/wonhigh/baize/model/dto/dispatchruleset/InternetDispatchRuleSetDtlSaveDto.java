package cn.wonhigh.baize.model.dto.dispatchruleset;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class InternetDispatchRuleSetDtlSaveDto implements Serializable {

    private Long id;

    @NotBlank(message = "终端编码不能为空")
    private String channelNo;

    @NotBlank(message = "终端名字不能为空")
    private String channelName;

    @NotBlank(message = "店铺编码不能为空")
    private String orderSourceNo;

    @NotBlank(message = "店铺编码不能为空")
    private String orderSourceName;
    private String ruleNo;

    private int moreShopFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getOrderSourceNo() {
        return orderSourceNo;
    }

    public void setOrderSourceNo(String orderSourceNo) {
        this.orderSourceNo = orderSourceNo;
    }

    public String getOrderSourceName() {
        return orderSourceName;
    }

    public void setOrderSourceName(String orderSourceName) {
        this.orderSourceName = orderSourceName;
    }

    public String getRuleNo() {
        return ruleNo;
    }

    public void setRuleNo(String ruleNo) {
        this.ruleNo = ruleNo;
    }

    public int getMoreShopFlag() {
        return moreShopFlag;
    }

    public void setMoreShopFlag(int moreShopFlag) {
        this.moreShopFlag = moreShopFlag;
    }
}
