package cn.wonhigh.baize.model.dto.dispatchruleset;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DispatchRuleSetDto implements Serializable {


    private Long id;

    private String ruleNo;
    private String ruleName;

    private Integer type;
    private String brandNo;

    private String brandName;

    private Integer state;

    private String createUser;

    private Date createTime;

    private Date updateTime;

    private String updateUser;

    private List<InternetDispatchRulePriorityDto> ruleList;

    private List<InternetDispatchRuleSetDtlDto> dtlList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleNo() {
        return ruleNo;
    }

    public void setRuleNo(String ruleNo) {
        this.ruleNo = ruleNo;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public List<InternetDispatchRulePriorityDto> getRuleList() {
        return ruleList;
    }

    public void setRuleList(List<InternetDispatchRulePriorityDto> ruleList) {
        this.ruleList = ruleList;
    }


    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }


    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public List<InternetDispatchRuleSetDtlDto> getDtlList() {
        return dtlList;
    }

    public void setDtlList(List<InternetDispatchRuleSetDtlDto> dtlList) {
        this.dtlList = dtlList;
    }
}
