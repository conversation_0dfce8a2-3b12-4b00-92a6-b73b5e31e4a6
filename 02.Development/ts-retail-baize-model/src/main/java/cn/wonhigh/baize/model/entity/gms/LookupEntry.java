/**  **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 数据字典项
**/
public class LookupEntry  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1716435042963L;
    
    //状态(1正常,0无效)
    @Label(value = "状态", defaultVal = "1") 
    private Integer status;
    
    //字典项编号
    @Label("字典项编号") 
    private String lookupEntryNo;
    
    //字典ID
    @Label("字典") 
    private Integer lookupId;
    
    //字典编码
    @Label("字典编码") 
    private String code;
    
    //特征码
    @Label("特征码") 
    private String opcode;
    
    //字典项名称
    @Label("字典项名称") 
    private String name;
    
    //字典项名称 繁体
    @Label("字典项名称") 
    private String lookupEntryNameHk;
    
    //字典项名称 英文
    @Label("字典项名称") 
    private String lookupEntryNameEn;
    
    //序号
    @Label("序号") 
    private Integer orderNo;
    
    //字典类型(1 系统;0 普通)
    @Label(value = "字典类型", defaultVal = "0") 
    private String type;
    
    //是否选中
    @Label("是否选中") 
    private String defaultFlag;
    
    //时间序列
    @Label("时间序列") 
    private Long timeSeq;
    
    //本部编码
    @Label("本部编码") 
    private String organTypeNo;
    
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public String getLookupEntryNo(){
        return  lookupEntryNo;
    }
    public void setLookupEntryNo(String val ){
        lookupEntryNo = val;
    }
    
    public Integer getLookupId(){
        return  lookupId;
    }
    public void setLookupId(Integer val ){
        lookupId = val;
    }
    
    public String getCode(){
        return  code;
    }
    public void setCode(String val ){
        code = val;
    }
    
    public String getOpcode(){
        return  opcode;
    }
    public void setOpcode(String val ){
        opcode = val;
    }
    
    public String getName(){
        return  name;
    }
    public void setName(String val ){
        name = val;
    }
    
    public String getLookupEntryNameHk(){
        return  lookupEntryNameHk;
    }
    public void setLookupEntryNameHk(String val ){
        lookupEntryNameHk = val;
    }
    
    public String getLookupEntryNameEn(){
        return  lookupEntryNameEn;
    }
    public void setLookupEntryNameEn(String val ){
        lookupEntryNameEn = val;
    }
    
    public Integer getOrderNo(){
        return  orderNo;
    }
    public void setOrderNo(Integer val ){
        orderNo = val;
    }
    
    public String getType(){
        return  type;
    }
    public void setType(String val ){
        type = val;
    }
    
    public String getDefaultFlag(){
        return  defaultFlag;
    }
    public void setDefaultFlag(String val ){
        defaultFlag = val;
    }
    
    public Long getTimeSeq(){
        return  timeSeq;
    }
    public void setTimeSeq(Long val ){
        timeSeq = val;
    }
    
    public String getOrganTypeNo(){
        return  organTypeNo;
    }
    public void setOrganTypeNo(String val ){
        organTypeNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public LookupEntryBuilder build(){
        return new LookupEntryBuilder(this);
    }

    public static class LookupEntryBuilder extends AbstractEntryBuilder<LookupEntry>{

        private LookupEntryBuilder(LookupEntry entry){
            this.obj = entry;
        }

       @Override
		public LookupEntry object() {
			return this.obj;
		}

        
        public LookupEntryBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public LookupEntryBuilder lookupEntryNo(String value ){
            this.obj.lookupEntryNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("lookupEntryNo", value);
            return this;
        }
        
        public LookupEntryBuilder lookupId(Integer value ){
            this.obj.lookupId = value;
            if( query == null  )
                query = new Query();
            this.query.where("lookupId", value);
            return this;
        }
        
        public LookupEntryBuilder code(String value ){
            this.obj.code = value;
            if( query == null  )
                query = new Query();
            this.query.where("code", value);
            return this;
        }
        
        public LookupEntryBuilder opcode(String value ){
            this.obj.opcode = value;
            if( query == null  )
                query = new Query();
            this.query.where("opcode", value);
            return this;
        }
        
        public LookupEntryBuilder name(String value ){
            this.obj.name = value;
            if( query == null  )
                query = new Query();
            this.query.where("name", value);
            return this;
        }
        
        public LookupEntryBuilder lookupEntryNameHk(String value ){
            this.obj.lookupEntryNameHk = value;
            if( query == null  )
                query = new Query();
            this.query.where("lookupEntryNameHk", value);
            return this;
        }
        
        public LookupEntryBuilder lookupEntryNameEn(String value ){
            this.obj.lookupEntryNameEn = value;
            if( query == null  )
                query = new Query();
            this.query.where("lookupEntryNameEn", value);
            return this;
        }
        
        public LookupEntryBuilder orderNo(Integer value ){
            this.obj.orderNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("orderNo", value);
            return this;
        }
        
        public LookupEntryBuilder type(String value ){
            this.obj.type = value;
            if( query == null  )
                query = new Query();
            this.query.where("type", value);
            return this;
        }
        
        public LookupEntryBuilder defaultFlag(String value ){
            this.obj.defaultFlag = value;
            if( query == null  )
                query = new Query();
            this.query.where("defaultFlag", value);
            return this;
        }
        
        public LookupEntryBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public LookupEntryBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public LookupEntryBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public LookupEntryBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public LookupEntryBuilder timeSeq(Long value ){
            this.obj.timeSeq = value;
            if( query == null  )
                query = new Query();
            this.query.where("timeSeq", value);
            return this;
        }
        
        public LookupEntryBuilder organTypeNo(String value ){
            this.obj.organTypeNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("organTypeNo", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}