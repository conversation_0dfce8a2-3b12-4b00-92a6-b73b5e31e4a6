package cn.wonhigh.baize.model.enums;

import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 调整单状态枚举
 * @date 2025/6/13 15:57
 */
public enum AdjustStatusEnums {
    NEW_STATUS(0, "新建"),
    AUDIT_SUCCESS(1, "审核成功"),
    AUDIT_FAIL(2, "审核失败"),
    CANCEL_STATUS(3, "已作废");

    private Integer value;
    private String desc;

    AdjustStatusEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AdjustStatusEnums getAdjustStatusEnums(Integer status) {
        for (AdjustStatusEnums adjustStatusEnums : AdjustStatusEnums.values()) {
            if (adjustStatusEnums.getValue().equals(status)) {
                return adjustStatusEnums;
            }
        }
        return null;
    }

    public static List<Map<String, Object>> getAdjustStatusList() {
        return Stream.of(AdjustStatusEnums.values())
                .map(adjustStatusEnums -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("value", adjustStatusEnums.getValue());
                    map.put("desc", adjustStatusEnums.getDesc());
                    return map;
                }).collect(Collectors.toList());
    }
    
    public static boolean isUpdateOperable(AdjustStatusEnums adjustStatusEnum) {
    	return NEW_STATUS == adjustStatusEnum || AUDIT_FAIL == adjustStatusEnum;
    }

    public Integer getValue() {
        return value;
    }
    public String getDesc() {
        return desc;
    }
}
