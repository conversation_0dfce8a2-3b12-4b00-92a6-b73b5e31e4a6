package cn.wonhigh.baize.model.dto;

import cn.mercury.annotation.Label;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "用户数据权限配置")
public class AuthorityUserDataDto extends cn.mercury.domain.BaseEntity<Integer> {

	private static final long serialVersionUID = -6091208202512545179L;

	@ApiModelProperty(value = "查询条件", name = "q")
	@Label("查询条件")
	private String q;

	public String getQ() {
		return q;
	}

	public void setQ(String val) {
		this.q = val;
	}

	@ApiModelProperty(value = "每页数量", name = "pageSize")
	@Label("每页数量")
	private Integer rows;

	/**
	 * 当前页码
	 */
	@ApiModelProperty(value = "当前页码", name = "page")
	@Label("当前页码")
	private Integer page;

	public Integer getRows() {
		return rows;
	}

	public void setRows(Integer rows) {
		this.rows = rows;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

}