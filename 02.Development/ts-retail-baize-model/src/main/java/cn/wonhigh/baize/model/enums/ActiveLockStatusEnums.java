package cn.wonhigh.baize.model.enums;

/**
 * TODO: 活动锁库状态
 * 
 * <AUTHOR>
 * @date 2022年7月28日 下午3:11:57
 * @version 0.1.0 
 * @copyright Wonhigh Information Technology (Shenzhen) Co.,Ltd.
 */
public enum ActiveLockStatusEnums {

	NEW_STATUS(1, "新建"),
	EFFECTIVE_STATUS(2, "已生效"),
	END_STATUS(7, "已结束"),
	TERMINATION_STATUS(8, "已终止"),
	INVALID_STATUS(9, "已作废");
	
	private Integer value;
	private String desc;
	
	private ActiveLockStatusEnums(Integer value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public static ActiveLockStatusEnums getActiveLockEnums(Integer status) {
		for (ActiveLockStatusEnums e : ActiveLockStatusEnums.values()) {
			if (e.getValue().equals(status)) {
				return e;
			}
		}
		return null;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
	
	public static ActiveLockStatusEnums getActiveLockEnums(int status) {
		for (ActiveLockStatusEnums e : ActiveLockStatusEnums.values()) {
			if (e.getValue() == status) {
				return e;
			}
		}
		return null;
	}
}
