package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ReflectionToStringBuilder;
import java.util.Collection;

public class ItemBaseInfo {

	/**
	 * itemSku的id
	 */
	private String skuNo;

	/**
	 * 条码
	 */
	private String barcode;

	/**
	 * 商品编码
	 */
	private String itemNo;

	/**
	 * 商品编码
	 */
	private String itemCode;

	/**
	 * 商品名称
	 */
	private String itemName;

	/**
	 * 尺寸分类
	 */
	private String sizeKind;

	/**
	 * 尺寸编码
	 */
	private String sizeNo;

	/**
	 * 品牌名称
	 */
	private String brandName;

	private String brandNo;
	/**
	 * 颜色编码
	 */
	private String colorNo;

	/**
	 * 类别编码
	 */
	private String categoryNo;

	/**
	 * 拓展字段 颜色名称
	 */
	private String colorName;

	/**
	 * 商品款号(必须输入且长度必须6位)
	 */
	private String styleNo;

	/**
	 * 所属本部
	 */
	private String organTypeNo;

	/**
	 * 所属本部名称
	 */
	private String organTypeName;
	/**
	 * 品牌类别 U070101-自有 U070102-代理 U070103-经销
	 */
	private String brandCategory;

	public String getOrganTypeName() {
		return organTypeName;
	}

	public void setOrganTypeName(String organTypeName) {
		this.organTypeName = organTypeName;
	}

	private Collection<String> skuNoList;

	private Collection<String> barcodeList;

	private int status;

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public ItemBaseInfo() {
		super();
	}

	public ItemBaseInfo(Collection<String> skuNoList) {
		super();
		this.skuNoList = skuNoList;
	}

	public static ItemBaseInfo getInstanceByBarcode(Collection<String> barCodeList) {
		ItemBaseInfo param = new ItemBaseInfo();
		param.setBarcodeList(barCodeList);
		return param;
	}

	public static ItemBaseInfo getInstance(String itemNo, String sizeNo) {
		ItemBaseInfo info = new ItemBaseInfo();
		info.setItemNo(itemNo);
		info.setSizeNo(sizeNo);
		return info;
	}

	public static ItemBaseInfo getInstanceByItmCode(String itemCode, String brandNo, String sizeNo) {
		ItemBaseInfo info = new ItemBaseInfo();
		info.setItemCode(itemCode);
		info.setSizeNo(sizeNo);
		info.setBrandNo(brandNo);
		return info;
	}

	public ItemBaseInfo(String barcode, String brandNo) {
		super();
		this.barcode = barcode;
		this.brandNo = brandNo;
	}

	public ItemBaseInfo(String skuNo) {
		super();
		this.skuNo = skuNo;
	}

	public String getSkuNo() {
		return skuNo;
	}

	public void setSkuNo(String skuNo) {
		this.skuNo = skuNo;
	}

	public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	public String getItemNo() {
		return itemNo;
	}

	public void setItemNo(String itemNo) {
		this.itemNo = itemNo;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getSizeKind() {
		return sizeKind;
	}

	public void setSizeKind(String sizeKind) {
		this.sizeKind = sizeKind;
	}

	public String getSizeNo() {
		return sizeNo;
	}

	public void setSizeNo(String sizeNo) {
		this.sizeNo = sizeNo;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getBrandNo() {
		return brandNo;
	}

	public void setBrandNo(String brandNo) {
		this.brandNo = brandNo;
	}

	public String getColorNo() {
		return colorNo;
	}

	public void setColorNo(String colorNo) {
		this.colorNo = colorNo;
	}

	public String getCategoryNo() {
		return categoryNo;
	}

	public void setCategoryNo(String categoryNo) {
		this.categoryNo = categoryNo;
	}

	public String getColorName() {
		return colorName;
	}

	public void setColorName(String colorName) {
		this.colorName = colorName;
	}

	public String getStyleNo() {
		return styleNo;
	}

	public void setStyleNo(String styleNo) {
		this.styleNo = styleNo;
	}

	public String getOrganTypeNo() {
		return organTypeNo;
	}

	public void setOrganTypeNo(String organTypeNo) {
		this.organTypeNo = organTypeNo;
	}

	public Collection<String> getSkuNoList() {
		return skuNoList;
	}

	public void setSkuNoList(Collection<String> skuNoList) {
		this.skuNoList = skuNoList;
	}

	public Collection<String> getBarcodeList() {
		return barcodeList;
	}

	public void setBarcodeList(Collection<String> barcodeList) {
		this.barcodeList = barcodeList;
	}

	/**
	 * @return the brandCategory
	 */
	public String getBrandCategory() {
		return brandCategory;
	}

	/**
	 * @param brandCategory the brandCategory to set
	 */
	public void setBrandCategory(String brandCategory) {
		this.brandCategory = brandCategory;
	}

	@Override
	public String toString() {
		return ReflectionToStringBuilder.toString(this);
	}

}
