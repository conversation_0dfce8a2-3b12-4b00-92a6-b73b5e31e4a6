package cn.wonhigh.baize.model.entity.ios;

import cn.wonhigh.baize.model.enums.StockTypeEnum;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


public class InternetAppletShopSetting extends cn.mercury.domain.BasicEntity {

    private static final long serialVersionUID = 1582511402121L;

    /**
     *internet_applet_shop_setting.shop_no - 仓库编码
     *
     * Generated by MyBatis Generator.
     */
    private String shopNo;

    /**
     *internet_applet_shop_setting.shop_name - 仓库名称
     *
     * Generated by MyBatis Generator.
     */
    private String shopName;

    /**
     *internet_applet_shop_setting.type - 1.本地库存销售，2.实店共库存，3.虚店对应仓发货
     *
     * Generated by MyBatis Generator.
     */
    private Integer type;
    /**
     *internet_applet_shop_setting.groups - 分组:1.小程序 2.移动自收营
     *
     * Generated by MyBatis Generator.
     */
    private Integer groups;
    
    /**
     * 库存类别 1:正品  17:B品. 拼接1,17
     */
    private String stockType;

    /**
     * 共享比例
     */
    private Integer sharingRatio;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column internet_applet_shop_setting.shop_no - 仓库编码
     * @return the value of internet_applet_shop_setting.shop_no
     */
    public String getShopNo() {
        return shopNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column internet_applet_shop_setting.shop_no - 仓库编码
     * @param shopNo the value for internet_applet_shop_setting.shop_no
     */
    public void setShopNo(String shopNo) {
        this.shopNo = shopNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column internet_applet_shop_setting.shop_name - 仓库名称
     * @return the value of internet_applet_shop_setting.shop_name
     */
    public String getShopName() {
        return shopName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column internet_applet_shop_setting.shop_name - 仓库名称
     * @param shopName the value for internet_applet_shop_setting.shop_name
     */
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column internet_applet_shop_setting.type - 1.本地库存销售，2.实店共库存，3.虚店对应仓发货
     * @return the value of internet_applet_shop_setting.type
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column internet_applet_shop_setting.type - 1.本地库存销售，2.实店共库存，3.虚店对应仓发货
     * @param type the value for internet_applet_shop_setting.type
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column internet_applet_shop_setting.groups - 分组:1.小程序 2.移动自收营
     * @return the value of internet_applet_shop_setting.groups
     */
	public Integer getGroups() {
		return groups;
	}

	/**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column internet_applet_shop_setting.groups - 分组:1.小程序 2.移动自收营
     * @param type the value for internet_applet_shop_setting.groups
     */
	public void setGroups(Integer groups) {
		this.groups = groups;
	}

	public String getStockType() {
		return stockType;
	}

	public void setStockType(String stockType) {
		this.stockType = stockType;
	}

    public Integer getSharingRatio() {
        return sharingRatio;
    }

    public void setSharingRatio(Integer sharingRatio) {
        this.sharingRatio = sharingRatio;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public boolean active(){
        return startTime != null && endTime != null && startTime.before(new Date()) && endTime.after(new Date());
    }

    public double sharingRatioDouble(){
    	return sharingRatio / 100d;
    }

    /**
     * 实店
     * @return
     */
    public boolean sdShop(){
        // 小程序里面 非3和4逻辑就是走的实店逻辑  保持一致
        return !this.groups.equals(3) && !this.groups.equals(4);
    }

    public List<StockTypeEnum> stockTypeList(){
        return stockTypeList(stockType);
    }

    public static List<StockTypeEnum> stockTypeList(String stockType){
        return Arrays.stream(stockType.split(","))
                .map(type -> StockTypeEnum.of(Integer.parseInt(type)))
                .collect(Collectors.toList());
    }
}