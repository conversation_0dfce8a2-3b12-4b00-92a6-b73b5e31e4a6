/** zkh **/
package cn.wonhigh.baize.model.entity.gms;

import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 经营区域信息表
**/
public class ZoneInfo  extends cn.mercury.domain.BaseEntity<Integer>  {

    private static final long serialVersionUID = 1722501306524L;
    
    //经营区域编号
    @Label("经营区域编号") 
    private String zoneNo;
    
    //地区编码(有且必须只能输入一位或两位)
    @Label("地区编码") 
    private String zoneCode;
    
    //经营区域名称
    @Label("经营区域名称") 
    private String name;
    
    //管理大区( EP 东区 SP 南区 WP 西区 NP 北区 CP 中区)
    @Label("管理大区") 
    private String manageZoneNo;
    
    //区域状态(0 = 撤消 1 = 正常)
    @Label(value = "区域状态", defaultVal = "1") 
    private Integer status;
    
    //品牌库编码(00-通用定义 其他则为相应品牌库)
    @Label(value = "品牌库编码", defaultVal = "00") 
    private String sysNo;
    
    //备注
    @Label("备注") 
    private String remark;
    
    //时间序列
    @Label("时间序列") 
    private Long timeSeq;
    
    
    public String getZoneNo(){
        return  zoneNo;
    }
    public void setZoneNo(String val ){
        zoneNo = val;
    }
    
    public String getZoneCode(){
        return  zoneCode;
    }
    public void setZoneCode(String val ){
        zoneCode = val;
    }
    
    public String getName(){
        return  name;
    }
    public void setName(String val ){
        name = val;
    }
    
    public String getManageZoneNo(){
        return  manageZoneNo;
    }
    public void setManageZoneNo(String val ){
        manageZoneNo = val;
    }
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public String getSysNo(){
        return  sysNo;
    }
    public void setSysNo(String val ){
        sysNo = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    public Long getTimeSeq(){
        return  timeSeq;
    }
    public void setTimeSeq(Long val ){
        timeSeq = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public ZoneInfoBuilder build(){
        return new ZoneInfoBuilder(this);
    }

    public static class ZoneInfoBuilder extends AbstractEntryBuilder<ZoneInfo>{

        private ZoneInfoBuilder(ZoneInfo entry){
            this.obj = entry;
        }

       @Override
		public ZoneInfo object() {
			return this.obj;
		}

        
        public ZoneInfoBuilder id(Integer value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public ZoneInfoBuilder zoneNo(String value ){
            this.obj.zoneNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("zoneNo", value);
            return this;
        }
        
        public ZoneInfoBuilder zoneCode(String value ){
            this.obj.zoneCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("zoneCode", value);
            return this;
        }
        
        public ZoneInfoBuilder name(String value ){
            this.obj.name = value;
            if( query == null  )
                query = new Query();
            this.query.where("name", value);
            return this;
        }
        
        public ZoneInfoBuilder manageZoneNo(String value ){
            this.obj.manageZoneNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("manageZoneNo", value);
            return this;
        }
        
        public ZoneInfoBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public ZoneInfoBuilder sysNo(String value ){
            this.obj.sysNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("sysNo", value);
            return this;
        }
        
        public ZoneInfoBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public ZoneInfoBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public ZoneInfoBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public ZoneInfoBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public ZoneInfoBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public ZoneInfoBuilder timeSeq(Long value ){
            this.obj.timeSeq = value;
            if( query == null  )
                query = new Query();
            this.query.where("timeSeq", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}