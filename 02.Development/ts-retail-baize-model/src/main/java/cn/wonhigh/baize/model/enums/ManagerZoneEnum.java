package cn.wonhigh.baize.model.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 大区枚举
 * @create 2024/8/5 15:56
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ManagerZoneEnum {

    EP("东区", "EP"),
    SP("南区", "SP"),
    WP("西区", "WP"),
    NP("北区", "NP"),
    CP("中区", "CP")
    ;


    private final String name;

    private final String type;


    ManagerZoneEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }


    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public static String getName(String type){
        ManagerZoneEnum[] enums = ManagerZoneEnum.values();
        for (ManagerZoneEnum anEnum : enums) {
            if(anEnum.type.equals(type)){
                return anEnum.getName();
            }
        }
        return type;
    }
}
