/**  **/
package cn.wonhigh.baize.model.entity.gms;

import cn.mercury.annotation.Label;
import cn.mercury.basic.query.Query;
import cn.mercury.domain.AbstractEntryBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;

/** auto generate start ,don't modify */

/**
* 机构信息表
**/
public class GmsStore extends cn.mercury.domain.BaseEntity<Integer>
{

    private static final long serialVersionUID = 1694415297016L;
    
    /**
    *时间序列
    **/ 
    @Label("时间序列") 
    private Long timeSeq;
    

    public Long getTimeSeq(){
        return  timeSeq;
    }
    public void setTimeSeq(Long val ){
        timeSeq = val;
    }
    /**
    *检索码
    **/ 
    @Label("检索码") 
    private String searchCode;
    

    public String getSearchCode(){
        return  searchCode;
    }
    public void setSearchCode(String val ){
        searchCode = val;
    }
    /**
    *备注
    **/ 
    @Label("备注") 
    private String remark;
    

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    /**
    *实虚仓类型(0：实仓, 1：虚仓, 2：外部虚仓)
    **/ 
    @Label("实虚仓类型") 
    private Integer virtPhyStorageType;
    

    public Integer getVirtPhyStorageType(){
        return  virtPhyStorageType;
    }
    public void setVirtPhyStorageType(Integer val ){
        virtPhyStorageType = val;
    }
    /**
    *仓库类别:0工厂仓、1、总仓、2城市仓、3 零售仓、4质检仓、5样品仓
    **/ 
    @Label("仓库类别") 
    private String storageType;
    

    public String getStorageType(){
        return  storageType;
    }
    public void setStorageType(String val ){
        storageType = val;
    }
    /**
    *机构类型(21:店仓  22:仓库)
    **/ 
    @Label("机构类型") 
    private Integer storeType;
    

    public Integer getStoreType(){
        return  storeType;
    }
    public void setStoreType(Integer val ){
        storeType = val;
    }
    /**
    *传真号
    **/ 
    @Label("传真号") 
    private String fax;
    

    public String getFax(){
        return  fax;
    }
    public void setFax(String val ){
        fax = val;
    }
    /**
    *电话号码
    **/ 
    @Label("电话号码") 
    private String tel;
    

    public String getTel(){
        return  tel;
    }
    public void setTel(String val ){
        tel = val;
    }
    /**
    *联系人
    **/ 
    @Label("联系人") 
    private String contactName;
    

    public String getContactName(){
        return  contactName;
    }
    public void setContactName(String val ){
        contactName = val;
    }
    /**
    *邮编
    **/ 
    @Label("邮编") 
    private String zipCode;
    

    public String getZipCode(){
        return  zipCode;
    }
    public void setZipCode(String val ){
        zipCode = val;
    }
    /**
    *地址(填写时不用包含省、市、县)
    **/ 
    @Label("地址") 
    private String address;
    

    public String getAddress(){
        return  address;
    }
    public void setAddress(String val ){
        address = val;
    }
    /**
    *经营区域编号
    **/ 
    @Label("经营区域编号") 
    private String zoneNo;
    

    public String getZoneNo(){
        return  zoneNo;
    }
    public void setZoneNo(String val ){
        zoneNo = val;
    }
    /**
    *行政县编码
    **/ 
    @Label("行政县编码") 
    private String countyNo;
    

    public String getCountyNo(){
        return  countyNo;
    }
    public void setCountyNo(String val ){
        countyNo = val;
    }
    /**
    *行政市编码
    **/ 
    @Label("行政市编码") 
    private String cityNo;
    

    public String getCityNo(){
        return  cityNo;
    }
    public void setCityNo(String val ){
        cityNo = val;
    }
    /**
    *行政省编码
    **/ 
    @Label("行政省编码") 
    private String provinceNo;
    

    public String getProvinceNo(){
        return  provinceNo;
    }
    public void setProvinceNo(String val ){
        provinceNo = val;
    }
    /**
    *机构状态( 0:冻结,1:正常,9:撤销)
    **/ 
    @Label("机构状态") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *所属业务单元
    **/ 
    @Label("所属业务单元") 
    private String sysNo;
    

    public String getSysNo(){
        return  sysNo;
    }
    public void setSysNo(String val ){
        sysNo = val;
    }
    /**
    *机构全称
    **/ 
    @Label("机构全称") 
    private String fullName;
    

    public String getFullName(){
        return  fullName;
    }
    public void setFullName(String val ){
        fullName = val;
    }
    /**
    *机构简称
    **/ 
    @Label("机构简称") 
    private String shortName;
    

    public String getShortName(){
        return  shortName;
    }
    public void setShortName(String val ){
        shortName = val;
    }
    /**
    *机构代号
    **/ 
    @Label("机构代号") 
    private String storeCode;
    

    public String getStoreCode(){
        return  storeCode;
    }
    public void setStoreCode(String val ){
        storeCode = val;
    }
    /**
    *所属主仓库
    **/ 
    @Label("所属主仓库") 
    private String parentNo;
    

    public String getParentNo(){
        return  parentNo;
    }
    public void setParentNo(String val ){
        parentNo = val;
    }
    /**
    *仓库面积
    **/ 
    @Label("仓库面积") 
    private BigDecimal area;
    

    public BigDecimal getArea(){
        return  area;
    }
    public void setArea(BigDecimal val ){
        area = val;
    }
    /**
    *仓库编码
    **/ 
    @Label("仓库编码") 
    private String storeNo;
    

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public StoreBuilder build(){
        return new StoreBuilder(this);
    }

    public static class StoreBuilder extends AbstractEntryBuilder<GmsStore>{

        private StoreBuilder(GmsStore entry){
            this.obj = entry;
        }

       @Override
		public GmsStore object() {
			return this.obj;
		}

        
        public StoreBuilder timeSeq(Long value ){
            
            this.obj.timeSeq = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("timeSeq", value);
            return this;
        }
        
        public StoreBuilder searchCode(String value ){
            
            this.obj.searchCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("searchCode", value);
            return this;
        }
        
        public StoreBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public StoreBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public StoreBuilder updateUser(String value ){
            
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public StoreBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public StoreBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public StoreBuilder virtPhyStorageType(Integer value ){
            
            this.obj.virtPhyStorageType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("virtPhyStorageType", value);
            return this;
        }
        
        public StoreBuilder storageType(String value ){
            
            this.obj.storageType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storageType", value);
            return this;
        }
        
        public StoreBuilder storeType(Integer value ){
            
            this.obj.storeType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeType", value);
            return this;
        }
        
        public StoreBuilder fax(String value ){
            
            this.obj.fax = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("fax", value);
            return this;
        }
        
        public StoreBuilder tel(String value ){
            
            this.obj.tel = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("tel", value);
            return this;
        }
        
        public StoreBuilder contactName(String value ){
            
            this.obj.contactName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("contactName", value);
            return this;
        }
        
        public StoreBuilder zipCode(String value ){
            
            this.obj.zipCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("zipCode", value);
            return this;
        }
        
        public StoreBuilder address(String value ){
            
            this.obj.address = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("address", value);
            return this;
        }
        
        public StoreBuilder zoneNo(String value ){
            
            this.obj.zoneNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("zoneNo", value);
            return this;
        }
        
        public StoreBuilder countyNo(String value ){
            
            this.obj.countyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("countyNo", value);
            return this;
        }
        
        public StoreBuilder cityNo(String value ){
            
            this.obj.cityNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("cityNo", value);
            return this;
        }
        
        public StoreBuilder provinceNo(String value ){
            
            this.obj.provinceNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("provinceNo", value);
            return this;
        }
        
        public StoreBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public StoreBuilder sysNo(String value ){
            
            this.obj.sysNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sysNo", value);
            return this;
        }
        
        public StoreBuilder fullName(String value ){
            
            this.obj.fullName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("fullName", value);
            return this;
        }
        
        public StoreBuilder shortName(String value ){
            
            this.obj.shortName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shortName", value);
            return this;
        }
        
        public StoreBuilder storeCode(String value ){
            
            this.obj.storeCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeCode", value);
            return this;
        }
        
        public StoreBuilder parentNo(String value ){
            
            this.obj.parentNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parentNo", value);
            return this;
        }
        
        public StoreBuilder area(BigDecimal value ){
            
            this.obj.area = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("area", value);
            return this;
        }
        
        public StoreBuilder storeNo(String value ){
            
            this.obj.storeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public StoreBuilder id(Integer value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }