/**  **/
package cn.wonhigh.baize.model.entity.ios;

import org.apache.commons.lang.builder.ToStringBuilder;

import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;

import java.util.Date;


import java.math.BigDecimal;

/** auto generate start ,don't modify */

/**
* 网络销售出库单
**/
public class RetailOrderOut  extends cn.mercury.domain.BasicEntity  
{

    private static final long serialVersionUID = 1676958778673L;
    
    /**
    *原始订单号
    **/ 
    @Label("原始订单号") 
    private String originalOrderNo;
    

    public String getOriginalOrderNo(){
        return  originalOrderNo;
    }
    public void setOriginalOrderNo(String val ){
        originalOrderNo = val;
    }
    /**
    *要求到货时间
    **/ 
    @Label("要求到货时间") 
    private Date arriveTime;
    

    public Date getArriveTime(){
        return  arriveTime;
    }
    public void setArriveTime(Date val ){
        arriveTime = val;
    }
    /**
    *单据编号
    **/ 
    @Label("单据编号") 
    private String billNo;
    

    public String getBillNo(){
        return  billNo;
    }
    public void setBillNo(String val ){
        billNo = val;
    }
    /**
    *单据类型 4001
    **/ 
    @Label("单据类型") 
    private Integer billType;
    

    public Integer getBillType(){
        return  billType;
    }
    public void setBillType(Integer val ){
        billType = val;
    }
    /**
    *出库类型:0-正常销售出库,1-作废质检出库
    **/ 
    @Label("出库类型") 
    private Integer outBizType;
    

    public Integer getOutBizType(){
        return  outBizType;
    }
    public void setOutBizType(Integer val ){
        outBizType = val;
    }
    /**
    *来源单号
    **/ 
    @Label("来源单号") 
    private String refBillNo;
    

    public String getRefBillNo(){
        return  refBillNo;
    }
    public void setRefBillNo(String val ){
        refBillNo = val;
    }
    /**
    *物流仓名称
    **/ 
    @Label("物流仓名称") 
    private String storeName;
    

    public String getStoreName(){
        return  storeName;
    }
    public void setStoreName(String val ){
        storeName = val;
    }
    /**
    *物流仓编码
    **/ 
    @Label("物流仓编码") 
    private String storeNo;
    

    public String getStoreNo(){
        return  storeNo;
    }
    public void setStoreNo(String val ){
        storeNo = val;
    }
    /**
    *纸箱型号
    **/ 
    @Label("纸箱型号") 
    private String boxCode;
    

    public String getBoxCode(){
        return  boxCode;
    }
    public void setBoxCode(String val ){
        boxCode = val;
    }
    /**
    *包裹体积
    **/ 
    @Label("包裹体积") 
    private BigDecimal parcelVolume;
    

    public BigDecimal getParcelVolume(){
        return  parcelVolume;
    }
    public void setParcelVolume(BigDecimal val ){
        parcelVolume = val;
    }
    /**
    *包裹重量
    **/ 
    @Label("包裹重量") 
    private BigDecimal parcelWeight;
    

    public BigDecimal getParcelWeight(){
        return  parcelWeight;
    }
    public void setParcelWeight(BigDecimal val ){
        parcelWeight = val;
    }
    /**
    *来源类型 目前是网销通知单4000
    **/ 
    @Label("来源类型") 
    private Integer refBillType;
    

    public Integer getRefBillType(){
        return  refBillType;
    }
    public void setRefBillType(Integer val ){
        refBillType = val;
    }
    /**
    *单据状态 ：5 确认 100完结
    **/ 
    @Label("单据状态") 
    private Integer status;
    

    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    /**
    *子订单号
    **/ 
    @Label("子订单号") 
    private String orderSubNo;
    

    public String getOrderSubNo(){
        return  orderSubNo;
    }
    public void setOrderSubNo(String val ){
        orderSubNo = val;
    }
    /**
    *销售店铺结算公司
    **/ 
    @Label("销售店铺结算公司") 
    private String saleCompanyNo;
    

    public String getSaleCompanyNo(){
        return  saleCompanyNo;
    }
    public void setSaleCompanyNo(String val ){
        saleCompanyNo = val;
    }
    /**
    *唯品会档期
    **/ 
    @Label("唯品会档期") 
    private String theaterSchedule;
    

    public String getTheaterSchedule(){
        return  theaterSchedule;
    }
    public void setTheaterSchedule(String val ){
        theaterSchedule = val;
    }
    /**
    *批发销货单
    **/ 
    @Label("批发销货单") 
    private String recordCode;
    

    public String getRecordCode(){
        return  recordCode;
    }
    public void setRecordCode(String val ){
        recordCode = val;
    }
    /**
    *是否匹配到订单：0-没有，1-有
    **/ 
    @Label("是否匹配到订单") 
    private Integer hasOrder;
    

    public Integer getHasOrder(){
        return  hasOrder;
    }
    public void setHasOrder(Integer val ){
        hasOrder = val;
    }
    /**
    *结算公司
    **/ 
    @Label("结算公司") 
    private String companyNo;
    

    public String getCompanyNo(){
        return  companyNo;
    }
    public void setCompanyNo(String val ){
        companyNo = val;
    }
    /**
    *入库单号
    **/ 
    @Label("入库单号") 
    private String storageNo;
    

    public String getStorageNo(){
        return  storageNo;
    }
    public void setStorageNo(String val ){
        storageNo = val;
    }
    /**
    *来源平台
    **/ 
    @Label("来源平台") 
    private String originPlatform;
    

    public String getOriginPlatform(){
        return  originPlatform;
    }
    public void setOriginPlatform(String val ){
        originPlatform = val;
    }
    /**
    *1店发，2仓发
    **/ 
    @Label("店发") 
    private Integer sendStoreType;
    

    public Integer getSendStoreType(){
        return  sendStoreType;
    }
    public void setSendStoreType(Integer val ){
        sendStoreType = val;
    }
    /**
    *订单留言
    **/ 
    @Label("订单留言") 
    private String message;
    

    public String getMessage(){
        return  message;
    }
    public void setMessage(String val ){
        message = val;
    }
    /**
    *客户编码,目前指优购
    **/ 
    @Label("客户编码") 
    private String customerNo;
    

    public String getCustomerNo(){
        return  customerNo;
    }
    public void setCustomerNo(String val ){
        customerNo = val;
    }
    /**
    *客户名称
    **/ 
    @Label("客户名称") 
    private String customerName;
    

    public String getCustomerName(){
        return  customerName;
    }
    public void setCustomerName(String val ){
        customerName = val;
    }
    /**
    *发货方机构编码
    **/ 
    @Label("发货方机构编码") 
    private String sendStoreNo;
    

    public String getSendStoreNo(){
        return  sendStoreNo;
    }
    public void setSendStoreNo(String val ){
        sendStoreNo = val;
    }
    /**
    *发货方名称
    **/ 
    @Label("发货方名称") 
    private String sendStoreName;
    

    public String getSendStoreName(){
        return  sendStoreName;
    }
    public void setSendStoreName(String val ){
        sendStoreName = val;
    }
    /**
    *订货单位
    **/ 
    @Label("订货单位") 
    private String orderUnitNo;
    

    public String getOrderUnitNo(){
        return  orderUnitNo;
    }
    public void setOrderUnitNo(String val ){
        orderUnitNo = val;
    }
    /**
    *订货单位名称
    **/ 
    @Label("订货单位名称") 
    private String orderUnitName;
    

    public String getOrderUnitName(){
        return  orderUnitName;
    }
    public void setOrderUnitName(String val ){
        orderUnitName = val;
    }
    /**
    *发货日期
    **/ 
    @Label("发货日期") 
    private Date sendOutDate;
    

    public Date getSendOutDate(){
        return  sendOutDate;
    }
    public void setSendOutDate(Date val ){
        sendOutDate = val;
    }
    /**
    *快递公司
    **/ 
    @Label("快递公司") 
    private String logisticCompanyCode;
    

    public String getLogisticCompanyCode(){
        return  logisticCompanyCode;
    }
    public void setLogisticCompanyCode(String val ){
        logisticCompanyCode = val;
    }
    /**
    *快递公司名称
    **/ 
    @Label("快递公司名称") 
    private String logisticsCompanyName;
    

    public String getLogisticsCompanyName(){
        return  logisticsCompanyName;
    }
    public void setLogisticsCompanyName(String val ){
        logisticsCompanyName = val;
    }
    /**
    *快递单号
    **/ 
    @Label("快递单号") 
    private String expressCodes;
    

    public String getExpressCodes(){
        return  expressCodes;
    }
    public void setExpressCodes(String val ){
        expressCodes = val;
    }
    /**
    *快递费
    **/ 
    @Label("快递费") 
    private BigDecimal expressPrice;
    

    public BigDecimal getExpressPrice(){
        return  expressPrice;
    }
    public void setExpressPrice(BigDecimal val ){
        expressPrice = val;
    }
    /**
    *总数量
    **/ 
    @Label("总数量") 
    private Integer sendDetailTotal;
    

    public Integer getSendDetailTotal(){
        return  sendDetailTotal;
    }
    public void setSendDetailTotal(Integer val ){
        sendDetailTotal = val;
    }
    /**
    *销售总价
    **/ 
    @Label("销售总价") 
    private BigDecimal totalPrice;
    

    public BigDecimal getTotalPrice(){
        return  totalPrice;
    }
    public void setTotalPrice(BigDecimal val ){
        totalPrice = val;
    }
    /**
    *备注
    **/ 
    @Label("备注") 
    private String remark;
    

    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    /**
    *分库字段：本部+序号
    **/ 
    @Label("分库字段") 
    private String shardingFlag;
    

    public String getShardingFlag(){
        return  shardingFlag;
    }
    public void setShardingFlag(String val ){
        shardingFlag = val;
    }
    /**
    *零售分库字段：本部+大区
    **/ 
    @Label("零售分库字段") 
    private String retailFlag;
    

    public String getRetailFlag(){
        return  retailFlag;
    }
    public void setRetailFlag(String val ){
        retailFlag = val;
    }
    /**
    *审核时间
    **/ 
    @Label("审核时间") 
    private Date auditTime;
    

    public Date getAuditTime(){
        return  auditTime;
    }
    public void setAuditTime(Date val ){
        auditTime = val;
    }
    /**
    *来源平台名称
    **/ 
    @Label("来源平台名称") 
    private String originPlatformName;
    

    public String getOriginPlatformName(){
        return  originPlatformName;
    }
    public void setOriginPlatformName(String val ){
        originPlatformName = val;
    }
    /**
    *店
    **/ 
    @Label("店") 
    private String shopNo;
    

    public String getShopNo(){
        return  shopNo;
    }
    public void setShopNo(String val ){
        shopNo = val;
    }
    /**
    *店铺名称
    **/ 
    @Label("店铺名称") 
    private String shopName;
    

    public String getShopName(){
        return  shopName;
    }
    public void setShopName(String val ){
        shopName = val;
    }
    /**
    *是否到付 0-否，1-是
    **/ 
    @Label("是否到付") 
    private String isPayment;
    

    public String getIsPayment(){
        return  isPayment;
    }
    public void setIsPayment(String val ){
        isPayment = val;
    }
    /**
    *渠道号
    **/ 
    @Label("渠道号") 
    private String channelNo;
    

    public String getChannelNo(){
        return  channelNo;
    }
    public void setChannelNo(String val ){
        channelNo = val;
    }
    /**
    *捡货单号
    **/ 
    @Label("捡货单号") 
    private String packageNo;
    

    public String getPackageNo(){
        return  packageNo;
    }
    public void setPackageNo(String val ){
        packageNo = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public RetailOrderOutBuilder build(){
        return new RetailOrderOutBuilder(this);
    }

    public static class RetailOrderOutBuilder extends AbstractEntryBuilder<RetailOrderOut>{

        private RetailOrderOutBuilder(RetailOrderOut entry){
            this.obj = entry;
        }

       @Override
		public RetailOrderOut object() {
			return this.obj;
		}

        
        public RetailOrderOutBuilder originalOrderNo(String value ){
            
            this.obj.originalOrderNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originalOrderNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder arriveTime(Date value ){
            
            this.obj.arriveTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("arriveTime", value);
            return this;
        }
        
        public RetailOrderOutBuilder billNo(String value ){
            
            this.obj.billNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder billType(Integer value ){
            
            this.obj.billType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("billType", value);
            return this;
        }
        
        public RetailOrderOutBuilder outBizType(Integer value ){
            
            this.obj.outBizType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("outBizType", value);
            return this;
        }
        
        public RetailOrderOutBuilder refBillNo(String value ){
            
            this.obj.refBillNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refBillNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder id(String value ){
            
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public RetailOrderOutBuilder storeName(String value ){
            
            this.obj.storeName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeName", value);
            return this;
        }
        
        public RetailOrderOutBuilder storeNo(String value ){
            
            this.obj.storeNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storeNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder boxCode(String value ){
            
            this.obj.boxCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("boxCode", value);
            return this;
        }
        
        public RetailOrderOutBuilder parcelVolume(BigDecimal value ){
            
            this.obj.parcelVolume = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parcelVolume", value);
            return this;
        }
        
        public RetailOrderOutBuilder parcelWeight(BigDecimal value ){
            
            this.obj.parcelWeight = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("parcelWeight", value);
            return this;
        }
        
        public RetailOrderOutBuilder refBillType(Integer value ){
            
            this.obj.refBillType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("refBillType", value);
            return this;
        }
        
        public RetailOrderOutBuilder status(Integer value ){
            
            this.obj.status = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public RetailOrderOutBuilder orderSubNo(String value ){
            
            this.obj.orderSubNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderSubNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder saleCompanyNo(String value ){
            
            this.obj.saleCompanyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("saleCompanyNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder theaterSchedule(String value ){
            
            this.obj.theaterSchedule = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("theaterSchedule", value);
            return this;
        }
        
        public RetailOrderOutBuilder recordCode(String value ){
            
            this.obj.recordCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("recordCode", value);
            return this;
        }
        
        public RetailOrderOutBuilder hasOrder(Integer value ){
            
            this.obj.hasOrder = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("hasOrder", value);
            return this;
        }
        
        public RetailOrderOutBuilder companyNo(String value ){
            
            this.obj.companyNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("companyNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder storageNo(String value ){
            
            this.obj.storageNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("storageNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder originPlatform(String value ){
            
            this.obj.originPlatform = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatform", value);
            return this;
        }
        
        public RetailOrderOutBuilder sendStoreType(Integer value ){
            
            this.obj.sendStoreType = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendStoreType", value);
            return this;
        }
        
        public RetailOrderOutBuilder message(String value ){
            
            this.obj.message = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("message", value);
            return this;
        }
        
        public RetailOrderOutBuilder customerNo(String value ){
            
            this.obj.customerNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customerNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder customerName(String value ){
            
            this.obj.customerName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("customerName", value);
            return this;
        }
        
        public RetailOrderOutBuilder sendStoreNo(String value ){
            
            this.obj.sendStoreNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendStoreNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder sendStoreName(String value ){
            
            this.obj.sendStoreName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendStoreName", value);
            return this;
        }
        
        public RetailOrderOutBuilder orderUnitNo(String value ){
            
            this.obj.orderUnitNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder orderUnitName(String value ){
            
            this.obj.orderUnitName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("orderUnitName", value);
            return this;
        }
        
        public RetailOrderOutBuilder sendOutDate(Date value ){
            
            this.obj.sendOutDate = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendOutDate", value);
            return this;
        }
        
        public RetailOrderOutBuilder logisticCompanyCode(String value ){
            
            this.obj.logisticCompanyCode = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticCompanyCode", value);
            return this;
        }
        
        public RetailOrderOutBuilder logisticsCompanyName(String value ){
            
            this.obj.logisticsCompanyName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("logisticsCompanyName", value);
            return this;
        }
        
        public RetailOrderOutBuilder expressCodes(String value ){
            
            this.obj.expressCodes = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressCodes", value);
            return this;
        }
        
        public RetailOrderOutBuilder expressPrice(BigDecimal value ){
            
            this.obj.expressPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("expressPrice", value);
            return this;
        }
        
        public RetailOrderOutBuilder sendDetailTotal(Integer value ){
            
            this.obj.sendDetailTotal = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("sendDetailTotal", value);
            return this;
        }
        
        public RetailOrderOutBuilder totalPrice(BigDecimal value ){
            
            this.obj.totalPrice = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("totalPrice", value);
            return this;
        }
        
        public RetailOrderOutBuilder createTime(Date value ){
            
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public RetailOrderOutBuilder createUser(String value ){
            
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public RetailOrderOutBuilder updateTime(Date value ){
            
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
        public RetailOrderOutBuilder remark(String value ){
            
            this.obj.remark = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public RetailOrderOutBuilder shardingFlag(String value ){
            
            this.obj.shardingFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shardingFlag", value);
            return this;
        }
        
        public RetailOrderOutBuilder retailFlag(String value ){
            
            this.obj.retailFlag = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("retailFlag", value);
            return this;
        }
        
        public RetailOrderOutBuilder auditTime(Date value ){
            
            this.obj.auditTime = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("auditTime", value);
            return this;
        }
        
        public RetailOrderOutBuilder originPlatformName(String value ){
            
            this.obj.originPlatformName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("originPlatformName", value);
            return this;
        }
        
        public RetailOrderOutBuilder shopNo(String value ){
            
            this.obj.shopNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder shopName(String value ){
            
            this.obj.shopName = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("shopName", value);
            return this;
        }
        
        public RetailOrderOutBuilder isPayment(String value ){
            
            this.obj.isPayment = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("isPayment", value);
            return this;
        }
        
        public RetailOrderOutBuilder channelNo(String value ){
            
            this.obj.channelNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("channelNo", value);
            return this;
        }
        
        public RetailOrderOutBuilder packageNo(String value ){
            
            this.obj.packageNo = value;
            
            if( query == null  )
                query = new Query();
            this.query.where("packageNo", value);
            return this;
        }
        
    }
     /** auto generate end,don't modify */
    }