package cn.wonhigh.baize.model.dto.dispatchruleset;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExitsQueryDataDto implements Serializable {


    private static final long serialVersionUID = 4178180132924877074L;
    private String noEqRuleNo;

    private String brandNo;

    private String channelNo;
    private String orderSourceNo;

    private List<Map<String,Object>> dtlList;


    public String getBrandNo() {
        return brandNo;
    }

    public void setBrandNo(String brandNo) {
        this.brandNo = brandNo;
    }

    public String getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(String channelNo) {
        this.channelNo = channelNo;
    }

    public String getOrderSourceNo() {
        return orderSourceNo;
    }

    public void setOrderSourceNo(String orderSourceNo) {
        this.orderSourceNo = orderSourceNo;
    }

    public List<Map<String, Object>> getDtlList() {
        return dtlList;
    }

    public void setDtlList(List<Map<String, Object>> dtlList) {
        this.dtlList = dtlList;
    }

    public String getNoEqRuleNo() {
        return noEqRuleNo;
    }

    public void setNoEqRuleNo(String noEqRuleNo) {
        this.noEqRuleNo = noEqRuleNo;
    }
}
