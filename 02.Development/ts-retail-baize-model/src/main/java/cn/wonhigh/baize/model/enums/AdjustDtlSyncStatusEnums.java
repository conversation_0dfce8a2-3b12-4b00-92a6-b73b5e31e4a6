package cn.wonhigh.baize.model.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 调整单同步状态枚举
 * @date 2025/6/13 15:57
 */
public enum AdjustDtlSyncStatusEnums {
    NOT_SYNC(0, "未同步"),
    SYNC_SUCCESS(1, "同步成功"),
    SYNC_FAIL(2, "同步失败");
    private Integer value;
    private String desc;

    AdjustDtlSyncStatusEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AdjustDtlSyncStatusEnums getAdjustDtlSyncStatusEnums(Integer status) {
        for (AdjustDtlSyncStatusEnums adjustDtlSyncStatusEnums : AdjustDtlSyncStatusEnums.values()) {
            if (adjustDtlSyncStatusEnums.getValue().equals(status)) {
                return adjustDtlSyncStatusEnums;
            }
        }
        return null;
    }
    public Integer getValue() {
        return value;
    }
    public void setValue(Integer value) {
        this.value = value;
    }
    public String getDesc() {
        return desc;
    }
    public void setDesc(String desc) {
        this.desc = desc;
    }
}
