package cn.wonhigh.baize.model.enums;

import java.util.Optional;

public enum InventoryActiveLockOpscodeEnum {
    OpscodeEnum_ADD(OpscodeEnum.ADD, "活动创建"),
    OpscodeEnum_DEL(OpscodeEnum.DEL, "活动删除"),
    OpscodeEnum_UP(OpscodeEnum.UP, "活动修改"),
    OpscodeEnum_AUDIT(OpscodeEnum.AUDIT, "活动审核"),
    OpscodeEnum_CANCEL(OpscodeEnum.CANCEL, "活动作废"),
    OpscodeEnum_EFFECT(OpscodeEnum.EFFECT, "活动生效"),
    SYNC("sync", "库存同步"),
    STOP("stop", "活动终止"),
    DTL_CREATE("DTL_INSERT", "明细新增"),
    DTL_UPDATE("DTL_UPDATE", "明细修改"),
    DTL_DELETE("DTL_DELETE", "明细删除"),
    DTL_IMPORT("DTL_IMPORT", "明细导入")
    ;

    private OpscodeEnum opscodeEnum;
    private String code;
    private final String desc;

    InventoryActiveLockOpscodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    InventoryActiveLockOpscodeEnum(OpscodeEnum opscodeEnum, String desc) {
        this.opscodeEnum = opscodeEnum;
        this.desc = desc;
    }


    public String getDesc() {
        return this.desc;
    }

    public String getCode() {
        return Optional.ofNullable(this.opscodeEnum)
                .map(OpscodeEnum::getCode)
                .orElseGet(() -> this.code);
    }

    public OpscodeEnum getOpscodeEnum() {
        return opscodeEnum;
    }

    public static String getCustomizeDesc(String code) {

        InventoryActiveLockOpscodeEnum[] values = InventoryActiveLockOpscodeEnum.values();
        String desc = "";
        for (InventoryActiveLockOpscodeEnum value : values) {
            if (value.getCode().equals(code)) {
                desc = value.getDesc();
                break;
            }
        }
        return desc;
    }
}
