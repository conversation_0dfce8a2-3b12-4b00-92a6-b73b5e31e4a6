/**  **/
package cn.wonhigh.baize.model.entity.gms;

import cn.wonhigh.baize.model.enums.ChannelTypeEnum;
import org.apache.commons.lang.builder.ToStringBuilder;
import cn.mercury.domain.AbstractEntryBuilder;
import cn.mercury.basic.query.Query;
import cn.mercury.annotation.Label;
import java.util.Date;

/** 
*auto generate start ,don't modify
* 库存同步配置表
**/
public class IcsInventorySyncConfig  extends cn.mercury.domain.BasicEntity  {

    private static final long serialVersionUID = 1723088372206L;
    
    //渠道编码
    @Label("渠道编码") 
    private String channelNo;
    
    //渠道名称
    @Label("渠道名称") 
    private String channelName;
    
    //虚仓编码
    @Label("虚仓编码") 
    private String vstoreCode;
    
    //虚拟仓名称
    @Label("虚拟仓名称") 
    private String vstoreName;
    
    //渠道类别 1:OMS 2小程序 3第三方店铺
    @Label(value = "渠道类别", defaultVal = "2") 
    private Integer channelType;
    
    //共享比例
    @Label(value = "共享比例", defaultVal = "10") 
    private Integer sharingRatio;
    
    //状态0关闭1开启
    @Label(value = "状态", defaultVal = "0") 
    private Integer status;
    
    //备注
    @Label("备注") 
    private String remark;

    private String statusName;

    private String channelTypeName;

    //三级来源编码
    private String thirdChannelNo;

    //渠道标准
    private Integer vstoreMold;

    public Integer getVstoreMold() {
        return vstoreMold;
    }

    public void setVstoreMold(Integer vstoreMold) {
        this.vstoreMold = vstoreMold;
    }

    public String getThirdChannelNo() {
        return thirdChannelNo;
    }

    public void setThirdChannelNo(String thirdChannelNo) {
        this.thirdChannelNo = thirdChannelNo;
    }

    public String getChannelTypeName() {
        return ChannelTypeEnum.getName(this.channelType);
    }

    public void setChannelTypeName(String channelTypeName) {
        this.channelTypeName = channelTypeName;
    }

    public String getStatusName() {
        return  (this.status != null && this.status == 1) ? "启用" : "禁用";
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getChannelNo(){
        return  channelNo;
    }
    public void setChannelNo(String val ){
        channelNo = val;
    }
    
    public String getChannelName(){
        return  channelName;
    }
    public void setChannelName(String val ){
        channelName = val;
    }
    
    public String getVstoreCode(){
        return  vstoreCode;
    }
    public void setVstoreCode(String val ){
        vstoreCode = val;
    }
    
    public String getVstoreName(){
        return  vstoreName;
    }
    public void setVstoreName(String val ){
        vstoreName = val;
    }
    
    public Integer getChannelType(){
        return  channelType;
    }
    public void setChannelType(Integer val ){
        channelType = val;
    }
    
    public Integer getSharingRatio(){
        return  sharingRatio;
    }
    public void setSharingRatio(Integer val ){
        sharingRatio = val;
    }
    
    public Integer getStatus(){
        return  status;
    }
    public void setStatus(Integer val ){
        status = val;
    }
    
    public String getRemark(){
        return  remark;
    }
    public void setRemark(String val ){
        remark = val;
    }
    
    @Override
	public String toString() {
         return ToStringBuilder.reflectionToString(this);
	}
	    
    public IcsInventorySyncConfigBuilder build(){
        return new IcsInventorySyncConfigBuilder(this);
    }

    public static class IcsInventorySyncConfigBuilder extends AbstractEntryBuilder<IcsInventorySyncConfig>{

        private IcsInventorySyncConfigBuilder(IcsInventorySyncConfig entry){
            this.obj = entry;
        }

       @Override
		public IcsInventorySyncConfig object() {
			return this.obj;
		}

        
        public IcsInventorySyncConfigBuilder id(String value ){
            this.obj.setId(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("id", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder channelNo(String value ){
            this.obj.channelNo = value;
            if( query == null  )
                query = new Query();
            this.query.where("channelNo", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder channelName(String value ){
            this.obj.channelName = value;
            if( query == null  )
                query = new Query();
            this.query.where("channelName", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder vstoreCode(String value ){
            this.obj.vstoreCode = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreCode", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder vstoreName(String value ){
            this.obj.vstoreName = value;
            if( query == null  )
                query = new Query();
            this.query.where("vstoreName", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder channelType(Integer value ){
            this.obj.channelType = value;
            if( query == null  )
                query = new Query();
            this.query.where("channelType", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder sharingRatio(Integer value ){
            this.obj.sharingRatio = value;
            if( query == null  )
                query = new Query();
            this.query.where("sharingRatio", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder status(Integer value ){
            this.obj.status = value;
            if( query == null  )
                query = new Query();
            this.query.where("status", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder remark(String value ){
            this.obj.remark = value;
            if( query == null  )
                query = new Query();
            this.query.where("remark", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder createUser(String value ){
            this.obj.setCreateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createUser", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder createTime(Date value ){
            this.obj.setCreateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("createTime", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder updateUser(String value ){
            this.obj.setUpdateUser(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateUser", value);
            return this;
        }
        
        public IcsInventorySyncConfigBuilder updateTime(Date value ){
            this.obj.setUpdateTime(value);
            
            if( query == null  )
                query = new Query();
            this.query.where("updateTime", value);
            return this;
        }
        
    }
/** auto generate end,don't modify */
}