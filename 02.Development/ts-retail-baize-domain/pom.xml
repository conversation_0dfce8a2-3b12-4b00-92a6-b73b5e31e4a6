<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.wonhigh.baize</groupId>
        <artifactId>ts-retail-baize</artifactId>
        <version>1.0.28-SNAPSHOT</version>
    </parent>
    <artifactId>ts-retail-baize-domain</artifactId>
    <name>ts-retail-baize-domain</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-servicemodel-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-domain</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>
            <groupId>io.github.lastincisor</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.29-tidb-1.0.0</version>
        </dependency>
-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-servicemodel-dubbo</artifactId> 
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.baize</groupId>
            <artifactId>ts-retail-baize-model</artifactId>
            <version>${project.version}</version>
        </dependency>


        <dependency>
            <groupId>cn.wonhigh.retail</groupId>
            <artifactId>retail-uc-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh</groupId>
            <artifactId>base-framework-common-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.2.6</version>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>cn.wonhigh.retail</groupId>
            <artifactId>retail-gms-api-client</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.wonhigh.retail</groupId>
            <artifactId>retail-iis-api-client</artifactId>
            <version>${iis.api.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>cn.wonhigh.retail</groupId>
                    <artifactId>retail-iis-server</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.wonhigh</groupId>
                    <artifactId>base-framework-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>