<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.oms.OmsVirtualWarehouseRepository">
    <!-- auto generate -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.oms.OmsVirtualWarehouse" >
        <result  column="virtual_warehouse_id" property="virtualWarehouseId" jdbcType="INTEGER"/>
        <result  column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
        <result  column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result  column="is_enable" property="isEnable" jdbcType="INTEGER"/>
        <result  column="virtual_warehouse_code" property="virtualWarehouseCode" jdbcType="VARCHAR"/>
        <result  column="virtual_warehouse_name" property="virtualWarehouseName" jdbcType="VARCHAR"/>
        <result  column="virtual_warehouse_type" property="virtualWarehouseType" jdbcType="INTEGER"/>
        <result  column="warehouse_id" property="warehouseId" jdbcType="INTEGER"/>
        <result  column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
    virtual_warehouse_id,
    modified_time,
    created_time,
    is_enable,
    virtual_warehouse_code,
    virtual_warehouse_name,
    virtual_warehouse_type,
    warehouse_id,
    warehouse_name
    </sql>

    <sql id="condition">
        <if test="null!=params.queryCondition and ''!=params.queryCondition">
            And ${params.queryCondition}
        </if>
        <if test="params.virtualWarehouseId != null">
            And `virtual_warehouse_id` = #{params.virtualWarehouseId}
        </if>
        <if test="params.modifiedTime != null">
            And `modified_time` = #{params.modifiedTime}
        </if>
        <if test="params.createdTime != null">
            And `created_time` = #{params.createdTime}
        </if>
        <if test="params.isEnable != null">
            And `is_enable` = #{params.isEnable}
        </if>
        <if test="params.virtualWarehouseCode != null and ''!=params.virtualWarehouseCode ">
            And `virtual_warehouse_code` = #{params.virtualWarehouseCode}
        </if>
        <if test="params.virtualWarehouseName != null and ''!=params.virtualWarehouseName ">
            And `virtual_warehouse_name` = #{params.virtualWarehouseName}
        </if>
        <if test="params.virtualWarehouseType != null">
            And `virtual_warehouse_type` = #{params.virtualWarehouseType}
        </if>
        <if test="params.warehouseId != null">
            And `warehouse_id` = #{params.warehouseId}
        </if>
        <if test="params.warehouseName != null and ''!=params.warehouseName ">
            And `warehouse_name` = #{params.warehouseName}
        </if>
    </sql>
    <!--查询单个-->
    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        Select
        <include refid="column_list" />
        From `oms_virtual_warehouse`
        Where virtual_warehouse_id = #{virtualWarehouseId}

    </select>

    <!--查询指定行数据-->
    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_virtual_warehouse`
        <where>
            <include refid="condition" />
        </where>
        Limit 1
    </select>

    <!--统计总行数-->
    <select id="selectCount" resultType="java.lang.Integer" parameterType="map">
        Select Count(1)
        From `oms_virtual_warehouse`
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_virtual_warehouse`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
        Limit ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_virtual_warehouse`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="virtualWarehouseId" useGeneratedKeys="true">
        Insert Into `oms_virtual_warehouse` (
            `virtual_warehouse_id` ,
            `modified_time` ,
            `created_time` ,
            `is_enable` ,
            `virtual_warehouse_code` ,
            `virtual_warehouse_name` ,
            `virtual_warehouse_type` ,
            `warehouse_id` ,
            `warehouse_name` 
        )
        Values (
            #{virtualWarehouseId} ,
            #{modifiedTime} ,
            #{createdTime} ,
            #{isEnable} ,
            #{virtualWarehouseCode} ,
            #{virtualWarehouseName} ,
            #{virtualWarehouseType} ,
            #{warehouseId} ,
            #{warehouseName} 
        )
    </insert>

    <insert id="insertBatch" keyProperty="virtualWarehouseId" useGeneratedKeys="true">
        Insert Into `oms_virtual_warehouse` (
           `virtual_warehouse_id`,
           `modified_time`,
           `created_time`,
           `is_enable`,
           `virtual_warehouse_code`,
           `virtual_warehouse_name`,
           `virtual_warehouse_type`,
           `warehouse_id`,
           `warehouse_name`
        ) Values
        <foreach collection="list" item="entity" separator=",">
        (
            #{entity.virtualWarehouseId},
            #{entity.modifiedTime},
            #{entity.createdTime},
            #{entity.isEnable},
            #{entity.virtualWarehouseCode},
            #{entity.virtualWarehouseName},
            #{entity.virtualWarehouseType},
            #{entity.warehouseId},
            #{entity.warehouseName}
        )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="virtualWarehouseId" useGeneratedKeys="true">
        Insert Into `oms_virtual_warehouse` (
        `virtual_warehouse_id` ,
        `modified_time` ,
        `created_time` ,
        `is_enable` ,
        `virtual_warehouse_code` ,
        `virtual_warehouse_name` ,
        `virtual_warehouse_type` ,
        `warehouse_id` ,
        `warehouse_name` 
       )Values
        <foreach collection="list" item="entity" separator=",">
         (
         #{entity.virtualWarehouseId},
         #{entity.modifiedTime},
         #{entity.createdTime},
         #{entity.isEnable},
         #{entity.virtualWarehouseCode},
         #{entity.virtualWarehouseName},
         #{entity.virtualWarehouseType},
         #{entity.warehouseId},
         #{entity.warehouseName}
 )
        </foreach>
        On Duplicate Key Update
        `modified_time` = values(modified_time) ,
        `created_time` = values(created_time) ,
        `is_enable` = values(is_enable) ,
        `virtual_warehouse_code` = values(virtual_warehouse_code) ,
        `virtual_warehouse_name` = values(virtual_warehouse_name) ,
        `virtual_warehouse_type` = values(virtual_warehouse_type) ,
        `warehouse_id` = values(warehouse_id) ,
        `warehouse_name` = values(warehouse_name) 

    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        Update `oms_virtual_warehouse`
        <set>
            <if test="modifiedTime != null">
                `modified_time` = #{modifiedTime},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime},
            </if>
            <if test="isEnable != null">
                `is_enable` = #{isEnable},
            </if>
            <if test="virtualWarehouseCode != null and virtualWarehouseCode != ''">
                `virtual_warehouse_code` = #{virtualWarehouseCode},
            </if>
            <if test="virtualWarehouseName != null and virtualWarehouseName != ''">
                `virtual_warehouse_name` = #{virtualWarehouseName},
            </if>
            <if test="virtualWarehouseType != null">
                `virtual_warehouse_type` = #{virtualWarehouseType},
            </if>
            <if test="warehouseId != null">
                `warehouse_id` = #{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                `warehouse_name` = #{warehouseName},
            </if>
        </set>
        Where virtual_warehouse_id = #{virtualWarehouseId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByPrimaryKey">
        Delete From `oms_virtual_warehouse` Where virtual_warehouse_id = #{virtualWarehouseId}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        Delete From `oms_virtual_warehouse`
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                And In ( ${params.ids} )
            </if>
        </where>
    </delete>

    <!-- auto generate end -->

</mapper>
