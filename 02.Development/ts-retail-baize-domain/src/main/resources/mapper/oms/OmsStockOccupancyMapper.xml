<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.oms.IOmsStockOccupancyRepository">
    <!-- auto generate -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.oms.OmsStockOccupancy" >
        <result  column="stock_occupancy_id" property="stockOccupancyId" jdbcType="INTEGER"/>
        <result  column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
        <result  column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result  column="sort_time" property="sortTime" jdbcType="TIMESTAMP"/>
        <result  column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result  column="stock_occupancy_type" property="stockOccupancyType" jdbcType="INTEGER"/>
        <result  column="sku_id" property="skuId" jdbcType="INTEGER"/>
        <result  column="sku_code" property="skuCode" jdbcType="VARCHAR"/>
        <result  column="main_id" property="mainId" jdbcType="INTEGER"/>
        <result  column="detail_id" property="detailId" jdbcType="INTEGER"/>
        <result  column="warehouse_id" property="warehouseId" jdbcType="INTEGER"/>
        <result  column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
        <result  column="virtual_warehouse_id" property="virtualWarehouseId" jdbcType="INTEGER"/>
        <result  column="virtual_warehouse_name" property="virtualWarehouseName" jdbcType="VARCHAR"/>
        <result  column="status" property="status" jdbcType="INTEGER"/>
        <result  column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
    stock_occupancy_id,
    modified_time,
    created_time,
    sort_time,
    quantity,
    stock_occupancy_type,
    sku_id,
    sku_code,
    main_id,
    detail_id,
    warehouse_id,
    warehouse_name,
    virtual_warehouse_id,
    virtual_warehouse_name,
    status,
    remark
    </sql>

    <sql id="condition">
        <if test="null!=params.queryCondition and ''!=params.queryCondition">
            And ${params.queryCondition}
        </if>
        <if test="params.stockOccupancyId != null">
            And `stock_occupancy_id` = #{params.stockOccupancyId}
        </if>
        <if test="params.modifiedTime != null">
            And `modified_time` = #{params.modifiedTime}
        </if>
        <if test="params.createdTime != null">
            And `created_time` = #{params.createdTime}
        </if>
        <if test="params.sortTime != null">
            And `sort_time` = #{params.sortTime}
        </if>
        <if test="params.quantity != null">
            And `quantity` = #{params.quantity}
        </if>
        <if test="params.stockOccupancyType != null">
            And `stock_occupancy_type` = #{params.stockOccupancyType}
        </if>
        <if test="params.skuId != null">
            And `sku_id` = #{params.skuId}
        </if>
        <if test="params.skuCode != null and ''!=params.skuCode ">
            And `sku_code` = #{params.skuCode}
        </if>
        <if test="params.mainId != null">
            And `main_id` = #{params.mainId}
        </if>
        <if test="params.detailId != null">
            And `detail_id` = #{params.detailId}
        </if>
        <if test="params.warehouseId != null">
            And `warehouse_id` = #{params.warehouseId}
        </if>
        <if test="params.warehouseName != null and ''!=params.warehouseName ">
            And `warehouse_name` = #{params.warehouseName}
        </if>
        <if test="params.virtualWarehouseId != null">
            And `virtual_warehouse_id` = #{params.virtualWarehouseId}
        </if>
        <if test="params.virtualWarehouseName != null and ''!=params.virtualWarehouseName ">
            And `virtual_warehouse_name` = #{params.virtualWarehouseName}
        </if>
        <if test="params.status != null">
            And `status` = #{params.status}
        </if>
        <if test="params.remark != null and ''!=params.remark ">
            And `remark` = #{params.remark}
        </if>
    </sql>
    <!--查询单个-->
    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        Select
        <include refid="column_list" />
        From `oms_stock_occupancy`
        Where stock_occupancy_id = #{stockOccupancyId}

    </select>

    <!--查询指定行数据-->
    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_stock_occupancy`
        <where>
            <include refid="condition" />
        </where>
        Limit 1
    </select>

    <!--统计总行数-->
    <select id="selectCount" resultType="java.lang.Integer" parameterType="map">
        Select Count(1)
        From `oms_stock_occupancy`
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_stock_occupancy`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
        Limit ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_stock_occupancy`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="stockOccupancyId" useGeneratedKeys="true">
        Insert Into `oms_stock_occupancy` (
            `stock_occupancy_id` ,
            `modified_time` ,
            `created_time` ,
            `sort_time` ,
            `quantity` ,
            `stock_occupancy_type` ,
            `sku_id` ,
            `sku_code` ,
            `main_id` ,
            `detail_id` ,
            `warehouse_id` ,
            `warehouse_name` ,
            `virtual_warehouse_id` ,
            `virtual_warehouse_name` ,
            `status` ,
            `remark` 
        )
        Values (
            #{stockOccupancyId} ,
            #{modifiedTime} ,
            #{createdTime} ,
            #{sortTime} ,
            #{quantity} ,
            #{stockOccupancyType} ,
            #{skuId} ,
            #{skuCode} ,
            #{mainId} ,
            #{detailId} ,
            #{warehouseId} ,
            #{warehouseName} ,
            #{virtualWarehouseId} ,
            #{virtualWarehouseName} ,
            #{status} ,
            #{remark} 
        )
    </insert>

    <insert id="insertBatch" keyProperty="stockOccupancyId" useGeneratedKeys="true">
        Insert Into `oms_stock_occupancy` (
           `stock_occupancy_id`,
           `modified_time`,
           `created_time`,
           `sort_time`,
           `quantity`,
           `stock_occupancy_type`,
           `sku_id`,
           `sku_code`,
           `main_id`,
           `detail_id`,
           `warehouse_id`,
           `warehouse_name`,
           `virtual_warehouse_id`,
           `virtual_warehouse_name`,
           `status`,
           `remark`
        ) Values
        <foreach collection="list" item="entity" separator=",">
        (
            #{entity.stockOccupancyId},
            #{entity.modifiedTime},
            #{entity.createdTime},
            #{entity.sortTime},
            #{entity.quantity},
            #{entity.stockOccupancyType},
            #{entity.skuId},
            #{entity.skuCode},
            #{entity.mainId},
            #{entity.detailId},
            #{entity.warehouseId},
            #{entity.warehouseName},
            #{entity.virtualWarehouseId},
            #{entity.virtualWarehouseName},
            #{entity.status},
            #{entity.remark}
        )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="stockOccupancyId" useGeneratedKeys="true">
        Insert Into `oms_stock_occupancy` (
        `stock_occupancy_id` ,
        `modified_time` ,
        `created_time` ,
        `sort_time` ,
        `quantity` ,
        `stock_occupancy_type` ,
        `sku_id` ,
        `sku_code` ,
        `main_id` ,
        `detail_id` ,
        `warehouse_id` ,
        `warehouse_name` ,
        `virtual_warehouse_id` ,
        `virtual_warehouse_name` ,
        `status` ,
        `remark` 
       )Values
        <foreach collection="list" item="entity" separator=",">
         (
         #{entity.stockOccupancyId},
         #{entity.modifiedTime},
         #{entity.createdTime},
         #{entity.sortTime},
         #{entity.quantity},
         #{entity.stockOccupancyType},
         #{entity.skuId},
         #{entity.skuCode},
         #{entity.mainId},
         #{entity.detailId},
         #{entity.warehouseId},
         #{entity.warehouseName},
         #{entity.virtualWarehouseId},
         #{entity.virtualWarehouseName},
         #{entity.status},
         #{entity.remark}
 )
        </foreach>
        On Duplicate Key Update
        `modified_time` = values(modified_time) ,
        `created_time` = values(created_time) ,
        `sort_time` = values(sort_time) ,
        `quantity` = values(quantity) ,
        `stock_occupancy_type` = values(stock_occupancy_type) ,
        `sku_id` = values(sku_id) ,
        `sku_code` = values(sku_code) ,
        `main_id` = values(main_id) ,
        `detail_id` = values(detail_id) ,
        `warehouse_id` = values(warehouse_id) ,
        `warehouse_name` = values(warehouse_name) ,
        `virtual_warehouse_id` = values(virtual_warehouse_id) ,
        `virtual_warehouse_name` = values(virtual_warehouse_name) ,
        `status` = values(status) ,
        `remark` = values(remark) 

    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        Update `oms_stock_occupancy`
        <set>
            <if test="modifiedTime != null">
                `modified_time` = #{modifiedTime},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime},
            </if>
            <if test="sortTime != null">
                `sort_time` = #{sortTime},
            </if>
            <if test="quantity != null">
                `quantity` = #{quantity},
            </if>
            <if test="stockOccupancyType != null">
                `stock_occupancy_type` = #{stockOccupancyType},
            </if>
            <if test="skuId != null">
                `sku_id` = #{skuId},
            </if>
            <if test="skuCode != null and skuCode != ''">
                `sku_code` = #{skuCode},
            </if>
            <if test="mainId != null">
                `main_id` = #{mainId},
            </if>
            <if test="detailId != null">
                `detail_id` = #{detailId},
            </if>
            <if test="warehouseId != null">
                `warehouse_id` = #{warehouseId},
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                `warehouse_name` = #{warehouseName},
            </if>
            <if test="virtualWarehouseId != null">
                `virtual_warehouse_id` = #{virtualWarehouseId},
            </if>
            <if test="virtualWarehouseName != null and virtualWarehouseName != ''">
                `virtual_warehouse_name` = #{virtualWarehouseName},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="remark != null and remark != ''">
                `remark` = #{remark},
            </if>
        </set>
        Where stock_occupancy_id = #{stockOccupancyId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByPrimaryKey">
        Delete From `oms_stock_occupancy` Where stock_occupancy_id = #{stockOccupancyId}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        Delete From `oms_stock_occupancy`
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                And In ( ${params.ids} )
            </if>
        </where>
    </delete>

    <!-- auto generate end -->
    <select id="queryLockStockQty" parameterType="map" resultType="cn.wonhigh.baize.model.dto.OmsStockOccupancyDTO">
        select
            sum(quantity) as lock_qty,
            t2.warehouse_name as virtualStoreName,
            t2.warehouse_code as virtualStoreCode,
            t1.sku_id,
            t3.wms_sku_id as skuCode
        from
            oms_stock_occupancy t1
                join oms_biz.oms_warehouse t2 on t1.warehouse_id = t2.warehouse_id
                join oms_biz.oms_product_wms_mapping t3 on t1.sku_id = t3.sku_id
        where t2.warehouse_code  = #{warehouseId} and t3.wms_sku_id  = #{skuId} and t1.STATUS != 4 ;
    </select>

</mapper>
