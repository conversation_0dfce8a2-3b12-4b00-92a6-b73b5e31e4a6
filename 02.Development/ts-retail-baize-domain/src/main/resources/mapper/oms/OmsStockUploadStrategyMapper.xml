<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.oms.OmsStockUploadStrategyRepository">
    <!-- auto generate -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.oms.OmsStockUploadStrategy" >
        <result  column="stock_upload_strategy_id" property="stockUploadStrategyId" jdbcType="INTEGER"/>
        <result  column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
        <result  column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result  column="stock_upload_strategy_name" property="stockUploadStrategyName" jdbcType="VARCHAR"/>
        <result  column="store_id" property="storeId" jdbcType="INTEGER"/>
        <result  column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result  column="is_auto_upload" property="isAutoUpload" jdbcType="INTEGER"/>
        <result  column="is_manual_upload" property="isManualUpload" jdbcType="INTEGER"/>
        <result  column="mall_warehouse" property="mallWarehouse" jdbcType="VARCHAR"/>
        <result  column="setting_json" property="settingJson" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
    stock_upload_strategy_id,
    modified_time,
    created_time,
    stock_upload_strategy_name,
    store_id,
    store_name,
    is_auto_upload,
    is_manual_upload,
    mall_warehouse,
    setting_json
    </sql>

    <sql id="condition">
        <if test="null!=params.queryCondition and ''!=params.queryCondition">
            And ${params.queryCondition}
        </if>
        <if test="params.stockUploadStrategyId != null">
            And `stock_upload_strategy_id` = #{params.stockUploadStrategyId}
        </if>
        <if test="params.modifiedTime != null">
            And `modified_time` = #{params.modifiedTime}
        </if>
        <if test="params.createdTime != null">
            And `created_time` = #{params.createdTime}
        </if>
        <if test="params.stockUploadStrategyName != null and ''!=params.stockUploadStrategyName ">
            And `stock_upload_strategy_name` = #{params.stockUploadStrategyName}
        </if>
        <if test="params.storeId != null">
            And `store_id` = #{params.storeId}
        </if>
        <if test="params.storeName != null and ''!=params.storeName ">
            And `store_name` = #{params.storeName}
        </if>
        <if test="params.isAutoUpload != null">
            And `is_auto_upload` = #{params.isAutoUpload}
        </if>
        <if test="params.isManualUpload != null">
            And `is_manual_upload` = #{params.isManualUpload}
        </if>
        <if test="params.mallWarehouse != null and ''!=params.mallWarehouse ">
            And `mall_warehouse` = #{params.mallWarehouse}
        </if>
        <if test="params.settingJson != null and ''!=params.settingJson ">
            And `setting_json` = #{params.settingJson}
        </if>
    </sql>
    <!--查询单个-->
    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        Select
        <include refid="column_list" />
        From `oms_stock_upload_strategy`
        Where stock_upload_strategy_id = #{stockUploadStrategyId}

    </select>

    <!--查询指定行数据-->
    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_stock_upload_strategy`
        <where>
            <include refid="condition" />
        </where>
        Limit 1
    </select>

    <!--统计总行数-->
    <select id="selectCount" resultType="java.lang.Integer" parameterType="map">
        Select Count(1)
        From `oms_stock_upload_strategy`
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_stock_upload_strategy`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
        Limit ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_stock_upload_strategy`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="stockUploadStrategyId" useGeneratedKeys="true">
        Insert Into `oms_stock_upload_strategy` (
            `stock_upload_strategy_id` ,
            `modified_time` ,
            `created_time` ,
            `stock_upload_strategy_name` ,
            `store_id` ,
            `store_name` ,
            `is_auto_upload` ,
            `is_manual_upload` ,
            `mall_warehouse` ,
            `setting_json` 
        )
        Values (
            #{stockUploadStrategyId} ,
            #{modifiedTime} ,
            #{createdTime} ,
            #{stockUploadStrategyName} ,
            #{storeId} ,
            #{storeName} ,
            #{isAutoUpload} ,
            #{isManualUpload} ,
            #{mallWarehouse} ,
            #{settingJson} 
        )
    </insert>

    <insert id="insertBatch" keyProperty="stockUploadStrategyId" useGeneratedKeys="true">
        Insert Into `oms_stock_upload_strategy` (
           `stock_upload_strategy_id`,
           `modified_time`,
           `created_time`,
           `stock_upload_strategy_name`,
           `store_id`,
           `store_name`,
           `is_auto_upload`,
           `is_manual_upload`,
           `mall_warehouse`,
           `setting_json`
        ) Values
        <foreach collection="list" item="entity" separator=",">
        (
            #{entity.stockUploadStrategyId},
            #{entity.modifiedTime},
            #{entity.createdTime},
            #{entity.stockUploadStrategyName},
            #{entity.storeId},
            #{entity.storeName},
            #{entity.isAutoUpload},
            #{entity.isManualUpload},
            #{entity.mallWarehouse},
            #{entity.settingJson}
        )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="stockUploadStrategyId" useGeneratedKeys="true">
        Insert Into `oms_stock_upload_strategy` (
        `stock_upload_strategy_id` ,
        `modified_time` ,
        `created_time` ,
        `stock_upload_strategy_name` ,
        `store_id` ,
        `store_name` ,
        `is_auto_upload` ,
        `is_manual_upload` ,
        `mall_warehouse` ,
        `setting_json` 
       )Values
        <foreach collection="list" item="entity" separator=",">
         (
         #{entity.stockUploadStrategyId},
         #{entity.modifiedTime},
         #{entity.createdTime},
         #{entity.stockUploadStrategyName},
         #{entity.storeId},
         #{entity.storeName},
         #{entity.isAutoUpload},
         #{entity.isManualUpload},
         #{entity.mallWarehouse},
         #{entity.settingJson}
 )
        </foreach>
        On Duplicate Key Update
        `modified_time` = values(modified_time) ,
        `created_time` = values(created_time) ,
        `stock_upload_strategy_name` = values(stock_upload_strategy_name) ,
        `store_id` = values(store_id) ,
        `store_name` = values(store_name) ,
        `is_auto_upload` = values(is_auto_upload) ,
        `is_manual_upload` = values(is_manual_upload) ,
        `mall_warehouse` = values(mall_warehouse) ,
        `setting_json` = values(setting_json) 

    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        Update `oms_stock_upload_strategy`
        <set>
            <if test="modifiedTime != null">
                `modified_time` = #{modifiedTime},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime},
            </if>
            <if test="stockUploadStrategyName != null and stockUploadStrategyName != ''">
                `stock_upload_strategy_name` = #{stockUploadStrategyName},
            </if>
            <if test="storeId != null">
                `store_id` = #{storeId},
            </if>
            <if test="storeName != null and storeName != ''">
                `store_name` = #{storeName},
            </if>
            <if test="isAutoUpload != null">
                `is_auto_upload` = #{isAutoUpload},
            </if>
            <if test="isManualUpload != null">
                `is_manual_upload` = #{isManualUpload},
            </if>
            <if test="mallWarehouse != null and mallWarehouse != ''">
                `mall_warehouse` = #{mallWarehouse},
            </if>
            <if test="settingJson != null and settingJson != ''">
                `setting_json` = #{settingJson},
            </if>
        </set>
        Where stock_upload_strategy_id = #{stockUploadStrategyId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByPrimaryKey">
        Delete From `oms_stock_upload_strategy` Where stock_upload_strategy_id = #{stockUploadStrategyId}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        Delete From `oms_stock_upload_strategy`
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                And In ( ${params.ids} )
            </if>
        </where>
    </delete>

    <!-- auto generate end -->

    <select id="selectStrategyByVstoreId" parameterType="list">
        select * from oms_stock_upload_strategy
        <where>
            <if test="vstoreIds != null and vstoreIds.size()>0">
                <foreach collection="vstoreIds" open="(" close=")" item="vsId" separator=" or ">
                    store_id like concat('%',#{vsId},'%')
                </foreach>
            </if>
        </where>
    </select>

</mapper>
