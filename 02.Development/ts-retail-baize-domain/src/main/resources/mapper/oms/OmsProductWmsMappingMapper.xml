<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.oms.OmsProductWmsMappingRepository">
    <!-- auto generate -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.oms.OmsProductWmsMapping" >
        <result  column="product_wms_mapping_id" property="productWmsMappingId" jdbcType="INTEGER"/>
        <result  column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
        <result  column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result  column="product_id" property="productId" jdbcType="INTEGER"/>
        <result  column="sku_id" property="skuId" jdbcType="INTEGER"/>
        <result  column="wms_type" property="wmsType" jdbcType="INTEGER"/>
        <result  column="wms_sku_id" property="wmsSkuId" jdbcType="VARCHAR"/>
        <result  column="customer_id" property="customerId" jdbcType="VARCHAR"/>
        <result  column="owner_code" property="ownerCode" jdbcType="VARCHAR"/>
        <result  column="priority_no" property="priorityNo" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="column_list">
    product_wms_mapping_id,
    modified_time,
    created_time,
    product_id,
    sku_id,
    wms_type,
    wms_sku_id,
    customer_id,
    owner_code,
    priority_no
    </sql>

    <sql id="condition">
        <if test="null!=params.queryCondition and ''!=params.queryCondition">
            And ${params.queryCondition}
        </if>
        <if test="params.productWmsMappingId != null">
            And `product_wms_mapping_id` = #{params.productWmsMappingId}
        </if>
        <if test="params.modifiedTime != null">
            And `modified_time` = #{params.modifiedTime}
        </if>
        <if test="params.createdTime != null">
            And `created_time` = #{params.createdTime}
        </if>
        <if test="params.productId != null">
            And `product_id` = #{params.productId}
        </if>
        <if test="params.skuId != null">
            And `sku_id` = #{params.skuId}
        </if>
        <if test="params.wmsType != null">
            And `wms_type` = #{params.wmsType}
        </if>
        <if test="params.wmsSkuId != null and ''!=params.wmsSkuId ">
            And `wms_sku_id` = #{params.wmsSkuId}
        </if>
        <if test="params.customerId != null and ''!=params.customerId ">
            And `customer_id` = #{params.customerId}
        </if>
        <if test="params.ownerCode != null and ''!=params.ownerCode ">
            And `owner_code` = #{params.ownerCode}
        </if>
        <if test="params.priorityNo != null">
            And `priority_no` = #{params.priorityNo}
        </if>
    </sql>
    <!--查询单个-->
    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        Select
        <include refid="column_list" />
        From `oms_product_wms_mapping`
        Where product_wms_mapping_id = #{productWmsMappingId}

    </select>

    <!--查询指定行数据-->
    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_product_wms_mapping`
        <where>
            <include refid="condition" />
        </where>
        Limit 1
    </select>

    <!--统计总行数-->
    <select id="selectCount" resultType="java.lang.Integer" parameterType="map">
        Select Count(1)
        From `oms_product_wms_mapping`
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_product_wms_mapping`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
        Limit ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_product_wms_mapping`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="productWmsMappingId" useGeneratedKeys="true">
        Insert Into `oms_product_wms_mapping` (
            `product_wms_mapping_id` ,
            `modified_time` ,
            `created_time` ,
            `product_id` ,
            `sku_id` ,
            `wms_type` ,
            `wms_sku_id` ,
            `customer_id` ,
            `owner_code` ,
            `priority_no` 
        )
        Values (
            #{productWmsMappingId} ,
            #{modifiedTime} ,
            #{createdTime} ,
            #{productId} ,
            #{skuId} ,
            #{wmsType} ,
            #{wmsSkuId} ,
            #{customerId} ,
            #{ownerCode} ,
            #{priorityNo} 
        )
    </insert>

    <insert id="insertBatch" keyProperty="productWmsMappingId" useGeneratedKeys="true">
        Insert Into `oms_product_wms_mapping` (
           `product_wms_mapping_id`,
           `modified_time`,
           `created_time`,
           `product_id`,
           `sku_id`,
           `wms_type`,
           `wms_sku_id`,
           `customer_id`,
           `owner_code`,
           `priority_no`
        ) Values
        <foreach collection="list" item="entity" separator=",">
        (
            #{entity.productWmsMappingId},
            #{entity.modifiedTime},
            #{entity.createdTime},
            #{entity.productId},
            #{entity.skuId},
            #{entity.wmsType},
            #{entity.wmsSkuId},
            #{entity.customerId},
            #{entity.ownerCode},
            #{entity.priorityNo}
        )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="productWmsMappingId" useGeneratedKeys="true">
        Insert Into `oms_product_wms_mapping` (
        `product_wms_mapping_id` ,
        `modified_time` ,
        `created_time` ,
        `product_id` ,
        `sku_id` ,
        `wms_type` ,
        `wms_sku_id` ,
        `customer_id` ,
        `owner_code` ,
        `priority_no` 
       )Values
        <foreach collection="list" item="entity" separator=",">
         (
         #{entity.productWmsMappingId},
         #{entity.modifiedTime},
         #{entity.createdTime},
         #{entity.productId},
         #{entity.skuId},
         #{entity.wmsType},
         #{entity.wmsSkuId},
         #{entity.customerId},
         #{entity.ownerCode},
         #{entity.priorityNo}
 )
        </foreach>
        On Duplicate Key Update
        `modified_time` = values(modified_time) ,
        `created_time` = values(created_time) ,
        `product_id` = values(product_id) ,
        `sku_id` = values(sku_id) ,
        `wms_type` = values(wms_type) ,
        `wms_sku_id` = values(wms_sku_id) ,
        `customer_id` = values(customer_id) ,
        `owner_code` = values(owner_code) ,
        `priority_no` = values(priority_no) 

    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        Update `oms_product_wms_mapping`
        <set>
            <if test="modifiedTime != null">
                `modified_time` = #{modifiedTime},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime},
            </if>
            <if test="productId != null">
                `product_id` = #{productId},
            </if>
            <if test="skuId != null">
                `sku_id` = #{skuId},
            </if>
            <if test="wmsType != null">
                `wms_type` = #{wmsType},
            </if>
            <if test="wmsSkuId != null and wmsSkuId != ''">
                `wms_sku_id` = #{wmsSkuId},
            </if>
            <if test="customerId != null and customerId != ''">
                `customer_id` = #{customerId},
            </if>
            <if test="ownerCode != null and ownerCode != ''">
                `owner_code` = #{ownerCode},
            </if>
            <if test="priorityNo != null">
                `priority_no` = #{priorityNo},
            </if>
        </set>
        Where product_wms_mapping_id = #{productWmsMappingId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByPrimaryKey">
        Delete From `oms_product_wms_mapping` Where product_wms_mapping_id = #{productWmsMappingId}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        Delete From `oms_product_wms_mapping`
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                And In ( ${params.ids} )
            </if>
        </where>
    </delete>

    <!-- auto generate end -->

</mapper>
