<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.oms.OmsWarehouseRepository">
    <!-- auto generate -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.oms.OmsWarehouse" >
        <result  column="warehouse_id" property="warehouseId" jdbcType="INTEGER"/>
        <result  column="city_id" property="cityId" jdbcType="INTEGER"/>
        <result  column="country_id" property="countryId" jdbcType="INTEGER"/>
        <result  column="district_id" property="districtId" jdbcType="INTEGER"/>
        <result  column="province_id" property="provinceId" jdbcType="INTEGER"/>
        <result  column="city_name" property="cityName" jdbcType="VARCHAR"/>
        <result  column="district_name" property="districtName" jdbcType="VARCHAR"/>
        <result  column="province_name" property="provinceName" jdbcType="VARCHAR"/>
        <result  column="country_name" property="countryName" jdbcType="VARCHAR"/>
        <result  column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
        <result  column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result  column="is_enable" property="isEnable" jdbcType="INTEGER"/>
        <result  column="warehouse_code" property="warehouseCode" jdbcType="VARCHAR"/>
        <result  column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
        <result  column="warehouse_type" property="warehouseType" jdbcType="INTEGER"/>
        <result  column="brand_codes" property="brandCodes" jdbcType="VARCHAR"/>
        <result  column="brand_names" property="brandNames" jdbcType="VARCHAR"/>
        <result  column="wms_app_id" property="wmsAppId" jdbcType="INTEGER"/>
        <result  column="contact" property="contact" jdbcType="VARCHAR"/>
        <result  column="address" property="address" jdbcType="VARCHAR"/>
        <result  column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result  column="telephone" property="telephone" jdbcType="VARCHAR"/>
        <result  column="owner_code" property="ownerCode" jdbcType="VARCHAR"/>
        <result  column="outer_code" property="outerCode" jdbcType="VARCHAR"/>
        <result  column="is_match_region" property="isMatchRegion" jdbcType="INTEGER"/>
        <result  column="vip_platform_code" property="vipPlatformCode" jdbcType="VARCHAR"/>
        <result  column="is_stock_available" property="isStockAvailable" jdbcType="INTEGER"/>
        <result  column="is_can_stock_upload" property="isCanStockUpload" jdbcType="INTEGER"/>
        <result  column="is_has_group_connected" property="isHasGroupConnected" jdbcType="INTEGER"/>
        <result  column="company_id" property="companyId" jdbcType="INTEGER"/>
        <result  column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result  column="is_sort_express_by_cost" property="isSortExpressByCost" jdbcType="INTEGER"/>
        <result  column="is_order_intercepted" property="isOrderIntercepted" jdbcType="INTEGER"/>
        <result  column="intercept_time_type" property="interceptTimeType" jdbcType="INTEGER"/>
        <result  column="intercept_time" property="interceptTime" jdbcType="VARCHAR"/>
        <result  column="intercept_begin_time" property="interceptBeginTime" jdbcType="VARCHAR"/>
        <result  column="intercept_end_time" property="interceptEndTime" jdbcType="VARCHAR"/>
        <result  column="advance_delivery_type" property="advanceDeliveryType" jdbcType="INTEGER"/>
        <result  column="is_o2o" property="isO2o" jdbcType="INTEGER"/>
        <result  column="is_enable_aoxiang" property="isEnableAoxiang" jdbcType="INTEGER"/>
        <result  column="zipcode" property="zipcode" jdbcType="VARCHAR"/>
        <result  column="is_split_by_brand" property="isSplitByBrand" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="column_list">
    warehouse_id,
    city_id,
    country_id,
    district_id,
    province_id,
    city_name,
    district_name,
    province_name,
    country_name,
    modified_time,
    created_time,
    is_enable,
    warehouse_code,
    warehouse_name,
    warehouse_type,
    brand_codes,
    brand_names,
    wms_app_id,
    contact,
    address,
    mobile,
    telephone,
    owner_code,
    outer_code,
    is_match_region,
    vip_platform_code,
    is_stock_available,
    is_can_stock_upload,
    is_has_group_connected,
    company_id,
    company_name,
    is_sort_express_by_cost,
    is_order_intercepted,
    intercept_time_type,
    intercept_time,
    intercept_begin_time,
    intercept_end_time,
    advance_delivery_type,
    is_o2o,
    is_enable_aoxiang,
    zipcode,
    is_split_by_brand
    </sql>

    <sql id="condition">
        <if test="null!=params.queryCondition and ''!=params.queryCondition">
            And ${params.queryCondition}
        </if>
        <if test="params.warehouseId != null">
            And `warehouse_id` = #{params.warehouseId}
        </if>
        <if test="params.cityId != null">
            And `city_id` = #{params.cityId}
        </if>
        <if test="params.countryId != null">
            And `country_id` = #{params.countryId}
        </if>
        <if test="params.districtId != null">
            And `district_id` = #{params.districtId}
        </if>
        <if test="params.provinceId != null">
            And `province_id` = #{params.provinceId}
        </if>
        <if test="params.cityName != null and ''!=params.cityName ">
            And `city_name` = #{params.cityName}
        </if>
        <if test="params.districtName != null and ''!=params.districtName ">
            And `district_name` = #{params.districtName}
        </if>
        <if test="params.provinceName != null and ''!=params.provinceName ">
            And `province_name` = #{params.provinceName}
        </if>
        <if test="params.countryName != null and ''!=params.countryName ">
            And `country_name` = #{params.countryName}
        </if>
        <if test="params.modifiedTime != null">
            And `modified_time` = #{params.modifiedTime}
        </if>
        <if test="params.createdTime != null">
            And `created_time` = #{params.createdTime}
        </if>
        <if test="params.isEnable != null">
            And `is_enable` = #{params.isEnable}
        </if>
        <if test="params.warehouseCode != null and ''!=params.warehouseCode ">
            And `warehouse_code` = #{params.warehouseCode}
        </if>
        <if test="params.warehouseName != null and ''!=params.warehouseName ">
            And `warehouse_name` = #{params.warehouseName}
        </if>
        <if test="params.warehouseType != null">
            And `warehouse_type` = #{params.warehouseType}
        </if>
        <if test="params.brandCodes != null and ''!=params.brandCodes ">
            And `brand_codes` = #{params.brandCodes}
        </if>
        <if test="params.brandNames != null and ''!=params.brandNames ">
            And `brand_names` = #{params.brandNames}
        </if>
        <if test="params.wmsAppId != null">
            And `wms_app_id` = #{params.wmsAppId}
        </if>
        <if test="params.contact != null and ''!=params.contact ">
            And `contact` = #{params.contact}
        </if>
        <if test="params.address != null and ''!=params.address ">
            And `address` = #{params.address}
        </if>
        <if test="params.mobile != null and ''!=params.mobile ">
            And `mobile` = #{params.mobile}
        </if>
        <if test="params.telephone != null and ''!=params.telephone ">
            And `telephone` = #{params.telephone}
        </if>
        <if test="params.ownerCode != null and ''!=params.ownerCode ">
            And `owner_code` = #{params.ownerCode}
        </if>
        <if test="params.outerCode != null and ''!=params.outerCode ">
            And `outer_code` = #{params.outerCode}
        </if>
        <if test="params.isMatchRegion != null">
            And `is_match_region` = #{params.isMatchRegion}
        </if>
        <if test="params.vipPlatformCode != null and ''!=params.vipPlatformCode ">
            And `vip_platform_code` = #{params.vipPlatformCode}
        </if>
        <if test="params.isStockAvailable != null">
            And `is_stock_available` = #{params.isStockAvailable}
        </if>
        <if test="params.isCanStockUpload != null">
            And `is_can_stock_upload` = #{params.isCanStockUpload}
        </if>
        <if test="params.isHasGroupConnected != null">
            And `is_has_group_connected` = #{params.isHasGroupConnected}
        </if>
        <if test="params.companyId != null">
            And `company_id` = #{params.companyId}
        </if>
        <if test="params.companyName != null and ''!=params.companyName ">
            And `company_name` = #{params.companyName}
        </if>
        <if test="params.isSortExpressByCost != null">
            And `is_sort_express_by_cost` = #{params.isSortExpressByCost}
        </if>
        <if test="params.isOrderIntercepted != null">
            And `is_order_intercepted` = #{params.isOrderIntercepted}
        </if>
        <if test="params.interceptTimeType != null">
            And `intercept_time_type` = #{params.interceptTimeType}
        </if>
        <if test="params.interceptTime != null">
            And `intercept_time` = #{params.interceptTime}
        </if>
        <if test="params.interceptBeginTime != null">
            And `intercept_begin_time` = #{params.interceptBeginTime}
        </if>
        <if test="params.interceptEndTime != null">
            And `intercept_end_time` = #{params.interceptEndTime}
        </if>
        <if test="params.advanceDeliveryType != null">
            And `advance_delivery_type` = #{params.advanceDeliveryType}
        </if>
        <if test="params.isO2o != null">
            And `is_o2o` = #{params.isO2o}
        </if>
        <if test="params.isEnableAoxiang != null">
            And `is_enable_aoxiang` = #{params.isEnableAoxiang}
        </if>
        <if test="params.zipcode != null and ''!=params.zipcode ">
            And `zipcode` = #{params.zipcode}
        </if>
        <if test="params.isSplitByBrand != null">
            And `is_split_by_brand` = #{params.isSplitByBrand}
        </if>
    </sql>
    <!--查询单个-->
    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        Select
        <include refid="column_list" />
        From `oms_warehouse`
        Where warehouse_id = #{warehouseId}

    </select>

    <!--查询指定行数据-->
    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_warehouse`
        <where>
            <include refid="condition" />
        </where>
        Limit 1
    </select>

    <!--统计总行数-->
    <select id="selectCount" resultType="java.lang.Integer" parameterType="map">
        Select Count(1)
        From `oms_warehouse`
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_warehouse`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
        Limit ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_warehouse`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="warehouseId" useGeneratedKeys="true">
        Insert Into `oms_warehouse` (
            `warehouse_id` ,
            `city_id` ,
            `country_id` ,
            `district_id` ,
            `province_id` ,
            `city_name` ,
            `district_name` ,
            `province_name` ,
            `country_name` ,
            `modified_time` ,
            `created_time` ,
            `is_enable` ,
            `warehouse_code` ,
            `warehouse_name` ,
            `warehouse_type` ,
            `brand_codes` ,
            `brand_names` ,
            `wms_app_id` ,
            `contact` ,
            `address` ,
            `mobile` ,
            `telephone` ,
            `owner_code` ,
            `outer_code` ,
            `is_match_region` ,
            `vip_platform_code` ,
            `is_stock_available` ,
            `is_can_stock_upload` ,
            `is_has_group_connected` ,
            `company_id` ,
            `company_name` ,
            `is_sort_express_by_cost` ,
            `is_order_intercepted` ,
            `intercept_time_type` ,
            `intercept_time` ,
            `intercept_begin_time` ,
            `intercept_end_time` ,
            `advance_delivery_type` ,
            `is_o2o` ,
            `is_enable_aoxiang` ,
            `zipcode` ,
            `is_split_by_brand` 
        )
        Values (
            #{warehouseId} ,
            #{cityId} ,
            #{countryId} ,
            #{districtId} ,
            #{provinceId} ,
            #{cityName} ,
            #{districtName} ,
            #{provinceName} ,
            #{countryName} ,
            #{modifiedTime} ,
            #{createdTime} ,
            #{isEnable} ,
            #{warehouseCode} ,
            #{warehouseName} ,
            #{warehouseType} ,
            #{brandCodes} ,
            #{brandNames} ,
            #{wmsAppId} ,
            #{contact} ,
            #{address} ,
            #{mobile} ,
            #{telephone} ,
            #{ownerCode} ,
            #{outerCode} ,
            #{isMatchRegion} ,
            #{vipPlatformCode} ,
            #{isStockAvailable} ,
            #{isCanStockUpload} ,
            #{isHasGroupConnected} ,
            #{companyId} ,
            #{companyName} ,
            #{isSortExpressByCost} ,
            #{isOrderIntercepted} ,
            #{interceptTimeType} ,
            #{interceptTime} ,
            #{interceptBeginTime} ,
            #{interceptEndTime} ,
            #{advanceDeliveryType} ,
            #{isO2o} ,
            #{isEnableAoxiang} ,
            #{zipcode} ,
            #{isSplitByBrand} 
        )
    </insert>

    <insert id="insertBatch" keyProperty="warehouseId" useGeneratedKeys="true">
        Insert Into `oms_warehouse` (
           `warehouse_id`,
           `city_id`,
           `country_id`,
           `district_id`,
           `province_id`,
           `city_name`,
           `district_name`,
           `province_name`,
           `country_name`,
           `modified_time`,
           `created_time`,
           `is_enable`,
           `warehouse_code`,
           `warehouse_name`,
           `warehouse_type`,
           `brand_codes`,
           `brand_names`,
           `wms_app_id`,
           `contact`,
           `address`,
           `mobile`,
           `telephone`,
           `owner_code`,
           `outer_code`,
           `is_match_region`,
           `vip_platform_code`,
           `is_stock_available`,
           `is_can_stock_upload`,
           `is_has_group_connected`,
           `company_id`,
           `company_name`,
           `is_sort_express_by_cost`,
           `is_order_intercepted`,
           `intercept_time_type`,
           `intercept_time`,
           `intercept_begin_time`,
           `intercept_end_time`,
           `advance_delivery_type`,
           `is_o2o`,
           `is_enable_aoxiang`,
           `zipcode`,
           `is_split_by_brand`
        ) Values
        <foreach collection="list" item="entity" separator=",">
        (
            #{entity.warehouseId},
            #{entity.cityId},
            #{entity.countryId},
            #{entity.districtId},
            #{entity.provinceId},
            #{entity.cityName},
            #{entity.districtName},
            #{entity.provinceName},
            #{entity.countryName},
            #{entity.modifiedTime},
            #{entity.createdTime},
            #{entity.isEnable},
            #{entity.warehouseCode},
            #{entity.warehouseName},
            #{entity.warehouseType},
            #{entity.brandCodes},
            #{entity.brandNames},
            #{entity.wmsAppId},
            #{entity.contact},
            #{entity.address},
            #{entity.mobile},
            #{entity.telephone},
            #{entity.ownerCode},
            #{entity.outerCode},
            #{entity.isMatchRegion},
            #{entity.vipPlatformCode},
            #{entity.isStockAvailable},
            #{entity.isCanStockUpload},
            #{entity.isHasGroupConnected},
            #{entity.companyId},
            #{entity.companyName},
            #{entity.isSortExpressByCost},
            #{entity.isOrderIntercepted},
            #{entity.interceptTimeType},
            #{entity.interceptTime},
            #{entity.interceptBeginTime},
            #{entity.interceptEndTime},
            #{entity.advanceDeliveryType},
            #{entity.isO2o},
            #{entity.isEnableAoxiang},
            #{entity.zipcode},
            #{entity.isSplitByBrand}
        )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="warehouseId" useGeneratedKeys="true">
        Insert Into `oms_warehouse` (
        `warehouse_id` ,
        `city_id` ,
        `country_id` ,
        `district_id` ,
        `province_id` ,
        `city_name` ,
        `district_name` ,
        `province_name` ,
        `country_name` ,
        `modified_time` ,
        `created_time` ,
        `is_enable` ,
        `warehouse_code` ,
        `warehouse_name` ,
        `warehouse_type` ,
        `brand_codes` ,
        `brand_names` ,
        `wms_app_id` ,
        `contact` ,
        `address` ,
        `mobile` ,
        `telephone` ,
        `owner_code` ,
        `outer_code` ,
        `is_match_region` ,
        `vip_platform_code` ,
        `is_stock_available` ,
        `is_can_stock_upload` ,
        `is_has_group_connected` ,
        `company_id` ,
        `company_name` ,
        `is_sort_express_by_cost` ,
        `is_order_intercepted` ,
        `intercept_time_type` ,
        `intercept_time` ,
        `intercept_begin_time` ,
        `intercept_end_time` ,
        `advance_delivery_type` ,
        `is_o2o` ,
        `is_enable_aoxiang` ,
        `zipcode` ,
        `is_split_by_brand` 
       )Values
        <foreach collection="list" item="entity" separator=",">
         (
         #{entity.warehouseId},
         #{entity.cityId},
         #{entity.countryId},
         #{entity.districtId},
         #{entity.provinceId},
         #{entity.cityName},
         #{entity.districtName},
         #{entity.provinceName},
         #{entity.countryName},
         #{entity.modifiedTime},
         #{entity.createdTime},
         #{entity.isEnable},
         #{entity.warehouseCode},
         #{entity.warehouseName},
         #{entity.warehouseType},
         #{entity.brandCodes},
         #{entity.brandNames},
         #{entity.wmsAppId},
         #{entity.contact},
         #{entity.address},
         #{entity.mobile},
         #{entity.telephone},
         #{entity.ownerCode},
         #{entity.outerCode},
         #{entity.isMatchRegion},
         #{entity.vipPlatformCode},
         #{entity.isStockAvailable},
         #{entity.isCanStockUpload},
         #{entity.isHasGroupConnected},
         #{entity.companyId},
         #{entity.companyName},
         #{entity.isSortExpressByCost},
         #{entity.isOrderIntercepted},
         #{entity.interceptTimeType},
         #{entity.interceptTime},
         #{entity.interceptBeginTime},
         #{entity.interceptEndTime},
         #{entity.advanceDeliveryType},
         #{entity.isO2o},
         #{entity.isEnableAoxiang},
         #{entity.zipcode},
         #{entity.isSplitByBrand}
 )
        </foreach>
        On Duplicate Key Update
        `city_id` = values(city_id) ,
        `country_id` = values(country_id) ,
        `district_id` = values(district_id) ,
        `province_id` = values(province_id) ,
        `city_name` = values(city_name) ,
        `district_name` = values(district_name) ,
        `province_name` = values(province_name) ,
        `country_name` = values(country_name) ,
        `modified_time` = values(modified_time) ,
        `created_time` = values(created_time) ,
        `is_enable` = values(is_enable) ,
        `warehouse_code` = values(warehouse_code) ,
        `warehouse_name` = values(warehouse_name) ,
        `warehouse_type` = values(warehouse_type) ,
        `brand_codes` = values(brand_codes) ,
        `brand_names` = values(brand_names) ,
        `wms_app_id` = values(wms_app_id) ,
        `contact` = values(contact) ,
        `address` = values(address) ,
        `mobile` = values(mobile) ,
        `telephone` = values(telephone) ,
        `owner_code` = values(owner_code) ,
        `outer_code` = values(outer_code) ,
        `is_match_region` = values(is_match_region) ,
        `vip_platform_code` = values(vip_platform_code) ,
        `is_stock_available` = values(is_stock_available) ,
        `is_can_stock_upload` = values(is_can_stock_upload) ,
        `is_has_group_connected` = values(is_has_group_connected) ,
        `company_id` = values(company_id) ,
        `company_name` = values(company_name) ,
        `is_sort_express_by_cost` = values(is_sort_express_by_cost) ,
        `is_order_intercepted` = values(is_order_intercepted) ,
        `intercept_time_type` = values(intercept_time_type) ,
        `intercept_time` = values(intercept_time) ,
        `intercept_begin_time` = values(intercept_begin_time) ,
        `intercept_end_time` = values(intercept_end_time) ,
        `advance_delivery_type` = values(advance_delivery_type) ,
        `is_o2o` = values(is_o2o) ,
        `is_enable_aoxiang` = values(is_enable_aoxiang) ,
        `zipcode` = values(zipcode) ,
        `is_split_by_brand` = values(is_split_by_brand) 

    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        Update `oms_warehouse`
        <set>
            <if test="cityId != null">
                `city_id` = #{cityId},
            </if>
            <if test="countryId != null">
                `country_id` = #{countryId},
            </if>
            <if test="districtId != null">
                `district_id` = #{districtId},
            </if>
            <if test="provinceId != null">
                `province_id` = #{provinceId},
            </if>
            <if test="cityName != null and cityName != ''">
                `city_name` = #{cityName},
            </if>
            <if test="districtName != null and districtName != ''">
                `district_name` = #{districtName},
            </if>
            <if test="provinceName != null and provinceName != ''">
                `province_name` = #{provinceName},
            </if>
            <if test="countryName != null and countryName != ''">
                `country_name` = #{countryName},
            </if>
            <if test="modifiedTime != null">
                `modified_time` = #{modifiedTime},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime},
            </if>
            <if test="isEnable != null">
                `is_enable` = #{isEnable},
            </if>
            <if test="warehouseCode != null and warehouseCode != ''">
                `warehouse_code` = #{warehouseCode},
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                `warehouse_name` = #{warehouseName},
            </if>
            <if test="warehouseType != null">
                `warehouse_type` = #{warehouseType},
            </if>
            <if test="brandCodes != null and brandCodes != ''">
                `brand_codes` = #{brandCodes},
            </if>
            <if test="brandNames != null and brandNames != ''">
                `brand_names` = #{brandNames},
            </if>
            <if test="wmsAppId != null">
                `wms_app_id` = #{wmsAppId},
            </if>
            <if test="contact != null and contact != ''">
                `contact` = #{contact},
            </if>
            <if test="address != null and address != ''">
                `address` = #{address},
            </if>
            <if test="mobile != null and mobile != ''">
                `mobile` = #{mobile},
            </if>
            <if test="telephone != null and telephone != ''">
                `telephone` = #{telephone},
            </if>
            <if test="ownerCode != null and ownerCode != ''">
                `owner_code` = #{ownerCode},
            </if>
            <if test="outerCode != null and outerCode != ''">
                `outer_code` = #{outerCode},
            </if>
            <if test="isMatchRegion != null">
                `is_match_region` = #{isMatchRegion},
            </if>
            <if test="vipPlatformCode != null and vipPlatformCode != ''">
                `vip_platform_code` = #{vipPlatformCode},
            </if>
            <if test="isStockAvailable != null">
                `is_stock_available` = #{isStockAvailable},
            </if>
            <if test="isCanStockUpload != null">
                `is_can_stock_upload` = #{isCanStockUpload},
            </if>
            <if test="isHasGroupConnected != null">
                `is_has_group_connected` = #{isHasGroupConnected},
            </if>
            <if test="companyId != null">
                `company_id` = #{companyId},
            </if>
            <if test="companyName != null and companyName != ''">
                `company_name` = #{companyName},
            </if>
            <if test="isSortExpressByCost != null">
                `is_sort_express_by_cost` = #{isSortExpressByCost},
            </if>
            <if test="isOrderIntercepted != null">
                `is_order_intercepted` = #{isOrderIntercepted},
            </if>
            <if test="interceptTimeType != null">
                `intercept_time_type` = #{interceptTimeType},
            </if>
            <if test="interceptTime != null">
                `intercept_time` = #{interceptTime},
            </if>
            <if test="interceptBeginTime != null">
                `intercept_begin_time` = #{interceptBeginTime},
            </if>
            <if test="interceptEndTime != null">
                `intercept_end_time` = #{interceptEndTime},
            </if>
            <if test="advanceDeliveryType != null">
                `advance_delivery_type` = #{advanceDeliveryType},
            </if>
            <if test="isO2o != null">
                `is_o2o` = #{isO2o},
            </if>
            <if test="isEnableAoxiang != null">
                `is_enable_aoxiang` = #{isEnableAoxiang},
            </if>
            <if test="zipcode != null and zipcode != ''">
                `zipcode` = #{zipcode},
            </if>
            <if test="isSplitByBrand != null">
                `is_split_by_brand` = #{isSplitByBrand},
            </if>
        </set>
        Where warehouse_id = #{warehouseId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByPrimaryKey">
        Delete From `oms_warehouse` Where warehouse_id = #{warehouseId}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        Delete From `oms_warehouse`
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                And In ( ${params.ids} )
            </if>
        </where>
    </delete>

    <!-- auto generate end -->

</mapper>
