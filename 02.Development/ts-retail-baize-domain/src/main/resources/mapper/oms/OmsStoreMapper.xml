<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.oms.OmsStoreRepository">
    <!-- auto generate -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.oms.OmsStore" >
        <result  column="store_id" property="storeId" jdbcType="INTEGER"/>
        <result  column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
        <result  column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result  column="is_enable" property="isEnable" jdbcType="INTEGER"/>
        <result  column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result  column="store_code" property="storeCode" jdbcType="VARCHAR"/>
        <result  column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result  column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result  column="telephone" property="telephone" jdbcType="VARCHAR"/>
        <result  column="address" property="address" jdbcType="VARCHAR"/>
        <result  column="access_token" property="accessToken" jdbcType="VARCHAR"/>
        <result  column="refresh_token" property="refreshToken" jdbcType="VARCHAR"/>
        <result  column="access_token_expiration_time" property="accessTokenExpirationTime" jdbcType="TIMESTAMP"/>
        <result  column="refresh_token_expiration_time" property="refreshTokenExpirationTime" jdbcType="TIMESTAMP"/>
        <result  column="remark" property="remark" jdbcType="VARCHAR"/>
        <result  column="setting_json" property="settingJson" jdbcType="VARCHAR"/>
        <result  column="mall_app_id" property="mallAppId" jdbcType="INTEGER"/>
        <result  column="mall_type" property="mallType" jdbcType="INTEGER"/>
        <result  column="company_id" property="companyId" jdbcType="INTEGER"/>
        <result  column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result  column="invoice_template_id" property="invoiceTemplateId" jdbcType="INTEGER"/>
        <result  column="sms_sign_id" property="smsSignId" jdbcType="INTEGER"/>
        <result  column="is_access_invalid" property="isAccessInvalid" jdbcType="INTEGER"/>
        <result  column="relate_store_id" property="relateStoreId" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="column_list">
    store_id,
    modified_time,
    created_time,
    is_enable,
    store_name,
    store_code,
    nickname,
    mobile,
    telephone,
    address,
    access_token,
    refresh_token,
    access_token_expiration_time,
    refresh_token_expiration_time,
    remark,
    setting_json,
    mall_app_id,
    mall_type,
    company_id,
    company_name,
    invoice_template_id,
    sms_sign_id,
    is_access_invalid,
    relate_store_id
    </sql>

    <sql id="condition">
        <if test="null!=params.queryCondition and ''!=params.queryCondition">
            And ${params.queryCondition}
        </if>
        <if test="params.storeId != null">
            And `store_id` = #{params.storeId}
        </if>
        <if test="params.modifiedTime != null">
            And `modified_time` = #{params.modifiedTime}
        </if>
        <if test="params.createdTime != null">
            And `created_time` = #{params.createdTime}
        </if>
        <if test="params.isEnable != null">
            And `is_enable` = #{params.isEnable}
        </if>
        <if test="params.storeName != null and ''!=params.storeName ">
            And `store_name` = #{params.storeName}
        </if>
        <if test="params.storeCode != null and ''!=params.storeCode ">
            And `store_code` = #{params.storeCode}
        </if>
        <if test="params.nickname != null and ''!=params.nickname ">
            And `nickname` = #{params.nickname}
        </if>
        <if test="params.mobile != null and ''!=params.mobile ">
            And `mobile` = #{params.mobile}
        </if>
        <if test="params.telephone != null and ''!=params.telephone ">
            And `telephone` = #{params.telephone}
        </if>
        <if test="params.address != null and ''!=params.address ">
            And `address` = #{params.address}
        </if>
        <if test="params.accessToken != null and ''!=params.accessToken ">
            And `access_token` = #{params.accessToken}
        </if>
        <if test="params.refreshToken != null and ''!=params.refreshToken ">
            And `refresh_token` = #{params.refreshToken}
        </if>
        <if test="params.accessTokenExpirationTime != null">
            And `access_token_expiration_time` = #{params.accessTokenExpirationTime}
        </if>
        <if test="params.refreshTokenExpirationTime != null">
            And `refresh_token_expiration_time` = #{params.refreshTokenExpirationTime}
        </if>
        <if test="params.remark != null and ''!=params.remark ">
            And `remark` = #{params.remark}
        </if>
        <if test="params.settingJson != null and ''!=params.settingJson ">
            And `setting_json` = #{params.settingJson}
        </if>
        <if test="params.mallAppId != null">
            And `mall_app_id` = #{params.mallAppId}
        </if>
        <if test="params.mallType != null">
            And `mall_type` = #{params.mallType}
        </if>
        <if test="params.companyId != null">
            And `company_id` = #{params.companyId}
        </if>
        <if test="params.companyName != null and ''!=params.companyName ">
            And `company_name` = #{params.companyName}
        </if>
        <if test="params.invoiceTemplateId != null">
            And `invoice_template_id` = #{params.invoiceTemplateId}
        </if>
        <if test="params.smsSignId != null">
            And `sms_sign_id` = #{params.smsSignId}
        </if>
        <if test="params.isAccessInvalid != null">
            And `is_access_invalid` = #{params.isAccessInvalid}
        </if>
        <if test="params.relateStoreId != null">
            And `relate_store_id` = #{params.relateStoreId}
        </if>
    </sql>
    <!--查询单个-->
    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        Select
        <include refid="column_list" />
        From `oms_store`
        Where store_id = #{storeId}

    </select>

    <!--查询指定行数据-->
    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_store`
        <where>
            <include refid="condition" />
        </where>
        Limit 1
    </select>

    <!--统计总行数-->
    <select id="selectCount" resultType="java.lang.Integer" parameterType="map">
        Select Count(1)
        From `oms_store`
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_store`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
        Limit ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `oms_store`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="storeId" useGeneratedKeys="true">
        Insert Into `oms_store` (
            `store_id` ,
            `modified_time` ,
            `created_time` ,
            `is_enable` ,
            `store_name` ,
            `store_code` ,
            `nickname` ,
            `mobile` ,
            `telephone` ,
            `address` ,
            `access_token` ,
            `refresh_token` ,
            `access_token_expiration_time` ,
            `refresh_token_expiration_time` ,
            `remark` ,
            `setting_json` ,
            `mall_app_id` ,
            `mall_type` ,
            `company_id` ,
            `company_name` ,
            `invoice_template_id` ,
            `sms_sign_id` ,
            `is_access_invalid` ,
            `relate_store_id` 
        )
        Values (
            #{storeId} ,
            #{modifiedTime} ,
            #{createdTime} ,
            #{isEnable} ,
            #{storeName} ,
            #{storeCode} ,
            #{nickname} ,
            #{mobile} ,
            #{telephone} ,
            #{address} ,
            #{accessToken} ,
            #{refreshToken} ,
            #{accessTokenExpirationTime} ,
            #{refreshTokenExpirationTime} ,
            #{remark} ,
            #{settingJson} ,
            #{mallAppId} ,
            #{mallType} ,
            #{companyId} ,
            #{companyName} ,
            #{invoiceTemplateId} ,
            #{smsSignId} ,
            #{isAccessInvalid} ,
            #{relateStoreId} 
        )
    </insert>

    <insert id="insertBatch" keyProperty="storeId" useGeneratedKeys="true">
        Insert Into `oms_store` (
           `store_id`,
           `modified_time`,
           `created_time`,
           `is_enable`,
           `store_name`,
           `store_code`,
           `nickname`,
           `mobile`,
           `telephone`,
           `address`,
           `access_token`,
           `refresh_token`,
           `access_token_expiration_time`,
           `refresh_token_expiration_time`,
           `remark`,
           `setting_json`,
           `mall_app_id`,
           `mall_type`,
           `company_id`,
           `company_name`,
           `invoice_template_id`,
           `sms_sign_id`,
           `is_access_invalid`,
           `relate_store_id`
        ) Values
        <foreach collection="list" item="entity" separator=",">
        (
            #{entity.storeId},
            #{entity.modifiedTime},
            #{entity.createdTime},
            #{entity.isEnable},
            #{entity.storeName},
            #{entity.storeCode},
            #{entity.nickname},
            #{entity.mobile},
            #{entity.telephone},
            #{entity.address},
            #{entity.accessToken},
            #{entity.refreshToken},
            #{entity.accessTokenExpirationTime},
            #{entity.refreshTokenExpirationTime},
            #{entity.remark},
            #{entity.settingJson},
            #{entity.mallAppId},
            #{entity.mallType},
            #{entity.companyId},
            #{entity.companyName},
            #{entity.invoiceTemplateId},
            #{entity.smsSignId},
            #{entity.isAccessInvalid},
            #{entity.relateStoreId}
        )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="storeId" useGeneratedKeys="true">
        Insert Into `oms_store` (
        `store_id` ,
        `modified_time` ,
        `created_time` ,
        `is_enable` ,
        `store_name` ,
        `store_code` ,
        `nickname` ,
        `mobile` ,
        `telephone` ,
        `address` ,
        `access_token` ,
        `refresh_token` ,
        `access_token_expiration_time` ,
        `refresh_token_expiration_time` ,
        `remark` ,
        `setting_json` ,
        `mall_app_id` ,
        `mall_type` ,
        `company_id` ,
        `company_name` ,
        `invoice_template_id` ,
        `sms_sign_id` ,
        `is_access_invalid` ,
        `relate_store_id` 
       )Values
        <foreach collection="list" item="entity" separator=",">
         (
         #{entity.storeId},
         #{entity.modifiedTime},
         #{entity.createdTime},
         #{entity.isEnable},
         #{entity.storeName},
         #{entity.storeCode},
         #{entity.nickname},
         #{entity.mobile},
         #{entity.telephone},
         #{entity.address},
         #{entity.accessToken},
         #{entity.refreshToken},
         #{entity.accessTokenExpirationTime},
         #{entity.refreshTokenExpirationTime},
         #{entity.remark},
         #{entity.settingJson},
         #{entity.mallAppId},
         #{entity.mallType},
         #{entity.companyId},
         #{entity.companyName},
         #{entity.invoiceTemplateId},
         #{entity.smsSignId},
         #{entity.isAccessInvalid},
         #{entity.relateStoreId}
 )
        </foreach>
        On Duplicate Key Update
        `modified_time` = values(modified_time) ,
        `created_time` = values(created_time) ,
        `is_enable` = values(is_enable) ,
        `store_name` = values(store_name) ,
        `store_code` = values(store_code) ,
        `nickname` = values(nickname) ,
        `mobile` = values(mobile) ,
        `telephone` = values(telephone) ,
        `address` = values(address) ,
        `access_token` = values(access_token) ,
        `refresh_token` = values(refresh_token) ,
        `access_token_expiration_time` = values(access_token_expiration_time) ,
        `refresh_token_expiration_time` = values(refresh_token_expiration_time) ,
        `remark` = values(remark) ,
        `setting_json` = values(setting_json) ,
        `mall_app_id` = values(mall_app_id) ,
        `mall_type` = values(mall_type) ,
        `company_id` = values(company_id) ,
        `company_name` = values(company_name) ,
        `invoice_template_id` = values(invoice_template_id) ,
        `sms_sign_id` = values(sms_sign_id) ,
        `is_access_invalid` = values(is_access_invalid) ,
        `relate_store_id` = values(relate_store_id) 

    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        Update `oms_store`
        <set>
            <if test="modifiedTime != null">
                `modified_time` = #{modifiedTime},
            </if>
            <if test="createdTime != null">
                `created_time` = #{createdTime},
            </if>
            <if test="isEnable != null">
                `is_enable` = #{isEnable},
            </if>
            <if test="storeName != null and storeName != ''">
                `store_name` = #{storeName},
            </if>
            <if test="storeCode != null and storeCode != ''">
                `store_code` = #{storeCode},
            </if>
            <if test="nickname != null and nickname != ''">
                `nickname` = #{nickname},
            </if>
            <if test="mobile != null and mobile != ''">
                `mobile` = #{mobile},
            </if>
            <if test="telephone != null and telephone != ''">
                `telephone` = #{telephone},
            </if>
            <if test="address != null and address != ''">
                `address` = #{address},
            </if>
            <if test="accessToken != null and accessToken != ''">
                `access_token` = #{accessToken},
            </if>
            <if test="refreshToken != null and refreshToken != ''">
                `refresh_token` = #{refreshToken},
            </if>
            <if test="accessTokenExpirationTime != null">
                `access_token_expiration_time` = #{accessTokenExpirationTime},
            </if>
            <if test="refreshTokenExpirationTime != null">
                `refresh_token_expiration_time` = #{refreshTokenExpirationTime},
            </if>
            <if test="remark != null and remark != ''">
                `remark` = #{remark},
            </if>
            <if test="settingJson != null and settingJson != ''">
                `setting_json` = #{settingJson},
            </if>
            <if test="mallAppId != null">
                `mall_app_id` = #{mallAppId},
            </if>
            <if test="mallType != null">
                `mall_type` = #{mallType},
            </if>
            <if test="companyId != null">
                `company_id` = #{companyId},
            </if>
            <if test="companyName != null and companyName != ''">
                `company_name` = #{companyName},
            </if>
            <if test="invoiceTemplateId != null">
                `invoice_template_id` = #{invoiceTemplateId},
            </if>
            <if test="smsSignId != null">
                `sms_sign_id` = #{smsSignId},
            </if>
            <if test="isAccessInvalid != null">
                `is_access_invalid` = #{isAccessInvalid},
            </if>
            <if test="relateStoreId != null">
                `relate_store_id` = #{relateStoreId},
            </if>
        </set>
        Where store_id = #{storeId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByPrimaryKey">
        Delete From `oms_store` Where store_id = #{storeId}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        Delete From `oms_store`
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                And In ( ${params.ids} )
            </if>
        </where>
    </delete>

    <!-- auto generate end -->

</mapper>
