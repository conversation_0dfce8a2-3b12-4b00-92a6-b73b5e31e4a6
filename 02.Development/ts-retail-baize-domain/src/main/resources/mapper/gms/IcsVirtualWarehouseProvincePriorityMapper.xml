<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.IcsVirtualWarehouseProvincePriorityRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority">
        <id column="id" property="id" jdbcType="CHAR"/>

        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="vstore_name" property="vstoreName" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="province_name" property="provinceName" jdbcType="VARCHAR"/>
        <result column="province_no" property="provinceNo" jdbcType="VARCHAR"/>
        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `status`,
        `vstore_name`,
        `create_user`,
        `remark`,
        `update_time`,
        `update_user`,
        `create_time`,
        `province_name`,
        `province_no`,
        `vstore_code`,
        `id`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.status">
                AND `status`=#{params.status}
            </if>

            <if test="null != params.vstoreName  and '' != params.vstoreName">
                AND `vstore_name`=#{params.vstoreName}
            </if>
            <if test="null != params.vstoreNameLike  and '' != params.vstoreNameLike">
                AND `vstore_name` like CONCAT('%',
                    #{params.vstoreNameLike}, '%')
            </if>

            <if test="null!=params.vstoreCodeOrName and '' != params.vstoreCodeOrName ">
                AND ( `vstore_code` like CONCAT('%',#{params.vstoreCodeOrName},'%') or `vstore_name` like CONCAT('%',#{params.vstoreCodeOrName},'%') )
            </if>

            <if test="params.provinceNoOrName!=null and ''!=params.provinceNoOrName">
                AND ( `province_no` like CONCAT('%',#{params.provinceNoOrName},'%') or `province_name` like CONCAT('%',#{params.provinceNoOrName},'%') )
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=
                    #{params.createUser}
            </if>

            <if test="null != params.remark  and '' != params.remark">
                AND `remark`=#{params.remark}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.provinceName  and '' != params.provinceName">
                AND `province_name`=#{params.provinceName}
            </if>
            <if test="null != params.provinceNameLike  and '' != params.provinceNameLike">
                AND `province_name` like CONCAT('%',
                    #{params.provinceNameLike}, '%')
            </if>

            <if test="null != params.provinceNo  and '' != params.provinceNo">
                AND `province_no`=
                    #{params.provinceNo}
            </if>

            <if test="null != params.vstoreCode  and '' != params.vstoreCode">
                AND `vstore_code`=#{params.vstoreCode}
            </if>

            <if test="null != params.id  and '' != params.id">
                AND `id`=#{params.id}
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_vir_province_priority
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_vir_province_priority
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_vir_province_priority
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM ics_vir_province_priority
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_vir_province_priority
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_vir_province_priority
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM ics_vir_province_priority
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM ics_vir_province_priority
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM ics_vir_province_priority
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority">
        INSERT INTO ics_vir_province_priority
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="status != null">
                `status`,
            </if>

            <if test="vstoreName != null">
                `vstore_name`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="provinceName != null">
                `province_name`,
            </if>

            <if test="provinceNo != null">
                `province_no`,
            </if>

            <if test="vstoreCode != null">
                `vstore_code`,
            </if>

            <if test="id != null">
                `id`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="status != null">
                #{status},
            </if>

            <if test="vstoreName != null">
                #{vstoreName},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="remark != null">
                #{remark},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="provinceName != null">
                #{provinceName},
            </if>

            <if test="provinceNo != null">
                #{provinceNo},
            </if>

            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>

            <if test="id != null">
                #{id},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ics_vir_province_priority (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            (#{item.status}, #{item.vstoreName}, #{item.createUser}, #{item.remark}, #{item.updateTime},
             #{item.updateUser}, #{item.createTime}, #{item.provinceName}, #{item.provinceNo}, #{item.vstoreCode},
             #{item.id})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority">
        UPDATE ics_vir_province_priority
        <set>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="vstoreName != null">
                `vstore_name` = #{vstoreName},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="provinceName != null">
                `province_name` = #{provinceName},
            </if>
            <if test="provinceNo != null">
                `province_no` = #{provinceNo},
            </if>
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if>
            update_time = now()
        </set>


        WHERE id = #{id}
    </update>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_vir_province_priority
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- auto generate end-->
</mapper>