<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetSystemLogsRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.InternetSystemLogs">
                
        <id column="id" property="id" jdbcType="BIGINT" />
        
        
        <result column="keyword2" property="keyword2" jdbcType="VARCHAR" />
        
        <result column="keyword2info" property="keyword2info" jdbcType="VARCHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="syscode" property="syscode" jdbcType="VARCHAR" />
        
        <result column="sysname" property="sysname" jdbcType="VARCHAR" />
        
        <result column="opscode" property="opscode" jdbcType="VARCHAR" />
        
        <result column="keyword1" property="keyword1" jdbcType="VARCHAR" />
        
        <result column="keyword1info" property="keyword1info" jdbcType="VARCHAR" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
    </resultMap>

    <sql id="column_list">
        `keyword2`,`keyword2info`,`create_user`,`id`,`syscode`,`sysname`,`opscode`,`keyword1`,`keyword1info`,`remark`,`create_time`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.keyword2  and ''!=params.keyword2 ">
                
                AND `keyword2`=#{params.keyword2}
                
            </if>
            
            <if test="null!=params.keyword2info  and ''!=params.keyword2info ">
                
                AND `keyword2info`=#{params.keyword2info}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.syscode  and ''!=params.syscode ">
                
                AND `syscode`=#{params.syscode}
                
            </if>
            
            <if test="null!=params.sysname  and ''!=params.sysname ">
                
                AND `sysname` like CONCAT('%',#{params.sysname},'%') 
                
            </if>
            
            <if test="null!=params.opscode  and ''!=params.opscode ">
                
                AND `opscode`=#{params.opscode}
                
            </if>
            
            <if test="null!=params.keyword1  and ''!=params.keyword1 ">
                
                AND `keyword1`=#{params.keyword1}
                
            </if>
            
            <if test="null!=params.keyword1info  and ''!=params.keyword1info ">
                
                AND `keyword1info`=#{params.keyword1info}
                
            </if>
            
            <if test="null!=params.remark  and ''!=params.remark ">
                
                AND `remark`=#{params.remark}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_system_logs
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_system_logs
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_system_logs
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_system_logs
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_system_logs
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_system_logs
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_system_logs
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_system_logs
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_system_logs
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.InternetSystemLogs"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO internet_system_logs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="keyword2 != null">
                `keyword2`,
            </if>
            
            <if test="keyword2info != null">
                `keyword2info`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="syscode != null">
                `syscode`,
            </if>
            
            <if test="sysname != null">
                `sysname`,
            </if>
            
            <if test="opscode != null">
                `opscode`,
            </if>
            
            <if test="keyword1 != null">
                `keyword1`,
            </if>
            
            <if test="keyword1info != null">
                `keyword1info`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="keyword2 != null">
                #{keyword2},
            </if>
            
            <if test="keyword2info != null">
                #{keyword2info},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="syscode != null">
                #{syscode},
            </if>
            
            <if test="sysname != null">
                #{sysname},
            </if>
            
            <if test="opscode != null">
                #{opscode},
            </if>
            
            <if test="keyword1 != null">
                #{keyword1},
            </if>
            
            <if test="keyword1info != null">
                #{keyword1info},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.InternetSystemLogs">
        UPDATE internet_system_logs
        <set>
            
            <if test="keyword2 != null">
                `keyword2` = #{keyword2},
            </if> 
            <if test="keyword2info != null">
                `keyword2info` = #{keyword2info},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="syscode != null">
                `syscode` = #{syscode},
            </if> 
            <if test="sysname != null">
                `sysname` = #{sysname},
            </if> 
            <if test="opscode != null">
                `opscode` = #{opscode},
            </if> 
            <if test="keyword1 != null">
                `keyword1` = #{keyword1},
            </if> 
            <if test="keyword1info != null">
                `keyword1info` = #{keyword1info},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE id = #{id}
        
            
    </update>
        <!-- auto generate end-->


</mapper>