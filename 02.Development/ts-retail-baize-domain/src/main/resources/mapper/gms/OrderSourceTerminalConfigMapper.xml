<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.OrderSourceTerminalConfigRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="third_platform_name" property="thirdPlatformName" jdbcType="VARCHAR" />
        
        <result column="third_channel_no" property="thirdChannelNo" jdbcType="VARCHAR" />
        
        <result column="terminal" property="terminal" jdbcType="VARCHAR" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="third_platform" property="thirdPlatform" jdbcType="VARCHAR" />
        
        <result column="second_platform_name" property="secondPlatformName" jdbcType="VARCHAR" />
        
        <result column="third_channel_name" property="thirdChannelName" jdbcType="VARCHAR" />

        <result column="province_no" property="provinceNo" jdbcType="VARCHAR" />

        <result column="city_no" property="cityNo" jdbcType="VARCHAR" />

        <result column="zone_no" property="zoneNo" jdbcType="CHAR" />

        <result column="manager_zone_no" property="managerZoneNo" jdbcType="VARCHAR" />

        <result column="company_no" property="companyNo" jdbcType="CHAR" />

        <result column="company_name" property="companyName" jdbcType="VARCHAR" />

        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="second_platform" property="secondPlatform" jdbcType="VARCHAR" />
        
        <result column="platform_name" property="platformName" jdbcType="VARCHAR" />
        
        <result column="platform" property="platform" jdbcType="VARCHAR" />
        
        <result column="terminal_name" property="terminalName" jdbcType="VARCHAR" />

        <result column="merchant_code" property="merchantCode" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="column_list">
        `id`,`terminal`,`terminal_name`,`merchant_code`,`platform`,`platform_name`,`second_platform`,`second_platform_name`,`third_platform`,`third_platform_name`,
        `third_channel_no`,`third_channel_name`,`province_no`,`city_no`,`zone_no`,`manager_zone_no`,`company_no`,`company_name`,`remark`,
         `update_time`,`update_user`,`create_user`,`create_time`
    </sql>

    <sql id="condition">
        1=1
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="params.queryParam != null and params.queryParam != ''">
               AND (third_platform like CONCAT('%',#{params.queryParam},'%') or third_platform_name like CONCAT('%',#{params.queryParam},'%'))
            </if>
            <if test="null!=params.updateTime ">
                AND `update_time`=#{params.updateTime}
            </if>
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                AND `update_user`=#{params.updateUser}
            </if>
            <if test="null!=params.thirdPlatformName  and ''!=params.thirdPlatformName ">
                AND (`third_platform_name` like CONCAT('%',#{params.thirdPlatformName},'%')
                OR `third_platform` = #{params.thirdPlatformName})
            </if>
            <if test="null!=params.thirdChannelNo  and ''!=params.thirdChannelNo ">
                AND `third_channel_no`=#{params.thirdChannelNo}
            </if>
            <if test="null!=params.merchantCode  and ''!=params.merchantCode ">
                AND `merchant_code`=#{params.merchantCode}
            </if>
            <if test="null!=params.terminal  and ''!=params.terminal ">
                AND `terminal`=#{params.terminal}
            </if>
            <if test="null!=params.terminals  and params.terminals.size() > 0 ">
                AND `terminal` IN
                <foreach collection="params.terminals" item="terminal" open="(" close=")" separator=",">
                    #{terminal}
                </foreach>
            </if>
            <if test="null!=params.remark  and ''!=params.remark ">
                AND `remark`=#{params.remark}
            </if>
            <if test="null!=params.thirdPlatform  and ''!=params.thirdPlatform ">
                AND `third_platform`=#{params.thirdPlatform}
            </if>
            <if test="null!=params.secondPlatformName  and ''!=params.secondPlatformName ">
                AND `second_platform_name` like CONCAT('%',#{params.secondPlatformName},'%')
            </if>
            <if test="null!=params.thirdChannelName  and ''!=params.thirdChannelName ">
                AND `third_channel_name` like CONCAT('%',#{params.thirdChannelName},'%')
            </if>
            <if test="null!=params.createUser  and ''!=params.createUser ">
                AND `create_user`=#{params.createUser}
            </if>
            <if test="null!=params.createTime ">
                AND `create_time`=#{params.createTime}
            </if>
            <if test="null!=params.secondPlatform  and ''!=params.secondPlatform ">
                AND `second_platform`=#{params.secondPlatform}
            </if>
            <if test="null!=params.platformName  and ''!=params.platformName ">
                AND `platform_name` like CONCAT('%',#{params.platformName},'%')
            </if>
            <if test="null!=params.platform  and ''!=params.platform ">
                AND `platform`=#{params.platform}
            </if>
            <if test="null!=params.terminalName  and ''!=params.terminalName ">
                AND `terminal_name` like CONCAT('%',#{params.terminalName},'%')
            </if>
            <if test="null!=params.id  and ''!=params.id ">
                AND `id`=#{params.id}
            </if>
            <if test="null != params.thirdChannelNoLike and ''!=params.thirdChannelNoLike ">
                AND `third_channel_no` like CONCAT('%',#{params.thirdChannelNoLike},'%')
            </if>
            <if test="null!=params.zoneNo  and ''!=params.zoneNo ">
                AND `zone_no` = #{params.zoneNo}
            </if>
            <if test="null!=params.ids  and params.ids.size() > 0 ">
                AND `id` IN
                <foreach collection="params.ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="params.groupCondition != null and ''!=params.groupCondition">
                group by ${params.groupCondition}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM order_source_terminal_config
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM order_source_terminal_config
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM order_source_terminal_config
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM
        (SELECT <include refid="column_list" /> from order_source_terminal_config
            <where>
                <include refid="condition" />
            </where>
        ) a
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM order_source_terminal_config
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM order_source_terminal_config
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM order_source_terminal_config
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM order_source_terminal_config
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM order_source_terminal_config
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig"  >
        INSERT INTO order_source_terminal_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>
            <if test="merchantCode != null">
                `merchant_code`,
            </if>
            <if test="terminal != null">
                `terminal`,
            </if>
            <if test="terminalName != null">
                `terminal_name`,
            </if>
            <if test="platform != null">
                `platform`,
            </if>
            <if test="platformName != null">
                `platform_name`,
            </if>
            <if test="secondPlatform != null">
                `second_platform`,
            </if>
            <if test="secondPlatformName != null">
                `second_platform_name`,
            </if>
            <if test="thirdPlatform != null">
                `third_platform`,
            </if>
            <if test="thirdPlatformName != null">
                `third_platform_name`,
            </if>
            <if test="thirdChannelNo != null">
                `third_channel_no`,
            </if>
            <if test="thirdChannelName != null">
                `third_channel_name`,
            </if>
            <if test="provinceNo != null">
                `province_no`,
            </if>
            <if test="cityNo != null">
                `city_no`,
            </if>
            <if test="zoneNo != null">
                `zone_no`,
            </if>
            <if test="managerZoneNo != null">
                `manager_zone_no`,
            </if>
            <if test="companyNo != null">
                `company_no`,
            </if>
            <if test="companyName != null">
                `company_name`,
            </if>
            <if test="remark != null">
                `remark`,
            </if>
            <if test="createUser != null">
                `create_user`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="updateUser != null">
                `update_user`,
            </if>
            <if test="updateTime != null">
                `update_time`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="merchantCode != null">
                #{merchantCode},
            </if>
            <if test="terminal != null">
                #{terminal},
            </if>
            <if test="terminalName != null">
                #{terminalName},
            </if>
            <if test="platform != null">
                #{platform},
            </if>
            <if test="platformName != null">
                #{platformName},
            </if>
            <if test="secondPlatform != null">
                #{secondPlatform},
            </if>
            <if test="secondPlatformName != null">
                #{secondPlatformName},
            </if>
            <if test="thirdPlatform != null">
                #{thirdPlatform},
            </if>
            <if test="thirdPlatformName != null">
                #{thirdPlatformName},
            </if>
            <if test="thirdChannelNo != null">
                #{thirdChannelNo},
            </if>
            <if test="thirdChannelName != null">
                #{thirdChannelName},
            </if>
            <if test="provinceNo != null">
                #{provinceNo},
            </if>
            <if test="cityNo != null">
                #{cityNo},
            </if>
            <if test="zoneNo != null">
                #{zoneNo},
            </if>
            <if test="managerZoneNo != null">
                #{managerZoneNo},
            </if>
            <if test="companyNo != null">
                #{companyNo},
            </if>
            <if test="companyName != null">
                #{companyName},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createUser != null">
                #{createUser},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig">
        UPDATE order_source_terminal_config
        <set>
            <if test="merchantCode != null">
                `merchant_code` = #{merchantCode},
            </if>
            <if test="platform != null">
                `platform` = #{platform},
            </if>
            <if test="platformName != null">
                `platform_name` = #{platformName},
            </if>
            <if test="secondPlatform != null">
                `second_platform` = #{secondPlatform},
            </if>
            <if test="secondPlatformName != null">
                `second_platform_name` = #{secondPlatformName},
            </if>
            <if test="thirdChannelNo != null">
                `third_channel_no` = #{thirdChannelNo},
            </if>
            <if test="provinceNo != null">
                `province_no` = #{provinceNo},
            </if>
            <if test="cityNo != null">
                `city_no` = #{cityNo},
            </if>
            <if test="zoneNo != null">
                `zone_no` = #{zoneNo},
            </if>
            <if test="managerZoneNo != null">
                `manager_zone_no` = #{managerZoneNo},
            </if>
            <if test="companyNo != null">
                `company_no` = #{companyNo},
            </if>
            <if test="companyName != null">
                `company_name` = #{companyName},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectShopPageByParams" resultMap="baseResultMap">
        select distinct third_platform, third_platform_name from order_source_terminal_config
        <where>
            <if test="params.channelType != null and '' != params.channelType">
                AND terminal = #{params.channelType}
            </if>
            <if test="params.queryParam!=null and params.queryParam!='' ">
                AND (third_platform like CONCAT('%',#{params.queryParam},'%')
                or third_platform_name like CONCAT('%',#{params.queryParam},'%'))
            </if>
        </where>
        order by third_platform
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectShopCountByParams" resultType="java.lang.Integer">
        select count(distinct third_platform) from order_source_terminal_config
        <where>
            <if test="params.channelType != null and '' != params.channelType">
                AND terminal = #{params.channelType}
            </if>
            <if test="params.queryParam!=null and params.queryParam!='' ">
                AND (third_platform like CONCAT('%',#{params.queryParam},'%')
                or third_platform_name like CONCAT('%',#{params.queryParam},'%'))
            </if>
        </where>
    </select>
    <!-- auto generate end-->


</mapper>