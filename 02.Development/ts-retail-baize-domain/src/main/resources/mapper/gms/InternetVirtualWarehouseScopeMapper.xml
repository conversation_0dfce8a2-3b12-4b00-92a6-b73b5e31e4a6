<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetVirtualWarehouseScopeRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="InternetVirtualWarehouseScope">
        <id column="id" property="id" jdbcType="VARCHAR"/>

        <result column="inventory_type" property="inventoryType" jdbcType="TINYINT"/>

        <result column="vstore_name" property="vstoreName" jdbcType="VARCHAR"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="status" property="status" jdbcType="TINYINT"/>

        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>

        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR"/>

        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR"/>

        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>

        <result column="store_no" property="storeNo" jdbcType="CHAR"/>

        <result column="more_store_flag" property="moreStoreFlag" jdbcType="TINYINT"/>

        <result column="store_type" property="storeType" jdbcType="TINYINT"/>

        <result column="vstore_type" property="vstoreType" jdbcType="INTEGER"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR"/>

        <result column="original_vstore_code" property="originalVstoreCode" jdbcType="VARCHAR"/>

        <result column="original_vstore_name" property="originalVstoreName" jdbcType="VARCHAR"/>

        <!-- 查询虚仓冗余字段-->
        <result column="vstore_mold" property="vstoreMold" jdbcType="TINYINT"/>
        <result column="parent_vstore_code" property="parentVstoreCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `inventory_type`,
        `vstore_name`,
        `create_user`,
        `create_time`,
        `status`,
        `update_user`,
        `order_unit_name`,
        `order_unit_no`,
        `store_name`,
        `store_no`,
        `more_store_flag`,
        `store_type`,
        `vstore_type`,
        `update_time`,
        `vstore_code`,
        `original_vstore_code`,
        `original_vstore_name`,
        `id`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.inventoryType">
                AND `inventory_type`=#{params.inventoryType}
            </if>

            <if test="null != params.vstoreName  and '' != params.vstoreName">
                AND `vstore_name` like CONCAT('%', #{params.vstoreName}, '%')
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=  #{params.createUser}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.status">
                AND `status`=#{params.status}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.orderUnitName  and '' != params.orderUnitName">
                AND `order_unit_name` like CONCAT('%', #{params.orderUnitName}, '%')
            </if>

            <if test="null != params.orderUnitNo  and '' != params.orderUnitNo">
                AND `order_unit_no`=  #{params.orderUnitNo}
            </if>

            <if test="params.orderUnitNos!=null and params.orderUnitNos.size()>0 ">
                and `order_unit_no` IN
                <foreach collection="params.orderUnitNos" item="orderUnitNo" open="(" close=")" separator=",">
                    #{orderUnitNo}
                </foreach>
            </if>

            <if test="null != params.storeName  and '' != params.storeName">
                AND `store_name` like CONCAT('%', #{params.storeName}, '%')
            </if>

            <if test="null != params.storeNo  and '' != params.storeNo">
                AND `store_no`= #{params.storeNo}
            </if>

            <if test="params.storeNos!=null and params.storeNos.length>0 ">
                and store_no IN
                <foreach collection="params.storeNos" item="storeNo" open="(" close=")" separator=",">
                    #{storeNo}
                </foreach>
            </if>

            <if test="null != params.storeNoOrName and '' != params.storeNoOrName">
                AND (store_name like CONCAT('%', #{params.storeNoOrName}, '%') or store_no like CONCAT('%', #{params.storeNoOrName}, '%'))
            </if>

            <if test="null != params.orderUnitNoOrName and '' != params.orderUnitNoOrName">
                AND (order_unit_name like CONCAT('%', #{params.orderUnitNoOrName}, '%') or order_unit_no like CONCAT('%', #{params.orderUnitNoOrName}, '%'))
            </if>

            <if test="null != params.moreStoreFlag">
                AND `more_store_flag`=#{params.moreStoreFlag}
            </if>

            <if test="null != params.storeType">
                AND `store_type`=#{params.storeType}
            </if>

            <if test="null != params.vstoreType">
                AND `vstore_type`=#{params.vstoreType}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.vstoreCode  and '' != params.vstoreCode">
                AND `vstore_code`=#{params.vstoreCode}
            </if>

            <if test="null != params.vstoreCodes  and params.vstoreCodes.size() > 0">
                AND `vstore_code` IN
                <foreach collection="params.vstoreCodes" item="vstoreCode" open="(" close=")" separator=",">
                    #{vstoreCode}
                </foreach>
            </if>

            <if test="null != params.id  and '' != params.id">
                AND `id`=#{params.id}
            </if>
            <if test="null != params.vstoreMold  and '' != params.vstoreMold">
                AND `vstore_mold`=#{params.vstoreMold}
            </if>
            <if test="params.ids!=null and params.ids.size() > 0 ">
                and `id` IN
                <foreach collection="params.ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM internet_virtual_warehouse_scope
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM internet_virtual_warehouse_scope
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM internet_virtual_warehouse_scope
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM internet_virtual_warehouse_scope
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM internet_virtual_warehouse_scope
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum}, ${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM internet_virtual_warehouse_scope
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM internet_virtual_warehouse_scope
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM internet_virtual_warehouse_scope
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_virtual_warehouse_scope
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="InternetVirtualWarehouseScope">
        INSERT INTO internet_virtual_warehouse_scope
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryType != null">
                `inventory_type`,
            </if>

            <if test="vstoreName != null">
                `vstore_name`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="status != null">
                `status`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="orderUnitName != null">
                `order_unit_name`,
            </if>

            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>

            <if test="storeName != null">
                `store_name`,
            </if>

            <if test="storeNo != null">
                `store_no`,
            </if>

            <if test="moreStoreFlag != null">
                `more_store_flag`,
            </if>

            <if test="storeType != null">
                `store_type`,
            </if>

            <if test="vstoreType != null">
                `vstore_type`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>

            <if test="vstoreCode != null">
                `vstore_code`,
            </if>

            <if test="id != null">
                `id`,
            </if>

            <if test="originalVstoreCode != null">
                `original_vstore_code`,
            </if>

            <if test="originalVstoreName != null">
                `original_vstore_name`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="inventoryType != null">
                #{inventoryType},
            </if>

            <if test="vstoreName != null">
                #{vstoreName},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="status != null">
                #{status},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="orderUnitName != null">
                #{orderUnitName},
            </if>

            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>

            <if test="storeName != null">
                #{storeName},
            </if>

            <if test="storeNo != null">
                #{storeNo},
            </if>

            <if test="moreStoreFlag != null">
                #{moreStoreFlag},
            </if>

            <if test="storeType != null">
                #{storeType},
            </if>

            <if test="vstoreType != null">
                #{vstoreType},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>

            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>

            <if test="id != null">
                #{id},
            </if>

            <if test="originalVstoreCode != null">
                #{originalVstoreCode},
            </if>

            <if test="originalVstoreName != null">
                #{originalVstoreName},
            </if>
        </trim>
    </insert>


    <update id="update" parameterType="InternetVirtualWarehouseScope">
        UPDATE internet_virtual_warehouse_scope
        <set>
            <if test="inventoryType != null">
                `inventory_type` = #{inventoryType},
            </if>
            <if test="vstoreName != null">
                `vstore_name` = #{vstoreName},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="orderUnitName != null">
                `order_unit_name` = #{orderUnitName},
            </if>
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if>
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if>
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if>
            <if test="moreStoreFlag != null">
                `more_store_flag` = #{moreStoreFlag},
            </if>
            <if test="storeType != null">
                `store_type` = #{storeType},
            </if>
            <if test="vstoreType != null">
                `vstore_type` = #{vstoreType},
            </if>
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if>

            <if test="originalVstoreCode != null">
                `original_vstore_code` = #{originalVstoreCode},
            </if>

            <if test="originalVstoreName != null">
                `original_vstore_name` = #{originalVstoreName},
            </if>

            update_time = now()
        </set>


        WHERE id = #{id}
    </update>
    <!-- auto generate end-->



    <!-- 查询机构数量 -->
    <select id="selectStoreCount" resultType="java.lang.Integer" >
        SELECT COUNT(1) as s FROM (
        SELECT
        o.store_no,
        o.store_name,
        o.order_unit_no
        FROM
        org_unit_brand_rel o
        LEFT JOIN store s ON o.store_no = s.store_no
        <if test="params.storeType != null and params.storeType==21 ">
            LEFT JOIN shop sp ON o.store_no = sp.store_no
        </if>
        WHERE
        1 = 1
        AND o.`status` = 1
        AND s.`status` = 1
        AND s.store_type = #{params.storeType,jdbcType=INTEGER}
        AND o.order_unit_no = #{params.orderUnitNo,jdbcType=CHAR}
        <if test="params.storeNos!=null and params.storeNos.length>0 ">
            AND o.store_no IN
            <foreach collection="params.storeNos" item="storeNo" open="(" close=")" separator=",">
                #{storeNo}
            </foreach>
        </if>
        <if test="params.storeType != null and params.storeType==21 and params.multis!=null and params.multis.size>0">
            AND sp.multi IN
            <foreach collection="params.multis" item="multi" open="(" close=")" separator=",">
                #{multi}
            </foreach>
        </if>
        GROUP BY
        o.store_no,
        o.order_unit_no
        ) a
    </select>

    <!-- 查询机构 -->
    <select id="selectStoreByPage" resultMap="baseResultMap" parameterType="map" >
        SELECT
        o.store_no,
        o.store_name,
        o.order_unit_no,
        o.order_unit_name
        FROM
        org_unit_brand_rel o
        LEFT JOIN store s ON o.store_no = s.store_no
        <if test="params.storeType != null and params.storeType==21">
            LEFT JOIN shop sp ON o.store_no = sp.store_no
        </if>
        WHERE
        1 = 1
        AND o.`status` = 1
        AND s.`status` = 1
        AND s.store_type = #{params.storeType,jdbcType=INTEGER}
        AND o.order_unit_no = #{params.orderUnitNo,jdbcType=CHAR}
        <if test="params.storeNos!=null and params.storeNos.length>0 ">
            AND o.store_no IN
            <foreach collection="params.storeNos" item="storeNo" open="(" close=")" separator=",">
                #{storeNo}
            </foreach>
        </if>
        <if test="params.storeType != null and params.storeType==21 and params.multis!=null and params.multis.size>0">
            AND sp.multi IN
            <foreach collection="params.multis" item="multi" open="(" close=")" separator=",">
                #{multi}
            </foreach>
        </if>
        GROUP BY
        o.store_no,
        o.order_unit_no
        LIMIT #{page.startRowNum} ,#{page.pageSize}
    </select>


    <select id="selectScopeListCount" resultType="java.lang.Integer">
       select count(1) from (
        select
            <choose>
                <when test="params.type == 1">
                    order_unit_no, order_unit_name
                </when>
                <otherwise>
                    store_no, store_name
                </otherwise>
            </choose>
        from internet_virtual_warehouse_scope
        <where>
            <if test="params.vstoreCode!=null and params.vstoreCode!=''">
                and  vstore_code = #{params.vstoreCode,jdbcType=VARCHAR}
            </if>
            <if test="params.containsKey('storeNoOrName') and null != params.storeNoOrName and '' != params.storeNoOrName">
                AND (store_name like CONCAT('%', #{params.storeNoOrName}, '%') or store_no like CONCAT('%', #{params.storeNoOrName}, '%'))
            </if>

            <if test="params.containsKey('orderUnitNoOrName') and null != params.orderUnitNoOrName and '' != params.orderUnitNoOrName">
                AND (order_unit_name like CONCAT('%', #{params.orderUnitNoOrName}, '%') or order_unit_no like CONCAT('%', #{params.orderUnitNoOrName}, '%'))
            </if>

            and status = 1

            <if test="params.type == 2">
                and store_type = 22
            </if>
        </where>

        <choose>
            <when test="params.type == 1">
                group by order_unit_no, order_unit_name
            </when>
            <otherwise>
                group by store_no, store_name
            </otherwise>
        </choose>

       ) as b
    </select>

    <select id="selectScopeListByPage" resultType="cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope">
        select
            <choose>
                <when test="params.type == 1">
                    order_unit_no, order_unit_name
                </when>
                <otherwise>
                    store_no, store_name
                </otherwise>
            </choose>
        from internet_virtual_warehouse_scope
        <where>
            <if test="params.vstoreCode!=null and params.vstoreCode!=''">
               and  vstore_code = #{params.vstoreCode,jdbcType=VARCHAR}
            </if>
            <if test="params.containsKey('storeNoOrName') and null != params.storeNoOrName and '' != params.storeNoOrName">
                AND (store_name like CONCAT('%', #{params.storeNoOrName}, '%') or store_no like CONCAT('%', #{params.storeNoOrName}, '%'))
            </if>

            <if test="params.containsKey('orderUnitNoOrName') and null != params.orderUnitNoOrName and '' != params.orderUnitNoOrName">
                AND (order_unit_name like CONCAT('%', #{params.orderUnitNoOrName}, '%') or order_unit_no like CONCAT('%', #{params.orderUnitNoOrName}, '%'))
            </if>

            and status = 1

            <if test="params.type == 2">
                and store_type = 22
            </if>
        </where>
        <choose>
            <when test="params.type == 1">
                group by order_unit_no, order_unit_name
            </when>
            <otherwise>
                group by store_no, store_name
            </otherwise>
        </choose>
        limit #{page.startRowNum}, #{page.pageSize}
    </select>


    <!-- 查询机构是否存在 -->
    <select id="selectExistStore" resultMap="baseResultMap" parameterType="map" >
        <if test="params.vstoreType != null and params.vstoreType == 1">
            SELECT
            <include refid="column_list" />
            FROM internet_virtual_warehouse_scope WHERE 1=1
            AND vstore_type = 1
            AND store_type = #{params.storeType,jdbcType=TINYINT}
            AND order_unit_no = #{params.orderUnitNo,jdbcType=CHAR}
            <if test="params.storeNo != null and !''.equals(params.storeNo)">
                AND (store_no = #{params.storeNo,jdbcType=VARCHAR} OR store_no IS NULL)
            </if>
            AND `status` = 1
        </if>
        <if test="params.vstoreType != null and params.vstoreType == 2">
            SELECT
            <include refid="column_list" />
            FROM internet_virtual_warehouse_scope WHERE 1=1 AND
            (
            (
            vstore_code != #{params.parentVstoreCode,jdbcType=VARCHAR}
            AND vstore_type = 1
            )
            OR (
            vstore_code = #{params.vstoreCode,jdbcType=VARCHAR}
            AND vstore_type = 2
            )
            )
            AND store_type = #{params.storeType,jdbcType=TINYINT}
            AND order_unit_no = #{params.orderUnitNo,jdbcType=CHAR}
            AND (store_no = #{params.storeNo,jdbcType=VARCHAR} OR store_no IS NULL)
            AND `status` = 1
        </if>
        <!-- 新增单机构店，不能存在多机构所有店 -->
        <if test="params.moreStoreFlag != null and params.moreStoreFlag == 1">
            AND more_store_flag = 0
            AND store_no IS NOT NULL
        </if>
        <!-- 新增多机构所有店，不能存在单机构店 -->
        <if test="params.moreStoreFlag != null and params.moreStoreFlag == 0">
            AND more_store_flag = 1
            AND store_no IS NULL
        </if>
    </select>

    <!-- 新增店库存范围时，查询虚仓下是否有所有店或者单店的范围 -->
    <select id="selectExistShop" resultMap="baseResultMap" parameterType="map" >
        SELECT
        <include refid="column_list" />
        FROM internet_virtual_warehouse_scope
        WHERE 1=1
        AND store_type = 21
        AND `status` = 1
        <if test="params.orderUnitNo!=null and params.orderUnitNo!=''">
            AND order_unit_no = #{params.orderUnitNo,jdbcType=VARCHAR}
        </if>
        <if test="params.vstoreType != null and params.vstoreType == 1">
            AND vstore_code = #{params.vstoreCode,jdbcType=VARCHAR}
        </if>
        <if test="params.vstoreType != null and params.vstoreType == 2">
            AND vstore_code = #{params.parentVstoreCode,jdbcType=VARCHAR}
        </if>
        <if test="params.vstoreType != null and params.vstoreType == 3">
            AND (vstore_code = #{params.vstoreCode,jdbcType=VARCHAR}
            OR vstore_code = #{params.parentVstoreCode,jdbcType=VARCHAR})
            <!-- 新增单机构店，不能存在多机构所有店 -->
            <if test="params.moreStoreFlag != null and params.moreStoreFlag == 0">
                AND more_store_flag = 1
                AND store_no IS NULL
            </if>
            <!-- 新增多机构所有店，不能存在单机构店 -->
            <if test="params.moreStoreFlag != null and params.moreStoreFlag == 1">
                AND more_store_flag = 0
                AND store_no IS NOT NULL
            </if>
        </if>
        <!-- 新增单机构店，不能存在多机构所有店 -->
        <if test="params.moreStoreFlag != null and params.moreStoreFlag == 0">
            AND more_store_flag = 0
            AND store_no IS NOT NULL
        </if>
        <!-- 新增多机构所有店，不能存在单机构店 -->
        <if test="params.moreStoreFlag != null and params.moreStoreFlag == 1">
            AND more_store_flag = 1
            AND store_no IS NULL
        </if>
    </select>


    <!-- 总仓删除所有店时，子仓删除 -->
    <update id="deleteAllShopScope" parameterType="map">
        UPDATE internet_virtual_warehouse_scope
        SET `status` = #{params.status,jdbcType=TINYINT},
        update_time = now(),
        update_user = #{params.updateUser,jdbcType=VARCHAR}
        WHERE store_type = 21
        AND order_unit_no = #{params.orderUnitNo,jdbcType=CHAR}
        AND vstore_code IN
        <foreach collection="params.listVstoreCode" item="vstoreCode"
                 open="(" close=")" separator=",">
            #{vstoreCode}
        </foreach>
    </update>

    <!-- 子仓修改库存类型 -->
    <update id="updateAllScope" parameterType="map">
        UPDATE internet_virtual_warehouse_scope  SET
        <if test="null != params.inventoryType">
            inventory_type = #{params.inventoryType},
        </if>
        <if test="null != params.status">
            status = #{params.status},
        </if>
        update_time = now(),
        update_user = #{params.updateUser,jdbcType=VARCHAR}
        WHERE order_unit_no = #{params.orderUnitNo,jdbcType=CHAR}
        AND store_no = #{params.storeNo,jdbcType=VARCHAR}
        AND vstore_code IN
        <foreach collection="params.listVstoreCode" item="vstoreCode"
                 open="(" close=")" separator=",">
            #{vstoreCode}
        </foreach>
    </update>

    <!-- 总仓删除指定店或者仓时，子仓删除 -->
    <update id="deleteStoreScope" parameterType="map">
        UPDATE internet_virtual_warehouse_scope
        SET `status` = #{params.status,jdbcType=TINYINT},
        update_time = now(),
        update_user = #{params.updateUser,jdbcType=VARCHAR}
        WHERE order_unit_no = #{params.orderUnitNo,jdbcType=CHAR}
        AND store_no = #{params.storeNo,jdbcType=VARCHAR}
        AND vstore_code IN
        <foreach collection="params.listVstoreCode" item="vstoreCode"
                 open="(" close=")" separator=",">
            #{vstoreCode}
        </foreach>
    </update>

    <select id="selectScopeInfo" resultMap="baseResultMap">
        SELECT
        ivs.`inventory_type`,
        ivs.`vstore_name`,
        ivs.`create_user`,
        ivs.`create_time`,
        ivs.`status`,
        ivs.`update_user`,
        ivs.`order_unit_name`,
        ivs.`order_unit_no`,
        ivs.`store_name`,
        ivs.`store_no`,
        ivs.`more_store_flag`,
        ivs.`store_type`,
        ivs.`vstore_type`,
        ivs.`update_time`,
        ivs.`vstore_code`,
        ivs.`id`,
        iv.`vstore_mold`,
        iv.`parent_vstore_code`
        FROM internet_virtual_warehouse_scope ivs
        INNER JOIN  internet_virtual_warehouse_info iv ON ivs.vstore_code=iv.vstore_code
        <where>
            <if test="null != params.orderUnitNo  and '' != params.orderUnitNo">
                AND ivs.`order_unit_no` = #{params.orderUnitNo}
            </if>
            <if test="null != params.storeNo  and '' != params.storeNo">
                AND (ivs.`store_no`= #{params.storeNo} OR ivs.`store_no` IS NULL)
            </if>
            <if test="null != params.standardStoreNo  and '' != params.standardStoreNo">
                AND ivs.`store_no`= #{params.standardStoreNo}
            </if>
            <if test="null != params.parentVstoreCode  and '' != params.parentVstoreCode">
                AND iv.`parent_vstore_code`= #{params.parentVstoreCode}
            </if>
            <if test="null != params.status  and '' != params.status">
                AND ivs.`status`= #{params.status}
            </if>
            <if test="null != params.vstoreMold  and '' != params.vstoreMold">
                AND iv.`vstore_mold`= #{params.vstoreMold}
            </if>
            <if test="null != params.vstoreType  and '' != params.vstoreType">
                AND ivs.`vstore_type`= #{params.vstoreType}
            </if>
        </where>
    </select>

    <insert id="insertBatch">
        INSERT INTO internet_virtual_warehouse_scope
        (
        `id`, `vstore_code`, `vstore_name`,`original_vstore_code`, `original_vstore_name`, `vstore_type`, `order_unit_no`, `order_unit_name`, `store_no`, `store_name`, `store_type`,
        `inventory_type`, `more_store_flag`, `status`, `create_user`, `create_time`, `update_user`, `update_time`
        )
        VALUES
        <foreach collection="list" item="dt" separator=",">
            (#{dt.id,jdbcType=VARCHAR},
            #{dt.vstoreCode,jdbcType=VARCHAR},
            #{dt.vstoreName,jdbcType=VARCHAR},
            #{dt.originalVstoreCode,jdbcType=VARCHAR},
            #{dt.originalVstoreName,jdbcType=VARCHAR},
            #{dt.vstoreType,jdbcType=INTEGER},
            #{dt.orderUnitNo,jdbcType=VARCHAR},
            #{dt.orderUnitName,jdbcType=VARCHAR},
            #{dt.storeNo,jdbcType=VARCHAR},
            #{dt.storeName,jdbcType=VARCHAR},
            #{dt.storeType,jdbcType=INTEGER},
            #{dt.inventoryType,jdbcType=TINYINT},
            #{dt.moreStoreFlag,jdbcType=TINYINT},
            #{dt.status,jdbcType=TINYINT},
            #{dt.createUser,jdbcType=VARCHAR},
            #{dt.createTime,jdbcType=TIMESTAMP},
            #{dt.updateUser,jdbcType=VARCHAR},
            #{dt.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>


</mapper>