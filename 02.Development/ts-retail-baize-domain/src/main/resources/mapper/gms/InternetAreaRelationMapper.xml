<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetAreaRelationRepository">
    <!-- auto generate zkh -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.InternetAreaRelation">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="outside_code" property="outsideCode" jdbcType="CHAR" />
        
        <result column="outside_name" property="outsideName" jdbcType="VARCHAR" />
        
        <result column="retail_code" property="retailCode" jdbcType="CHAR" />
        
        <result column="retail_name" property="retailName" jdbcType="VARCHAR" />
        
        <result column="level" property="level" jdbcType="TINYINT" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="visitor" property="visitor" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `id`,`outside_code`,`outside_name`,`retail_code`,`retail_name`,`level`,`status`,`visitor`,`update_time`,`create_user`,`create_time`,`update_user`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            <if test="null!=params.id  and ''!=params.id ">
                    AND `id`=#{params.id}
            </if>
            <if test="null!=params.outsideCode  and ''!=params.outsideCode ">
                    AND `outside_code`=#{params.outsideCode}
            </if>
            <if test="null!=params.outsideName  and ''!=params.outsideName ">
                    AND `outside_name` like CONCAT('%',#{params.outsideName},'%')
            </if>
            <if test="null!=params.retailCode  and ''!=params.retailCode ">
                    AND `retail_code`=#{params.retailCode}
            </if>
            <if test="params.retailCodeParams != null and !''.equals(params.retailCodeParams)" >
                and retail_code like CONCAT(#{params.retailCodeParams}, '%')
            </if>
            <if test="null!=params.retailName  and ''!=params.retailName ">
                    AND `retail_name` like CONCAT('%',#{params.retailName},'%')
            </if>
            <if test="null!=params.level ">
                    AND `level`=#{params.level}
            </if>
            <if test="null!=params.status ">
                    AND `status`=#{params.status}
            </if>
            <if test="null!=params.visitor  and ''!=params.visitor ">
                    AND `visitor`=#{params.visitor}
            </if>
            <if test="null!=params.updateTime ">
                    AND `update_time`=#{params.updateTime}
            </if>
            <if test="null!=params.createUser  and ''!=params.createUser ">
                    AND `create_user`=#{params.createUser}
            </if>
            <if test="null!=params.createTime ">
                    AND `create_time`=#{params.createTime}
            </if>
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                    AND `update_user`=#{params.updateUser}
            </if>
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="selectByRetailCodeName" resultMap="baseResultMap" parameterType="map" >
        SELECT
        distinct retail_code, max(retail_name) as retail_name
        FROM internet_area_relation WHERE 1=1
        <include refid="condition" /> group by retail_code
    </select>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_area_relation
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_area_relation
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_area_relation
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_area_relation
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_area_relation
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_area_relation
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_area_relation
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_area_relation
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_area_relation
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.InternetAreaRelation"  >
        INSERT INTO internet_area_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="outsideCode != null">
                `outside_code`,
            </if>
            
            <if test="outsideName != null">
                `outside_name`,
            </if>
            
            <if test="retailCode != null">
                `retail_code`,
            </if>
            
            <if test="retailName != null">
                `retail_name`,
            </if>
            
            <if test="level != null">
                `level`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="visitor != null">
                `visitor`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="outsideCode != null">
                #{outsideCode},
            </if>
            
            <if test="outsideName != null">
                #{outsideName},
            </if>
            
            <if test="retailCode != null">
                #{retailCode},
            </if>
            
            <if test="retailName != null">
                #{retailName},
            </if>
            
            <if test="level != null">
                #{level},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="visitor != null">
                #{visitor},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.InternetAreaRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO internet_area_relation (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.outsideCode}, #{item.outsideName}, #{item.retailCode}, #{item.retailName}, #{item.level}, #{item.status}, #{item.visitor}, #{item.updateTime}, #{item.createUser}, #{item.createTime}, #{item.updateUser})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.InternetAreaRelation">
        UPDATE internet_area_relation
        <set>
            
            <if test="outsideCode != null">
                `outside_code` = #{outsideCode},
            </if> 
            <if test="outsideName != null">
                `outside_name` = #{outsideName},
            </if> 
            <if test="retailCode != null">
                `retail_code` = #{retailCode},
            </if> 
            <if test="retailName != null">
                `retail_name` = #{retailName},
            </if> 
            <if test="level != null">
                `level` = #{level},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="visitor != null">
                `visitor` = #{visitor},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE id = #{id}
                
    </update>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM internet_area_relation 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <!-- auto generate end-->
</mapper>