<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.OrderUnitRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.OrderUnit">
                
        <id column="id" property="id" jdbcType="INTEGER" />
        
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="time_seq" property="timeSeq" jdbcType="BIGINT" />
        
        <result column="search_code" property="searchCode" jdbcType="VARCHAR" />
        
        <result column="type" property="type" jdbcType="TINYINT" />
        
        <result column="company_no" property="companyNo" jdbcType="CHAR" />
        
        <result column="order_no" property="orderNo" jdbcType="INTEGER" />
        
        <result column="map_flag" property="mapFlag" jdbcType="TINYINT" />
        
        <result column="cargo_tube_brand_type" property="cargoTubeBrandType" jdbcType="TINYINT" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR" />
        
        <result column="order_unit_code" property="orderUnitCode" jdbcType="VARCHAR" />
        
        <result column="organ_no" property="organNo" jdbcType="CHAR" />
        
        <result column="name" property="name" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `create_time`,`organ_type_no`,`status`,`update_user`,`update_time`,`remark`,`time_seq`,`search_code`,`type`,`company_no`,`order_no`,`map_flag`,`cargo_tube_brand_type`,`create_user`,`id`,`order_unit_no`,`order_unit_code`,`organ_no`,`name`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            <if test="null!=params.q and ''!=params.q">
                AND (`order_unit_no` like CONCAT('%',#{params.q},'%') or `name` like CONCAT('%',#{params.q},'%'))
            </if>
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                
                AND `organ_type_no`=#{params.organTypeNo}
                
            </if>
            
            <if test="null!=params.status ">
                
                AND `status`=#{params.status}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.remark  and ''!=params.remark ">
                
                AND `remark`=#{params.remark}
                
            </if>
            
            <if test="null!=params.timeSeq ">
                
                AND `time_seq`=#{params.timeSeq}
                
            </if>
            
            <if test="null!=params.searchCode  and ''!=params.searchCode ">
                
                AND `search_code`=#{params.searchCode}
                
            </if>
            
            <if test="null!=params.type ">
                
                AND `type`=#{params.type}
                
            </if>
            
            <if test="null!=params.companyNo  and ''!=params.companyNo ">
                
                AND `company_no`=#{params.companyNo}
                
            </if>
            
            <if test="null!=params.orderNo ">
                
                AND `order_no`=#{params.orderNo}
                
            </if>
            
            <if test="null!=params.mapFlag ">
                
                AND `map_flag`=#{params.mapFlag}
                
            </if>
            
            <if test="null!=params.cargoTubeBrandType ">
                
                AND `cargo_tube_brand_type`=#{params.cargoTubeBrandType}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.orderUnitNo  and ''!=params.orderUnitNo ">
                
                AND `order_unit_no`=#{params.orderUnitNo}
                
            </if>
            
            <if test="null!=params.orderUnitCode  and ''!=params.orderUnitCode ">
                
                AND `order_unit_code`=#{params.orderUnitCode}
                
            </if>

            <if test="params.orderUnitNos != null and params.orderUnitNos.size() > 0">
                AND `order_unit_no` IN
                <foreach collection="params.orderUnitNos" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            
            <if test="null!=params.organNo  and ''!=params.organNo ">
                
                AND `organ_no`=#{params.organNo}
                
            </if>
            
            <if test="null!=params.name  and ''!=params.name ">
                
                AND `name` like CONCAT('%',#{params.name},'%') 
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=orderUnitNo and ''!=orderUnitNo">
            AND `order_unit_no`=#{orderUnitNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM order_unit
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM order_unit
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM order_unit
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM order_unit
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM order_unit
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM order_unit
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM order_unit
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM order_unit
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM order_unit
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.OrderUnit"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO order_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="timeSeq != null">
                `time_seq`,
            </if>
            
            <if test="searchCode != null">
                `search_code`,
            </if>
            
            <if test="type != null">
                `type`,
            </if>
            
            <if test="companyNo != null">
                `company_no`,
            </if>
            
            <if test="orderNo != null">
                `order_no`,
            </if>
            
            <if test="mapFlag != null">
                `map_flag`,
            </if>
            
            <if test="cargoTubeBrandType != null">
                `cargo_tube_brand_type`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>
            
            <if test="orderUnitCode != null">
                `order_unit_code`,
            </if>
            
            <if test="organNo != null">
                `organ_no`,
            </if>
            
            <if test="name != null">
                `name`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="timeSeq != null">
                #{timeSeq},
            </if>
            
            <if test="searchCode != null">
                #{searchCode},
            </if>
            
            <if test="type != null">
                #{type},
            </if>
            
            <if test="companyNo != null">
                #{companyNo},
            </if>
            
            <if test="orderNo != null">
                #{orderNo},
            </if>
            
            <if test="mapFlag != null">
                #{mapFlag},
            </if>
            
            <if test="cargoTubeBrandType != null">
                #{cargoTubeBrandType},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>
            
            <if test="orderUnitCode != null">
                #{orderUnitCode},
            </if>
            
            <if test="organNo != null">
                #{organNo},
            </if>
            
            <if test="name != null">
                #{name},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.gms.OrderUnit"  useGeneratedKeys="true" keyProperty="id"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.OrderUnit">
        UPDATE order_unit
        <set>
            
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if> 
            <if test="searchCode != null">
                `search_code` = #{searchCode},
            </if> 
            <if test="type != null">
                `type` = #{type},
            </if> 
            <if test="companyNo != null">
                `company_no` = #{companyNo},
            </if> 
            <if test="orderNo != null">
                `order_no` = #{orderNo},
            </if> 
            <if test="mapFlag != null">
                `map_flag` = #{mapFlag},
            </if> 
            <if test="cargoTubeBrandType != null">
                `cargo_tube_brand_type` = #{cargoTubeBrandType},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if> 
            <if test="orderUnitCode != null">
                `order_unit_code` = #{orderUnitCode},
            </if> 
            <if test="organNo != null">
                `organ_no` = #{organNo},
            </if> 
            <if test="name != null">
                `name` = #{name},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE order_unit_no = #{orderUnitNo}
        
            
    </update>
        <!-- auto generate end-->


</mapper>