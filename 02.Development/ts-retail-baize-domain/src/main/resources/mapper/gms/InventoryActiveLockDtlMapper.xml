<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InventoryActiveLockDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl">
        <id column="id" property="id" jdbcType="CHAR"/>

        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR"/>

        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR"/>

        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>

        <result column="store_no" property="storeNo" jdbcType="CHAR"/>

        <result column="store_type" property="storeType" jdbcType="INTEGER"/>

        <result column="brand_no" property="brandNo" jdbcType="CHAR"/>

        <result column="bill_no" property="billNo" jdbcType="CHAR"/>

        <result column="sku_no" property="skuNo" jdbcType="CHAR"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>

        <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>

        <result column="lock_qty" property="lockQty" jdbcType="INTEGER"/>

        <result column="barcode" property="barcode" jdbcType="VARCHAR"/>

        <result column="size_no" property="sizeNo" jdbcType="VARCHAR"/>

        <result column="balance_lock_qty" property="balanceLockQty" jdbcType="INTEGER"/>

        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="sync_status" property="syncStatus" jdbcType="TINYINT"/>

    </resultMap>

    <sql id="column_list">
        `order_unit_name`,
        `order_unit_no`,
        `store_name`,
        `store_no`,
        `store_type`,
        `brand_no`,
        `id`,
        `bill_no`,
        `sku_no`,
        `create_time`,
        `create_user`,
        `item_code`,
        `lock_qty`,
        `barcode`,
        `size_no`,
        `balance_lock_qty`,
        `update_user`,
        `update_time`,
        `sync_status`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.orderUnitName  and '' != params.orderUnitName">
                AND ( `order_unit_name` like CONCAT('%',#{params.orderUnitName},'%') or `order_unit_no` like CONCAT('%',#{params.orderUnitName},'%') )
            </if>

            <if test="null != params.orderUnitNo  and '' != params.orderUnitNo">
                AND `order_unit_no`=#{params.orderUnitNo}
            </if>

            <if test="null != params.storeName  and '' != params.storeName">
                AND ( `store_name` like CONCAT('%',#{params.storeName},'%') or `store_no` like CONCAT('%',#{params.storeName},'%') )
            </if>

            <if test="null != params.storeNo  and '' != params.storeNo">
                AND `store_no`=#{params.storeNo}
            </if>

            <if test="null != params.storeType">
                AND `store_type`=#{params.storeType}
            </if>

            <if test="null != params.brandNo  and '' != params.brandNo">
                AND `brand_no`=#{params.brandNo}
            </if>

            <if test="null != params.id  and '' != params.id">
                AND `id`=#{params.id}
            </if>

            <if test="null != params.billNo  and '' != params.billNo">
                AND `bill_no`=#{params.billNo}
            </if>

            <if test="null != params.skuNo  and '' != params.skuNo">
                AND `sku_no`=#{params.skuNo}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.itemCode  and '' != params.itemCode">
                AND `item_code`=#{params.itemCode}
            </if>

            <if test="null != params.lockQty">
                AND `lock_qty`=#{params.lockQty}
            </if>

            <if test="null != params.barcode  and '' != params.barcode">
                AND `barcode`=#{params.barcode}
            </if>

            <if test="null != params.sizeNo  and '' != params.sizeNo">
                AND `size_no`=#{params.sizeNo}
            </if>

            <if test="null != params.balanceLockQty">
                AND `balance_lock_qty`=#{params.balanceLockQty}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
            <if test="null != params.syncStatus">
                AND `sync_status`= #{params.syncStatus}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">

    </sql>

    <select id="selectByBillNoQty" resultType="java.util.HashMap" parameterType="list">
        select bill_no as billNo, sum(lock_qty) as totalLockQty, sum(balance_lock_qty) as totalBalanceLockQty
        from inventory_active_lock_dtl where
        bill_no in
        <foreach item="item" index="index" collection="billNos" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by bill_no
    </select>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock_dtl
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock_dtl
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM inventory_active_lock_dtl
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock_dtl
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum}, ${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock_dtl
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM inventory_active_lock_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM inventory_active_lock_dtl
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM inventory_active_lock_dtl
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl">
        INSERT INTO inventory_active_lock_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>

            <if test="orderUnitName != null">
                `order_unit_name`,
            </if>

            <if test="storeNo != null">
                `store_no`,
            </if>

            <if test="storeName != null">
                `store_name`,
            </if>

            <if test="storeType != null">
                `store_type`,
            </if>

            <if test="brandNo != null">
                `brand_no`,
            </if>

            <if test="id != null">
                `id`,
            </if>

            <if test="billNo != null">
                `bill_no`,
            </if>

            <if test="skuNo != null">
                `sku_no`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="itemCode != null">
                `item_code`,
            </if>

            <if test="lockQty != null">
                `lock_qty`,
            </if>

            <if test="barcode != null">
                `barcode`,
            </if>

            <if test="sizeNo != null">
                `size_no`,
            </if>

            <if test="balanceLockQty != null">
                `balance_lock_qty`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>
            <if test="syncStatus != null">
                `sync_status`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>

            <if test="orderUnitName != null">
                #{orderUnitName},
            </if>

            <if test="storeNo != null">
                #{storeNo},
            </if>

            <if test="storeName != null">
                #{storeName},
            </if>

            <if test="storeType != null">
                #{storeType},
            </if>

            <if test="brandNo != null">
                #{brandNo},
            </if>

            <if test="id != null">
                #{id},
            </if>

            <if test="billNo != null">
                #{billNo},
            </if>

            <if test="skuNo != null">
                #{skuNo},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="itemCode != null">
                #{itemCode},
            </if>

            <if test="lockQty != null">
                #{lockQty},
            </if>

            <if test="barcode != null">
                #{barcode},
            </if>

            <if test="sizeNo != null">
                #{sizeNo},
            </if>

            <if test="balanceLockQty != null">
                #{balanceLockQty},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="syncStatus != null">
                #{syncStatus},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO inventory_active_lock_dtl (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            ( #{item.orderUnitName},#{item.orderUnitNo},  #{item.storeName},#{item.storeNo}, #{item.storeType},
             #{item.brandNo}, #{item.id}, #{item.billNo}, #{item.skuNo}, #{item.createTime}, #{item.createUser},
             #{item.itemCode}, #{item.lockQty}, #{item.barcode}, #{item.sizeNo}, #{item.balanceLockQty},
             #{item.updateUser}, #{item.updateTime}, #{item.syncStatus})
        </foreach>
    </insert>

    <insert id="batchSaveOrUpdateDtl" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl" >
        INSERT INTO inventory_active_lock_dtl (<include refid="column_list">
    </include>) values
        <foreach collection="list" item="item" separator=",">
            ( #{item.orderUnitName},#{item.orderUnitNo},  #{item.storeName},#{item.storeNo}, #{item.storeType},
            #{item.brandNo}, #{item.id}, #{item.billNo}, #{item.skuNo}, #{item.createTime}, #{item.createUser},
            #{item.itemCode}, #{item.lockQty}, #{item.barcode}, #{item.sizeNo}, #{item.balanceLockQty},
            #{item.updateUser}, #{item.updateTime}, #{item.syncStatus})
        </foreach>
        ON DUPLICATE KEY UPDATE lock_qty = values(lock_qty), balance_lock_qty = values(balance_lock_qty)
    </insert>

    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl">
        UPDATE inventory_active_lock_dtl
        <set>
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if>

            <if test="orderUnitName != null">
                `order_unit_name` = #{orderUnitName},
            </if>

            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if>

            <if test="storeName != null">
                `store_name` = #{storeName},
            </if>
            <if test="storeType != null">
                `store_type` = #{storeType},
            </if>
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if>
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if>
            <if test="skuNo != null">
                `sku_no` = #{skuNo},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="itemCode != null">
                `item_code` = #{itemCode},
            </if>
            <if test="lockQty != null">
                `lock_qty` = #{lockQty},
            </if>
            <if test="barcode != null">
                `barcode` = #{barcode},
            </if>
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},
            </if>
            <if test="balanceLockQty != null">
                `balance_lock_qty` = #{balanceLockQty},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="syncStatus != null">
                `sync_status` = #{syncStatus},
            </if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock_dtl
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectByVstoreInfo" resultMap="baseResultMap">
        SELECT
        <include refid="column_list1"/>
        FROM inventory_active_lock a INNER JOIN inventory_active_lock_dtl d
        ON  a.`bill_no` = d.`bill_no`
        WHERE a.`vstore_code` = #{params.vstoreCode} AND d.`store_no` = #{params.storeNo}
        AND d.`order_unit_no` = #{params.orderUnitNo} AND a.`status` = 2
        AND a.`start_time` &lt;= #{params.qtime} AND a.`end_time` &gt;= #{params.qtime}
        AND d.`balance_lock_qty` > 0
    </select>

    <sql id="column_list1">
        <!--@sql select -->
        d.`order_unit_name`,
        d.`order_unit_no`,
        d.`store_name`,
        d.`store_no`,
        d.`store_type`,
        d.`brand_no`,
        d.`id`,
        d.`bill_no`,
        d.`sku_no`,
        d.`create_time`,
        d.`create_user`,
        d.`item_code`,
        d.`lock_qty`,
        d.`barcode`,
        d.`size_no`,
        d.`balance_lock_qty`,
        d.`update_user`,
        d.`update_time`,
        d.`sync_status`
        <!--@sql from inventory_active_lock_dtl d-->
    </sql>

    <select id="findBalanceLockQtyInOnStore" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock_dtl
        WHERE 1=1
        <if test="params.storeNo != null">
            AND store_no  = #{params.storeNo}
        </if>
        <if test="params.orderUnitNo != null">
            AND order_unit_no  = #{params.orderUnitNo}
        </if>
        <if test="params.billNo != null">
            AND bill_no  = #{params.billNo}
        </if>
        <if test="params.skuNoList != null">
            AND sku_no in
            <foreach item="item" collection="params.skuNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchUpdateSyncStatus" parameterType="map">
        UPDATE inventory_active_lock_dtl set sync_status = #{syncStatus}
        WHERE id IN
        <foreach collection="list" item="dtl" open="(" separator="," close=")">
            #{dtl.id}
        </foreach>
    </select>

    <select id="selectByAdjustBillNo" resultMap="baseResultMap" parameterType="map">
    	SELECT
			d2.id,
			d2.bill_no,
			d2.order_unit_no,
			d2.order_unit_name,
			d2.store_no,
			d2.store_name,
			d2.sku_no,
			d2.brand_no,
			d2.item_code,
			d2.size_no,
			epm.barcode,
			d2.lock_qty,
			d2.balance_lock_qty + GREATEST(IFNULL(coi.qty, 0), 0) balance_lock_qty,
			d2.sync_status
		FROM
			ics_active_lock_adjust adj
			INNER JOIN ics_active_lock_adjust_dtl d1 ON adj.bill_no = d1.bill_no
			INNER JOIN inventory_active_lock_dtl d2 ON adj.ref_bill_no = d2.bill_no 
			AND d1.store_no = d2.store_no 
			AND d1.order_unit_no = d2.order_unit_no 
			AND d1.sku_no = d2.sku_no
			LEFT JOIN external_product_mapping epm
			ON epm.product_code = d2.item_code
			AND epm.brand_code = d2.brand_no
			AND epm.size_code = d2.size_no
			AND epm.merchants_code = #{params.merchantsCode} 
			LEFT JOIN channel_occupied_inventory coi 
			ON coi.store_no = d2.store_no
			AND coi.order_unit_no = d2.order_unit_no
			AND coi.sku_no = d2.sku_no
			AND coi.channel_no = #{params.channelNo}
		WHERE
			adj.bill_no = #{params.billNo} 
			AND d1.sync_status = #{params.syncStatus}
    </select>

    <update id="batchUpdateByBillNo">
        UPDATE inventory_active_lock_dtl set sync_status = #{status}
        WHERE bill_no = #{billNo} and sync_status !=#{status}
    </update>

    <select id="selectStatusByBillNo" resultType="java.util.Map">
        SELECT
        sync_status as syncStatus ,
        count(1) as count
        FROM inventory_active_lock_dtl WHERE bill_no = #{billNo}
        GROUP BY sync_status
    </select>
    
    <update id="batchUpdateDtlForAudit" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl">
    	INSERT INTO inventory_active_lock_dtl (<include refid="column_list">
    	</include>) values
        <foreach collection="list" item="item" separator=",">
            ( #{item.orderUnitName},#{item.orderUnitNo},  #{item.storeName},#{item.storeNo}, #{item.storeType},
            #{item.brandNo}, #{item.id}, #{item.billNo}, #{item.skuNo}, #{item.createTime}, #{item.createUser},
            #{item.itemCode}, #{item.lockQty}, #{item.barcode}, #{item.sizeNo}, #{item.balanceLockQty},
            #{item.updateUser}, #{item.updateTime}, #{item.syncStatus})
        </foreach>
        ON DUPLICATE KEY UPDATE balance_lock_qty = balance_lock_qty + values(balance_lock_qty)
    </update>


    <resultMap id="extendBaseResultMap" extends="baseResultMap" type="cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied">
        <result column="occupied_qty" property="occupiedQty" jdbcType="INTEGER"/>
    </resultMap>

    <select id="selectByInventoryActiveLockDtlQuery"
            parameterType="cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery"
            resultMap="extendBaseResultMap">
        SELECT
        d2.order_unit_name,
        d2.order_unit_no,
        d2.store_name,
        d2.store_no,
        d2.brand_no,
        d2.item_code,
        d2.size_no,
        d2.sku_no,
        d2.balance_lock_qty,
        d2.lock_qty,
        d2.sync_status,
        d2.id,
        d2.bill_no,
        d2.create_time,
        d2.create_user,
        d2.update_user,
        d2.update_time,
        d2.store_type,
        d2.barcode
        <if test="query.containerOccupiedQty and query.channelNo != null and query.channelNo != ''">
            ,coi.qty as occupied_qty
        </if>

        FROM inventory_active_lock_dtl as d2

        <if test="query.containerOccupiedQty and query.channelNo != null and query.channelNo != ''">
            LEFT JOIN channel_occupied_inventory coi
            ON coi.store_no = d2.store_no
            AND coi.order_unit_no = d2.order_unit_no
            AND coi.sku_no = d2.sku_no
            AND coi.channel_no = #{query.channelNo}
        </if>

        <where>
            <if test="query.billNo != null and query.billNo != ''">
                AND d2.bill_no = #{query.billNo}
            </if>
            <if test="query.syncStatus != null and query.syncStatus.size() > 0">
                    AND d2.sync_status IN
                    <foreach collection="query.syncStatus" item="syncStatus" open="(" close=")" separator=",">
                        #{syncStatus}
                    </foreach>
            </if>
        </where>
    </select>
    <!-- auto generate end-->
</mapper>