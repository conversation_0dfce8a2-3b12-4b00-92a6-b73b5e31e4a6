<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.ZoneInfoRepository">
    <!-- auto generate zkh -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.ZoneInfo">
                
        <id column="id" property="id" jdbcType="INTEGER" />
        
        
        <result column="zone_no" property="zoneNo" jdbcType="CHAR" />
        
        <result column="zone_code" property="zoneCode" jdbcType="CHAR" />
        
        <result column="name" property="name" jdbcType="VARCHAR" />
        
        <result column="manage_zone_no" property="manageZoneNo" jdbcType="VARCHAR" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="sys_no" property="sysNo" jdbcType="VARCHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="time_seq" property="timeSeq" jdbcType="BIGINT" />
        
    </resultMap>

    <sql id="column_list">
        `id`,`zone_no`,`zone_code`,`name`,`manage_zone_no`,`status`,`sys_no`,`create_user`,`create_time`,`update_user`,`update_time`,`remark`,`time_seq`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
                <if test="null!=params.id ">
                    
                        AND `id`=#{params.id}
                    
                </if>
            
                <if test="null!=params.zoneNo  and ''!=params.zoneNo ">
                    
                        AND `zone_no`=#{params.zoneNo}
                    
                </if>
            
                <if test="null!=params.zoneCode  and ''!=params.zoneCode ">
                    
                        AND `zone_code`=#{params.zoneCode}
                    
                </if>
            
                <if test="null!=params.name  and ''!=params.name ">
                    
                        AND `name` like CONCAT('%',#{params.name},'%') 
                    
                </if>
            
                <if test="null!=params.manageZoneNo  and ''!=params.manageZoneNo ">
                    
                        AND `manage_zone_no`=#{params.manageZoneNo}
                    
                </if>
            
                <if test="null!=params.status ">
                    
                        AND `status`=#{params.status}
                    
                </if>
            
                <if test="null!=params.sysNo  and ''!=params.sysNo ">
                    
                        AND `sys_no`=#{params.sysNo}
                    
                </if>
            
                <if test="null!=params.createUser  and ''!=params.createUser ">
                    
                        AND `create_user`=#{params.createUser}
                    
                </if>
            
                <if test="null!=params.createTime ">
                    
                        AND `create_time`=#{params.createTime}
                    
                </if>
            
                <if test="null!=params.updateUser  and ''!=params.updateUser ">
                    
                        AND `update_user`=#{params.updateUser}
                    
                </if>
            
                <if test="null!=params.updateTime ">
                    
                        AND `update_time`=#{params.updateTime}
                    
                </if>
            
                <if test="null!=params.remark  and ''!=params.remark ">
                    
                        AND `remark`=#{params.remark}
                    
                </if>
            
                <if test="null!=params.timeSeq ">
                    
                        AND `time_seq`=#{params.timeSeq}
                    
                </if>
            
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=zoneNo and ''!=zoneNo">
            AND `zone_no`=#{zoneNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM zone_info
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM zone_info
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM zone_info
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM zone_info
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM zone_info
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM zone_info
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM zone_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM zone_info
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM zone_info
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.ZoneInfo"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO zone_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="zoneNo != null">
                `zone_no`,
            </if>
            
            <if test="zoneCode != null">
                `zone_code`,
            </if>
            
            <if test="name != null">
                `name`,
            </if>
            
            <if test="manageZoneNo != null">
                `manage_zone_no`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="sysNo != null">
                `sys_no`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="timeSeq != null">
                `time_seq`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="zoneNo != null">
                #{zoneNo},
            </if>
            
            <if test="zoneCode != null">
                #{zoneCode},
            </if>
            
            <if test="name != null">
                #{name},
            </if>
            
            <if test="manageZoneNo != null">
                #{manageZoneNo},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="sysNo != null">
                #{sysNo},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="timeSeq != null">
                #{timeSeq},
            </if>
            
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.ZoneInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO zone_info (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.zoneNo}, #{item.zoneCode}, #{item.name}, #{item.manageZoneNo}, #{item.status}, #{item.sysNo}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime}, #{item.remark}, #{item.timeSeq})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.ZoneInfo">
        UPDATE zone_info
        <set>
            
            <if test="zoneNo != null">
                `zone_no` = #{zoneNo},
            </if> 
            <if test="zoneCode != null">
                `zone_code` = #{zoneCode},
            </if> 
            <if test="name != null">
                `name` = #{name},
            </if> 
            <if test="manageZoneNo != null">
                `manage_zone_no` = #{manageZoneNo},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="sysNo != null">
                `sys_no` = #{sysNo},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE zone_no = #{zoneNo}
                
    </update>

    <select id="selectByUniques" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM zone_info 
        where zone_no in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM zone_info 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <!-- auto generate end-->
</mapper>