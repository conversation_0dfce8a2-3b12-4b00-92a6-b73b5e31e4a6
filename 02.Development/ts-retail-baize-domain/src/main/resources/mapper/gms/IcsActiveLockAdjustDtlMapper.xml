<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.IcsActiveLockAdjustDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl">
                
        <id column="id" property="id" jdbcType="CHAR" />	
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />	
        <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />	
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />	
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />	
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />	
        <result column="brand_no" property="brandNo" jdbcType="CHAR" />	
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />	
        <result column="bill_no" property="billNo" jdbcType="CHAR" />	
        <result column="sync_status" property="syncStatus" jdbcType="TINYINT" />	
        <result column="store_no" property="storeNo" jdbcType="CHAR" />	
        <result column="barcode" property="barcode" jdbcType="VARCHAR" />	
        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR" />	
        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR" />	
        <result column="adjust_qty" property="adjustQty" jdbcType="INTEGER" />	
        <result column="sku_no" property="skuNo" jdbcType="CHAR" />
        <result column="balance_lock_qty" property="balanceLockQty" jdbcType="INTEGER" />
        <result column="lock_qty" property="lockQty" jdbcType="INTEGER" />
    </resultMap>

    <sql id="column_list">
        `create_user`,`update_user`,`size_no`,`update_time`,`item_code`,`create_time`,`brand_no`,`store_name`,`bill_no`,`sync_status`,`store_no`,`barcode`,`order_unit_no`,`id`,`order_unit_name`,`adjust_qty`,`sku_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
        
        	<if test="null!=params.createUser  and ''!=params.createUser ">
				 AND `create_user`=#{params.createUser}
            </if>
        	<if test="null!=params.updateUser  and ''!=params.updateUser ">
				 AND `update_user`=#{params.updateUser}
            </if>	
        	<if test="null!=params.sizeNo  and ''!=params.sizeNo ">
				 AND `size_no`=#{params.sizeNo}
            </if>	
        	<if test="null!=params.updateTime ">
				 AND `update_time`=#{params.updateTime}
            </if>	
        	<if test="null!=params.itemCode  and ''!=params.itemCode ">
				 AND `item_code`=#{params.itemCode}
            </if>	
        	<if test="null!=params.createTime ">
				 AND `create_time`=#{params.createTime}
            </if>	
        	<if test="null!=params.brandNo  and ''!=params.brandNo ">
				 AND `brand_no`=#{params.brandNo}
            </if>	
        	<if test="null!=params.storeName  and ''!=params.storeName ">
                AND ( `store_name` like CONCAT('%',#{params.storeName},'%') or `store_no` like CONCAT('%',#{params.storeName},'%') )
            </if>	
			<if test="null!=params.storeNameLike  and ''!=params.storeNameLike ">
                AND `store_name` like CONCAT('%',#{params.storeNameLike},'%') 
			</if>	
        	<if test="null!=params.billNo  and ''!=params.billNo ">
				 AND `bill_no`=#{params.billNo}
            </if>	
        	<if test="null!=params.syncStatus ">
				 AND `sync_status`=#{params.syncStatus}
            </if>	
        	<if test="null!=params.storeNo  and ''!=params.storeNo ">
				 AND `store_no`=#{params.storeNo}
            </if>	
        	<if test="null!=params.barcode  and ''!=params.barcode ">
				 AND `barcode`=#{params.barcode}
            </if>	
        	<if test="null!=params.orderUnitNo  and ''!=params.orderUnitNo ">
				 AND `order_unit_no`=#{params.orderUnitNo}
            </if>	
        	<if test="null!=params.id  and ''!=params.id ">
				 AND `id`=#{params.id}
            </if>	
        	<if test="null!=params.orderUnitName  and ''!=params.orderUnitName ">
                AND ( `order_unit_name` like CONCAT('%',#{params.orderUnitName},'%') or `order_unit_no` like CONCAT('%',#{params.orderUnitName},'%') )
            </if>	
			<if test="null!=params.orderUnitNameLike  and ''!=params.orderUnitNameLike ">
                AND `order_unit_name` like CONCAT('%',#{params.orderUnitNameLike},'%') 
			</if>	
        	<if test="null!=params.adjustQty ">
				 AND `adjust_qty`=#{params.adjustQty}
            </if>	
        	<if test="null!=params.skuNo  and ''!=params.skuNo ">
				 AND `sku_no`=#{params.skuNo}
            </if>	

            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust_dtl
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM ics_active_lock_adjust_dtl
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM ics_active_lock_adjust_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM ics_active_lock_adjust_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM ics_active_lock_adjust_dtl
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl"  >
        INSERT INTO ics_active_lock_adjust_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="createUser != null">
                `create_user`,
            </if>
            <if test="updateUser != null">
                `update_user`,
            </if>
            <if test="sizeNo != null">
                `size_no`,
            </if>
            <if test="updateTime != null">
                `update_time`,
            </if>
            <if test="itemCode != null">
                `item_code`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="brandNo != null">
                `brand_no`,
            </if>
            <if test="storeName != null">
                `store_name`,
            </if>
            <if test="billNo != null">
                `bill_no`,
            </if>
            <if test="syncStatus != null">
                `sync_status`,
            </if>
            <if test="storeNo != null">
                `store_no`,
            </if>
            <if test="barcode != null">
                `barcode`,
            </if>
            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>
            <if test="id != null">
                `id`,
            </if>
            <if test="orderUnitName != null">
                `order_unit_name`,
            </if>
            <if test="adjustQty != null">
                `adjust_qty`,
            </if>
            <if test="skuNo != null">
                `sku_no`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="createUser != null">
                #{createUser},
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="sizeNo != null">
                #{sizeNo},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="itemCode != null">
                #{itemCode},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="brandNo != null">
                #{brandNo},
            </if>
            <if test="storeName != null">
                #{storeName},
            </if>
            <if test="billNo != null">
                #{billNo},
            </if>
            <if test="syncStatus != null">
                #{syncStatus},
            </if>
            <if test="storeNo != null">
                #{storeNo},
            </if>
            <if test="barcode != null">
                #{barcode},
            </if>
            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>
            <if test="id != null">
                #{id},
            </if>
            <if test="orderUnitName != null">
                #{orderUnitName},
            </if>
            <if test="adjustQty != null">
                #{adjustQty},
            </if>
            <if test="skuNo != null">
                #{skuNo},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ics_active_lock_adjust_dtl (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.createUser}, #{item.updateUser}, #{item.sizeNo}, #{item.updateTime}, #{item.itemCode}, #{item.createTime}, #{item.brandNo}, #{item.storeName}, #{item.billNo}, #{item.syncStatus}, #{item.storeNo}, #{item.barcode}, #{item.orderUnitNo}, #{item.id}, #{item.orderUnitName}, #{item.adjustQty}, #{item.skuNo})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl">
        UPDATE ics_active_lock_adjust_dtl
        <set>
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},
            </if> 
            <if test="itemCode != null">
                `item_code` = #{itemCode},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="syncStatus != null">
                `sync_status` = #{syncStatus},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            <if test="barcode != null">
                `barcode` = #{barcode},
            </if> 
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if> 
            <if test="orderUnitName != null">
                `order_unit_name` = #{orderUnitName},
            </if> 
            <if test="adjustQty != null">
                `adjust_qty` = #{adjustQty},
            </if> 
            <if test="skuNo != null">
                `sku_no` = #{skuNo},
            </if> 
            update_time =  now() 
        </set>
        
        
        WHERE id = #{id}
                
    </update>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_active_lock_adjust_dtl 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <select id="selectActiveDtlList" resultMap="baseResultMap">
        SELECT
            ad.*,
            ld.balance_lock_qty,
            ld.lock_qty
        FROM
            ics_active_lock_adjust la
            INNER JOIN ics_active_lock_adjust_dtl ad ON la.bill_no = ad.bill_no
            LEFT JOIN inventory_active_lock_dtl ld ON la.ref_bill_no = ld.bill_no
            AND ad.store_no = ld.store_no
            AND ad.order_unit_no = ld.order_unit_no
            AND ad.sku_no = ld.sku_no
        WHERE la.bill_no = #{params.billNo}
        <if test="params.storeNo != null">
            AND ad.store_no = #{params.storeNo}
        </if>
        <if test="params.orderUnitNo != null">
            AND ad.order_unit_no = #{params.orderUnitNo}
        </if>
        <if test="params.itemCode != null">
            AND ad.item_code = #{params.itemCode}
        </if>
        <if test="params.barCode != null">
            AND ad.barcode = #{params.barcode}
        </if>
        <if test="params.sizeNo != null">
            AND ad.size_no = #{params.sizeNo}
        </if>
        ORDER BY ad.update_time
        <if test="page != null">
            LIMIT ${page.startRowNum},${page.pageSize}
        </if>
    </select>

    <insert id="batchSaveOrUpdateDtl" parameterType="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl">
        INSERT INTO ics_active_lock_adjust_dtl (<include refid="column_list">
        </include>) values
        <foreach collection="list" item="item" separator=",">
            ( #{item.createUser},  #{item.updateUser},#{item.sizeNo}, #{item.updateTime},
            #{item.itemCode}, #{item.createTime}, #{item.brandNo}, #{item.storeName}, #{item.billNo}, #{item.syncStatus},
            #{item.storeNo}, #{item.barcode}, #{item.orderUnitNo}, #{item.id}, #{item.orderUnitName},
            #{item.adjustQty}, #{item.skuNo})
        </foreach>
        ON DUPLICATE KEY UPDATE adjust_qty = values(adjust_qty)
    </insert>

    <update id="batchUpdateSyncStatus" parameterType="map">
        UPDATE ics_active_lock_adjust_dtl set sync_status = #{syncStatus}
        WHERE id IN
        <foreach collection="list" item="dtl" open="(" separator="," close=")">
            #{dtl.id}
        </foreach>
    </update>
    <!-- auto generate end-->
</mapper>