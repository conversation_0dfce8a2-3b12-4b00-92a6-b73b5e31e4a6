<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.OrderUnitPriorityConfigRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig">
        <id column="id" property="id" jdbcType="CHAR"/>


        <result column="vstore_name" property="vstoreName" jdbcType="VARCHAR"/>

        <result column="order_unit_no" property="orderUnitNo" jdbcType="VARCHAR"/>

        <result column="order_unit_level" property="orderUnitLevel" jdbcType="TINYINT"/>

        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR"/>

        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR"/>

        <result column="remark" property="remark" jdbcType="VARCHAR"/>

        <result column="status" property="status" jdbcType="TINYINT"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `vstore_name`,
        `order_unit_no`,
        `order_unit_level`,
        `order_unit_name`,
        `vstore_code`,
        `id`,
        `remark`,
        `status`,
        `create_user`,
        `create_time`,
        `update_time`,
        `update_user`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.vstoreName  and '' != params.vstoreName">
                AND `vstore_name` like CONCAT('%', #{params.vstoreName}, '%')
            </if>

            <if test="null != params.orderUnitNo  and '' != params.orderUnitNo">
                AND `order_unit_no`=
                    #{params.orderUnitNo}
            </if>

            <if test="null != params.orderUnitLevel">
                AND `order_unit_level`=#{params.orderUnitLevel}
            </if>

            <if test="null != params.orderUnitName  and '' != params.orderUnitName">
                AND `order_unit_name` like CONCAT('%',
                    #{params.orderUnitName}, '%')
            </if>

            <if test="null != params.vstoreCode  and '' != params.vstoreCode">
                AND `vstore_code`=
                    #{params.vstoreCode}
            </if>


            <if test="null != params.orderUnitNoOrName and '' != params.orderUnitNoOrName">
                AND ( `order_unit_no`=#{params.orderUnitNoOrName} OR `order_unit_name` like CONCAT('%',
                    #{params.orderUnitNoOrName}, '%')
                    )
            </if>


            <if test="null != params.id  and '' != params.id">
                AND `id`=  #{params.id}
            </if>

            <if test="null != params.remark  and '' != params.remark">
                AND `remark`=#{params.remark}
            </if>

            <if test="null != params.status">
                AND `status`=#{params.status}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_order_unit_priority_config
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_order_unit_priority_config
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_order_unit_priority_config
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM ics_order_unit_priority_config
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_order_unit_priority_config
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_order_unit_priority_config
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM ics_order_unit_priority_config
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM ics_order_unit_priority_config
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM ics_order_unit_priority_config
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig">
        INSERT INTO ics_order_unit_priority_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vstoreName != null">
                `vstore_name`,
            </if>

            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>

            <if test="orderUnitLevel != null">
                `order_unit_level`,
            </if>

            <if test="orderUnitName != null">
                `order_unit_name`,
            </if>

            <if test="vstoreCode != null">
                `vstore_code`,
            </if>

            <if test="id != null">
                `id`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="status != null">
                `status`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="vstoreName != null">
                #{vstoreName},
            </if>

            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>

            <if test="orderUnitLevel != null">
                #{orderUnitLevel},
            </if>

            <if test="orderUnitName != null">
                #{orderUnitName},
            </if>

            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>

            <if test="id != null">
                #{id},
            </if>

            <if test="remark != null">
                #{remark},
            </if>

            <if test="status != null">
                #{status},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ics_order_unit_priority_config (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            (#{item.vstoreName}, #{item.orderUnitNo}, #{item.orderUnitLevel}, #{item.orderUnitName}, #{item.vstoreCode},
             #{item.id}, #{item.remark}, #{item.status}, #{item.createUser}, #{item.createTime}, #{item.updateTime},
             #{item.updateUser})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig">
        UPDATE ics_order_unit_priority_config
        <set>
            <if test="vstoreName != null">
                `vstore_name` = #{vstoreName},
            </if>
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if>
            <if test="orderUnitLevel != null">
                `order_unit_level` = #{orderUnitLevel},
            </if>
            <if test="orderUnitName != null">
                `order_unit_name` = #{orderUnitName},
            </if>
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            update_time = now()
        </set>


        WHERE id = #{id}
    </update>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_order_unit_priority_config
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- auto generate end-->
</mapper>