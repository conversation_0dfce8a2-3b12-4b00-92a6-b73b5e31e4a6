<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.SellOutConfigDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl">
                
        <id column="id" property="id" jdbcType="CHAR" />	
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />	
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />	
        <result column="bill_no" property="billNo" jdbcType="CHAR" />	
        <result column="target_field" property="targetField" jdbcType="VARCHAR" />	
        <result column="target_value" property="targetValue" jdbcType="DECIMAL" />	
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />	
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />	
    </resultMap>

    <sql id="column_list">
        `update_user`,`update_time`,`id`,`bill_no`,`target_field`,`target_value`,`create_time`,`create_user`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
        
        	<if test="null!=params.updateUser  and ''!=params.updateUser ">
				 AND `update_user`=#{params.updateUser}
            </if>	
        	<if test="null!=params.updateTime ">
				 AND `update_time`=#{params.updateTime}
            </if>	
        	<if test="null!=params.id  and ''!=params.id ">
				 AND `id`=#{params.id}
            </if>	
        	<if test="null!=params.billNo  and ''!=params.billNo ">
				 AND `bill_no`=#{params.billNo}
            </if>

            <!--billNos-->
            <if test="null!=params.billNos and params.billNos.size()>0 ">
                AND `bill_no` IN
                <foreach collection="params.billNos" item="billNo" open="(" close=")" separator=",">
                    #{billNo}
                </foreach>
            </if>

        	<if test="null!=params.targetField  and ''!=params.targetField ">
				 AND `target_field`=#{params.targetField}
            </if>	
        	<if test="null!=params.targetValue ">
				 AND `target_value`=#{params.targetValue}
            </if>	
        	<if test="null!=params.createTime ">
				 AND `create_time`=#{params.createTime}
            </if>	
        	<if test="null!=params.createUser  and ''!=params.createUser ">
				 AND `create_user`=#{params.createUser}
            </if>	

            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM sell_out_config_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM sell_out_config_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM sell_out_config_dtl
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM sell_out_config_dtl
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM sell_out_config_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM sell_out_config_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM sell_out_config_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM sell_out_config_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM sell_out_config_dtl
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl"  >
        INSERT INTO sell_out_config_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            <if test="updateTime != null">
                `update_time`,
            </if>
            <if test="id != null">
                `id`,
            </if>
            <if test="billNo != null">
                `bill_no`,
            </if>
            <if test="targetField != null">
                `target_field`,
            </if>
            <if test="targetValue != null">
                `target_value`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="createUser != null">
                `create_user`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="id != null">
                #{id},
            </if>
            <if test="billNo != null">
                #{billNo},
            </if>
            <if test="targetField != null">
                #{targetField},
            </if>
            <if test="targetValue != null">
                #{targetValue},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createUser != null">
                #{createUser},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sell_out_config_dtl (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.updateUser}, #{item.updateTime}, #{item.id}, #{item.billNo}, #{item.targetField}, #{item.targetValue}, #{item.createTime}, #{item.createUser})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl">
        UPDATE sell_out_config_dtl
        <set>
            
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="targetField != null">
                `target_field` = #{targetField},
            </if> 
            <if test="targetValue != null">
                `target_value` = #{targetValue},
            </if> 
            update_time =  now() 
        </set>
        
        
        WHERE id = #{id}
                
    </update>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM sell_out_config_dtl 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <delete id="deleteByBillNo">
        DELETE FROM sell_out_config_dtl
        WHERE bill_no in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </delete>

    <!-- auto generate end-->
</mapper>