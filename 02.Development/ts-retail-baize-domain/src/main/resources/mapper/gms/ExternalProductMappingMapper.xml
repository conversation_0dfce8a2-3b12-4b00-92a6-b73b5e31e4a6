<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.ExternalProductMappingRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.ExternalProductMapping">
        <id column="id" property="id" jdbcType="CHAR"/>


        <result column="merchants_code" property="merchantsCode" jdbcType="VARCHAR"/>

        <result column="barcode" property="barcode" jdbcType="VARCHAR"/>

        <result column="brand_code" property="brandCode" jdbcType="CHAR"/>

        <result column="product_code" property="productCode" jdbcType="VARCHAR"/>

        <result column="size_code" property="sizeCode" jdbcType="VARCHAR"/>

        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="sku_no" property="skuNo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `id`,
        `merchants_code`,
        `barcode`,
        `brand_code`,
        `product_code`,
        `size_code`,
        `update_user`,
        `update_time`,
        `create_user`,
        `create_time`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.id  and '' != params.id">
                AND `id`=#{params.id}
            </if>

            <if test="null != params.merchantsCode  and '' != params.merchantsCode">
                AND `merchants_code`=#{params.merchantsCode}
            </if>

            <if test="null != params.barcode  and '' != params.barcode">
                AND `barcode`=#{params.barcode}
            </if>

            <if test="null != params.brandCode  and '' != params.brandCode">
                AND `brand_code`=#{params.brandCode}
            </if>

            <if test="null != params.productCode  and '' != params.productCode">
                AND `product_code`=#{params.productCode}
            </if>

            <if test="null != params.sizeCode  and '' != params.sizeCode">
                AND `size_code`=#{params.sizeCode}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM external_product_mapping
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM external_product_mapping
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM external_product_mapping
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM external_product_mapping
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectItemSkuByParams" resultMap="baseResultMap" parameterType="map">
        select m.*,
               sku.id AS sku_no
        from external_product_mapping m
                 INNER JOIN item i ON m.product_code = i.`code`
            AND m.brand_code = i.brand_no
                 INNER JOIN item_sku sku ON sku.item_no = i.item_no
            AND m.size_code = sku.size_no
        where sku.STATUS = 1
          AND i.STATUS = 1
          AND sku.barcode_status = 1
        <if test="params.merchantsCode != null and !''.equals(params.merchantsCode)">
            and m.merchants_code = #{params.merchantsCode,jdbcType=CHAR}
        </if>
        <if test="params.brandCode != null and !''.equals(params.brandCode)">
            and m.brand_code = #{params.brandCode,jdbcType=CHAR}
        </if>
        <if test="params.productCode != null and !''.equals(params.productCode)">
            and m.product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.sizeCode != null and !''.equals(params.sizeCode)">
            and m.size_code = #{params.sizeCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM external_product_mapping
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM external_product_mapping
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM external_product_mapping
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM external_product_mapping
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM external_product_mapping
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.ExternalProductMapping">
        INSERT INTO external_product_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>

            <if test="merchantsCode != null">
                `merchants_code`,
            </if>

            <if test="barcode != null">
                `barcode`,
            </if>

            <if test="brandCode != null">
                `brand_code`,
            </if>

            <if test="productCode != null">
                `product_code`,
            </if>

            <if test="sizeCode != null">
                `size_code`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>

            <if test="merchantsCode != null">
                #{merchantsCode},
            </if>

            <if test="barcode != null">
                #{barcode},
            </if>

            <if test="brandCode != null">
                #{brandCode},
            </if>

            <if test="productCode != null">
                #{productCode},
            </if>

            <if test="sizeCode != null">
                #{sizeCode},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.ExternalProductMapping"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO external_product_mapping (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.merchantsCode}, #{item.barcode}, #{item.brandCode}, #{item.productCode},
             #{item.sizeCode}, #{item.updateUser}, #{item.updateTime}, #{item.createUser}, #{item.createTime})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.ExternalProductMapping">
        UPDATE external_product_mapping
        <set>
            <if test="merchantsCode != null">
                `merchants_code` = #{merchantsCode},
            </if>
            <if test="barcode != null">
                `barcode` = #{barcode},
            </if>
            <if test="brandCode != null">
                `brand_code` = #{brandCode},
            </if>
            <if test="productCode != null">
                `product_code` = #{productCode},
            </if>
            <if test="sizeCode != null">
                `size_code` = #{sizeCode},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            update_time = now()
        </set>


        WHERE id = #{id}
    </update>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM external_product_mapping
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectItemsByTuple3" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM external_product_mapping
        where merchants_code = #{merchantsCode}
        and (product_code, brand_code, size_code) in
        <foreach item="item" index="index" collection="itemBrandSizeList" open="(" separator="," close=")">
            (#{item.t1}, #{item.t2}, #{item.t3})
        </foreach>
    </select>

    <!-- auto generate end-->
</mapper>