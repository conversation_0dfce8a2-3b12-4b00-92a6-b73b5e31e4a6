<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.CompanyRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.Company">
                
        <id column="id" property="id" jdbcType="INTEGER" />
        
        
        <result column="company_no" property="companyNo" jdbcType="CHAR" />
        
        <result column="name" property="name" jdbcType="VARCHAR" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="bank_name" property="bankName" jdbcType="VARCHAR" />
        
        <result column="bank_account" property="bankAccount" jdbcType="VARCHAR" />
        
        <result column="bank_account_name" property="bankAccountName" jdbcType="VARCHAR" />
        
        <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
        
        <result column="tel" property="tel" jdbcType="VARCHAR" />
        
        <result column="tax_registry_no" property="taxRegistryNo" jdbcType="VARCHAR" />
        
        <result column="tax_level" property="taxLevel" jdbcType="VARCHAR" />
        
        <result column="legal_person" property="legalPerson" jdbcType="VARCHAR" />
        
        <result column="identity_card" property="identityCard" jdbcType="VARCHAR" />
        
        <result column="fax" property="fax" jdbcType="VARCHAR" />
        
        <result column="email" property="email" jdbcType="VARCHAR" />
        
        <result column="zone_no" property="zoneNo" jdbcType="CHAR" />
        
        <result column="nc_project_organize_no" property="ncProjectOrganizeNo" jdbcType="VARCHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="time_seq" property="timeSeq" jdbcType="BIGINT" />
        
        <result column="search_code" property="searchCode" jdbcType="VARCHAR" />
        
        <result column="address" property="address" jdbcType="VARCHAR" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
        <result column="whether_wholesale_pool" property="whetherWholesalePool" jdbcType="TINYINT" />
        
        <result column="postcode" property="postcode" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `id`,`company_no`,`name`,`status`,`bank_name`,`bank_account`,`bank_account_name`,`contact_name`,`tel`,`tax_registry_no`,`tax_level`,`legal_person`,`identity_card`,`fax`,`email`,`zone_no`,`nc_project_organize_no`,`create_user`,`create_time`,`update_user`,`update_time`,`remark`,`time_seq`,`search_code`,`address`,`organ_type_no`,`whether_wholesale_pool`,`postcode`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.q and ''!=params.q">
                AND `company_no` = #{params.q}
            </if>
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
                <if test="null!=params.id ">
                    
                        AND `id`=#{params.id}
                    
                </if>
            
                <if test="null!=params.companyNo  and ''!=params.companyNo ">
                    
                        AND `company_no`=#{params.companyNo}
                    
                </if>
            
                <if test="null!=params.name  and ''!=params.name ">
                    
                        AND `name` like CONCAT('%',#{params.name},'%') 
                    
                </if>
            
                <if test="null!=params.status ">
                    
                        AND `status`=#{params.status}
                    
                </if>
            
                <if test="null!=params.bankName  and ''!=params.bankName ">
                    
                        AND `bank_name` like CONCAT('%',#{params.bankName},'%') 
                    
                </if>
            
                <if test="null!=params.bankAccount  and ''!=params.bankAccount ">
                    
                        AND `bank_account`=#{params.bankAccount}
                    
                </if>
            
                <if test="null!=params.bankAccountName  and ''!=params.bankAccountName ">
                    
                        AND `bank_account_name` like CONCAT('%',#{params.bankAccountName},'%') 
                    
                </if>
            
                <if test="null!=params.contactName  and ''!=params.contactName ">
                    
                        AND `contact_name` like CONCAT('%',#{params.contactName},'%') 
                    
                </if>
            
                <if test="null!=params.tel  and ''!=params.tel ">
                    
                        AND `tel`=#{params.tel}
                    
                </if>
            
                <if test="null!=params.taxRegistryNo  and ''!=params.taxRegistryNo ">
                    
                        AND `tax_registry_no`=#{params.taxRegistryNo}
                    
                </if>
            
                <if test="null!=params.taxLevel  and ''!=params.taxLevel ">
                    
                        AND `tax_level`=#{params.taxLevel}
                    
                </if>
            
                <if test="null!=params.legalPerson  and ''!=params.legalPerson ">
                    
                        AND `legal_person`=#{params.legalPerson}
                    
                </if>
            
                <if test="null!=params.identityCard  and ''!=params.identityCard ">
                    
                        AND `identity_card`=#{params.identityCard}
                    
                </if>
            
                <if test="null!=params.fax  and ''!=params.fax ">
                    
                        AND `fax`=#{params.fax}
                    
                </if>
            
                <if test="null!=params.email  and ''!=params.email ">
                    
                        AND `email`=#{params.email}
                    
                </if>
            
                <if test="null!=params.zoneNo  and ''!=params.zoneNo ">
                    
                        AND `zone_no`=#{params.zoneNo}
                    
                </if>
            
                <if test="null!=params.ncProjectOrganizeNo  and ''!=params.ncProjectOrganizeNo ">
                    
                        AND `nc_project_organize_no`=#{params.ncProjectOrganizeNo}
                    
                </if>
            
                <if test="null!=params.createUser  and ''!=params.createUser ">
                    
                        AND `create_user`=#{params.createUser}
                    
                </if>
            
                <if test="null!=params.createTime ">
                    
                        AND `create_time`=#{params.createTime}
                    
                </if>
            
                <if test="null!=params.updateUser  and ''!=params.updateUser ">
                    
                        AND `update_user`=#{params.updateUser}
                    
                </if>
            
                <if test="null!=params.updateTime ">
                    
                        AND `update_time`=#{params.updateTime}
                    
                </if>
            
                <if test="null!=params.remark  and ''!=params.remark ">
                    
                        AND `remark`=#{params.remark}
                    
                </if>
            
                <if test="null!=params.timeSeq ">
                    
                        AND `time_seq`=#{params.timeSeq}
                    
                </if>
            
                <if test="null!=params.searchCode  and ''!=params.searchCode ">
                    
                        AND `search_code`=#{params.searchCode}
                    
                </if>
            
                <if test="null!=params.address  and ''!=params.address ">
                    
                        AND `address`=#{params.address}
                    
                </if>
            
                <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                    
                        AND `organ_type_no`=#{params.organTypeNo}
                    
                </if>
            
                <if test="null!=params.whetherWholesalePool ">
                    
                        AND `whether_wholesale_pool`=#{params.whetherWholesalePool}
                    
                </if>
            
                <if test="null!=params.postcode  and ''!=params.postcode ">
                    
                        AND `postcode`=#{params.postcode}
                    
                </if>
            
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=companyNo and ''!=companyNo">
            AND `company_no`=#{companyNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM company
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM company
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM company
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM company
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM company
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM company
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM company
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM company
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM company
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.Company"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="companyNo != null">
                `company_no`,
            </if>
            
            <if test="name != null">
                `name`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="bankName != null">
                `bank_name`,
            </if>
            
            <if test="bankAccount != null">
                `bank_account`,
            </if>
            
            <if test="bankAccountName != null">
                `bank_account_name`,
            </if>
            
            <if test="contactName != null">
                `contact_name`,
            </if>
            
            <if test="tel != null">
                `tel`,
            </if>
            
            <if test="taxRegistryNo != null">
                `tax_registry_no`,
            </if>
            
            <if test="taxLevel != null">
                `tax_level`,
            </if>
            
            <if test="legalPerson != null">
                `legal_person`,
            </if>
            
            <if test="identityCard != null">
                `identity_card`,
            </if>
            
            <if test="fax != null">
                `fax`,
            </if>
            
            <if test="email != null">
                `email`,
            </if>
            
            <if test="zoneNo != null">
                `zone_no`,
            </if>
            
            <if test="ncProjectOrganizeNo != null">
                `nc_project_organize_no`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="timeSeq != null">
                `time_seq`,
            </if>
            
            <if test="searchCode != null">
                `search_code`,
            </if>
            
            <if test="address != null">
                `address`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
            <if test="whetherWholesalePool != null">
                `whether_wholesale_pool`,
            </if>
            
            <if test="postcode != null">
                `postcode`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="companyNo != null">
                #{companyNo},
            </if>
            
            <if test="name != null">
                #{name},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="bankName != null">
                #{bankName},
            </if>
            
            <if test="bankAccount != null">
                #{bankAccount},
            </if>
            
            <if test="bankAccountName != null">
                #{bankAccountName},
            </if>
            
            <if test="contactName != null">
                #{contactName},
            </if>
            
            <if test="tel != null">
                #{tel},
            </if>
            
            <if test="taxRegistryNo != null">
                #{taxRegistryNo},
            </if>
            
            <if test="taxLevel != null">
                #{taxLevel},
            </if>
            
            <if test="legalPerson != null">
                #{legalPerson},
            </if>
            
            <if test="identityCard != null">
                #{identityCard},
            </if>
            
            <if test="fax != null">
                #{fax},
            </if>
            
            <if test="email != null">
                #{email},
            </if>
            
            <if test="zoneNo != null">
                #{zoneNo},
            </if>
            
            <if test="ncProjectOrganizeNo != null">
                #{ncProjectOrganizeNo},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="timeSeq != null">
                #{timeSeq},
            </if>
            
            <if test="searchCode != null">
                #{searchCode},
            </if>
            
            <if test="address != null">
                #{address},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
            <if test="whetherWholesalePool != null">
                #{whetherWholesalePool},
            </if>
            
            <if test="postcode != null">
                #{postcode},
            </if>
            
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.Company" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO company (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.companyNo}, #{item.name}, #{item.status}, #{item.bankName}, #{item.bankAccount}, #{item.bankAccountName}, #{item.contactName}, #{item.tel}, #{item.taxRegistryNo}, #{item.taxLevel}, #{item.legalPerson}, #{item.identityCard}, #{item.fax}, #{item.email}, #{item.zoneNo}, #{item.ncProjectOrganizeNo}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime}, #{item.remark}, #{item.timeSeq}, #{item.searchCode}, #{item.address}, #{item.organTypeNo}, #{item.whetherWholesalePool}, #{item.postcode})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.Company">
        UPDATE company
        <set>
            
            <if test="companyNo != null">
                `company_no` = #{companyNo},
            </if> 
            <if test="name != null">
                `name` = #{name},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="bankName != null">
                `bank_name` = #{bankName},
            </if> 
            <if test="bankAccount != null">
                `bank_account` = #{bankAccount},
            </if> 
            <if test="bankAccountName != null">
                `bank_account_name` = #{bankAccountName},
            </if> 
            <if test="contactName != null">
                `contact_name` = #{contactName},
            </if> 
            <if test="tel != null">
                `tel` = #{tel},
            </if> 
            <if test="taxRegistryNo != null">
                `tax_registry_no` = #{taxRegistryNo},
            </if> 
            <if test="taxLevel != null">
                `tax_level` = #{taxLevel},
            </if> 
            <if test="legalPerson != null">
                `legal_person` = #{legalPerson},
            </if> 
            <if test="identityCard != null">
                `identity_card` = #{identityCard},
            </if> 
            <if test="fax != null">
                `fax` = #{fax},
            </if> 
            <if test="email != null">
                `email` = #{email},
            </if> 
            <if test="zoneNo != null">
                `zone_no` = #{zoneNo},
            </if> 
            <if test="ncProjectOrganizeNo != null">
                `nc_project_organize_no` = #{ncProjectOrganizeNo},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if> 
            <if test="searchCode != null">
                `search_code` = #{searchCode},
            </if> 
            <if test="address != null">
                `address` = #{address},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            <if test="whetherWholesalePool != null">
                `whether_wholesale_pool` = #{whetherWholesalePool},
            </if> 
            <if test="postcode != null">
                `postcode` = #{postcode},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE company_no = #{companyNo}
                
    </update>

    <select id="selectByUniques" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM company 
        where company_no in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM company 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <!-- auto generate end-->
</mapper>