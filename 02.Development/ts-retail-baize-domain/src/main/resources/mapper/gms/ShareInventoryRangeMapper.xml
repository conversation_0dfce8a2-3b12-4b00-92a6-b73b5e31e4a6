<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.ShareInventoryRangeRepository" >
  <resultMap id="BaseResultMap" type="ShareInventoryRange" >
    <id column="id" property="id" jdbcType="CHAR" />
    <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
    <result column="item_no" property="itemNo" jdbcType="CHAR" />
    <result column="item_name" property="itemName" jdbcType="VARCHAR" />
    <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
    <result column="brand_no" property="brandNo" jdbcType="CHAR" />
    <result column="size_kind" property="sizeKind" jdbcType="CHAR" />
    <result column="organ_type_no" property="organTypeNo" jdbcType="CHAR" />
    <result column="organ_type_name" property="organTypeName" jdbcType="VARCHAR" />
    <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />
    <result column="sharing_ratio" property="sharingRatio" jdbcType="INTEGER" />
    <result column="barcode" property="barcode" jdbcType="VARCHAR" />
    <result column="sku_no" property="skuNo" jdbcType="CHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="safety_stock" property="safetyStock" jdbcType="INTEGER" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="interface_platform" property="interfacePlatform" jdbcType="VARCHAR" />
    <result column="create_user" property="createUser" jdbcType="VARCHAR" />
    <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, item_code, item_no, item_name, brand_name, brand_no, size_kind,
    organ_type_no, organ_type_name, size_no, sharing_ratio, barcode, sku_no, create_time, 
    update_time,safety_stock,start_time, end_time,interface_platform,create_user,update_user
  </sql>

  <sql id="condition" >
    <if test="null!=params" >
      <if test="params.brandNoList!=null">
			and b.brand_no in
				<foreach collection="params.brandNoList" item="brandNo"
					open="(" close=")" separator=",">
					#{brandNo}
				</foreach>
	  </if>

      <if test="params.barcodeList!=null">
        and b.barcode in
        <foreach collection="params.barcodeList" item="barcode"
                 open="(" close=")" separator=",">
          #{barcode}
        </foreach>
      </if>
	  
	  <if test="params.barcode != null and !''.equals(params.barcode)">
				and b.barcode=#{params.barcode}
	  </if>
	  
	  <if test="params.queryCondition != null and !''.equals(params.queryCondition)">
				${params.queryCondition}
	  </if>
	  
	  <if test="params.sharingRatio != null and !''.equals(params.sharingRatio)">
      	AND b.sharing_ratio = #{params.sharingRatio}
      </if>
      
	  <if test="params.zoneNo != null and !''.equals(params.zoneNo)">
      	AND b.zone_no = #{params.zoneNo}
      </if>
      
	  <if test="params.itemCode != null and !''.equals(params.itemCode)">
				and b.item_code like CONCAT(#{params.itemCode},'%')
	  </if>
	  <if test="params.itemNo != null and !''.equals(params.itemNo)">
				and b.item_no=#{params.itemNo}
	  </if>

	  <if test="params.sizeNo != null and !''.equals(params.sizeNo)">
				and b.size_no=#{params.sizeNo}
	  </if>
			
      <if test="params.brandNo != null and !''.equals(params.brandNo)">
                and b.brand_no=#{params.brandNo}
      </if>
         
	  <if test="params.itemCondition != null and !''.equals(params.itemCondition)">
				and b.item_no in ( select item_no from item where
				${params.itemCondition} )
	  </if>
	  <if test="params.itemNos != null and !''.equals(params.itemNos)">
				AND b.item_no IN 
			      <foreach item="itemNo" index="index" collection="params.itemNos" open="(" separator="," close=")">
                    #{itemNo}
                </foreach>
	  </if>
           
      <if test="params.itemCodes != null and !''.equals(params.itemCodes)">
                AND b.item_code IN 
                  <foreach item="itemCode" index="index" collection="params.itemCodes" open="(" separator="," close=")">
                    #{itemCode}
                </foreach>
      </if>
            
      <if test="params.brandNos != null and !''.equals(params.brandNos)">
                AND b.brand_no IN 
                  <foreach item="brandNo" index="index" collection="params.brandNos" open="(" separator="," close=")">
                    #{brandNo}
                </foreach>
       </if>
      <if test="params.vstoreCodeList != null and !''.equals(params.vstoreCodeList)">
                AND b.interface_platform IN 
                  <foreach item="vstoreCode" index="index" collection="params.vstoreCodeList" open="(" separator="," close=")">
                    #{vstoreCode}
                </foreach>
       </if>
      <if test="params.interfacePlatform != null and !''.equals(params.interfacePlatform)">
            and b.interface_platform=#{params.interfacePlatform}
      </if>
       <if test="params.organTypeNo != null and !''.equals(params.organTypeNo)">
				and b.organ_type_no=#{params.organTypeNo}
	   </if>
	   <if test="params.vstoreNo != null and !''.equals(params.vstoreNo)">
				and b.interface_platform=#{params.vstoreNo}
	  	</if>
	   <if test="params.status != null and params.status != ''">
	   	<choose>
	   		<when test="params.status == 1">
	   			AND NOW() BETWEEN b.start_time and b.end_time
	   		</when>
	   		<when test="params.status == 2">
	   			AND NOW() &lt; b.start_time
	   		</when>
	   		<when test="params.status == 3">
	   			AND NOW() &gt; b.end_time
	   		</when>
	   	</choose> 
	   </if>
    </if>
  </sql>
  
  

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from share_inventory_range
    where id = #{id,jdbcType=CHAR}
  </select>
  <select id="selectCount" resultType="java.lang.Integer" >
      <if test="params.isGroupBy != null and !''.equals(params.isGroupBy)">
          select count(1) from (
          </if>
    select count(1) as s from share_inventory_range b where 1=1 
    <include refid="condition" />
    <if test="params.isGroupBy != null and !''.equals(params.isGroupBy)">
	 group by sku_no ) temp
	  </if>
  </select>
  <select id="selectByPage" resultMap="BaseResultMap" parameterType="map" >
   SELECT
    <include refid="Base_Column_List" />
     from share_inventory_range  b  where 1=1 
    <include refid="condition" />
    <!--<if test="orderByField != null and !''.equals(orderByField)" >
      order by ${orderByField}
      <if test="orderByField" >
        ${orderBy}
      </if>
    </if>
   <if test="params.isGroupBy != null and !''.equals(params.isGroupBy)">
	 group by sku_no
	</if>-->
   LIMIT #{page.startRowNum} ,#{page.pageSize} 
  </select>
 
  <select id="selectByParams" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
     from share_inventory_range b where 1=1 
    <include refid="condition" />
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from share_inventory_range
    where id = #{id,jdbcType=CHAR}
  </delete>
  <delete id="deleteByPrimarayKeyForModel" parameterType="java.lang.String" >
    delete from share_inventory_range
    where id = #{id,jdbcType=CHAR}
  </delete>
 
    <!-- 根据itemCode,zoneNo和sizeNo删除数据  -->
    <delete id="deleteByItemCode" parameterType="map"  >
    delete from share_inventory_range
    where item_Code = #{itemCode,jdbcType=VARCHAR} and size_no=#{sizeNo,jdbcType=VARCHAR}
    and zone_no=#{zoneNo,jdbcType=VARCHAR}
  </delete>
 
  
  <insert id="insert" parameterType="ShareInventoryRange" >
    insert into share_inventory_range (id, item_code, item_no, 
      item_name, brand_name, brand_no, 
      size_kind, 
      organ_type_no, organ_type_name, size_no, 
      sharing_ratio,safety_stock, barcode, sku_no, 
      create_time, update_time,start_time, end_time,interface_platform,create_user,update_user)
    values (#{id,jdbcType=CHAR}, #{itemCode,jdbcType=VARCHAR}, #{itemNo,jdbcType=CHAR}, 
      #{itemName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{brandNo,jdbcType=CHAR}, 
      #{sizeKind,jdbcType=CHAR}, 
      #{organTypeNo,jdbcType=CHAR}, #{organTypeName,jdbcType=VARCHAR}, #{sizeNo,jdbcType=VARCHAR}, 
      #{sharingRatio,jdbcType=INTEGER},#{safetyStock,jdbcType=INTEGER}, #{barcode,jdbcType=VARCHAR}, #{skuNo,jdbcType=CHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},#{interfacePlatform,jdbcType=VARCHAR},
      #{createUser,jdbcType=VARCHAR},#{updateUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="ShareInventoryRange" >
    insert into share_inventory_range
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="itemCode != null" >
        item_code,
      </if>
      <if test="itemNo != null" >
        item_no,
      </if>
      <if test="itemName != null" >
        item_name,
      </if>
      <if test="brandName != null" >
        brand_name,
      </if>
      <if test="brandNo != null" >
        brand_no,
      </if>
      <if test="sizeKind != null" >
        size_kind,
      </if>
      
      <if test="organTypeNo != null" >
        organ_type_no,
      </if>
      <if test="organTypeName != null" >
        organ_type_name,
      </if>
      <if test="sizeNo != null" >
        size_no,
      </if>
      <if test="sharingRatio != null" >
        sharing_ratio,
      </if>
        <if test="safetyStock != null" >
        safety_stock,
      </if>
      <if test="barcode != null" >
        barcode,
      </if>
      <if test="skuNo != null" >
        sku_no,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
       <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="interfacePlatform != null" >
        interface_platform,
      </if>
      <if test="createUser != null" >
        create_user,
      </if>
      <if test="updateUser != null" >
        update_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=CHAR},
      </if>
      <if test="itemCode != null" >
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null" >
        #{itemNo,jdbcType=CHAR},
      </if>
      <if test="itemName != null" >
        #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandNo != null" >
        #{brandNo,jdbcType=CHAR},
      </if>
      <if test="sizeKind != null" >
        #{sizeKind,jdbcType=CHAR},
      </if>
      
      <if test="organTypeNo != null" >
        #{organTypeNo,jdbcType=CHAR},
      </if>
      <if test="organTypeName != null" >
        #{organTypeName,jdbcType=VARCHAR},
      </if>
      <if test="sizeNo != null" >
        #{sizeNo,jdbcType=VARCHAR},
      </if>
      <if test="sharingRatio != null" >
        #{sharingRatio,jdbcType=INTEGER},
      </if>
       <if test="safetyStock != null" >
        #{safetyStock,jdbcType=INTEGER},
      </if>
      <if test="barcode != null" >
        #{barcode,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null" >
        #{skuNo,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
       <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="interfacePlatform != null" >
        #{interfacePlatform,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null" >
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null" >
        #{updateUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="ShareInventoryRange" >
    update share_inventory_range
    <set >
      <if test="itemCode != null" >
        item_code = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="itemNo != null" >
        item_no = #{itemNo,jdbcType=CHAR},
      </if>
      <if test="itemName != null" >
        item_name = #{itemName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandNo != null" >
        brand_no = #{brandNo,jdbcType=CHAR},
      </if>
      <if test="sizeKind != null" >
        size_kind = #{sizeKind,jdbcType=CHAR},
      </if>
     
      <if test="organTypeNo != null" >
        organ_type_no = #{organTypeNo,jdbcType=CHAR},
      </if>
      <if test="organTypeName != null" >
        organ_type_name = #{organTypeName,jdbcType=VARCHAR},
      </if>
      <if test="sizeNo != null" >
        size_no = #{sizeNo,jdbcType=VARCHAR},
      </if>
      <if test="sharingRatio != null" >
        sharing_ratio = #{sharingRatio,jdbcType=INTEGER},
      </if>
       <if test="safetyStock != null" >
        safety_stock = #{safetyStock,jdbcType=INTEGER},
      </if>
      <if test="barcode != null" >
        barcode = #{barcode,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null" >
        sku_no = #{skuNo,jdbcType=CHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
        <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="interfacePlatform != null" >
        interface_platform = #{interfacePlatform,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null" >
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null" >
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="ShareInventoryRange" >
    update share_inventory_range
    set item_code = #{itemCode,jdbcType=VARCHAR},
      item_no = #{itemNo,jdbcType=CHAR},
      item_name = #{itemName,jdbcType=VARCHAR},
      brand_name = #{brandName,jdbcType=VARCHAR},
      brand_no = #{brandNo,jdbcType=CHAR},
      size_kind = #{sizeKind,jdbcType=CHAR},
      organ_type_no = #{organTypeNo,jdbcType=CHAR},
      organ_type_name = #{organTypeName,jdbcType=VARCHAR},
      size_no = #{sizeNo,jdbcType=VARCHAR},
      sharing_ratio = #{sharingRatio,jdbcType=INTEGER},
      safety_stock = #{safetyStock,jdbcType=INTEGER},
      barcode = #{barcode,jdbcType=VARCHAR},
      sku_no = #{skuNo,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      interface_platform = #{interfacePlatform,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=CHAR}
  </update>
  
  
   <!-- 查询店铺编码是否已存在   店铺编码唯一 -->
  <select id="selectShareNoExist" resultType="java.lang.Integer">
    SELECT 
    COUNT(1) as s 
   FROM share_inventory_range
    WHERE 1 = 1 
    	<if test="params.itemCode != null and !''.equals(params.itemCode)" >
        	and item_code = #{params.itemCode}
        </if>
      	<if test="params.sizeNo != null and !''.equals(params.sizeNo)" >
       		and size_no = #{params.sizeNo}
      	</if>
      	<if test="params.brandNo != null and !''.equals(params.brandNo)" >
       		and brand_no = #{params.brandNo}
      	</if>
      	<if test="params.interfacePlatform != null and !''.equals(params.interfacePlatform)" >
       		and interface_platform = #{params.interfacePlatform}
      	</if>
  </select>

  <select id="findExistInBrandInventoryRange" resultType="java.lang.Integer" parameterType="map">
    SELECT
      count(sku.id)
    FROM
      item i
        INNER JOIN item_sku sku ON sku.item_no = i.item_no
        INNER JOIN brand b ON i.brand_no = b.brand_no
        LEFT JOIN organ_type o ON b.organ_type_no = o.organ_type_no
        INNER JOIN brand_inventory_range s ON i.years=s.years AND i.purchase_season=s.purchase_season AND i.brand_no=s.brand_no
    WHERE
      i.CODE = #{params.code,jdbcType=VARCHAR}
      AND i.brand_no = #{params.brandNo,jdbcType=VARCHAR}
      AND s.bussiness_type=#{params.interfacePlatform,jdbcType=VARCHAR}
      AND sku.size_no = #{params.sizeNo,jdbcType=VARCHAR}
  </select>
  
  
  
 <update id="updateShare" parameterType="map" >
    update share_inventory_range
    set 
       sharing_ratio = #{params.sharingRatio,jdbcType=INTEGER},
       safety_stock = #{params.safetyStock,jdbcType=INTEGER},
       end_time = #{params.endTime,jdbcType=TIMESTAMP},
       start_time = #{params.startTime,jdbcType=TIMESTAMP},
	   update_time = #{params.updateTime,jdbcType=TIMESTAMP},
	   update_user = #{params.updateUser,jdbcType=VARCHAR}
    where item_code = #{params.itemCode,jdbcType=VARCHAR}
    and size_no = #{params.sizeNo,jdbcType=VARCHAR}
    and interface_platform = #{params.interfacePlatform,jdbcType=VARCHAR}
 </update>


  <delete id="deleteByParams" parameterType="map">
    DELETE
    FROM share_inventory_range
    <where>
      <include refid="condition" />
      <if test="params.ids!=null and ''!=params.ids ">
        AND id in ( ${params.ids} )
      </if>
    </where>
  </delete>


  <insert id="batchInsert" parameterType="java.util.List">
    insert into share_inventory_range (id, item_code, item_no,item_name, brand_name, brand_no, size_kind,organ_type_no,
    organ_type_name, size_no, sharing_ratio,safety_stock, barcode, sku_no,
    create_time, update_time,start_time, end_time,interface_platform,create_user,update_user)
    values
    <foreach collection="datas" item="data" separator=",">
      (#{data.id,jdbcType=CHAR}, #{data.itemCode,jdbcType=VARCHAR}, #{data.itemNo,jdbcType=CHAR},
      #{data.itemName,jdbcType=VARCHAR}, #{data.brandName,jdbcType=VARCHAR}, #{data.brandNo,jdbcType=CHAR},
      #{data.sizeKind,jdbcType=CHAR}, #{data.organTypeNo,jdbcType=CHAR}, #{data.organTypeName,jdbcType=VARCHAR},
      #{data.sizeNo,jdbcType=VARCHAR}, #{data.sharingRatio,jdbcType=INTEGER},#{data.safetyStock,jdbcType=INTEGER},
      #{data.barcode,jdbcType=VARCHAR}, #{data.skuNo,jdbcType=CHAR}, #{data.createTime,jdbcType=TIMESTAMP},
      #{data.updateTime,jdbcType=TIMESTAMP}, #{data.startTime,jdbcType=TIMESTAMP}, #{data.endTime,jdbcType=TIMESTAMP},
      #{data.interfacePlatform,jdbcType=VARCHAR}, #{data.createUser,jdbcType=VARCHAR},#{data.updateUser,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    update share_inventory_range
    <set>
      <trim prefix="sharing_ratio =case " suffix="end,">
        <foreach collection="datas" item="data" index="index">
          when
          id=#{data.id} then #{data.sharingRatio}
        </foreach>
      </trim>
      <trim prefix="safety_stock =case " suffix="end,">
        <foreach collection="datas" item="data" index="index">
          when
          id=#{data.id} then #{data.safetyStock}
        </foreach>
      </trim>
      <trim prefix="end_time =case " suffix="end,">
        <foreach collection="datas" item="data" index="index">
          when
          id=#{data.id} then #{data.endTime}
        </foreach>
      </trim>
      <trim prefix="start_time =case " suffix="end,">
        <foreach collection="datas" item="data" index="index">
          when
          id=#{data.id} then #{data.startTime}
        </foreach>
      </trim>
      <trim prefix="update_time =case " suffix="end,">
        <foreach collection="datas" item="data" index="index">
          when
          id=#{data.id} then #{data.updateTime}
        </foreach>
      </trim>
      <trim prefix="update_user =case " suffix="end,">
        <foreach collection="datas" item="data" index="index">
          when
          id=#{data.id} then #{data.updateUser}
        </foreach>
      </trim>
      <trim prefix="interface_platform =case " suffix="end,">
        <foreach collection="datas" item="data" index="index">
          when
          id=#{data.id} then #{data.interfacePlatform}
        </foreach>
      </trim>
    </set>
    where id in
    <foreach collection="datas" index="index" item="data"
             separator="," open="(" close=")">
      #{data.id}
    </foreach>
  </update>


</mapper>