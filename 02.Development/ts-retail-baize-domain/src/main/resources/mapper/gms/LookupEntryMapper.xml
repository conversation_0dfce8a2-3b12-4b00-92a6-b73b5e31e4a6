<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.LookupEntryRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.LookupEntry">
                
        <id column="lookup_entry_no" property="lookupEntryNo" jdbcType="CHAR" />
        
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="lookup_id" property="lookupId" jdbcType="INTEGER" />
        
        <result column="code" property="code" jdbcType="VARCHAR" />
        
        <result column="opcode" property="opcode" jdbcType="VARCHAR" />
        
        <result column="name" property="name" jdbcType="VARCHAR" />
        
        <result column="lookup_entry_name_hk" property="lookupEntryNameHk" jdbcType="VARCHAR" />
        
        <result column="lookup_entry_name_en" property="lookupEntryNameEn" jdbcType="VARCHAR" />
        
        <result column="order_no" property="orderNo" jdbcType="INTEGER" />
        
        <result column="type" property="type" jdbcType="CHAR" />
        
        <result column="default_flag" property="defaultFlag" jdbcType="CHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="time_seq" property="timeSeq" jdbcType="BIGINT" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `status`,`lookup_entry_no`,`lookup_id`,`code`,`opcode`,`name`,`lookup_entry_name_hk`,`lookup_entry_name_en`,`order_no`,`type`,`default_flag`,`create_user`,`create_time`,`update_user`,`update_time`,`time_seq`,`organ_type_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
                <if test="null!=params.status ">
                    
                        AND `status`=#{params.status}
                    
                </if>
            
                <if test="null!=params.lookupEntryNo  and ''!=params.lookupEntryNo ">
                    
                        AND `lookup_entry_no`=#{params.lookupEntryNo}
                    
                </if>
            
                <if test="null!=params.lookupId ">
                    
                        AND `lookup_id`=#{params.lookupId}
                    
                </if>
            
                <if test="null!=params.code  and ''!=params.code ">
                    
                        AND `code`=#{params.code}
                    
                </if>
            
                <if test="null!=params.opcode  and ''!=params.opcode ">
                    
                        AND `opcode`=#{params.opcode}
                    
                </if>
            
                <if test="null!=params.name  and ''!=params.name ">
                    
                        AND `name` like CONCAT('%',#{params.name},'%') 
                    
                </if>
            
                <if test="null!=params.lookupEntryNameHk  and ''!=params.lookupEntryNameHk ">
                    
                        AND `lookup_entry_name_hk` like CONCAT('%',#{params.lookupEntryNameHk},'%') 
                    
                </if>
            
                <if test="null!=params.lookupEntryNameEn  and ''!=params.lookupEntryNameEn ">
                    
                        AND `lookup_entry_name_en` like CONCAT('%',#{params.lookupEntryNameEn},'%') 
                    
                </if>
            
                <if test="null!=params.orderNo ">
                    
                        AND `order_no`=#{params.orderNo}
                    
                </if>
            
                <if test="null!=params.type  and ''!=params.type ">
                    
                        AND `type`=#{params.type}
                    
                </if>
            
                <if test="null!=params.defaultFlag  and ''!=params.defaultFlag ">
                    
                        AND `default_flag`=#{params.defaultFlag}
                    
                </if>
            
                <if test="null!=params.createUser  and ''!=params.createUser ">
                    
                        AND `create_user`=#{params.createUser}
                    
                </if>
            
                <if test="null!=params.createTime ">
                    
                        AND `create_time`=#{params.createTime}
                    
                </if>
            
                <if test="null!=params.updateUser  and ''!=params.updateUser ">
                    
                        AND `update_user`=#{params.updateUser}
                    
                </if>
            
                <if test="null!=params.updateTime ">
                    
                        AND `update_time`=#{params.updateTime}
                    
                </if>
            
                <if test="null!=params.timeSeq ">
                    
                        AND `time_seq`=#{params.timeSeq}
                    
                </if>
            
                <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                    
                        AND `organ_type_no`=#{params.organTypeNo}
                    
                </if>
            
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM lookup_entry
        WHERE lookup_entry_no = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM lookup_entry
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM lookup_entry
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM lookup_entry
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM lookup_entry
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM lookup_entry
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM lookup_entry
        WHERE lookup_entry_no = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM lookup_entry
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM lookup_entry
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND lookup_entry_no in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.LookupEntry"  >
        INSERT INTO lookup_entry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="lookupEntryNo != null">
                `lookup_entry_no`,
            </if>
            
            <if test="lookupId != null">
                `lookup_id`,
            </if>
            
            <if test="code != null">
                `code`,
            </if>
            
            <if test="opcode != null">
                `opcode`,
            </if>
            
            <if test="name != null">
                `name`,
            </if>
            
            <if test="lookupEntryNameHk != null">
                `lookup_entry_name_hk`,
            </if>
            
            <if test="lookupEntryNameEn != null">
                `lookup_entry_name_en`,
            </if>
            
            <if test="orderNo != null">
                `order_no`,
            </if>
            
            <if test="type != null">
                `type`,
            </if>
            
            <if test="defaultFlag != null">
                `default_flag`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="timeSeq != null">
                `time_seq`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="lookupEntryNo != null">
                #{lookupEntryNo},
            </if>
            
            <if test="lookupId != null">
                #{lookupId},
            </if>
            
            <if test="code != null">
                #{code},
            </if>
            
            <if test="opcode != null">
                #{opcode},
            </if>
            
            <if test="name != null">
                #{name},
            </if>
            
            <if test="lookupEntryNameHk != null">
                #{lookupEntryNameHk},
            </if>
            
            <if test="lookupEntryNameEn != null">
                #{lookupEntryNameEn},
            </if>
            
            <if test="orderNo != null">
                #{orderNo},
            </if>
            
            <if test="type != null">
                #{type},
            </if>
            
            <if test="defaultFlag != null">
                #{defaultFlag},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="timeSeq != null">
                #{timeSeq},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.LookupEntry" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO lookup_entry (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.status}, #{item.lookupEntryNo}, #{item.lookupId}, #{item.code}, #{item.opcode}, #{item.name}, #{item.lookupEntryNameHk}, #{item.lookupEntryNameEn}, #{item.orderNo}, #{item.type}, #{item.defaultFlag}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime}, #{item.timeSeq}, #{item.organTypeNo})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.LookupEntry">
        UPDATE lookup_entry
        <set>
            
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="lookupId != null">
                `lookup_id` = #{lookupId},
            </if> 
            <if test="code != null">
                `code` = #{code},
            </if> 
            <if test="opcode != null">
                `opcode` = #{opcode},
            </if> 
            <if test="name != null">
                `name` = #{name},
            </if> 
            <if test="lookupEntryNameHk != null">
                `lookup_entry_name_hk` = #{lookupEntryNameHk},
            </if> 
            <if test="lookupEntryNameEn != null">
                `lookup_entry_name_en` = #{lookupEntryNameEn},
            </if> 
            <if test="orderNo != null">
                `order_no` = #{orderNo},
            </if> 
            <if test="type != null">
                `type` = #{type},
            </if> 
            <if test="defaultFlag != null">
                `default_flag` = #{defaultFlag},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE lookup_entry_no = #{lookupEntryNo}
                
    </update>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM lookup_entry 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <!-- auto generate end-->
</mapper>