<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetSearchInventoryRepository">


    <resultMap id="InternetResultMap" type="cn.wonhigh.baize.model.entity.gms.InternetAvailableInventory">
        <result column="available_qty" property="availableQty" jdbcType="INTEGER" />
        <result column="balance_qty" property="balanceQty" jdbcType="INTEGER" />
        <result column="level" property="level" jdbcType="INTEGER" />
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        <result column="store_no" property="storeNo" jdbcType="VARCHAR" />
        <result column="order_unit_no" property="orderUnitNo" jdbcType="VARCHAR" />
        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR" />
        <result column="company_no" property="companyNo" jdbcType="VARCHAR" />
        <result column="sharding_flag" property="shardingFlag" jdbcType="VARCHAR" />
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        <result column="sku_list" property="skuList" jdbcType="VARCHAR" />
        <result column="sku_qty_list" property="skuQtyList" jdbcType="VARCHAR" />
        <result column="un_share_qty" property="unShareQty" jdbcType="INTEGER"/>
        <result column="item_no" property="itemNo" jdbcType="VARCHAR" />
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
        <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />
        <result column="brand_no" property="brandNo" jdbcType="VARCHAR" />
        <result column="address" property="shopAddress" jdbcType="VARCHAR" />
        <result column="tel" property="shopTel" jdbcType="VARCHAR" />
        <result column="short_name" property="shopName" jdbcType="VARCHAR" />
        <result column="store_type" property="storeType" jdbcType="VARCHAR" />
    </resultMap>

    <select id="findAvailableInOneStore" resultMap="InternetResultMap" parameterType="map">
        SELECT i.order_unit_no,
               i.store_no,
               i.sku_no,
               i.item_code,
               i.brand_no,
               i.size_no,
               i.available_qty2 AS available_qty
        FROM inventory i
                 INNER JOIN order_unit o ON i.order_unit_no = o.order_unit_no
        WHERE i.store_no = #{storeNo}
          AND i.order_unit_no = #{orderUnitNo}
        <choose>
            <when test="skuNoList != null">
                AND i.sku_no IN
                <foreach item="item" collection="skuNoList" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                AND i.sku_no = #{skuNo}
            </otherwise>
        </choose>

        <choose>
            <when test="terminal != null and terminal == @<EMAIL>()">
                AND EXISTS (SELECT 1
                            FROM external_product_mapping epm
                            WHERE epm.product_code = i.item_code
                              AND epm.brand_code = i.brand_no
                              and epm.size_code = i.size_no
                              and epm.merchants_code = #{merchantsCode})
            </when>
            <otherwise>
                AND EXISTS (SELECT 1
                            FROM tbl_commodity_corp_match_product irg
                            WHERE irg.style_color_code = i.item_code
                              AND irg.corp_brand_no = i.brand_no
                              and irg.corp_size_code = i.size_no)
            </otherwise>
        </choose>

        GROUP BY i.sku_no;
    </select>


    <select id="queryVstoreQtyDetail" resultType="cn.wonhigh.baize.model.entity.gms.VstoreStoreAvailableInventory">
        SELECT
            scp.vstore_code vstoreCode,
            wh.vstore_name vstoreName,
            v.store_no storeNo,
            v.store_name storeName,
            v.order_unit_no orderUnitNo,
            v.order_unit_name orderUnitName,
            v.brand_no brandNo,
            v.item_code itemCode,
            v.size_no sizeNo,
            v.sku_no skuNo,
            v.available_qty2 availableQty,
            IF(bi.id IS NULL,0,bi.un_qty) defectiveGoodsQty,
            v.work_qty workQty,
            IF(shop.id IS NULL,0,1) unavailableShopFlag,
            IF(io.id IS NULL,0,1) refuseRecordFlag,
            score.credit_score creditScore
        FROM internet_virtual_warehouse_scope scp
        INNER JOIN internet_virtual_warehouse_info wh on wh.vstore_code = scp.vstore_code
        INNER JOIN inventory v ON scp.store_no=v.store_no
            AND scp.order_unit_no=v.order_unit_no
            AND scp.inventory_type=1
            AND scp.status=1
            AND scp.more_store_flag=0
        LEFT JOIN internet_unavailable_shop shop ON shop.shop_no = v.store_no
            AND shop.`status` = 1
            AND shop.type = 1
        LEFT JOIN bill_internet_shop_unavailable_inventory bi ON bi.shop_no = v.store_no
            AND bi.brand_no = v.brand_no and v.sku_no = bi.sku_no
        LEFT JOIN internet_orders_refuse_record io ON io.shop_no = v.store_no
            AND io.order_unit_no = v.order_unit_no
            AND io.sku_no = v.sku_no
            AND io.isvalid = 1
            AND v.stock_in_date &lt;= io.refuse_time
        LEFT JOIN ics_shop_credit_score score ON score.shop_no = v.store_no AND score.status = 1
        WHERE
            v.available_qty2 &gt; 0
            <foreach collection="vstoreCodeList" item="vstoreCode" open="AND scp.vstore_code in (" close=")" separator=",">
                #{vstoreCode}
            </foreach>
            <foreach collection="skuNoList" item="skuNo" open="AND v.sku_no in (" close=")" separator=",">
                #{skuNo}
            </foreach>
        order by scp.vstore_code,v.store_no,v.order_unit_no,v.sku_no desc
        LIMIT ${startRowNum},${pageSize}
    </select>


    <select id="countVstoreQty" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM internet_virtual_warehouse_scope scp
        INNER JOIN internet_virtual_warehouse_info wh on wh.vstore_code = scp.vstore_code
        INNER JOIN inventory v ON scp.store_no=v.store_no
            AND scp.order_unit_no=v.order_unit_no
            AND scp.inventory_type=1
            AND scp.status=1
            AND scp.more_store_flag=0
        WHERE
        v.available_qty2 &gt; 0
        <foreach collection="vstoreCodeList" item="vstoreCode" open="AND scp.vstore_code in (" close=")" separator=",">
            #{vstoreCode}
        </foreach>
        <foreach collection="skuNoList" item="skuNo" open="AND v.sku_no in (" close=")" separator=",">
            #{skuNo}
        </foreach>
    </select>
</mapper>