<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.IcsActiveLockAdjustRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust">
                
        <id column="id" property="id" jdbcType="CHAR" />	
        
        <result column="adjust_type" property="adjustType" jdbcType="TINYINT" />	
        <result column="remark" property="remark" jdbcType="VARCHAR" />	
        <result column="sync_status" property="syncStatus" jdbcType="TINYINT" />	
        <result column="adjust_status" property="adjustStatus" jdbcType="TINYINT" />	
        <result column="ref_bill_no" property="refBillNo" jdbcType="CHAR" />	
        <result column="bill_no" property="billNo" jdbcType="CHAR" />	
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />	
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />	
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />	
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />	
    </resultMap>

    <sql id="column_list">
        `adjust_type`,`remark`,`sync_status`,`adjust_status`,`ref_bill_no`,`bill_no`,`id`,`create_user`,`create_time`,`update_user`,`update_time`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
        
        	<if test="null!=params.adjustType ">
				 AND `adjust_type`=#{params.adjustType}
            </if>	
        	<if test="null!=params.remark  and ''!=params.remark ">
				 AND `remark`=#{params.remark}
            </if>	
        	<if test="null!=params.syncStatus ">
				 AND `sync_status`=#{params.syncStatus}
            </if>	
        	<if test="null!=params.adjustStatus ">
				 AND `adjust_status`=#{params.adjustStatus}
            </if>	
        	<if test="null!=params.refBillNo  and ''!=params.refBillNo ">
				 AND `ref_bill_no`=#{params.refBillNo}
            </if>	
        	<if test="null!=params.billNo  and ''!=params.billNo ">
				 AND `bill_no`=#{params.billNo}
            </if>	
        	<if test="null!=params.id  and ''!=params.id ">
				 AND `id`=#{params.id}
            </if>	
        	<if test="null!=params.createUser  and ''!=params.createUser ">
				 AND `create_user`=#{params.createUser}
            </if>	
        	<if test="null!=params.createTime ">
				 AND `create_time`=#{params.createTime}
            </if>	
        	<if test="null!=params.updateUser  and ''!=params.updateUser ">
				 AND `update_user`=#{params.updateUser}
            </if>	
        	<if test="null!=params.updateTime ">
				 AND `update_time`=#{params.updateTime}
            </if>	

            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=billNo and ''!=billNo">
            AND `bill_no`=#{billNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM ics_active_lock_adjust
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust
        <where>
            <include refid="condition" />
        </where>
        <choose>
        	<when test="orderby != null and ''!=orderby">
        		ORDER BY ${orderby}
        	</when>
        	<otherwise>
        		ORDER BY update_time DESC
        	</otherwise>
        </choose>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM ics_active_lock_adjust
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM ics_active_lock_adjust
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM ics_active_lock_adjust
        <where>
            <include refid="uniqe_condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM ics_active_lock_adjust
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust"  >
        INSERT INTO ics_active_lock_adjust
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="adjustType != null">
                `adjust_type`,
            </if>
            <if test="remark != null">
                `remark`,
            </if>
            <if test="syncStatus != null">
                `sync_status`,
            </if>
            <if test="adjustStatus != null">
                `adjust_status`,
            </if>
            <if test="refBillNo != null">
                `ref_bill_no`,
            </if>
            <if test="billNo != null">
                `bill_no`,
            </if>
            <if test="id != null">
                `id`,
            </if>
            <if test="createUser != null">
                `create_user`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="updateUser != null">
                `update_user`,
            </if>
            <if test="updateTime != null">
                `update_time`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="adjustType != null">
                #{adjustType},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="syncStatus != null">
                #{syncStatus},
            </if>
            <if test="adjustStatus != null">
                #{adjustStatus},
            </if>
            <if test="refBillNo != null">
                #{refBillNo},
            </if>
            <if test="billNo != null">
                #{billNo},
            </if>
            <if test="id != null">
                #{id},
            </if>
            <if test="createUser != null">
                #{createUser},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ics_active_lock_adjust (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.adjustType}, #{item.remark}, #{item.syncStatus}, #{item.adjustStatus}, #{item.refBillNo}, #{item.billNo}, #{item.id}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust">
        UPDATE ics_active_lock_adjust
        <set>
            
            <if test="adjustType != null">
                `adjust_type` = #{adjustType},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="syncStatus != null">
                `sync_status` = #{syncStatus},
            </if> 
            <if test="adjustStatus != null">
                `adjust_status` = #{adjustStatus},
            </if> 
            <if test="refBillNo != null">
                `ref_bill_no` = #{refBillNo},
            </if>
        </set>
        
        
        WHERE bill_no = #{billNo}
                
    </update>

    <select id="selectByUniques" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_active_lock_adjust 
        where bill_no in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_active_lock_adjust 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <update id="updateAdjustStatus" parameterType="map">
        UPDATE ics_active_lock_adjust
        SET adjust_status = #{status}
        WHERE bill_no = #{billNo}
    </update>

    <!-- auto generate end-->
</mapper>