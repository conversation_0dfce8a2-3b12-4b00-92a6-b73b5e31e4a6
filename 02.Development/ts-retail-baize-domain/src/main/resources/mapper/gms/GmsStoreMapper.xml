<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.GmsStoreRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.GmsStore">
                
        <id column="id" property="id" jdbcType="INTEGER" />
        
        
        <result column="time_seq" property="timeSeq" jdbcType="BIGINT" />
        
        <result column="search_code" property="searchCode" jdbcType="VARCHAR" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="virt_phy_storage_type" property="virtPhyStorageType" jdbcType="TINYINT" />
        
        <result column="storage_type" property="storageType" jdbcType="VARCHAR" />
        
        <result column="store_type" property="storeType" jdbcType="TINYINT" />
        
        <result column="fax" property="fax" jdbcType="VARCHAR" />
        
        <result column="tel" property="tel" jdbcType="VARCHAR" />
        
        <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
        
        <result column="zip_code" property="zipCode" jdbcType="VARCHAR" />
        
        <result column="address" property="address" jdbcType="VARCHAR" />
        
        <result column="zone_no" property="zoneNo" jdbcType="CHAR" />
        
        <result column="county_no" property="countyNo" jdbcType="VARCHAR" />
        
        <result column="city_no" property="cityNo" jdbcType="VARCHAR" />
        
        <result column="province_no" property="provinceNo" jdbcType="VARCHAR" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="sys_no" property="sysNo" jdbcType="VARCHAR" />
        
        <result column="full_name" property="fullName" jdbcType="VARCHAR" />
        
        <result column="short_name" property="shortName" jdbcType="VARCHAR" />
        
        <result column="store_code" property="storeCode" jdbcType="VARCHAR" />
        
        <result column="parent_no" property="parentNo" jdbcType="CHAR" />
        
        <result column="area" property="area" jdbcType="DECIMAL" />
        
        <result column="store_no" property="storeNo" jdbcType="CHAR" />
        
    </resultMap>

    <sql id="column_list">
        `time_seq`,`search_code`,`remark`,`update_time`,`update_user`,`create_time`,`create_user`,`virt_phy_storage_type`,`storage_type`,`store_type`,`fax`,`tel`,`contact_name`,`zip_code`,`address`,`zone_no`,`county_no`,`city_no`,`province_no`,`status`,`sys_no`,`full_name`,`short_name`,`store_code`,`parent_no`,`area`,`store_no`,`id`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            <if test="null!=params.q and ''!=params.q">
                AND (`store_no` like CONCAT('%',#{params.q},'%') or `short_name` like CONCAT('%',#{params.q},'%'))
            </if>
            <if test="null!=params.timeSeq ">
                
                AND `time_seq`=#{params.timeSeq}
                
            </if>
            
            <if test="null!=params.searchCode  and ''!=params.searchCode ">
                
                AND `search_code`=#{params.searchCode}
                
            </if>
            
            <if test="null!=params.remark  and ''!=params.remark ">
                
                AND `remark`=#{params.remark}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.virtPhyStorageType ">
                
                AND `virt_phy_storage_type`=#{params.virtPhyStorageType}
                
            </if>
            
            <if test="null!=params.storageType  and ''!=params.storageType ">
                
                AND `storage_type`=#{params.storageType}
                
            </if>
            
            <if test="null!=params.storeType ">
                
                AND `store_type`=#{params.storeType}
                
            </if>
            
            <if test="null!=params.fax  and ''!=params.fax ">
                
                AND `fax`=#{params.fax}
                
            </if>
            
            <if test="null!=params.tel  and ''!=params.tel ">
                
                AND `tel`=#{params.tel}
                
            </if>
            
            <if test="null!=params.contactName  and ''!=params.contactName ">
                
                AND `contact_name` like CONCAT('%',#{params.contactName},'%') 
                
            </if>
            
            <if test="null!=params.zipCode  and ''!=params.zipCode ">
                
                AND `zip_code`=#{params.zipCode}
                
            </if>
            
            <if test="null!=params.address  and ''!=params.address ">
                
                AND `address`=#{params.address}
                
            </if>
            
            <if test="null!=params.zoneNo  and ''!=params.zoneNo ">
                
                AND `zone_no`=#{params.zoneNo}
                
            </if>
            
            <if test="null!=params.countyNo  and ''!=params.countyNo ">
                
                AND `county_no`=#{params.countyNo}
                
            </if>
            
            <if test="null!=params.cityNo  and ''!=params.cityNo ">
                
                AND `city_no`=#{params.cityNo}
                
            </if>
            
            <if test="null!=params.provinceNo  and ''!=params.provinceNo ">
                
                AND `province_no`=#{params.provinceNo}
                
            </if>
            
            <if test="null!=params.status ">
                
                AND `status`=#{params.status}
                
            </if>
            
            <if test="null!=params.sysNo  and ''!=params.sysNo ">
                
                AND `sys_no`=#{params.sysNo}
                
            </if>
            
            <if test="null!=params.fullName  and ''!=params.fullName ">
                
                AND `full_name` like CONCAT('%',#{params.fullName},'%') 
                
            </if>
            
            <if test="null!=params.shortName  and ''!=params.shortName ">
                
                AND `short_name` like CONCAT('%',#{params.shortName},'%') 
                
            </if>
            
            <if test="null!=params.storeCode  and ''!=params.storeCode ">
                
                AND `store_code`=#{params.storeCode}
                
            </if>
            
            <if test="null!=params.parentNo  and ''!=params.parentNo ">
                
                AND `parent_no`=#{params.parentNo}
                
            </if>
            
            <if test="null!=params.area ">
                
                AND `area`=#{params.area}
                
            </if>
            
            <if test="null!=params.storeNo  and ''!=params.storeNo ">
                
                AND `store_no`=#{params.storeNo}
                
            </if>
            
            <if test="null!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=storeNo and ''!=storeNo">
            AND `store_no`=#{storeNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM store
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM store
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM store
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM store
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM store
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM store
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM store
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM store
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM store
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.GmsStore"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="timeSeq != null">
                `time_seq`,
            </if>
            
            <if test="searchCode != null">
                `search_code`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="virtPhyStorageType != null">
                `virt_phy_storage_type`,
            </if>
            
            <if test="storageType != null">
                `storage_type`,
            </if>
            
            <if test="storeType != null">
                `store_type`,
            </if>
            
            <if test="fax != null">
                `fax`,
            </if>
            
            <if test="tel != null">
                `tel`,
            </if>
            
            <if test="contactName != null">
                `contact_name`,
            </if>
            
            <if test="zipCode != null">
                `zip_code`,
            </if>
            
            <if test="address != null">
                `address`,
            </if>
            
            <if test="zoneNo != null">
                `zone_no`,
            </if>
            
            <if test="countyNo != null">
                `county_no`,
            </if>
            
            <if test="cityNo != null">
                `city_no`,
            </if>
            
            <if test="provinceNo != null">
                `province_no`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="sysNo != null">
                `sys_no`,
            </if>
            
            <if test="fullName != null">
                `full_name`,
            </if>
            
            <if test="shortName != null">
                `short_name`,
            </if>
            
            <if test="storeCode != null">
                `store_code`,
            </if>
            
            <if test="parentNo != null">
                `parent_no`,
            </if>
            
            <if test="area != null">
                `area`,
            </if>
            
            <if test="storeNo != null">
                `store_no`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="timeSeq != null">
                #{timeSeq},
            </if>
            
            <if test="searchCode != null">
                #{searchCode},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="virtPhyStorageType != null">
                #{virtPhyStorageType},
            </if>
            
            <if test="storageType != null">
                #{storageType},
            </if>
            
            <if test="storeType != null">
                #{storeType},
            </if>
            
            <if test="fax != null">
                #{fax},
            </if>
            
            <if test="tel != null">
                #{tel},
            </if>
            
            <if test="contactName != null">
                #{contactName},
            </if>
            
            <if test="zipCode != null">
                #{zipCode},
            </if>
            
            <if test="address != null">
                #{address},
            </if>
            
            <if test="zoneNo != null">
                #{zoneNo},
            </if>
            
            <if test="countyNo != null">
                #{countyNo},
            </if>
            
            <if test="cityNo != null">
                #{cityNo},
            </if>
            
            <if test="provinceNo != null">
                #{provinceNo},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="sysNo != null">
                #{sysNo},
            </if>
            
            <if test="fullName != null">
                #{fullName},
            </if>
            
            <if test="shortName != null">
                #{shortName},
            </if>
            
            <if test="storeCode != null">
                #{storeCode},
            </if>
            
            <if test="parentNo != null">
                #{parentNo},
            </if>
            
            <if test="area != null">
                #{area},
            </if>
            
            <if test="storeNo != null">
                #{storeNo},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.gms.GmsStore"  useGeneratedKeys="true" keyProperty="id"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.GmsStore">
        UPDATE store
        <set>
            
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if> 
            <if test="searchCode != null">
                `search_code` = #{searchCode},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="virtPhyStorageType != null">
                `virt_phy_storage_type` = #{virtPhyStorageType},
            </if> 
            <if test="storageType != null">
                `storage_type` = #{storageType},
            </if> 
            <if test="storeType != null">
                `store_type` = #{storeType},
            </if> 
            <if test="fax != null">
                `fax` = #{fax},
            </if> 
            <if test="tel != null">
                `tel` = #{tel},
            </if> 
            <if test="contactName != null">
                `contact_name` = #{contactName},
            </if> 
            <if test="zipCode != null">
                `zip_code` = #{zipCode},
            </if> 
            <if test="address != null">
                `address` = #{address},
            </if> 
            <if test="zoneNo != null">
                `zone_no` = #{zoneNo},
            </if> 
            <if test="countyNo != null">
                `county_no` = #{countyNo},
            </if> 
            <if test="cityNo != null">
                `city_no` = #{cityNo},
            </if> 
            <if test="provinceNo != null">
                `province_no` = #{provinceNo},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="sysNo != null">
                `sys_no` = #{sysNo},
            </if> 
            <if test="fullName != null">
                `full_name` = #{fullName},
            </if> 
            <if test="shortName != null">
                `short_name` = #{shortName},
            </if> 
            <if test="storeCode != null">
                `store_code` = #{storeCode},
            </if> 
            <if test="parentNo != null">
                `parent_no` = #{parentNo},
            </if> 
            <if test="area != null">
                `area` = #{area},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE store_no = #{storeNo}
        
            
    </update>
        <!-- auto generate end-->


</mapper>