<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.SellOutConfigRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.SellOutConfig">
                
        <id column="id" property="id" jdbcType="CHAR" />
        <result column="bill_no" property="billNo" jdbcType="CHAR" />
        <result column="type" property="type" jdbcType="TINYINT" />	
        <result column="brand_no" property="brandNo" jdbcType="CHAR" />	
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />	
        <result column="classify_code" property="classifyCode" jdbcType="VARCHAR" />	
        <result column="classify_name" property="classifyName" jdbcType="VARCHAR" />	
        <result column="classify_value_code" property="classifyValueCode" jdbcType="VARCHAR" />	
        <result column="classify_value_name" property="classifyValueName" jdbcType="VARCHAR" />	
        <result column="item_no" property="itemNo" jdbcType="CHAR" />	
        <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />	
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />	
        <result column="target_field" property="targetField" jdbcType="VARCHAR" />	
        <result column="target_value" property="targetValue" jdbcType="DECIMAL" />	
        <result column="status" property="status" jdbcType="TINYINT" />	
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />	
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />	
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />	
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />	
    </resultMap>

    <sql id="column_list">
        `id`,`bill_no`,`type`,`brand_no`,`brand_name`,`classify_code`,`classify_name`,`classify_value_code`,`classify_value_name`,`item_no`,`size_no`,`item_code`,`target_field`,`target_value`,`status`,`create_user`,`create_time`,`update_user`,`update_time`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
        
        	<if test="null!=params.id  and ''!=params.id ">
				 AND `id`=#{params.id}
            </if>
            <if test="null!=params.billNo  and ''!=params.billNo ">
				 AND `bill_no`=#{params.billNo}
            </if>

        	<if test="null!=params.type ">
				 AND `type`=#{params.type}
            </if>	
        
        	<if test="null!=params.brandNo  and ''!=params.brandNo ">
				 AND `brand_no`=#{params.brandNo}
            </if>	
        
        	<if test="null!=params.brandName  and ''!=params.brandName ">
				 AND `brand_name`=#{params.brandName}
            </if>	
			<if test="null!=params.brandNameLike  and ''!=params.brandNameLike ">
                AND `brand_name` like CONCAT('%',#{params.brandNameLike},'%') 
			</if>	
        
        	<if test="null!=params.classifyCode  and ''!=params.classifyCode ">
				 AND `classify_code`=#{params.classifyCode}
            </if>	
        
        	<if test="null!=params.classifyName  and ''!=params.classifyName ">
				 AND `classify_name`=#{params.classifyName}
            </if>	
			<if test="null!=params.classifyNameLike  and ''!=params.classifyNameLike ">
                AND `classify_name` like CONCAT('%',#{params.classifyNameLike},'%') 
			</if>	
        
        	<if test="null!=params.classifyValueCode  and ''!=params.classifyValueCode ">
				 AND `classify_value_code`=#{params.classifyValueCode}
            </if>	
        
        	<if test="null!=params.classifyValueName  and ''!=params.classifyValueName ">
				 AND `classify_value_name`=#{params.classifyValueName}
            </if>	
			<if test="null!=params.classifyValueNameLike  and ''!=params.classifyValueNameLike ">
                AND `classify_value_name` like CONCAT('%',#{params.classifyValueNameLike},'%') 
			</if>	
        
        	<if test="null!=params.itemNo  and ''!=params.itemNo ">
				 AND `item_no`=#{params.itemNo}
            </if>	
        
        	<if test="null!=params.sizeNo  and ''!=params.sizeNo ">
				 AND `size_no`=#{params.sizeNo}
            </if>	
        
        	<if test="null!=params.itemCode  and ''!=params.itemCode ">
				 AND `item_code`=#{params.itemCode}
            </if>	
        
        	<if test="null!=params.targetField  and ''!=params.targetField ">
				 AND `target_field`=#{params.targetField}
            </if>	
        
        	<if test="null!=params.targetValue ">
				 AND `target_value`=#{params.targetValue}
            </if>	
        
        	<if test="null!=params.status ">
				 AND `status`=#{params.status}
            </if>	
        
        	<if test="null!=params.createUser  and ''!=params.createUser ">
				 AND `create_user`=#{params.createUser}
            </if>	
        
        	<if test="null!=params.createTime ">
				 AND `create_time`=#{params.createTime}
            </if>	
        
        	<if test="null!=params.updateUser  and ''!=params.updateUser ">
				 AND `update_user`=#{params.updateUser}
            </if>	
        
        	<if test="null!=params.updateTime ">
				 AND `update_time`=#{params.updateTime}
            </if>	
        
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM sell_out_config
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM sell_out_config
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM sell_out_config
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM sell_out_config
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM sell_out_config
        <where>
            <include refid="condition" />
        </where>
        <choose>
            <when test="orderby != null and ''!=orderby">
                ORDER BY ${orderby}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>    
        </choose>

        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM sell_out_config
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <select id="selectByParamsForHandler"
            resultMap="baseResultMap"
            parameterType="map"
            resultSetType="FORWARD_ONLY"
            fetchSize="-2147483648">
        SELECT
        <include refid="column_list" />
        FROM sell_out_config
        <where>
            <include refid="condition" />
        </where>
        ORDER BY bill_no desc
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM sell_out_config
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM sell_out_config
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM sell_out_config
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfig"  >
        INSERT INTO sell_out_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                `id`,
            </if>

            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="type != null">
                `type`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="brandName != null">
                `brand_name`,
            </if>
            
            <if test="classifyCode != null">
                `classify_code`,
            </if>
            
            <if test="classifyName != null">
                `classify_name`,
            </if>
            
            <if test="classifyValueCode != null">
                `classify_value_code`,
            </if>
            
            <if test="classifyValueName != null">
                `classify_value_name`,
            </if>
            
            <if test="itemNo != null">
                `item_no`,
            </if>
            
            <if test="sizeNo != null">
                `size_no`,
            </if>
            
            <if test="itemCode != null">
                `item_code`,
            </if>
            
            <if test="targetField != null">
                `target_field`,
            </if>
            
            <if test="targetValue != null">
                `target_value`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                #{id},
            </if>

            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="type != null">
                #{type},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="brandName != null">
                #{brandName},
            </if>
            
            <if test="classifyCode != null">
                #{classifyCode},
            </if>
            
            <if test="classifyName != null">
                #{classifyName},
            </if>
            
            <if test="classifyValueCode != null">
                #{classifyValueCode},
            </if>
            
            <if test="classifyValueName != null">
                #{classifyValueName},
            </if>
            
            <if test="itemNo != null">
                #{itemNo},
            </if>
            
            <if test="sizeNo != null">
                #{sizeNo},
            </if>
            
            <if test="itemCode != null">
                #{itemCode},
            </if>
            
            <if test="targetField != null">
                #{targetField},
            </if>
            
            <if test="targetValue != null">
                #{targetValue},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sell_out_config (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.billNo}, #{item.type}, #{item.brandNo}, #{item.brandName}, #{item.classifyCode}, #{item.classifyName}, #{item.classifyValueCode}, #{item.classifyValueName}, #{item.itemNo}, #{item.sizeNo}, #{item.itemCode}, #{item.targetField}, #{item.targetValue}, #{item.status}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfig">
        UPDATE sell_out_config
        <set>
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if>
            
            <if test="type != null">
                `type` = #{type},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="brandName != null">
                `brand_name` = #{brandName},
            </if> 
            <if test="classifyCode != null">
                `classify_code` = #{classifyCode},
            </if> 
            <if test="classifyName != null">
                `classify_name` = #{classifyName},
            </if> 
            <if test="classifyValueCode != null">
                `classify_value_code` = #{classifyValueCode},
            </if> 
            <if test="classifyValueName != null">
                `classify_value_name` = #{classifyValueName},
            </if> 
            <if test="itemNo != null">
                `item_no` = #{itemNo},
            </if> 
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},
            </if> 
            <if test="itemCode != null">
                `item_code` = #{itemCode},
            </if> 
            <if test="targetField != null">
                `target_field` = #{targetField},
            </if> 
            <if test="targetValue != null">
                `target_value` = #{targetValue},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE id = #{id}
                
    </update>
    
    <update id="updateStatusByParams" parameterType="map">
    	update sell_out_config set status = #{params.status}
        <where>
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                AND `brand_no`=#{params.brandNo}
            </if>
            <if test="null!=params.classifyCode  and ''!=params.classifyCode ">
                AND `classify_code`=#{params.classifyCode}
            </if>

            <if test="null!=params.classifyValueCode  and ''!=params.classifyValueCode ">
                AND `classify_value_code`=#{params.classifyValueCode}
            </if>

            <if test="null!=params.itemNo  and ''!=params.itemNo ">
                AND `item_no`=#{params.itemNo}
            </if>

            <if test="null!=params.sizeNo  and ''!=params.sizeNo ">
                AND `size_no`=#{params.sizeNo}
            </if>
        </where>    	
    </update>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM sell_out_config 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <select id="selectShareClassifyList" resultType="cn.wonhigh.baize.model.entity.gms.ItemAttr">
        select
            d.attribute_no AS attr_no,
            i.name AS attr_name,
            d.attribute_detail_no AS attr_dtl_no,
            d.name AS attr_dtl_name,
            i.brand_no from item_attribute i
        inner join item_attribute_detail d on i.attribute_no = d.attribute_no
        where i.name ='共享标识' and i.brand_no =#{params.brandNo} and d.status = 1 and i.status = 1
        <if test="null!=params.attrDtlName and '' != params.attrDtlName">
            AND d.name = #{params.attrDtlName}
        </if>
    </select>

    <select id="selectSellOutCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
            SELECT
                1
            FROM sell_out_config
            <where>
                <include refid="condition" />
            </where>
            <choose>
                <when test="null!=params.selectType and 'item'.equals(params.selectType)">
                    GROUP BY item_no,size_no
                </when>
                <otherwise>
                    GROUP BY classify_code,classify_value_code
                </otherwise>
            </choose>
        ) G
    </select>


    <resultMap id="PageResultMap" type="cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto">
        <result column="brand_no" property="brandNo" jdbcType="VARCHAR"/>
        <result column="brand_name" property="brandName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="group_value" property="groupValue" jdbcType="VARCHAR"/>
        <discriminator column="selectType" javaType="java.lang.String" >
            <case value="item" resultType="cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigItemDto">
                <result column="item_no" property="itemNo" jdbcType="VARCHAR"/>
                <result column="size_no" property="sizeNo" jdbcType="VARCHAR"/>
                <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>
            </case>
            <case value="classify" resultType="cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigClassifyDto">
                <result column="classify_code" property="classifyCode" jdbcType="VARCHAR"/>
                <result column="classify_value_code" property="classifyValueCode" jdbcType="VARCHAR"/>
                <result column="classify_name" property="classifyName" jdbcType="VARCHAR"/>
                <result column="classify_value_name" property="classifyValueName" jdbcType="VARCHAR"/>
            </case>
        </discriminator>
    </resultMap>

    <select id="selectSellOutList"
            resultMap="PageResultMap">
             SELECT
                 MAX(brand_no) brand_no,
                 MAX(brand_name) brand_name,
                 MAX(status) status,
                 MAX(update_user) update_user,
                 MAX(update_time) update_time,
                 #{params.selectType} selectType,
                GROUP_CONCAT(target_field,':',target_value) group_value,
                <choose>
                    <when test="null!=params.selectType and 'item'.equals(params.selectType)">
                         item_no,size_no,MAX(item_code) item_code
                    </when>
                    <otherwise>
                         classify_code,classify_value_code,MAX(classify_name) classify_name, MAX(classify_value_name) classify_value_name
                    </otherwise>
                </choose>
            FROM sell_out_config
            <where>
                <include refid="condition" />
            </where>
            <choose>
                <when test="null!=params.selectType and 'item'.equals(params.selectType)">
                    GROUP BY item_no,size_no
                    ORDER BY item_no,size_no
                </when>
                <otherwise>
                    GROUP BY classify_code,classify_value_code
                    ORDER BY classify_code,classify_value_code
                </otherwise>
            </choose>
        LIMIT ${page.startRowNum},${page.pageSize}

     </select>

    <select id="selectByUniqueList" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM sell_out_config
        <where>
            <if test="list!=null and list.size>0">
                and
                <choose>
                    <when test="type == 1">
                        (brand_no, item_no, size_no) in
                        <foreach collection="list" item="item" open="(" separator="," close=")">
                            (#{item.t1}, #{item.t2}, #{item.t3})
                        </foreach>
                    </when>
                    <otherwise>
                        (brand_no, classify_code, classify_value_code) in
                        <foreach collection="list" item="item" open="(" separator="," close=")">
                            (#{item.t1}, #{item.t2}, #{item.t3})
                        </foreach>
                    </otherwise>
                </choose>
            </if>
            and type = #{type}
        </where>
    </select>

    <!-- auto generate end-->
</mapper>
