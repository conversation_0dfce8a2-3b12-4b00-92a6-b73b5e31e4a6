<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.gms.StockDistirbutionConfigMapper">
    <resultMap id="BaseResultMap" type="cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig">
        <id column="id" jdbcType="CHAR" property="id" />
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
        <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
        <result column="brand_no" jdbcType="VARCHAR" property="brandNo" />
        <result column="vstore_code" jdbcType="VARCHAR" property="vstoreCode" />
        <result column="online_type" jdbcType="TINYINT" property="onlineType" />
        <result column="vstore_scope_type" jdbcType="TINYINT" property="vstoreScopeType" />
        <result column="vstore_level" jdbcType="TINYINT" property="vstoreLevel" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="channel_type" jdbcType="TINYINT" property="channelType" />
        <result column="third_channel_no" property="thirdChannelNo" jdbcType="VARCHAR" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, shop_no, shop_name, brand_no, vstore_code, online_type, vstore_scope_type, vstore_level,
         `status`, remark, update_user, update_time, create_user, create_time,channel_type
    </sql>
    
    <sql id="Condition">
        <if test="params != null">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
        	<if test="params.shopNo != null and params.shopNo != ''">
                AND shop_no = #{params.shopNo,jdbcType=VARCHAR}
            </if>
            <if test="params.vstoreCode != null and params.vstoreCode != ''">
                AND vstore_code = #{params.vstoreCode,jdbcType=VARCHAR}
            </if>
            <if test="params.status != null and params.status != ''">
                AND `status` = #{params.status,jdbcType=TINYINT}
            </if>
            <if test="params.channelType != null and params.channelType != ''">
                AND `channel_type` = #{params.channelType,jdbcType=TINYINT}
            </if>
            <if test="params.brandNo != null and params.brandNo != ''">
                AND `brand_no` = #{params.brandNo,jdbcType=VARCHAR}
            </if>
            <if test="null!=params.vstoreCodeList and params.vstoreCodeList.size()>0">
                AND `vstore_code` IN
                <foreach collection="params.vstoreCodeList" item="vstoreCode" open="(" close=")" separator=",">
                    #{vstoreCode}
                </foreach>
            </if>

            <if test="null!=params.shopNoList  and params.shopNoList.size() > 0 ">
                AND `shop_no` IN
                <foreach collection="params.shopNoList" item="shopNo" open="(" close=")" separator=",">
                    #{shopNo}
                </foreach>
            </if>
        </if>
    </sql>
    
    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig">
        insert into stock_distirbution_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="shopNo != null">
                shop_no,
            </if>
            <if test="shopName != null">
                shop_name,
            </if>
            <if test="brandNo != null">
                brand_no,
            </if>
            <if test="vstoreCode != null">
                vstore_code,
            </if>
            <if test="onlineType != null">
                online_type,
            </if>
            <if test="vstoreScopeType != null">
            	vstore_scope_type,
            </if>
            <if test="vstoreLevel != null">
            	vstore_level,
            </if>
            <if test="status != null">
            	`status`,
            </if>
            <if test="remark != null">
            	remark,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="channelType != null">
                channel_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=CHAR},
            </if>
            <if test="shopNo != null">
                #{shopNo,jdbcType=VARCHAR},
            </if>
            <if test="shopName != null">
                #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null">
                #{brandNo,jdbcType=VARCHAR},
            </if>
            <if test="vstoreCode != null">
                #{vstoreCode,jdbcType=VARCHAR},
            </if>
            <if test="onlineType != null">
                #{onlineType,jdbcType=TINYINT},
            </if>
            <if test="vstoreScopeType != null">
            	#{vstoreScopeType,jdbcType=TINYINT},
            </if>
            <if test="vstoreLevel != null">
            	#{vstoreLevel,jdbcType=TINYINT},
            </if>
            <if test="status != null">
            	#{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelType != null">
                #{channelType,jdbcType=TINYINT},
            </if>

        </trim>
    </insert>
    <insert id="batchInsert" >
        insert into stock_distirbution_config (
        id, shop_no, shop_name, brand_no, vstore_code, online_type, vstore_scope_type, vstore_level,
         `status`, remark, update_user, update_time, create_user, create_time,channel_type) values
        <foreach collection="datas" item="data" separator=",">
            (#{data.id,jdbcType=CHAR}, 
            #{data.shopNo,jdbcType=VARCHAR}, 
            #{data.shopName,jdbcType=VARCHAR}, 
            #{data.brandNo,jdbcType=VARCHAR}, 
            #{data.vstoreCode,jdbcType=VARCHAR}, 
            #{data.onlineType,jdbcType=TINYINT}, 
            #{data.vstoreScopeType,jdbcType=TINYINT}, 
            #{data.vstoreLevel,jdbcType=TINYINT}, 
            #{data.status,jdbcType=TINYINT},
            #{data.remark,jdbcType=VARCHAR}, 
            #{data.updateUser,jdbcType=VARCHAR}, 
            #{data.updateTime,jdbcType=TIMESTAMP}, 
            #{data.createUser,jdbcType=VARCHAR}, 
            #{data.createTime,jdbcType=TIMESTAMP},
            #{data.channelType,jdbcType=TINYINT})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig">
        update stock_distirbution_config
        <set>
        	<if test="shopNo != null">
                shop_no = #{shopNo,jdbcType=VARCHAR},
            </if>
            <if test="shopName != null">
                shop_name = #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null">
                brand_no = #{brandNo,jdbcType=VARCHAR},
            </if>
            <if test="vstoreCode != null">
                vstore_code = #{vstoreCode,jdbcType=VARCHAR},
            </if>
            <if test="onlineType != null">
                online_type = #{onlineType,jdbcType=TINYINT},
            </if>
            <if test="vstoreScopeType != null">
                vstore_scope_type = #{vstoreScopeType,jdbcType=TINYINT},
            </if>
            <if test="vstoreLevel != null">
                vstore_level = #{vstoreLevel,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
             <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelType != null">
                channel_type = #{channelType,jdbcType=TINYINT},
            </if>
        </set>
        where  1=1
        <if test="id != null and id != ''">
        	and id = #{id,jdbcType=CHAR} 
        </if>
    </update>
    
    <delete id="deleteByParams" parameterType="cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig">
        delete from stock_distirbution_config
        where  1=1
        <include refid="Condition" />
    </delete>

    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from stock_distirbution_config 
        Where 1 = 1 
        <include refid="Condition" />
    </select>
    
    <select id="selectByParams" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from stock_distirbution_config
        where 1 = 1 
        <include refid="Condition" />
    </select>
    
    <select id="selectByPage" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from stock_distirbution_config
        where 1 = 1 
        <include refid="Condition" />
        LIMIT #{page.startRowNum} ,#{page.pageSize}
    </select>


    <select id="countByShop" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM (
        SELECT 1
        FROM stock_distirbution_config a LEFT JOIN order_source_terminal_config b ON a.shop_no=b.third_platform
        <where>
            1=1
            <if test="null!=params.channelType ">
                AND a.`channel_type`=#{params.channelType}
            </if>
            <if test="null!=params.shopNo  and ''!=params.shopNo ">
                AND a.`shop_no` like CONCAT('%',#{params.shopNo},'%')
            </if>
            <if test="null!=params.shopName  and ''!=params.shopName ">
                AND a.`shop_name` like CONCAT('%',#{params.shopName},'%')
            </if>
            <if test="null!=params.thirdChannelNo  and ''!=params.thirdChannelNo ">
                AND b.`third_channel_no` like CONCAT('%',#{params.thirdChannelNo},'%')
            </if>
        </where>
        GROUP BY a.shop_no
        ) t
    </select>

    <select id="pageByShop" resultMap="BaseResultMap" parameterType="map">
        SELECT
        a.id, a.shop_no, a.shop_name, a.brand_no, a.online_type, a.vstore_scope_type, a.remark, a.channel_type,
        a.create_user,a.create_time,a.update_user,max(a.update_time) as update_time,b.third_channel_no
        FROM stock_distirbution_config a LEFT JOIN order_source_terminal_config b ON a.shop_no=b.third_platform
        <where>
            1=1
            <if test="null!=params.channelType ">
                AND a.`channel_type`=#{params.channelType}
            </if>
            <if test="null!=params.shopNo  and ''!=params.shopNo ">
                AND a.`shop_no` like CONCAT('%',#{params.shopNo},'%')
            </if>
            <if test="null!=params.shopName  and ''!=params.shopName ">
                AND a.`shop_name` like CONCAT('%',#{params.shopName},'%')
            </if>
            <if test="null!=params.thirdChannelNo  and ''!=params.thirdChannelNo ">
                AND b.`third_channel_no` like CONCAT('%',#{params.thirdChannelNo},'%')
            </if>
        </where>
        GROUP BY a.shop_no
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="findByPrimaryKey" resultMap="BaseResultMap" >
        SELECT
        <include refid="Base_Column_List" />
        FROM stock_distirbution_config
        WHERE id = #{id}
    </select>
</mapper>