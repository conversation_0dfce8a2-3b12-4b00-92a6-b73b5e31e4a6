<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetDispatchRuleSetLogRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetLog">
                
        <id column="id" property="id" jdbcType="VARCHAR" />
        
        
        <result column="platform_no" property="platformNo" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="operation_time" property="operationTime" jdbcType="TIMESTAMP" />
        
        <result column="parent_id" property="parentId" jdbcType="INTEGER" />
        
        <result column="close_rule_str" property="closeRuleStr" jdbcType="VARCHAR" />
        
        <result column="open_rule_str" property="openRuleStr" jdbcType="VARCHAR" />
        
        <result column="operation_user" property="operationUser" jdbcType="VARCHAR" />
        
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        
        <result column="brand_no" property="brandNo" jdbcType="VARCHAR" />
        
        <result column="organ_type_name" property="organTypeName" jdbcType="VARCHAR" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
        <result column="platform_name" property="platformName" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `platform_no`,`update_time`,`id`,`operation_time`,`parent_id`,`close_rule_str`,`open_rule_str`,`operation_user`,`brand_name`,`brand_no`,`organ_type_name`,`organ_type_no`,`platform_name`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.platformNo  and ''!=params.platformNo ">
                
                AND `platform_no`=#{params.platformNo}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.operationTime ">
                
                AND `operation_time`=#{params.operationTime}
                
            </if>
            
            <if test="null!=params.parentId ">
                
                AND `parent_id`=#{params.parentId}
                
            </if>
            
            <if test="null!=params.closeRuleStr  and ''!=params.closeRuleStr ">
                
                AND `close_rule_str`=#{params.closeRuleStr}
                
            </if>
            
            <if test="null!=params.openRuleStr  and ''!=params.openRuleStr ">
                
                AND `open_rule_str`=#{params.openRuleStr}
                
            </if>
            
            <if test="null!=params.operationUser  and ''!=params.operationUser ">
                
                AND `operation_user`=#{params.operationUser}
                
            </if>
            
            <if test="null!=params.brandName  and ''!=params.brandName ">
                
                AND `brand_name` like CONCAT('%',#{params.brandName},'%') 
                
            </if>
            
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                
                AND `brand_no`=#{params.brandNo}
                
            </if>
            
            <if test="null!=params.organTypeName  and ''!=params.organTypeName ">
                
                AND `organ_type_name` like CONCAT('%',#{params.organTypeName},'%') 
                
            </if>
            
            <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                
                AND `organ_type_no`=#{params.organTypeNo}
                
            </if>
            
            <if test="null!=params.platformName  and ''!=params.platformName ">
                
                AND `platform_name` like CONCAT('%',#{params.platformName},'%') 
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_log
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_log
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_log
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_dispatch_rule_set_log
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_log
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_log
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_dispatch_rule_set_log
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_dispatch_rule_set_log
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_dispatch_rule_set_log
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetLog"  >
        INSERT INTO internet_dispatch_rule_set_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="platformNo != null">
                `platform_no`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="operationTime != null">
                `operation_time`,
            </if>
            
            <if test="parentId != null">
                `parent_id`,
            </if>
            
            <if test="closeRuleStr != null">
                `close_rule_str`,
            </if>
            
            <if test="openRuleStr != null">
                `open_rule_str`,
            </if>
            
            <if test="operationUser != null">
                `operation_user`,
            </if>
            
            <if test="brandName != null">
                `brand_name`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="organTypeName != null">
                `organ_type_name`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
            <if test="platformName != null">
                `platform_name`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="platformNo != null">
                #{platformNo},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="operationTime != null">
                #{operationTime},
            </if>
            
            <if test="parentId != null">
                #{parentId},
            </if>
            
            <if test="closeRuleStr != null">
                #{closeRuleStr},
            </if>
            
            <if test="openRuleStr != null">
                #{openRuleStr},
            </if>
            
            <if test="operationUser != null">
                #{operationUser},
            </if>
            
            <if test="brandName != null">
                #{brandName},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="organTypeName != null">
                #{organTypeName},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
            <if test="platformName != null">
                #{platformName},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetLog">
        UPDATE internet_dispatch_rule_set_log
        <set>
            
            <if test="platformNo != null">
                `platform_no` = #{platformNo},
            </if> 
            <if test="operationTime != null">
                `operation_time` = #{operationTime},
            </if> 
            <if test="parentId != null">
                `parent_id` = #{parentId},
            </if> 
            <if test="closeRuleStr != null">
                `close_rule_str` = #{closeRuleStr},
            </if> 
            <if test="openRuleStr != null">
                `open_rule_str` = #{openRuleStr},
            </if> 
            <if test="operationUser != null">
                `operation_user` = #{operationUser},
            </if> 
            <if test="brandName != null">
                `brand_name` = #{brandName},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="organTypeName != null">
                `organ_type_name` = #{organTypeName},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            <if test="platformName != null">
                `platform_name` = #{platformName},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE id = #{id}
        
            
    </update>
        <!-- auto generate end-->


</mapper>