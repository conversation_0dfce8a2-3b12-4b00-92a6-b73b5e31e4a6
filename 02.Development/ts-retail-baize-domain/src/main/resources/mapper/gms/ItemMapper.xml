<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.gms.ItemRepository">
    <resultMap id="BaseResultMap" type="Item">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="item_no" jdbcType="CHAR" property="itemNo" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="full_name" jdbcType="VARCHAR" property="fullName" />
        <result column="en_name" jdbcType="VARCHAR" property="enName" />
        <result column="sys_no" jdbcType="VARCHAR" property="sysNo" />
        <result column="style_no" jdbcType="VARCHAR" property="styleNo" />
        <result column="brand_no" jdbcType="CHAR" property="brandNo" />
        <result column="shoe_model" jdbcType="VARCHAR" property="shoeModel" />
        <result column="ingredients" jdbcType="VARCHAR" property="ingredients" />
        <result column="mainqdb" jdbcType="VARCHAR" property="mainqdb" />
        <result column="lining" jdbcType="VARCHAR" property="lining" />
        <result column="main_color" jdbcType="VARCHAR" property="mainColor" />
        <result column="color_no" jdbcType="CHAR" property="colorNo" />
        <result column="category_no" jdbcType="CHAR" property="categoryNo" />
        <result column="root_category_no" jdbcType="CHAR" property="rootCategoryNo" />
        <result column="repeatlisting" jdbcType="VARCHAR" property="repeatlisting" />
        <result column="gender" jdbcType="VARCHAR" property="gender" />
        <result column="heeltype" jdbcType="VARCHAR" property="heeltype" />
        <result column="bottomtype" jdbcType="VARCHAR" property="bottomtype" />
        <result column="size_kind" jdbcType="CHAR" property="sizeKind" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="tag_price" jdbcType="DECIMAL" property="tagPrice" />
        <result column="suggest_tag_price" jdbcType="DECIMAL" property="suggestTagPrice" />
        <result column="purchase_tax_price" jdbcType="DECIMAL" property="purchaseTaxPrice" />
        <result column="costtaxrate" jdbcType="DECIMAL" property="costtaxrate" />
        <result column="saletaxrate" jdbcType="DECIMAL" property="saletaxrate" />
        <result column="material_price" jdbcType="DECIMAL" property="materialPrice" />
        <result column="supplier_no" jdbcType="CHAR" property="supplierNo" />
        <result column="supplier_item_no" jdbcType="VARCHAR" property="supplierItemNo" />
        <result column="supplier_item_name" jdbcType="VARCHAR" property="supplierItemName" />
        <result column="orderfrom" jdbcType="VARCHAR" property="orderfrom" />
        <result column="years" jdbcType="VARCHAR" property="years" />
        <result column="sell_season" jdbcType="VARCHAR" property="sellSeason" />
        <result column="purchase_season" jdbcType="VARCHAR" property="purchaseSeason" />
        <result column="sale_date" jdbcType="DATE" property="saleDate" />
        <result column="search_code" jdbcType="VARCHAR" property="searchCode" />
        <result column="style" jdbcType="VARCHAR" property="style" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="time_seq" jdbcType="BIGINT" property="timeSeq" />
        <result column="season" jdbcType="VARCHAR" property="season" />
        <result column="liner" jdbcType="VARCHAR" property="liner" />
        <result column="outsole" jdbcType="VARCHAR" property="outsole" />
        <result column="pattern" jdbcType="VARCHAR" property="pattern" />
        <result column="item_code2" jdbcType="VARCHAR" property="itemCode2" />
        <result column="ext_dev_prop" jdbcType="VARCHAR" property="extDevProp" />
        <result column="ext_brand_style" jdbcType="VARCHAR" property="extBrandStyle" />
        <result column="ext_style" jdbcType="VARCHAR" property="extStyle" />
        <result column="ext_series" jdbcType="VARCHAR" property="extSeries" />
        <result column="ext_spec_prop" jdbcType="VARCHAR" property="extSpecProp" />
        <result column="item_style_no" jdbcType="VARCHAR" property="itemStyleNo" />
        <result column="item_series_no" jdbcType="VARCHAR" property="itemSeriesNo" />
        <result column="japan_name" jdbcType="VARCHAR" property="japanName" />
        <result column="designer_name" jdbcType="VARCHAR" property="designerName" />
        <result column="origine_country" jdbcType="VARCHAR" property="origineCountry" />
        <result column="japan_tag_price" jdbcType="DECIMAL" property="japanTagPrice" />
        <result column="japan_cost" jdbcType="DECIMAL" property="japanCost" />
        <result column="sale_year" jdbcType="CHAR" property="saleYear" />
        <result column="sale_week" jdbcType="CHAR" property="saleWeek" />
        <result column="brand_season" jdbcType="VARCHAR" property="brandSeason" />
        <result column="item_flag" jdbcType="VARCHAR" property="itemFlag" />
        <result column="plate_code" jdbcType="VARCHAR" property="plateCode" />
    </resultMap>
    <sql id="Base_Column_List">
        id, item_no, code, name, full_name, en_name, sys_no, style_no, brand_no, shoe_model, 
        ingredients, mainqdb, lining, main_color, color_no, category_no, root_category_no, repeatlisting, gender, heeltype, 
        bottomtype, size_kind, status, tag_price, suggest_tag_price, purchase_tax_price, costtaxrate, saletaxrate, material_price, supplier_no, 
        supplier_item_no, supplier_item_name, orderfrom, years, sell_season, purchase_season, sale_date, search_code, style, create_user, 
        create_time, update_user, update_time, remark, time_seq, season, liner, outsole, pattern, item_code2, 
        ext_dev_prop, ext_brand_style, ext_style, ext_series, ext_spec_prop, item_style_no, item_series_no, japan_name, designer_name, origine_country, 
        japan_tag_price, japan_cost, sale_year, sale_week, brand_season, item_flag, plate_code
    </sql>
    <sql id="Condition">
        <if test="params != null">
            <if test="params.queryParam != null and params.queryParam != ''">
                <!--AND (code like CONCAT('%',#{params.queryParam},'%') or name like CONCAT('%',#{params.queryParam},'%'))-->
                AND code = #{params.queryParam,jdbcType=VARCHAR}
            </if>
            <if test="params.itemNo != null and params.itemNo != ''">
                AND item_no = #{params.itemNo,jdbcType=CHAR}
            </if>
            <if test="params.code != null and params.code != ''">
                AND code = #{params.code,jdbcType=VARCHAR}
            </if>
            <if test="params.name != null and params.name != ''">
                AND name = #{params.name,jdbcType=VARCHAR}
            </if>
            <if test="params.fullName != null and params.fullName != ''">
                AND full_name = #{params.fullName,jdbcType=VARCHAR}
            </if>
            <if test="params.enName != null and params.enName != ''">
                AND en_name = #{params.enName,jdbcType=VARCHAR}
            </if>
            <if test="params.sysNo != null and params.sysNo != ''">
                AND sys_no = #{params.sysNo,jdbcType=VARCHAR}
            </if>
            <if test="params.styleNo != null and params.styleNo != ''">
                AND style_no = #{params.styleNo,jdbcType=VARCHAR}
            </if>
            <if test="params.brandNo != null and params.brandNo != ''">
                AND brand_no = #{params.brandNo,jdbcType=CHAR}
            </if>
            <if test="params.shoeModel != null and params.shoeModel != ''">
                AND shoe_model = #{params.shoeModel,jdbcType=VARCHAR}
            </if>
            <if test="params.ingredients != null and params.ingredients != ''">
                AND ingredients = #{params.ingredients,jdbcType=VARCHAR}
            </if>
            <if test="params.mainqdb != null and params.mainqdb != ''">
                AND mainqdb = #{params.mainqdb,jdbcType=VARCHAR}
            </if>
            <if test="params.lining != null and params.lining != ''">
                AND lining = #{params.lining,jdbcType=VARCHAR}
            </if>
            <if test="params.mainColor != null and params.mainColor != ''">
                AND main_color = #{params.mainColor,jdbcType=VARCHAR}
            </if>
            <if test="params.colorNo != null and params.colorNo != ''">
                AND color_no = #{params.colorNo,jdbcType=CHAR}
            </if>
            <if test="params.categoryNo != null and params.categoryNo != ''">
                AND category_no = #{params.categoryNo,jdbcType=CHAR}
            </if>
            <if test="params.rootCategoryNo != null and params.rootCategoryNo != ''">
                AND root_category_no = #{params.rootCategoryNo,jdbcType=CHAR}
            </if>
            <if test="params.repeatlisting != null and params.repeatlisting != ''">
                AND repeatlisting = #{params.repeatlisting,jdbcType=VARCHAR}
            </if>
            <if test="params.gender != null and params.gender != ''">
                AND gender = #{params.gender,jdbcType=VARCHAR}
            </if>
            <if test="params.heeltype != null and params.heeltype != ''">
                AND heeltype = #{params.heeltype,jdbcType=VARCHAR}
            </if>
            <if test="params.bottomtype != null and params.bottomtype != ''">
                AND bottomtype = #{params.bottomtype,jdbcType=VARCHAR}
            </if>
            <if test="params.sizeKind != null and params.sizeKind != ''">
                AND size_kind = #{params.sizeKind,jdbcType=CHAR}
            </if>
            <if test="params.status != null ">
                AND status = #{params.status,jdbcType=TINYINT}
            </if>
            <if test="params.tagPrice != null ">
                AND tag_price = #{params.tagPrice,jdbcType=DECIMAL}
            </if>
            <if test="params.suggestTagPrice != null ">
                AND suggest_tag_price = #{params.suggestTagPrice,jdbcType=DECIMAL}
            </if>
            <if test="params.purchaseTaxPrice != null ">
                AND purchase_tax_price = #{params.purchaseTaxPrice,jdbcType=DECIMAL}
            </if>
            <if test="params.costtaxrate != null ">
                AND costtaxrate = #{params.costtaxrate,jdbcType=DECIMAL}
            </if>
            <if test="params.saletaxrate != null ">
                AND saletaxrate = #{params.saletaxrate,jdbcType=DECIMAL}
            </if>
            <if test="params.materialPrice != null ">
                AND material_price = #{params.materialPrice,jdbcType=DECIMAL}
            </if>
            <if test="params.supplierNo != null and params.supplierNo != ''">
                AND supplier_no = #{params.supplierNo,jdbcType=CHAR}
            </if>
            <if test="params.supplierItemNo != null and params.supplierItemNo != ''">
                AND supplier_item_no = #{params.supplierItemNo,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierItemName != null and params.supplierItemName != ''">
                AND supplier_item_name = #{params.supplierItemName,jdbcType=VARCHAR}
            </if>
            <if test="params.orderfrom != null and params.orderfrom != ''">
                AND orderfrom = #{params.orderfrom,jdbcType=VARCHAR}
            </if>
            <if test="params.years != null and params.years != ''">
                AND years = #{params.years,jdbcType=VARCHAR}
            </if>
            <if test="params.sellSeason != null and params.sellSeason != ''">
                AND sell_season = #{params.sellSeason,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseSeason != null and params.purchaseSeason != ''">
                AND purchase_season = #{params.purchaseSeason,jdbcType=VARCHAR}
            </if>
            <if test="params.saleDateStart != null ">
                AND sale_date &gt;= #{params.saleDateStart,jdbcType=DATE}
            </if>
            <if test="params.saleDateEnd != null ">
                AND sale_date &lt;= #{params.saleDateEnd,jdbcType=DATE}
            </if>
            <if test="params.searchCode != null and params.searchCode != ''">
                AND search_code = #{params.searchCode,jdbcType=VARCHAR}
            </if>
            <if test="params.style != null and params.style != ''">
                AND style = #{params.style,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                AND remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.timeSeq != null ">
                AND time_seq = #{params.timeSeq,jdbcType=BIGINT}
            </if>
            <if test="params.season != null and params.season != ''">
                AND season = #{params.season,jdbcType=VARCHAR}
            </if>
            <if test="params.liner != null and params.liner != ''">
                AND liner = #{params.liner,jdbcType=VARCHAR}
            </if>
            <if test="params.outsole != null and params.outsole != ''">
                AND outsole = #{params.outsole,jdbcType=VARCHAR}
            </if>
            <if test="params.pattern != null and params.pattern != ''">
                AND pattern = #{params.pattern,jdbcType=VARCHAR}
            </if>
            <if test="params.itemCode2 != null and params.itemCode2 != ''">
                AND item_code2 = #{params.itemCode2,jdbcType=VARCHAR}
            </if>
            <if test="params.extDevProp != null and params.extDevProp != ''">
                AND ext_dev_prop = #{params.extDevProp,jdbcType=VARCHAR}
            </if>
            <if test="params.extBrandStyle != null and params.extBrandStyle != ''">
                AND ext_brand_style = #{params.extBrandStyle,jdbcType=VARCHAR}
            </if>
            <if test="params.extStyle != null and params.extStyle != ''">
                AND ext_style = #{params.extStyle,jdbcType=VARCHAR}
            </if>
            <if test="params.extSeries != null and params.extSeries != ''">
                AND ext_series = #{params.extSeries,jdbcType=VARCHAR}
            </if>
            <if test="params.extSpecProp != null and params.extSpecProp != ''">
                AND ext_spec_prop = #{params.extSpecProp,jdbcType=VARCHAR}
            </if>
            <if test="params.itemStyleNo != null and params.itemStyleNo != ''">
                AND item_style_no = #{params.itemStyleNo,jdbcType=VARCHAR}
            </if>
            <if test="params.itemSeriesNo != null and params.itemSeriesNo != ''">
                AND item_series_no = #{params.itemSeriesNo,jdbcType=VARCHAR}
            </if>
            <if test="params.japanName != null and params.japanName != ''">
                AND japan_name = #{params.japanName,jdbcType=VARCHAR}
            </if>
            <if test="params.designerName != null and params.designerName != ''">
                AND designer_name = #{params.designerName,jdbcType=VARCHAR}
            </if>
            <if test="params.origineCountry != null and params.origineCountry != ''">
                AND origine_country = #{params.origineCountry,jdbcType=VARCHAR}
            </if>
            <if test="params.japanTagPrice != null ">
                AND japan_tag_price = #{params.japanTagPrice,jdbcType=DECIMAL}
            </if>
            <if test="params.japanCost != null ">
                AND japan_cost = #{params.japanCost,jdbcType=DECIMAL}
            </if>
            <if test="params.saleYear != null and params.saleYear != ''">
                AND sale_year = #{params.saleYear,jdbcType=CHAR}
            </if>
            <if test="params.saleWeek != null and params.saleWeek != ''">
                AND sale_week = #{params.saleWeek,jdbcType=CHAR}
            </if>
            <if test="params.brandSeason != null and params.brandSeason != ''">
                AND brand_season = #{params.brandSeason,jdbcType=VARCHAR}
            </if>
            <if test="params.itemFlag != null and params.itemFlag != ''">
                AND item_flag = #{params.itemFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.plateCode != null and params.plateCode != ''">
                AND plate_code = #{params.plateCode,jdbcType=VARCHAR}
            </if>
            <if test="params.idList != null ">
                <foreach collection="params.idList" item="id" open="AND id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </if>
    </sql>
    <insert id="insertSelective" parameterType="Item">
        insert into item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="itemNo != null">
                item_no,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="fullName != null">
                full_name,
            </if>
            <if test="enName != null">
                en_name,
            </if>
            <if test="sysNo != null">
                sys_no,
            </if>
            <if test="styleNo != null">
                style_no,
            </if>
            <if test="brandNo != null">
                brand_no,
            </if>
            <if test="shoeModel != null">
                shoe_model,
            </if>
            <if test="ingredients != null">
                ingredients,
            </if>
            <if test="mainqdb != null">
                mainqdb,
            </if>
            <if test="lining != null">
                lining,
            </if>
            <if test="mainColor != null">
                main_color,
            </if>
            <if test="colorNo != null">
                color_no,
            </if>
            <if test="categoryNo != null">
                category_no,
            </if>
            <if test="rootCategoryNo != null">
                root_category_no,
            </if>
            <if test="repeatlisting != null">
                repeatlisting,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="heeltype != null">
                heeltype,
            </if>
            <if test="bottomtype != null">
                bottomtype,
            </if>
            <if test="sizeKind != null">
                size_kind,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="tagPrice != null">
                tag_price,
            </if>
            <if test="suggestTagPrice != null">
                suggest_tag_price,
            </if>
            <if test="purchaseTaxPrice != null">
                purchase_tax_price,
            </if>
            <if test="costtaxrate != null">
                costtaxrate,
            </if>
            <if test="saletaxrate != null">
                saletaxrate,
            </if>
            <if test="materialPrice != null">
                material_price,
            </if>
            <if test="supplierNo != null">
                supplier_no,
            </if>
            <if test="supplierItemNo != null">
                supplier_item_no,
            </if>
            <if test="supplierItemName != null">
                supplier_item_name,
            </if>
            <if test="orderfrom != null">
                orderfrom,
            </if>
            <if test="years != null">
                years,
            </if>
            <if test="sellSeason != null">
                sell_season,
            </if>
            <if test="purchaseSeason != null">
                purchase_season,
            </if>
            <if test="saleDate != null">
                sale_date,
            </if>
            <if test="searchCode != null">
                search_code,
            </if>
            <if test="style != null">
                style,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="timeSeq != null">
                time_seq,
            </if>
            <if test="season != null">
                season,
            </if>
            <if test="liner != null">
                liner,
            </if>
            <if test="outsole != null">
                outsole,
            </if>
            <if test="pattern != null">
                pattern,
            </if>
            <if test="itemCode2 != null">
                item_code2,
            </if>
            <if test="extDevProp != null">
                ext_dev_prop,
            </if>
            <if test="extBrandStyle != null">
                ext_brand_style,
            </if>
            <if test="extStyle != null">
                ext_style,
            </if>
            <if test="extSeries != null">
                ext_series,
            </if>
            <if test="extSpecProp != null">
                ext_spec_prop,
            </if>
            <if test="itemStyleNo != null">
                item_style_no,
            </if>
            <if test="itemSeriesNo != null">
                item_series_no,
            </if>
            <if test="japanName != null">
                japan_name,
            </if>
            <if test="designerName != null">
                designer_name,
            </if>
            <if test="origineCountry != null">
                origine_country,
            </if>
            <if test="japanTagPrice != null">
                japan_tag_price,
            </if>
            <if test="japanCost != null">
                japan_cost,
            </if>
            <if test="saleYear != null">
                sale_year,
            </if>
            <if test="saleWeek != null">
                sale_week,
            </if>
            <if test="brandSeason != null">
                brand_season,
            </if>
            <if test="itemFlag != null">
                item_flag,
            </if>
            <if test="plateCode != null">
                plate_code,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="itemNo != null">
                #{itemNo,jdbcType=CHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="enName != null">
                #{enName,jdbcType=VARCHAR},
            </if>
            <if test="sysNo != null">
                #{sysNo,jdbcType=VARCHAR},
            </if>
            <if test="styleNo != null">
                #{styleNo,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null">
                #{brandNo,jdbcType=CHAR},
            </if>
            <if test="shoeModel != null">
                #{shoeModel,jdbcType=VARCHAR},
            </if>
            <if test="ingredients != null">
                #{ingredients,jdbcType=VARCHAR},
            </if>
            <if test="mainqdb != null">
                #{mainqdb,jdbcType=VARCHAR},
            </if>
            <if test="lining != null">
                #{lining,jdbcType=VARCHAR},
            </if>
            <if test="mainColor != null">
                #{mainColor,jdbcType=VARCHAR},
            </if>
            <if test="colorNo != null">
                #{colorNo,jdbcType=CHAR},
            </if>
            <if test="categoryNo != null">
                #{categoryNo,jdbcType=CHAR},
            </if>
            <if test="rootCategoryNo != null">
                #{rootCategoryNo,jdbcType=CHAR},
            </if>
            <if test="repeatlisting != null">
                #{repeatlisting,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="heeltype != null">
                #{heeltype,jdbcType=VARCHAR},
            </if>
            <if test="bottomtype != null">
                #{bottomtype,jdbcType=VARCHAR},
            </if>
            <if test="sizeKind != null">
                #{sizeKind,jdbcType=CHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="tagPrice != null">
                #{tagPrice,jdbcType=DECIMAL},
            </if>
            <if test="suggestTagPrice != null">
                #{suggestTagPrice,jdbcType=DECIMAL},
            </if>
            <if test="purchaseTaxPrice != null">
                #{purchaseTaxPrice,jdbcType=DECIMAL},
            </if>
            <if test="costtaxrate != null">
                #{costtaxrate,jdbcType=DECIMAL},
            </if>
            <if test="saletaxrate != null">
                #{saletaxrate,jdbcType=DECIMAL},
            </if>
            <if test="materialPrice != null">
                #{materialPrice,jdbcType=DECIMAL},
            </if>
            <if test="supplierNo != null">
                #{supplierNo,jdbcType=CHAR},
            </if>
            <if test="supplierItemNo != null">
                #{supplierItemNo,jdbcType=VARCHAR},
            </if>
            <if test="supplierItemName != null">
                #{supplierItemName,jdbcType=VARCHAR},
            </if>
            <if test="orderfrom != null">
                #{orderfrom,jdbcType=VARCHAR},
            </if>
            <if test="years != null">
                #{years,jdbcType=VARCHAR},
            </if>
            <if test="sellSeason != null">
                #{sellSeason,jdbcType=VARCHAR},
            </if>
            <if test="purchaseSeason != null">
                #{purchaseSeason,jdbcType=VARCHAR},
            </if>
            <if test="saleDate != null">
                #{saleDate,jdbcType=DATE},
            </if>
            <if test="searchCode != null">
                #{searchCode,jdbcType=VARCHAR},
            </if>
            <if test="style != null">
                #{style,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="timeSeq != null">
                #{timeSeq,jdbcType=BIGINT},
            </if>
            <if test="season != null">
                #{season,jdbcType=VARCHAR},
            </if>
            <if test="liner != null">
                #{liner,jdbcType=VARCHAR},
            </if>
            <if test="outsole != null">
                #{outsole,jdbcType=VARCHAR},
            </if>
            <if test="pattern != null">
                #{pattern,jdbcType=VARCHAR},
            </if>
            <if test="itemCode2 != null">
                #{itemCode2,jdbcType=VARCHAR},
            </if>
            <if test="extDevProp != null">
                #{extDevProp,jdbcType=VARCHAR},
            </if>
            <if test="extBrandStyle != null">
                #{extBrandStyle,jdbcType=VARCHAR},
            </if>
            <if test="extStyle != null">
                #{extStyle,jdbcType=VARCHAR},
            </if>
            <if test="extSeries != null">
                #{extSeries,jdbcType=VARCHAR},
            </if>
            <if test="extSpecProp != null">
                #{extSpecProp,jdbcType=VARCHAR},
            </if>
            <if test="itemStyleNo != null">
                #{itemStyleNo,jdbcType=VARCHAR},
            </if>
            <if test="itemSeriesNo != null">
                #{itemSeriesNo,jdbcType=VARCHAR},
            </if>
            <if test="japanName != null">
                #{japanName,jdbcType=VARCHAR},
            </if>
            <if test="designerName != null">
                #{designerName,jdbcType=VARCHAR},
            </if>
            <if test="origineCountry != null">
                #{origineCountry,jdbcType=VARCHAR},
            </if>
            <if test="japanTagPrice != null">
                #{japanTagPrice,jdbcType=DECIMAL},
            </if>
            <if test="japanCost != null">
                #{japanCost,jdbcType=DECIMAL},
            </if>
            <if test="saleYear != null">
                #{saleYear,jdbcType=CHAR},
            </if>
            <if test="saleWeek != null">
                #{saleWeek,jdbcType=CHAR},
            </if>
            <if test="brandSeason != null">
                #{brandSeason,jdbcType=VARCHAR},
            </if>
            <if test="itemFlag != null">
                #{itemFlag,jdbcType=VARCHAR},
            </if>
            <if test="plateCode != null">
                #{plateCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" >
        insert into item (
        id, item_no, code, name, full_name, en_name, sys_no, style_no, brand_no, shoe_model, 
        ingredients, mainqdb, lining, main_color, color_no, category_no, root_category_no, repeatlisting, gender, heeltype, 
        bottomtype, size_kind, status, tag_price, suggest_tag_price, purchase_tax_price, costtaxrate, saletaxrate, material_price, supplier_no, 
        supplier_item_no, supplier_item_name, orderfrom, years, sell_season, purchase_season, sale_date, search_code, style, create_user, 
        create_time, update_user, update_time, remark, time_seq, season, liner, outsole, pattern, item_code2, 
        ext_dev_prop, ext_brand_style, ext_style, ext_series, ext_spec_prop, item_style_no, item_series_no, japan_name, designer_name, origine_country, 
        japan_tag_price, japan_cost, sale_year, sale_week, brand_season, item_flag, plate_code
        ) values 
        <foreach collection="datas" item="data" separator=",">
            (#{data.id,jdbcType=INTEGER}, #{data.itemNo,jdbcType=CHAR}, #{data.code,jdbcType=VARCHAR}, 
            #{data.name,jdbcType=VARCHAR}, #{data.fullName,jdbcType=VARCHAR}, #{data.enName,jdbcType=VARCHAR}, 
            #{data.sysNo,jdbcType=VARCHAR}, #{data.styleNo,jdbcType=VARCHAR}, #{data.brandNo,jdbcType=CHAR}, 
            #{data.shoeModel,jdbcType=VARCHAR}, #{data.ingredients,jdbcType=VARCHAR}, #{data.mainqdb,jdbcType=VARCHAR}, 
            #{data.lining,jdbcType=VARCHAR}, #{data.mainColor,jdbcType=VARCHAR}, #{data.colorNo,jdbcType=CHAR}, 
            #{data.categoryNo,jdbcType=CHAR}, #{data.rootCategoryNo,jdbcType=CHAR}, #{data.repeatlisting,jdbcType=VARCHAR}, 
            #{data.gender,jdbcType=VARCHAR}, #{data.heeltype,jdbcType=VARCHAR}, #{data.bottomtype,jdbcType=VARCHAR}, 
            #{data.sizeKind,jdbcType=CHAR}, #{data.status,jdbcType=TINYINT}, #{data.tagPrice,jdbcType=DECIMAL}, 
            #{data.suggestTagPrice,jdbcType=DECIMAL}, #{data.purchaseTaxPrice,jdbcType=DECIMAL}, #{data.costtaxrate,jdbcType=DECIMAL}, 
            #{data.saletaxrate,jdbcType=DECIMAL}, #{data.materialPrice,jdbcType=DECIMAL}, #{data.supplierNo,jdbcType=CHAR}, 
            #{data.supplierItemNo,jdbcType=VARCHAR}, #{data.supplierItemName,jdbcType=VARCHAR}, #{data.orderfrom,jdbcType=VARCHAR}, 
            #{data.years,jdbcType=VARCHAR}, #{data.sellSeason,jdbcType=VARCHAR}, #{data.purchaseSeason,jdbcType=VARCHAR}, 
            #{data.saleDate,jdbcType=DATE}, #{data.searchCode,jdbcType=VARCHAR}, #{data.style,jdbcType=VARCHAR}, 
            #{data.createUser,jdbcType=VARCHAR}, #{data.createTime,jdbcType=TIMESTAMP}, #{data.updateUser,jdbcType=VARCHAR}, 
            #{data.updateTime,jdbcType=TIMESTAMP}, #{data.remark,jdbcType=VARCHAR}, #{data.timeSeq,jdbcType=BIGINT}, 
            #{data.season,jdbcType=VARCHAR}, #{data.liner,jdbcType=VARCHAR}, #{data.outsole,jdbcType=VARCHAR}, 
            #{data.pattern,jdbcType=VARCHAR}, #{data.itemCode2,jdbcType=VARCHAR}, #{data.extDevProp,jdbcType=VARCHAR}, 
            #{data.extBrandStyle,jdbcType=VARCHAR}, #{data.extStyle,jdbcType=VARCHAR}, #{data.extSeries,jdbcType=VARCHAR}, 
            #{data.extSpecProp,jdbcType=VARCHAR}, #{data.itemStyleNo,jdbcType=VARCHAR}, #{data.itemSeriesNo,jdbcType=VARCHAR}, 
            #{data.japanName,jdbcType=VARCHAR}, #{data.designerName,jdbcType=VARCHAR}, #{data.origineCountry,jdbcType=VARCHAR}, 
            #{data.japanTagPrice,jdbcType=DECIMAL}, #{data.japanCost,jdbcType=DECIMAL}, #{data.saleYear,jdbcType=CHAR}, 
            #{data.saleWeek,jdbcType=CHAR}, #{data.brandSeason,jdbcType=VARCHAR}, #{data.itemFlag,jdbcType=VARCHAR}, 
            #{data.plateCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="Item">
        update item
        <set>
            <if test="itemNo != null">
                item_no = #{itemNo,jdbcType=CHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="fullName != null">
                full_name = #{fullName,jdbcType=VARCHAR},
            </if>
            <if test="enName != null">
                en_name = #{enName,jdbcType=VARCHAR},
            </if>
            <if test="sysNo != null">
                sys_no = #{sysNo,jdbcType=VARCHAR},
            </if>
            <if test="styleNo != null">
                style_no = #{styleNo,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null">
                brand_no = #{brandNo,jdbcType=CHAR},
            </if>
            <if test="shoeModel != null">
                shoe_model = #{shoeModel,jdbcType=VARCHAR},
            </if>
            <if test="ingredients != null">
                ingredients = #{ingredients,jdbcType=VARCHAR},
            </if>
            <if test="mainqdb != null">
                mainqdb = #{mainqdb,jdbcType=VARCHAR},
            </if>
            <if test="lining != null">
                lining = #{lining,jdbcType=VARCHAR},
            </if>
            <if test="mainColor != null">
                main_color = #{mainColor,jdbcType=VARCHAR},
            </if>
            <if test="colorNo != null">
                color_no = #{colorNo,jdbcType=CHAR},
            </if>
            <if test="categoryNo != null">
                category_no = #{categoryNo,jdbcType=CHAR},
            </if>
            <if test="rootCategoryNo != null">
                root_category_no = #{rootCategoryNo,jdbcType=CHAR},
            </if>
            <if test="repeatlisting != null">
                repeatlisting = #{repeatlisting,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="heeltype != null">
                heeltype = #{heeltype,jdbcType=VARCHAR},
            </if>
            <if test="bottomtype != null">
                bottomtype = #{bottomtype,jdbcType=VARCHAR},
            </if>
            <if test="sizeKind != null">
                size_kind = #{sizeKind,jdbcType=CHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="tagPrice != null">
                tag_price = #{tagPrice,jdbcType=DECIMAL},
            </if>
            <if test="suggestTagPrice != null">
                suggest_tag_price = #{suggestTagPrice,jdbcType=DECIMAL},
            </if>
            <if test="purchaseTaxPrice != null">
                purchase_tax_price = #{purchaseTaxPrice,jdbcType=DECIMAL},
            </if>
            <if test="costtaxrate != null">
                costtaxrate = #{costtaxrate,jdbcType=DECIMAL},
            </if>
            <if test="saletaxrate != null">
                saletaxrate = #{saletaxrate,jdbcType=DECIMAL},
            </if>
            <if test="materialPrice != null">
                material_price = #{materialPrice,jdbcType=DECIMAL},
            </if>
            <if test="supplierNo != null">
                supplier_no = #{supplierNo,jdbcType=CHAR},
            </if>
            <if test="supplierItemNo != null">
                supplier_item_no = #{supplierItemNo,jdbcType=VARCHAR},
            </if>
            <if test="supplierItemName != null">
                supplier_item_name = #{supplierItemName,jdbcType=VARCHAR},
            </if>
            <if test="orderfrom != null">
                orderfrom = #{orderfrom,jdbcType=VARCHAR},
            </if>
            <if test="years != null">
                years = #{years,jdbcType=VARCHAR},
            </if>
            <if test="sellSeason != null">
                sell_season = #{sellSeason,jdbcType=VARCHAR},
            </if>
            <if test="purchaseSeason != null">
                purchase_season = #{purchaseSeason,jdbcType=VARCHAR},
            </if>
            <if test="saleDate != null">
                sale_date = #{saleDate,jdbcType=DATE},
            </if>
            <if test="searchCode != null">
                search_code = #{searchCode,jdbcType=VARCHAR},
            </if>
            <if test="style != null">
                style = #{style,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="timeSeq != null">
                time_seq = #{timeSeq,jdbcType=BIGINT},
            </if>
            <if test="season != null">
                season = #{season,jdbcType=VARCHAR},
            </if>
            <if test="liner != null">
                liner = #{liner,jdbcType=VARCHAR},
            </if>
            <if test="outsole != null">
                outsole = #{outsole,jdbcType=VARCHAR},
            </if>
            <if test="pattern != null">
                pattern = #{pattern,jdbcType=VARCHAR},
            </if>
            <if test="itemCode2 != null">
                item_code2 = #{itemCode2,jdbcType=VARCHAR},
            </if>
            <if test="extDevProp != null">
                ext_dev_prop = #{extDevProp,jdbcType=VARCHAR},
            </if>
            <if test="extBrandStyle != null">
                ext_brand_style = #{extBrandStyle,jdbcType=VARCHAR},
            </if>
            <if test="extStyle != null">
                ext_style = #{extStyle,jdbcType=VARCHAR},
            </if>
            <if test="extSeries != null">
                ext_series = #{extSeries,jdbcType=VARCHAR},
            </if>
            <if test="extSpecProp != null">
                ext_spec_prop = #{extSpecProp,jdbcType=VARCHAR},
            </if>
            <if test="itemStyleNo != null">
                item_style_no = #{itemStyleNo,jdbcType=VARCHAR},
            </if>
            <if test="itemSeriesNo != null">
                item_series_no = #{itemSeriesNo,jdbcType=VARCHAR},
            </if>
            <if test="japanName != null">
                japan_name = #{japanName,jdbcType=VARCHAR},
            </if>
            <if test="designerName != null">
                designer_name = #{designerName,jdbcType=VARCHAR},
            </if>
            <if test="origineCountry != null">
                origine_country = #{origineCountry,jdbcType=VARCHAR},
            </if>
            <if test="japanTagPrice != null">
                japan_tag_price = #{japanTagPrice,jdbcType=DECIMAL},
            </if>
            <if test="japanCost != null">
                japan_cost = #{japanCost,jdbcType=DECIMAL},
            </if>
            <if test="saleYear != null">
                sale_year = #{saleYear,jdbcType=CHAR},
            </if>
            <if test="saleWeek != null">
                sale_week = #{saleWeek,jdbcType=CHAR},
            </if>
            <if test="brandSeason != null">
                brand_season = #{brandSeason,jdbcType=VARCHAR},
            </if>
            <if test="itemFlag != null">
                item_flag = #{itemFlag,jdbcType=VARCHAR},
            </if>
            <if test="plateCode != null">
                plate_code = #{plateCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER} 
    </update>
    <delete id="deleteByPrimaryKey" parameterType="Item">
        delete from item
        where id = #{id,jdbcType=INTEGER} 
    </delete>
    <select id="selectByPrimaryKey" parameterType="Item" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from item
        where id = #{id,jdbcType=INTEGER} 
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from item 
        Where 1 = 1 
        <include refid="Condition" />
    </select>
    <select id="selectByParams" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from item
        where 1 = 1 
        <include refid="Condition" />
    </select>
    <select id="selectByPage" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from item
        where 1 = 1 
        <include refid="Condition" />
       <!-- <if test="orderByField != null and ''!=orderByField" >
            order by ${orderByField}
            <if test="orderByField" >
                ${orderBy}
            </if>
        </if>-->
        LIMIT #{page.startRowNum} ,#{page.pageSize}
    </select>
    <select id="queryItemByParams" resultType="ItemBaseInfo" parameterType="map">
        SELECT
        i.code itemCode,
        i.item_no itemNo,
        i.name as itemName,
        i.size_kind sizeKind,
        b.organ_type_no organTypeNo,
        o.name organTypeName,
        b.name brandName,
        sku.size_no sizeNo,
        sku.id as skuNo,
        sku.barcode barcode,
        i.status
        from item i
        INNER JOIN item_sku sku on sku.item_no = i.item_no
        inner join brand b on i.brand_no = b.brand_no
        left join organ_type o on b.organ_type_no = o.organ_type_no
        <where>
            <if test="params.barcode != null and !''.equals(params.barcode)">
                sku.barcode = #{params.barcode}
            </if>
            <if test="params.code != null and !''.equals(params.code)">
                i.code = #{params.code}
            </if>
            <if test="params.brandNo != null and !''.equals(params.brandNo)">
                and i.brand_no = #{params.brandNo}
            </if>
            <if test="params.sizeNo != null and !''.equals(params.sizeNo)">
                and sku.size_no = #{params.sizeNo}
            </if>
            <if test="params.status != null">
                and i.status = #{params.status}
            </if>
        </where>
    </select>
</mapper>