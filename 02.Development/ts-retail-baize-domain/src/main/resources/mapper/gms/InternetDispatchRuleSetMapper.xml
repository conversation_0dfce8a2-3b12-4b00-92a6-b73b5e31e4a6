<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetDispatchRuleSetRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet">
                
        <id column="id" property="id" jdbcType="BIGINT" />
        
        
        <result column="store_state" property="storeState" jdbcType="TINYINT" />
        
        <result column="noshared_state" property="nosharedState" jdbcType="TINYINT" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR" />
        
        <result column="rule_no" property="ruleNo" jdbcType="CHAR" />
        
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        
        <result column="begin_time" property="beginTime" jdbcType="TIMESTAMP" />
        
        <result column="type" property="type" jdbcType="TINYINT" />
        
        <result column="state" property="state" jdbcType="TINYINT" />
        
        <result column="ruletype" property="ruletype" jdbcType="TINYINT" />
        
        <result column="dispatch_num_state" property="dispatchNumState" jdbcType="TINYINT" />
        
        <result column="dispatch_num_priority" property="dispatchNumPriority" jdbcType="TINYINT" />
        
        <result column="company_state" property="companyState" jdbcType="TINYINT" />
        
        <result column="company_priority" property="companyPriority" jdbcType="TINYINT" />
        
        <result column="shop_type_state" property="shopTypeState" jdbcType="TINYINT" />
        
        <result column="shop_type_priority" property="shopTypePriority" jdbcType="TINYINT" />
        
        <result column="noshared_priority" property="nosharedPriority" jdbcType="TINYINT" />
        
        <result column="bag_priority" property="bagPriority" jdbcType="TINYINT" />
        
        <result column="bag_state" property="bagState" jdbcType="TINYINT" />
        
        <result column="shop_priority" property="shopPriority" jdbcType="TINYINT" />
        
        <result column="shop_state" property="shopState" jdbcType="TINYINT" />
        
        <result column="store_priority" property="storePriority" jdbcType="TINYINT" />
        
        <result column="inventory_priority" property="inventoryPriority" jdbcType="TINYINT" />
        
        <result column="inventory_state" property="inventoryState" jdbcType="TINYINT" />
        
        <result column="action_priority" property="actionPriority" jdbcType="TINYINT" />
        
        <result column="action_state" property="actionState" jdbcType="TINYINT" />
        
        <result column="near_priority" property="nearPriority" jdbcType="TINYINT" />
        
        <result column="near_state" property="nearState" jdbcType="TINYINT" />
        
        <result column="avg_priority" property="avgPriority" jdbcType="TINYINT" />
        
        <result column="avg_state" property="avgState" jdbcType="TINYINT" />

        <result column="order_unit_state" property="orderUnitState" jdbcType="TINYINT" />
        <result column="order_unit_priority" property="orderUnitPriority" jdbcType="TINYINT" />

        <result column="shop_credit_priority" property="shopCreditPriority" jdbcType="TINYINT" />
        <result column="shop_credit_state" property="shopCreditState" jdbcType="TINYINT" />

        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        
        <result column="brand_no" property="brandNo" jdbcType="VARCHAR" />
        
        <result column="platform_name" property="platformName" jdbcType="VARCHAR" />
        
        <result column="platform_no" property="platformNo" jdbcType="VARCHAR" />
        
        <result column="organ_type_name" property="organTypeName" jdbcType="VARCHAR" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `store_state`,
        `noshared_state`,
        `update_time`,
        `update_user`,
        `create_time`,
        `create_user`,
        `rule_name`,
        `rule_no`,
        `end_time`,
        `begin_time`,
        `type`,
        `state`,
        `ruletype`,
        `dispatch_num_state`,
        `dispatch_num_priority`,
        `company_state`,
        `company_priority`,
        `shop_type_state`,
        `shop_type_priority`,
        `noshared_priority`,
        `bag_priority`,
        `bag_state`,
        `shop_priority`,
        `shop_state`,
        `store_priority`,
        `inventory_priority`,
        `inventory_state`,
        `action_priority`,
        `action_state`,
        `near_priority`,
        `near_state`,
        `avg_priority`,
        `avg_state`,
        `brand_name`,
        `brand_no`,
        `platform_name`,
        `platform_no`,
        `organ_type_name`,
        `id`,
        `organ_type_no`,
        `order_unit_state`,
        `order_unit_priority`,
        `shop_credit_priority`,
        `shop_credit_state`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.storeState ">
                
                AND `store_state`=#{params.storeState}
                
            </if>
            
            <if test="null!=params.nosharedState ">
                
                AND `noshared_state`=#{params.nosharedState}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.ruleName  and ''!=params.ruleName ">
                
                AND `rule_name` like CONCAT('%',#{params.ruleName},'%') 
                
            </if>
            
            <if test="null!=params.ruleNo  and ''!=params.ruleNo ">
                
                AND `rule_no`=#{params.ruleNo}
                
            </if>
            
            <if test="null!=params.endTime ">
                
                AND `end_time`=#{params.endTime}
                
            </if>
            
            <if test="null!=params.beginTime ">
                
                AND `begin_time`=#{params.beginTime}
                
            </if>
            
            <if test="null!=params.type ">
                
                AND `type`=#{params.type}
                
            </if>
            
            <if test="null!=params.state ">
                
                AND `state`=#{params.state}
                
            </if>
            
            <if test="null!=params.ruletype ">
                
                AND `ruletype`=#{params.ruletype}
                
            </if>
            
            <if test="null!=params.dispatchNumState ">
                
                AND `dispatch_num_state`=#{params.dispatchNumState}
                
            </if>
            
            <if test="null!=params.dispatchNumPriority ">
                
                AND `dispatch_num_priority`=#{params.dispatchNumPriority}
                
            </if>
            
            <if test="null!=params.companyState ">
                
                AND `company_state`=#{params.companyState}
                
            </if>
            
            <if test="null!=params.companyPriority ">
                
                AND `company_priority`=#{params.companyPriority}
                
            </if>
            
            <if test="null!=params.shopTypeState ">
                
                AND `shop_type_state`=#{params.shopTypeState}
                
            </if>
            
            <if test="null!=params.shopTypePriority ">
                
                AND `shop_type_priority`=#{params.shopTypePriority}
                
            </if>
            
            <if test="null!=params.nosharedPriority ">
                
                AND `noshared_priority`=#{params.nosharedPriority}
                
            </if>
            
            <if test="null!=params.bagPriority ">
                
                AND `bag_priority`=#{params.bagPriority}
                
            </if>
            
            <if test="null!=params.bagState ">
                
                AND `bag_state`=#{params.bagState}
                
            </if>
            
            <if test="null!=params.shopPriority ">
                
                AND `shop_priority`=#{params.shopPriority}
                
            </if>
            
            <if test="null!=params.shopState ">
                
                AND `shop_state`=#{params.shopState}
                
            </if>
            
            <if test="null!=params.storePriority ">
                
                AND `store_priority`=#{params.storePriority}
                
            </if>
            
            <if test="null!=params.inventoryPriority ">
                
                AND `inventory_priority`=#{params.inventoryPriority}
                
            </if>
            
            <if test="null!=params.inventoryState ">
                
                AND `inventory_state`=#{params.inventoryState}
                
            </if>
            
            <if test="null!=params.actionPriority ">
                
                AND `action_priority`=#{params.actionPriority}
                
            </if>
            
            <if test="null!=params.actionState ">
                
                AND `action_state`=#{params.actionState}
                
            </if>
            
            <if test="null!=params.nearPriority ">
                
                AND `near_priority`=#{params.nearPriority}
                
            </if>
            
            <if test="null!=params.nearState ">
                
                AND `near_state`=#{params.nearState}
                
            </if>
            
            <if test="null!=params.avgPriority ">
                
                AND `avg_priority`=#{params.avgPriority}
                
            </if>
            
            <if test="null!=params.avgState ">
                
                AND `avg_state`=#{params.avgState}
                
            </if>

            <if test="null!=params.orderUnitState">
                AND `order_unit_state`=#{params.orderUnitState}
            </if>

            <if test="null!=params.orderUnitPriority">
                AND `order_unit_priority`=#{params.orderUnitPriority}
            </if>

            <if test="null!=params.shopCreditPriority">
                AND `shop_credit_priority`=#{params.shopCreditPriority}
            </if>

            <if test="null!=params.shopCreditState">
                AND `shop_credit_state`=#{params.shopCreditState}
            </if>
            
            <if test="null!=params.brandName  and ''!=params.brandName ">
                
                AND `brand_name` like CONCAT('%',#{params.brandName},'%') 
                
            </if>
            
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                
                AND `brand_no`=#{params.brandNo}
                
            </if>
            
            <if test="null!=params.platformName  and ''!=params.platformName ">
                
                AND `platform_name` like CONCAT('%',#{params.platformName},'%') 
                
            </if>
            
            <if test="null!=params.platformNo  and ''!=params.platformNo ">
                
                AND `platform_no`=#{params.platformNo}
                
            </if>
            
            <if test="null!=params.organTypeName  and ''!=params.organTypeName ">
                
                AND `organ_type_name` like CONCAT('%',#{params.organTypeName},'%') 
                
            </if>
            
            <if test="null!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                
                AND `organ_type_no`=#{params.organTypeNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_dispatch_rule_set
        <where>
            <include refid="condition" />
            and (length(ifnull(rule_no, '')) != 0)
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set
        <where>
            <include refid="condition" />
            and (length(ifnull(rule_no, '')) != 0)
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_dispatch_rule_set
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_dispatch_rule_set
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_dispatch_rule_set
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet"
            useGeneratedKeys="true"
            keyColumn="id"
            keyProperty="id"  >
        INSERT INTO internet_dispatch_rule_set
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="storeState != null">
                `store_state`,
            </if>
            
            <if test="nosharedState != null">
                `noshared_state`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="ruleName != null">
                `rule_name`,
            </if>
            
            <if test="ruleNo != null">
                `rule_no`,
            </if>
            
            <if test="endTime != null">
                `end_time`,
            </if>
            
            <if test="beginTime != null">
                `begin_time`,
            </if>
            
            <if test="type != null">
                `type`,
            </if>
            
            <if test="state != null">
                `state`,
            </if>
            
            <if test="ruletype != null">
                `ruletype`,
            </if>
            
            <if test="dispatchNumState != null">
                `dispatch_num_state`,
            </if>
            
            <if test="dispatchNumPriority != null">
                `dispatch_num_priority`,
            </if>
            
            <if test="companyState != null">
                `company_state`,
            </if>
            
            <if test="companyPriority != null">
                `company_priority`,
            </if>
            
            <if test="shopTypeState != null">
                `shop_type_state`,
            </if>
            
            <if test="shopTypePriority != null">
                `shop_type_priority`,
            </if>
            
            <if test="nosharedPriority != null">
                `noshared_priority`,
            </if>
            
            <if test="bagPriority != null">
                `bag_priority`,
            </if>
            
            <if test="bagState != null">
                `bag_state`,
            </if>
            
            <if test="shopPriority != null">
                `shop_priority`,
            </if>
            
            <if test="shopState != null">
                `shop_state`,
            </if>
            
            <if test="storePriority != null">
                `store_priority`,
            </if>
            
            <if test="inventoryPriority != null">
                `inventory_priority`,
            </if>
            
            <if test="inventoryState != null">
                `inventory_state`,
            </if>
            
            <if test="actionPriority != null">
                `action_priority`,
            </if>
            
            <if test="actionState != null">
                `action_state`,
            </if>
            
            <if test="nearPriority != null">
                `near_priority`,
            </if>
            
            <if test="nearState != null">
                `near_state`,
            </if>
            
            <if test="avgPriority != null">
                `avg_priority`,
            </if>
            
            <if test="avgState != null">
                `avg_state`,
            </if>

            <if test="orderUnitState != null">
                `order_unit_state`,
            </if>

            <if test="orderUnitPriority != null">
                `order_unit_priority`,
            </if>

            <if test="shopCreditState != null">
                `shop_credit_state`,
            </if>

            <if test="shopCreditPriority != null">
                `shop_credit_priority`,
            </if>
            
            <if test="brandName != null">
                `brand_name`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="platformName != null">
                `platform_name`,
            </if>
            
            <if test="platformNo != null">
                `platform_no`,
            </if>
            
            <if test="organTypeName != null">
                `organ_type_name`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="storeState != null">
                #{storeState},
            </if>
            
            <if test="nosharedState != null">
                #{nosharedState},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="ruleName != null">
                #{ruleName},
            </if>
            
            <if test="ruleNo != null">
                #{ruleNo},
            </if>
            
            <if test="endTime != null">
                #{endTime},
            </if>
            
            <if test="beginTime != null">
                #{beginTime},
            </if>
            
            <if test="type != null">
                #{type},
            </if>
            
            <if test="state != null">
                #{state},
            </if>
            
            <if test="ruletype != null">
                #{ruletype},
            </if>
            
            <if test="dispatchNumState != null">
                #{dispatchNumState},
            </if>
            
            <if test="dispatchNumPriority != null">
                #{dispatchNumPriority},
            </if>
            
            <if test="companyState != null">
                #{companyState},
            </if>
            
            <if test="companyPriority != null">
                #{companyPriority},
            </if>
            
            <if test="shopTypeState != null">
                #{shopTypeState},
            </if>
            
            <if test="shopTypePriority != null">
                #{shopTypePriority},
            </if>
            
            <if test="nosharedPriority != null">
                #{nosharedPriority},
            </if>
            
            <if test="bagPriority != null">
                #{bagPriority},
            </if>
            
            <if test="bagState != null">
                #{bagState},
            </if>
            
            <if test="shopPriority != null">
                #{shopPriority},
            </if>
            
            <if test="shopState != null">
                #{shopState},
            </if>
            
            <if test="storePriority != null">
                #{storePriority},
            </if>
            
            <if test="inventoryPriority != null">
                #{inventoryPriority},
            </if>
            
            <if test="inventoryState != null">
                #{inventoryState},
            </if>
            
            <if test="actionPriority != null">
                #{actionPriority},
            </if>
            
            <if test="actionState != null">
                #{actionState},
            </if>
            
            <if test="nearPriority != null">
                #{nearPriority},
            </if>
            
            <if test="nearState != null">
                #{nearState},
            </if>
            
            <if test="avgPriority != null">
                #{avgPriority},
            </if>
            
            <if test="avgState != null">
                #{avgState},
            </if>

            <if test="orderUnitState != null">
                #{orderUnitState},
            </if>

            <if test="orderUnitPriority != null">
                #{orderUnitPriority},
            </if>

            <if test="shopCreditState != null">
                #{shopCreditState},
            </if>

            <if test="shopCreditPriority != null">
                #{shopCreditPriority},
            </if>
            
            <if test="brandName != null">
                #{brandName},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="platformName != null">
                #{platformName},
            </if>
            
            <if test="platformNo != null">
                #{platformNo},
            </if>
            
            <if test="organTypeName != null">
                #{organTypeName},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet">
        UPDATE internet_dispatch_rule_set
        <set>
            
            <if test="storeState != null">
                `store_state` = #{storeState},
            </if> 
            <if test="nosharedState != null">
                `noshared_state` = #{nosharedState},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="ruleName != null">
                `rule_name` = #{ruleName},
            </if> 
            <if test="ruleNo != null">
                `rule_no` = #{ruleNo},
            </if> 
            <if test="endTime != null">
                `end_time` = #{endTime},
            </if> 
            <if test="beginTime != null">
                `begin_time` = #{beginTime},
            </if> 
            <if test="type != null">
                `type` = #{type},
            </if> 
            <if test="state != null">
                `state` = #{state},
            </if> 
            <if test="ruletype != null">
                `ruletype` = #{ruletype},
            </if> 
            <if test="dispatchNumState != null">
                `dispatch_num_state` = #{dispatchNumState},
            </if> 
            <if test="dispatchNumPriority != null">
                `dispatch_num_priority` = #{dispatchNumPriority},
            </if> 
            <if test="companyState != null">
                `company_state` = #{companyState},
            </if> 
            <if test="companyPriority != null">
                `company_priority` = #{companyPriority},
            </if> 
            <if test="shopTypeState != null">
                `shop_type_state` = #{shopTypeState},
            </if> 
            <if test="shopTypePriority != null">
                `shop_type_priority` = #{shopTypePriority},
            </if> 
            <if test="nosharedPriority != null">
                `noshared_priority` = #{nosharedPriority},
            </if> 
            <if test="bagPriority != null">
                `bag_priority` = #{bagPriority},
            </if> 
            <if test="bagState != null">
                `bag_state` = #{bagState},
            </if> 
            <if test="shopPriority != null">
                `shop_priority` = #{shopPriority},
            </if> 
            <if test="shopState != null">
                `shop_state` = #{shopState},
            </if> 
            <if test="storePriority != null">
                `store_priority` = #{storePriority},
            </if> 
            <if test="inventoryPriority != null">
                `inventory_priority` = #{inventoryPriority},
            </if> 
            <if test="inventoryState != null">
                `inventory_state` = #{inventoryState},
            </if> 
            <if test="actionPriority != null">
                `action_priority` = #{actionPriority},
            </if> 
            <if test="actionState != null">
                `action_state` = #{actionState},
            </if> 
            <if test="nearPriority != null">
                `near_priority` = #{nearPriority},
            </if> 
            <if test="nearState != null">
                `near_state` = #{nearState},
            </if> 
            <if test="avgPriority != null">
                `avg_priority` = #{avgPriority},
            </if> 
            <if test="avgState != null">
                `avg_state` = #{avgState},
            </if>
            <if test="orderUnitState != null">
                `order_unit_state` = #{orderUnitState},
            </if>
            <if test="orderUnitPriority != null">
                `order_unit_priority` = #{orderUnitPriority},
            </if>

            <if test="shopCreditState != null">
                `shop_credit_state` = #{shopCreditState},
            </if>

            <if test="shopCreditPriority != null">
                `shop_credit_priority` = #{shopCreditPriority},
            </if>

            <if test="brandName != null">
                `brand_name` = #{brandName},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="platformName != null">
                `platform_name` = #{platformName},
            </if> 
            <if test="platformNo != null">
                `platform_no` = #{platformNo},
            </if> 
            <if test="organTypeName != null">
                `organ_type_name` = #{organTypeName},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


    <select id="exitsQueryData" resultType="int">
        select count(*) from internet_dispatch_rule_set rule
        inner join internet_dispatch_rule_set_dtl dtl on rule.rule_no = dtl.rule_no
        <where>
            and rule.state = 1
            <choose>
                <when test="'empty' == query.brandNo">
                    and length(ifnull(rule.brand_no, ''))=0
                </when>
                <otherwise>
                    and rule.brand_no = #{query.brandNo}
                </otherwise>
            </choose>
            <if test="query.noEqRuleNo!=null and query.noEqRuleNo!=''">
                and rule.rule_No !=#{query.noEqRuleNo}
            </if>
            <if test="query.noEqId!=null">
                and rule.id !=#{query.noEqId}
            </if>
            <if test="query.channelNo!=null and query.channelNo!=''">
                and dtl.channel_no = #{query.channelNo}
            </if>
            <if test="query.orderSourceNo!=null and query.orderSourceNo!=''">
                and dtl.order_source_no = #{query.orderSourceNo}
            </if>
            <if test="query.dtlList != null and query.dtlList.size() > 0">
                and
                <foreach collection="query.dtlList" item="item" separator="or" open="(" close=")">
                    (
                        dtl.channel_no = #{item.channelNo}
                        and <choose>
                                <when test="null == item.orderSourceNo or item.orderSourceNo.trim() == ''">
                                    length (ifnull(dtl.order_source_no , ''))=0
                                </when>
                                <otherwise>
                                    dtl.order_source_no = #{item.orderSourceNo}
                                </otherwise>
                            </choose>
                    )
                </foreach>
            </if>
        </where>
    </select>

    <select id="nextId" resultType="long">
        select id+1 from internet_dispatch_rule_set order by id desc limit 1;
	</select>
</mapper>