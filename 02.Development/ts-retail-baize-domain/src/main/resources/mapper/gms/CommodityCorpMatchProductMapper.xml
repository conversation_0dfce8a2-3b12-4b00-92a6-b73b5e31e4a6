<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.CommodityCorpMatchProductRepository">
    <!-- auto generate q -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct">
        <id column="id" property="id" jdbcType="VARCHAR" />	
        <result column="corp_brand_no" property="corpBrandNo" jdbcType="CHAR" />	
        <result column="style_color_code" property="styleColorCode" jdbcType="VARCHAR" />	
        <result column="corp_size_code" property="corpSizeCode" jdbcType="VARCHAR" />	
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />	
        <result column="inside_barcode" property="insideBarcode" jdbcType="VARCHAR" />	
        <result column="inside_barcode_id" property="insideBarcodeId" jdbcType="VARCHAR" />	
        <result column="is_sync_inv" property="isSyncInv" jdbcType="TINYINT" />	
    </resultMap>

    <sql id="column_list">
        `id`,`corp_brand_no`,`style_color_code`,`corp_size_code`,`update_time`,`inside_barcode`,`inside_barcode_id`,`is_sync_inv`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
        
        	<if test="null!=params.id  and ''!=params.id ">
				 AND `id`=#{params.id}
            </if>	
        
        	<if test="null!=params.corpBrandNo  and ''!=params.corpBrandNo ">
				 AND `corp_brand_no`=#{params.corpBrandNo}
            </if>	
        
        	<if test="null!=params.styleColorCode  and ''!=params.styleColorCode ">
				 AND `style_color_code`=#{params.styleColorCode}
            </if>	
        
        	<if test="null!=params.corpSizeCode  and ''!=params.corpSizeCode ">
				 AND `corp_size_code`=#{params.corpSizeCode}
            </if>	
        
        	<if test="null!=params.updateTime ">
				 AND `update_time`=#{params.updateTime}
            </if>	
        
        	<if test="null!=params.insideBarcode  and ''!=params.insideBarcode ">
				 AND `inside_barcode`=#{params.insideBarcode}
            </if>	
        
        	<if test="null!=params.insideBarcodeId  and ''!=params.insideBarcodeId ">
				 AND `inside_barcode_id`=#{params.insideBarcodeId}
            </if>	
        
        	<if test="null!=params.isSyncInv ">
				 AND `is_sync_inv`=#{params.isSyncInv}
            </if>	
        
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM tbl_commodity_corp_match_product
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM tbl_commodity_corp_match_product
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM tbl_commodity_corp_match_product
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM tbl_commodity_corp_match_product
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM tbl_commodity_corp_match_product
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM tbl_commodity_corp_match_product
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM tbl_commodity_corp_match_product
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM tbl_commodity_corp_match_product
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM tbl_commodity_corp_match_product
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct"  >
        INSERT INTO tbl_commodity_corp_match_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="corpBrandNo != null">
                `corp_brand_no`,
            </if>
            
            <if test="styleColorCode != null">
                `style_color_code`,
            </if>
            
            <if test="corpSizeCode != null">
                `corp_size_code`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="insideBarcode != null">
                `inside_barcode`,
            </if>
            
            <if test="insideBarcodeId != null">
                `inside_barcode_id`,
            </if>
            
            <if test="isSyncInv != null">
                `is_sync_inv`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="corpBrandNo != null">
                #{corpBrandNo},
            </if>
            
            <if test="styleColorCode != null">
                #{styleColorCode},
            </if>
            
            <if test="corpSizeCode != null">
                #{corpSizeCode},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="insideBarcode != null">
                #{insideBarcode},
            </if>
            
            <if test="insideBarcodeId != null">
                #{insideBarcodeId},
            </if>
            
            <if test="isSyncInv != null">
                #{isSyncInv},
            </if>
            
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tbl_commodity_corp_match_product (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.corpBrandNo}, #{item.styleColorCode}, #{item.corpSizeCode}, #{item.updateTime}, #{item.insideBarcode}, #{item.insideBarcodeId}, #{item.isSyncInv})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct">
        UPDATE tbl_commodity_corp_match_product
        <set>
            
            <if test="corpBrandNo != null">
                `corp_brand_no` = #{corpBrandNo},
            </if> 
            <if test="styleColorCode != null">
                `style_color_code` = #{styleColorCode},
            </if> 
            <if test="corpSizeCode != null">
                `corp_size_code` = #{corpSizeCode},
            </if> 
            <if test="insideBarcode != null">
                `inside_barcode` = #{insideBarcode},
            </if> 
            <if test="insideBarcodeId != null">
                `inside_barcode_id` = #{insideBarcodeId},
            </if> 
            <if test="isSyncInv != null">
                `is_sync_inv` = #{isSyncInv},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE id = #{id}
                
    </update>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM tbl_commodity_corp_match_product 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <select id="selectItemSkuByParams" resultMap="baseResultMap">
        select
        tccmp.id,
        tccmp.corp_brand_no,
        tccmp.style_color_code,
        tccmp.corp_size_code,
        tccmp.update_time,
        tccmp.inside_barcode,
        sku.id as inside_barcode_id,
        tccmp.is_sync_inv
        from tbl_commodity_corp_match_product tccmp
        INNER JOIN item i ON tccmp.style_color_code = i.`code`
        AND tccmp.corp_brand_no = i.brand_no
        INNER JOIN item_sku sku ON sku.item_no = i.item_no
        AND tccmp.corp_size_code = sku.size_no
        <where>
            sku.STATUS = 1
            AND i.STATUS = 1
            AND sku.barcode_status = 1
            <if test="param.styleColorCode !=null ">
                AND tccmp.style_color_code = #{param.styleColorCode}
            </if>
            <if test="param.corpBrandNo !=null ">
                AND tccmp.corp_brand_no = #{param.corpBrandNo}
            </if>
            <if test="param.corpSizeCode !=null ">
                AND tccmp.corp_size_code = #{param.corpSizeCode}
            </if>
        </where>

    </select>

    <!-- auto generate end-->
</mapper>