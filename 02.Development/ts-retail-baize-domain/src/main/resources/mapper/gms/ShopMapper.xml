<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.ShopRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.Shop">
                
        <id column="id" property="id" jdbcType="INTEGER" />
        
        
        <result column="fax" property="fax" jdbcType="VARCHAR" />
        
        <result column="location" property="location" jdbcType="VARCHAR" />
        
        <result column="parent_sale_no" property="parentSaleNo" jdbcType="VARCHAR" />
        
        <result column="full_name" property="fullName" jdbcType="VARCHAR" />
        
        <result column="short_name" property="shortName" jdbcType="VARCHAR" />
        
        <result column="search_code" property="searchCode" jdbcType="VARCHAR" />
        
        <result column="company_no" property="companyNo" jdbcType="CHAR" />
        
        <result column="store_no" property="storeNo" jdbcType="CHAR" />
        
        <result column="shop_mode_no" property="shopModeNo" jdbcType="VARCHAR" />
        
        <result column="time_seq" property="timeSeq" jdbcType="BIGINT" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="organ_no" property="organNo" jdbcType="CHAR" />
        
        <result column="group_brand" property="groupBrand" jdbcType="VARCHAR" />
        
        <result column="customer_no" property="customerNo" jdbcType="CHAR" />
        
        <result column="logistics_cost_payer" property="logisticsCostPayer" jdbcType="VARCHAR" />
        
        <result column="shop_longitude" property="shopLongitude" jdbcType="VARCHAR" />
        
        <result column="shop_lcode" property="shopLcode" jdbcType="VARCHAR" />
        
        <result column="code" property="code" jdbcType="VARCHAR" />
        
        <result column="shop_no" property="shopNo" jdbcType="CHAR" />
        
        <result column="shop_latitude" property="shopLatitude" jdbcType="VARCHAR" />
        
        <result column="biz_city_no" property="bizCityNo" jdbcType="CHAR" />
        
        <result column="sys_no" property="sysNo" jdbcType="VARCHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="on_line_flag" property="onLineFlag" jdbcType="TINYINT" />
        
        <result column="price_adjust_level" property="priceAdjustLevel" jdbcType="VARCHAR" />
        
        <result column="shop_classify" property="shopClassify" jdbcType="VARCHAR" />
        
        <result column="category_code" property="categoryCode" jdbcType="VARCHAR" />
        
        <result column="open_date" property="openDate" jdbcType="TIMESTAMP" />
        
        <result column="close_date" property="closeDate" jdbcType="TIMESTAMP" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="area" property="area" jdbcType="DECIMAL" />
        
        <result column="area_left" property="areaLeft" jdbcType="DECIMAL" />
        
        <result column="area_total" property="areaTotal" jdbcType="DECIMAL" />
        
        <result column="area_unit" property="areaUnit" jdbcType="VARCHAR" />
        
        <result column="province_no" property="provinceNo" jdbcType="VARCHAR" />
        
        <result column="city_no" property="cityNo" jdbcType="VARCHAR" />
        
        <result column="county_no" property="countyNo" jdbcType="VARCHAR" />
        
        <result column="address" property="address" jdbcType="VARCHAR" />
        
        <result column="zip_code" property="zipCode" jdbcType="VARCHAR" />
        
        <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
        
        <result column="tel" property="tel" jdbcType="VARCHAR" />
        
        <result column="email" property="email" jdbcType="VARCHAR" />
        
        <result column="channel_no" property="channelNo" jdbcType="CHAR" />
        
        <result column="cmcdist_no" property="cmcdistNo" jdbcType="CHAR" />
        
        <result column="employe_amount" property="employeAmount" jdbcType="INTEGER" />
        
        <result column="pay_type" property="payType" jdbcType="VARCHAR" />
        
        <result column="digits" property="digits" jdbcType="VARCHAR" />
        
        <result column="startup_time" property="startupTime" jdbcType="VARCHAR" />
        
        <result column="shutdown_time" property="shutdownTime" jdbcType="VARCHAR" />
        
        <result column="shop_level" property="shopLevel" jdbcType="VARCHAR" />
        
        <result column="major" property="major" jdbcType="VARCHAR" />
        
        <result column="multi" property="multi" jdbcType="VARCHAR" />
        
        <result column="sale_mode" property="saleMode" jdbcType="VARCHAR" />
        
        <result column="retail_type" property="retailType" jdbcType="VARCHAR" />
        
        <result column="mall_no" property="mallNo" jdbcType="CHAR" />
        
        <result column="map_flag" property="mapFlag" jdbcType="TINYINT" />
        
        <result column="corporation_no" property="corporationNo" jdbcType="VARCHAR" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
        <result column="sale_type" property="saleType" jdbcType="TINYINT" />
        
        <result column="sale_no" property="saleNo" jdbcType="VARCHAR" />
        
        <result column="region_no" property="regionNo" jdbcType="CHAR" />
        
    </resultMap>

    <sql id="column_list">
        `fax`,`location`,`parent_sale_no`,`full_name`,`short_name`,`search_code`,`company_no`,`store_no`,`shop_mode_no`,`time_seq`,`remark`,`update_time`,`update_user`,`create_time`,`organ_no`,`group_brand`,`customer_no`,`logistics_cost_payer`,`shop_longitude`,`shop_lcode`,`code`,`shop_no`,`id`,`shop_latitude`,`biz_city_no`,`sys_no`,`create_user`,`on_line_flag`,`price_adjust_level`,`shop_classify`,`category_code`,`open_date`,`close_date`,`status`,`area`,`area_left`,`area_total`,`area_unit`,`province_no`,`city_no`,`county_no`,`address`,`zip_code`,`contact_name`,`tel`,`email`,`channel_no`,`cmcdist_no`,`employe_amount`,`pay_type`,`digits`,`startup_time`,`shutdown_time`,`shop_level`,`major`,`multi`,`sale_mode`,`retail_type`,`mall_no`,`map_flag`,`corporation_no`,`organ_type_no`,`sale_type`,`sale_no`,`region_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            <if test="params.q != null and params.q != ''">
                AND (shop_no like CONCAT('%',#{params.q},'%') or short_name like CONCAT('%',#{params.queryParam},'%'))
            </if>
            
            <if test="null!=params.fax  and ''!=params.fax ">
                
                AND `fax`=#{params.fax}
                
            </if>
            
            <if test="null!=params.location  and ''!=params.location ">
                
                AND `location`=#{params.location}
                
            </if>
            
            <if test="null!=params.parentSaleNo  and ''!=params.parentSaleNo ">
                
                AND `parent_sale_no`=#{params.parentSaleNo}
                
            </if>
            
            <if test="null!=params.fullName  and ''!=params.fullName ">
                
                AND `full_name` like CONCAT('%',#{params.fullName},'%') 
                
            </if>
            
            <if test="null!=params.shortName  and ''!=params.shortName ">
                
                AND `short_name` like CONCAT('%',#{params.shortName},'%') 
                
            </if>
            
            <if test="null!=params.searchCode  and ''!=params.searchCode ">
                
                AND `search_code`=#{params.searchCode}
                
            </if>
            
            <if test="null!=params.companyNo  and ''!=params.companyNo ">
                
                AND `company_no`=#{params.companyNo}
                
            </if>
            
            <if test="null!=params.storeNo  and ''!=params.storeNo ">
                
                AND `store_no`=#{params.storeNo}
                
            </if>
            
            <if test="null!=params.shopModeNo  and ''!=params.shopModeNo ">
                
                AND `shop_mode_no`=#{params.shopModeNo}
                
            </if>
            
            <if test="null!=params.timeSeq ">
                
                AND `time_seq`=#{params.timeSeq}
                
            </if>
            
            <if test="null!=params.remark  and ''!=params.remark ">
                
                AND `remark`=#{params.remark}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.organNo  and ''!=params.organNo ">
                
                AND `organ_no`=#{params.organNo}
                
            </if>
            
            <if test="null!=params.groupBrand  and ''!=params.groupBrand ">
                
                AND `group_brand`=#{params.groupBrand}
                
            </if>
            
            <if test="null!=params.customerNo  and ''!=params.customerNo ">
                
                AND `customer_no`=#{params.customerNo}
                
            </if>
            
            <if test="null!=params.logisticsCostPayer  and ''!=params.logisticsCostPayer ">
                
                AND `logistics_cost_payer`=#{params.logisticsCostPayer}
                
            </if>
            
            <if test="null!=params.shopLongitude  and ''!=params.shopLongitude ">
                
                AND `shop_longitude`=#{params.shopLongitude}
                
            </if>
            
            <if test="null!=params.shopLcode  and ''!=params.shopLcode ">
                
                AND `shop_lcode`=#{params.shopLcode}
                
            </if>
            
            <if test="null!=params.code  and ''!=params.code ">
                
                AND `code`=#{params.code}
                
            </if>
            
            <if test="null!=params.shopNo  and ''!=params.shopNo ">
                
                AND `shop_no`=#{params.shopNo}
                
            </if>
            
            <if test="null!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.shopLatitude  and ''!=params.shopLatitude ">
                
                AND `shop_latitude`=#{params.shopLatitude}
                
            </if>
            
            <if test="null!=params.bizCityNo  and ''!=params.bizCityNo ">
                
                AND `biz_city_no`=#{params.bizCityNo}
                
            </if>
            
            <if test="null!=params.sysNo  and ''!=params.sysNo ">
                
                AND `sys_no`=#{params.sysNo}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.onLineFlag ">
                
                AND `on_line_flag`=#{params.onLineFlag}
                
            </if>
            
            <if test="null!=params.priceAdjustLevel  and ''!=params.priceAdjustLevel ">
                
                AND `price_adjust_level`=#{params.priceAdjustLevel}
                
            </if>
            
            <if test="null!=params.shopClassify  and ''!=params.shopClassify ">
                
                AND `shop_classify`=#{params.shopClassify}
                
            </if>
            
            <if test="null!=params.categoryCode  and ''!=params.categoryCode ">
                
                AND `category_code`=#{params.categoryCode}
                
            </if>
            
            <if test="null!=params.openDate ">
                
                AND `open_date`=#{params.openDate}
                
            </if>
            
            <if test="null!=params.closeDate ">
                
                AND `close_date`=#{params.closeDate}
                
            </if>
            
            <if test="null!=params.status ">
                
                AND `status`=#{params.status}
                
            </if>
            
            <if test="null!=params.area ">
                
                AND `area`=#{params.area}
                
            </if>
            
            <if test="null!=params.areaLeft ">
                
                AND `area_left`=#{params.areaLeft}
                
            </if>
            
            <if test="null!=params.areaTotal ">
                
                AND `area_total`=#{params.areaTotal}
                
            </if>
            
            <if test="null!=params.areaUnit  and ''!=params.areaUnit ">
                
                AND `area_unit`=#{params.areaUnit}
                
            </if>
            
            <if test="null!=params.provinceNo  and ''!=params.provinceNo ">
                
                AND `province_no`=#{params.provinceNo}
                
            </if>
            
            <if test="null!=params.cityNo  and ''!=params.cityNo ">
                
                AND `city_no`=#{params.cityNo}
                
            </if>
            
            <if test="null!=params.countyNo  and ''!=params.countyNo ">
                
                AND `county_no`=#{params.countyNo}
                
            </if>
            
            <if test="null!=params.address  and ''!=params.address ">
                
                AND `address`=#{params.address}
                
            </if>
            
            <if test="null!=params.zipCode  and ''!=params.zipCode ">
                
                AND `zip_code`=#{params.zipCode}
                
            </if>
            
            <if test="null!=params.contactName  and ''!=params.contactName ">
                
                AND `contact_name` like CONCAT('%',#{params.contactName},'%') 
                
            </if>
            
            <if test="null!=params.tel  and ''!=params.tel ">
                
                AND `tel`=#{params.tel}
                
            </if>
            
            <if test="null!=params.email  and ''!=params.email ">
                
                AND `email`=#{params.email}
                
            </if>
            
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                
                AND `channel_no`=#{params.channelNo}
                
            </if>
            
            <if test="null!=params.cmcdistNo  and ''!=params.cmcdistNo ">
                
                AND `cmcdist_no`=#{params.cmcdistNo}
                
            </if>
            
            <if test="null!=params.employeAmount ">
                
                AND `employe_amount`=#{params.employeAmount}
                
            </if>
            
            <if test="null!=params.payType  and ''!=params.payType ">
                
                AND `pay_type`=#{params.payType}
                
            </if>
            
            <if test="null!=params.digits  and ''!=params.digits ">
                
                AND `digits`=#{params.digits}
                
            </if>
            
            <if test="null!=params.startupTime  and ''!=params.startupTime ">
                
                AND `startup_time`=#{params.startupTime}
                
            </if>
            
            <if test="null!=params.shutdownTime  and ''!=params.shutdownTime ">
                
                AND `shutdown_time`=#{params.shutdownTime}
                
            </if>
            
            <if test="null!=params.shopLevel  and ''!=params.shopLevel ">
                
                AND `shop_level`=#{params.shopLevel}
                
            </if>
            
            <if test="null!=params.major  and ''!=params.major ">
                
                AND `major`=#{params.major}
                
            </if>
            
            <if test="null!=params.multi  and ''!=params.multi ">
                
                AND `multi`=#{params.multi}
                
            </if>
            
            <if test="null!=params.saleMode  and ''!=params.saleMode ">
                
                AND `sale_mode`=#{params.saleMode}
                
            </if>
            
            <if test="null!=params.retailType  and ''!=params.retailType ">
                
                AND `retail_type`=#{params.retailType}
                
            </if>
            
            <if test="null!=params.mallNo  and ''!=params.mallNo ">
                
                AND `mall_no`=#{params.mallNo}
                
            </if>
            
            <if test="null!=params.mapFlag ">
                
                AND `map_flag`=#{params.mapFlag}
                
            </if>
            
            <if test="null!=params.corporationNo  and ''!=params.corporationNo ">
                
                AND `corporation_no`=#{params.corporationNo}
                
            </if>
            
            <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                
                AND `organ_type_no`=#{params.organTypeNo}
                
            </if>
            
            <if test="null!=params.saleType ">
                
                AND `sale_type`=#{params.saleType}
                
            </if>
            
            <if test="null!=params.saleNo  and ''!=params.saleNo ">
                
                AND `sale_no`=#{params.saleNo}
                
            </if>
            
            <if test="null!=params.regionNo  and ''!=params.regionNo ">
                
                AND `region_no`=#{params.regionNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=shopNo and ''!=shopNo">
            AND `shop_no`=#{shopNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM shop
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM shop
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM shop
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM shop
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM shop
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM shop
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM shop
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM shop
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM shop
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.Shop"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO shop
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="fax != null">
                `fax`,
            </if>
            
            <if test="location != null">
                `location`,
            </if>
            
            <if test="parentSaleNo != null">
                `parent_sale_no`,
            </if>
            
            <if test="fullName != null">
                `full_name`,
            </if>
            
            <if test="shortName != null">
                `short_name`,
            </if>
            
            <if test="searchCode != null">
                `search_code`,
            </if>
            
            <if test="companyNo != null">
                `company_no`,
            </if>
            
            <if test="storeNo != null">
                `store_no`,
            </if>
            
            <if test="shopModeNo != null">
                `shop_mode_no`,
            </if>
            
            <if test="timeSeq != null">
                `time_seq`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="organNo != null">
                `organ_no`,
            </if>
            
            <if test="groupBrand != null">
                `group_brand`,
            </if>
            
            <if test="customerNo != null">
                `customer_no`,
            </if>
            
            <if test="logisticsCostPayer != null">
                `logistics_cost_payer`,
            </if>
            
            <if test="shopLongitude != null">
                `shop_longitude`,
            </if>
            
            <if test="shopLcode != null">
                `shop_lcode`,
            </if>
            
            <if test="code != null">
                `code`,
            </if>
            
            <if test="shopNo != null">
                `shop_no`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="shopLatitude != null">
                `shop_latitude`,
            </if>
            
            <if test="bizCityNo != null">
                `biz_city_no`,
            </if>
            
            <if test="sysNo != null">
                `sys_no`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="onLineFlag != null">
                `on_line_flag`,
            </if>
            
            <if test="priceAdjustLevel != null">
                `price_adjust_level`,
            </if>
            
            <if test="shopClassify != null">
                `shop_classify`,
            </if>
            
            <if test="categoryCode != null">
                `category_code`,
            </if>
            
            <if test="openDate != null">
                `open_date`,
            </if>
            
            <if test="closeDate != null">
                `close_date`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="area != null">
                `area`,
            </if>
            
            <if test="areaLeft != null">
                `area_left`,
            </if>
            
            <if test="areaTotal != null">
                `area_total`,
            </if>
            
            <if test="areaUnit != null">
                `area_unit`,
            </if>
            
            <if test="provinceNo != null">
                `province_no`,
            </if>
            
            <if test="cityNo != null">
                `city_no`,
            </if>
            
            <if test="countyNo != null">
                `county_no`,
            </if>
            
            <if test="address != null">
                `address`,
            </if>
            
            <if test="zipCode != null">
                `zip_code`,
            </if>
            
            <if test="contactName != null">
                `contact_name`,
            </if>
            
            <if test="tel != null">
                `tel`,
            </if>
            
            <if test="email != null">
                `email`,
            </if>
            
            <if test="channelNo != null">
                `channel_no`,
            </if>
            
            <if test="cmcdistNo != null">
                `cmcdist_no`,
            </if>
            
            <if test="employeAmount != null">
                `employe_amount`,
            </if>
            
            <if test="payType != null">
                `pay_type`,
            </if>
            
            <if test="digits != null">
                `digits`,
            </if>
            
            <if test="startupTime != null">
                `startup_time`,
            </if>
            
            <if test="shutdownTime != null">
                `shutdown_time`,
            </if>
            
            <if test="shopLevel != null">
                `shop_level`,
            </if>
            
            <if test="major != null">
                `major`,
            </if>
            
            <if test="multi != null">
                `multi`,
            </if>
            
            <if test="saleMode != null">
                `sale_mode`,
            </if>
            
            <if test="retailType != null">
                `retail_type`,
            </if>
            
            <if test="mallNo != null">
                `mall_no`,
            </if>
            
            <if test="mapFlag != null">
                `map_flag`,
            </if>
            
            <if test="corporationNo != null">
                `corporation_no`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
            <if test="saleType != null">
                `sale_type`,
            </if>
            
            <if test="saleNo != null">
                `sale_no`,
            </if>
            
            <if test="regionNo != null">
                `region_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="fax != null">
                #{fax},
            </if>
            
            <if test="location != null">
                #{location},
            </if>
            
            <if test="parentSaleNo != null">
                #{parentSaleNo},
            </if>
            
            <if test="fullName != null">
                #{fullName},
            </if>
            
            <if test="shortName != null">
                #{shortName},
            </if>
            
            <if test="searchCode != null">
                #{searchCode},
            </if>
            
            <if test="companyNo != null">
                #{companyNo},
            </if>
            
            <if test="storeNo != null">
                #{storeNo},
            </if>
            
            <if test="shopModeNo != null">
                #{shopModeNo},
            </if>
            
            <if test="timeSeq != null">
                #{timeSeq},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="organNo != null">
                #{organNo},
            </if>
            
            <if test="groupBrand != null">
                #{groupBrand},
            </if>
            
            <if test="customerNo != null">
                #{customerNo},
            </if>
            
            <if test="logisticsCostPayer != null">
                #{logisticsCostPayer},
            </if>
            
            <if test="shopLongitude != null">
                #{shopLongitude},
            </if>
            
            <if test="shopLcode != null">
                #{shopLcode},
            </if>
            
            <if test="code != null">
                #{code},
            </if>
            
            <if test="shopNo != null">
                #{shopNo},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="shopLatitude != null">
                #{shopLatitude},
            </if>
            
            <if test="bizCityNo != null">
                #{bizCityNo},
            </if>
            
            <if test="sysNo != null">
                #{sysNo},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="onLineFlag != null">
                #{onLineFlag},
            </if>
            
            <if test="priceAdjustLevel != null">
                #{priceAdjustLevel},
            </if>
            
            <if test="shopClassify != null">
                #{shopClassify},
            </if>
            
            <if test="categoryCode != null">
                #{categoryCode},
            </if>
            
            <if test="openDate != null">
                #{openDate},
            </if>
            
            <if test="closeDate != null">
                #{closeDate},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="area != null">
                #{area},
            </if>
            
            <if test="areaLeft != null">
                #{areaLeft},
            </if>
            
            <if test="areaTotal != null">
                #{areaTotal},
            </if>
            
            <if test="areaUnit != null">
                #{areaUnit},
            </if>
            
            <if test="provinceNo != null">
                #{provinceNo},
            </if>
            
            <if test="cityNo != null">
                #{cityNo},
            </if>
            
            <if test="countyNo != null">
                #{countyNo},
            </if>
            
            <if test="address != null">
                #{address},
            </if>
            
            <if test="zipCode != null">
                #{zipCode},
            </if>
            
            <if test="contactName != null">
                #{contactName},
            </if>
            
            <if test="tel != null">
                #{tel},
            </if>
            
            <if test="email != null">
                #{email},
            </if>
            
            <if test="channelNo != null">
                #{channelNo},
            </if>
            
            <if test="cmcdistNo != null">
                #{cmcdistNo},
            </if>
            
            <if test="employeAmount != null">
                #{employeAmount},
            </if>
            
            <if test="payType != null">
                #{payType},
            </if>
            
            <if test="digits != null">
                #{digits},
            </if>
            
            <if test="startupTime != null">
                #{startupTime},
            </if>
            
            <if test="shutdownTime != null">
                #{shutdownTime},
            </if>
            
            <if test="shopLevel != null">
                #{shopLevel},
            </if>
            
            <if test="major != null">
                #{major},
            </if>
            
            <if test="multi != null">
                #{multi},
            </if>
            
            <if test="saleMode != null">
                #{saleMode},
            </if>
            
            <if test="retailType != null">
                #{retailType},
            </if>
            
            <if test="mallNo != null">
                #{mallNo},
            </if>
            
            <if test="mapFlag != null">
                #{mapFlag},
            </if>
            
            <if test="corporationNo != null">
                #{corporationNo},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
            <if test="saleType != null">
                #{saleType},
            </if>
            
            <if test="saleNo != null">
                #{saleNo},
            </if>
            
            <if test="regionNo != null">
                #{regionNo},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.gms.Shop"  useGeneratedKeys="true" keyProperty="id"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.Shop">
        UPDATE shop
        <set>
            
            <if test="fax != null">
                `fax` = #{fax},
            </if> 
            <if test="location != null">
                `location` = #{location},
            </if> 
            <if test="parentSaleNo != null">
                `parent_sale_no` = #{parentSaleNo},
            </if> 
            <if test="fullName != null">
                `full_name` = #{fullName},
            </if> 
            <if test="shortName != null">
                `short_name` = #{shortName},
            </if> 
            <if test="searchCode != null">
                `search_code` = #{searchCode},
            </if> 
            <if test="companyNo != null">
                `company_no` = #{companyNo},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            <if test="shopModeNo != null">
                `shop_mode_no` = #{shopModeNo},
            </if> 
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="organNo != null">
                `organ_no` = #{organNo},
            </if> 
            <if test="groupBrand != null">
                `group_brand` = #{groupBrand},
            </if> 
            <if test="customerNo != null">
                `customer_no` = #{customerNo},
            </if> 
            <if test="logisticsCostPayer != null">
                `logistics_cost_payer` = #{logisticsCostPayer},
            </if> 
            <if test="shopLongitude != null">
                `shop_longitude` = #{shopLongitude},
            </if> 
            <if test="shopLcode != null">
                `shop_lcode` = #{shopLcode},
            </if> 
            <if test="code != null">
                `code` = #{code},
            </if> 
            <if test="shopNo != null">
                `shop_no` = #{shopNo},
            </if> 
            <if test="shopLatitude != null">
                `shop_latitude` = #{shopLatitude},
            </if> 
            <if test="bizCityNo != null">
                `biz_city_no` = #{bizCityNo},
            </if> 
            <if test="sysNo != null">
                `sys_no` = #{sysNo},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="onLineFlag != null">
                `on_line_flag` = #{onLineFlag},
            </if> 
            <if test="priceAdjustLevel != null">
                `price_adjust_level` = #{priceAdjustLevel},
            </if> 
            <if test="shopClassify != null">
                `shop_classify` = #{shopClassify},
            </if> 
            <if test="categoryCode != null">
                `category_code` = #{categoryCode},
            </if> 
            <if test="openDate != null">
                `open_date` = #{openDate},
            </if> 
            <if test="closeDate != null">
                `close_date` = #{closeDate},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="area != null">
                `area` = #{area},
            </if> 
            <if test="areaLeft != null">
                `area_left` = #{areaLeft},
            </if> 
            <if test="areaTotal != null">
                `area_total` = #{areaTotal},
            </if> 
            <if test="areaUnit != null">
                `area_unit` = #{areaUnit},
            </if> 
            <if test="provinceNo != null">
                `province_no` = #{provinceNo},
            </if> 
            <if test="cityNo != null">
                `city_no` = #{cityNo},
            </if> 
            <if test="countyNo != null">
                `county_no` = #{countyNo},
            </if> 
            <if test="address != null">
                `address` = #{address},
            </if> 
            <if test="zipCode != null">
                `zip_code` = #{zipCode},
            </if> 
            <if test="contactName != null">
                `contact_name` = #{contactName},
            </if> 
            <if test="tel != null">
                `tel` = #{tel},
            </if> 
            <if test="email != null">
                `email` = #{email},
            </if> 
            <if test="channelNo != null">
                `channel_no` = #{channelNo},
            </if> 
            <if test="cmcdistNo != null">
                `cmcdist_no` = #{cmcdistNo},
            </if> 
            <if test="employeAmount != null">
                `employe_amount` = #{employeAmount},
            </if> 
            <if test="payType != null">
                `pay_type` = #{payType},
            </if> 
            <if test="digits != null">
                `digits` = #{digits},
            </if> 
            <if test="startupTime != null">
                `startup_time` = #{startupTime},
            </if> 
            <if test="shutdownTime != null">
                `shutdown_time` = #{shutdownTime},
            </if> 
            <if test="shopLevel != null">
                `shop_level` = #{shopLevel},
            </if> 
            <if test="major != null">
                `major` = #{major},
            </if> 
            <if test="multi != null">
                `multi` = #{multi},
            </if> 
            <if test="saleMode != null">
                `sale_mode` = #{saleMode},
            </if> 
            <if test="retailType != null">
                `retail_type` = #{retailType},
            </if> 
            <if test="mallNo != null">
                `mall_no` = #{mallNo},
            </if> 
            <if test="mapFlag != null">
                `map_flag` = #{mapFlag},
            </if> 
            <if test="corporationNo != null">
                `corporation_no` = #{corporationNo},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            <if test="saleType != null">
                `sale_type` = #{saleType},
            </if> 
            <if test="saleNo != null">
                `sale_no` = #{saleNo},
            </if> 
            <if test="regionNo != null">
                `region_no` = #{regionNo},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE shop_no = #{shopNo}
        
            
    </update>

    <select id="selectShopAndCompany" resultType="cn.wonhigh.baize.model.dto.ShopCompanyDto">
        select s.id, s.shop_no as shopNo, s.code,
               s.shop_lcode   as shopLcode,
               s.store_no     as storeNo,
               s.company_no   as companyNo,
               s.search_code  as searchCode,
               s.short_name   as shortName,
               s.full_name    as fullName,
               c.name as companyName
        from shop s
                 left join company c on s.company_no = c.company_no
        <where>
           <if test="params.shopNo!=null and params.shopNo!=''">
               AND s.shop_no = #{params.shopNo}
           </if>
        </where>
        limit 1
    </select>
    <!-- auto generate end-->


</mapper>