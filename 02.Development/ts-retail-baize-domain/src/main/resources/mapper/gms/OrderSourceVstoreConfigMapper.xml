<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.gms.OrderSourceVstoreConfigRepository">
    <resultMap id="BaseResultMap" type="OrderSourceVstoreConfig">
        <id column="id" jdbcType="CHAR" property="id" />
        <result column="order_source_no" jdbcType="VARCHAR" property="orderSourceNo" />
        <result column="vstore_code" jdbcType="VARCHAR" property="vstoreCode" />
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
        <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
        <result column="channel_occupied_no" jdbcType="VARCHAR" property="channelOccupiedNo" />
        <result column="online_type" jdbcType="TINYINT" property="onlineType" />
        <result column="groups" jdbcType="TINYINT" property="groups" />
        <result column="vstore_scope_type" jdbcType="TINYINT" property="vstoreScopeType" />
        <result column="vstore_level" jdbcType="TINYINT" property="vstoreLevel" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="vstore_name" jdbcType="VARCHAR" property="vstoreName" />
        <result column="third_channel_name" jdbcType="VARCHAR" property="orderSourceName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, order_source_no,vstore_code, shop_no,shop_name,channel_occupied_no,online_type, update_user, update_time, create_user, create_time,
        groups,vstore_scope_type,vstore_level,remark
    </sql>

    <sql id="Condition">
        <if test="params != null">
            <if test="params.orderSourceNo != null and params.orderSourceNo != ''">
                AND order_source_no = #{params.orderSourceNo,jdbcType=VARCHAR}
            </if>
            <if test="params.vstoreCode != null and params.vstoreCode != ''">
                AND vstore_code = #{params.vstoreCode,jdbcType=VARCHAR}
            </if>
            <if test="params.groups != null and params.groups != ''">
                AND `groups` = #{params.groups}
            </if>
        </if>
    </sql>

    <sql id="Condition1">
        <if test="params != null">
            <if test="params.orderSourceNo != null and params.orderSourceNo != ''">
                AND o.order_source_no like CONCAT('%',#{params.orderSourceNo},'%')
             </if>
            <if test="params.vstoreCode != null and params.vstoreCode != ''">
                AND o.vstore_code like CONCAT('%',#{params.vstoreCode},'%')
            </if>
            <if test="params.shopNo != null and params.shopNo != ''">
                AND o.shop_no like CONCAT('%',#{params.shopNo},'%')
            </if>
            <if test="params.channelOccupiedNo != null and params.channelOccupiedNo != ''">
                AND o.channel_occupied_no = #{params.channelOccupiedNo}
            </if>
            <if test="params.remark != null and params.remark != ''">
                AND o.remark like CONCAT('%',#{params.remark},'%')
            </if>
            <if test="params.onlineType != null and params.onlineType != ''">
                AND o.online_type = #{params.onlineType}
            </if>
            <if test="params.groups != null and params.groups != ''">
                AND o.groups = #{params.groups}
            </if>
            <if test="params.vstoreScopeType != null and params.vstoreScopeType != ''">
                AND o.vstore_scope_type = #{params.vstoreScopeType}
            </if>
        </if>
    </sql>

    <insert id="insertSelective" parameterType="OrderSourceVstoreConfig">
        insert into order_source_vstore_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="orderSourceNo != null">
                order_source_no,
            </if>
            <if test="vstoreCode != null">
                vstore_code,
            </if>
            <if test="shopNo != null">
                shop_no,
            </if>
            <if test="shopName != null">
                shop_name,
            </if>
            <if test="channelOccupiedNo != null">
                channel_occupied_no,
            </if>
            <if test="onlineType != null">
                online_type,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=CHAR},
            </if>
            <if test="orderSourceNo != null">
                #{orderSourceNo,jdbcType=VARCHAR},
            </if>
            <if test="vstoreCode != null">
                #{vstoreCode,jdbcType=VARCHAR},
            </if>
            <if test="shopNo != null">
                #{shopNo,jdbcType=VARCHAR},
            </if>
            <if test="shopName != null">
                #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="channelOccupiedNo != null">
                #{channelOccupiedNo,jdbcType=VARCHAR},
            </if>
            <if test="onlineType != null">
                #{onlineType,jdbcType=TINYINT},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" >
        insert into order_source_vstore_config (
        id, order_source_no,vstore_code, shop_no,shop_name,channel_occupied_no,online_type,update_user, update_time, create_user, create_time,) values
        <foreach collection="datas" item="data" separator=",">
            (#{data.id,jdbcType=CHAR},
            #{data.orderSourceNo,jdbcType=VARCHAR},
            #{data.vstoreCode,jdbcType=VARCHAR},
            #{data.shopNo,jdbcType=VARCHAR},
            #{data.shopName,jdbcType=VARCHAR},
            #{data.channelOccupiedNo,jdbcType=VARCHAR},
            #{data.onlineType,jdbcType=TINYINT},
            #{data.updateUser,jdbcType=VARCHAR},
            #{data.updateTime,jdbcType=TIMESTAMP},
            #{data.createUser,jdbcType=VARCHAR},
            #{data.createTime,jdbcType=TIMESTAMP} )
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="OrderSourceVstoreConfig">
        update order_source_vstore_config
        <set>
            <if test="orderSourceNo != null">
                order_source_no = #{orderSourceNo,jdbcType=VARCHAR},
            </if>
            <if test="vstoreCode != null">
                vstore_code = #{vstoreCode,jdbcType=VARCHAR},
            </if>
            <if test="shopNo != null">
                shop_no = #{shopNo,jdbcType=VARCHAR},
            </if>
            <if test="shopName != null">
                shop_name = #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="channelOccupiedNo != null">
                channel_occupied_no = #{channelOccupiedNo,jdbcType=VARCHAR},
            </if>
            <if test="onlineType != null">
                online_type = #{onlineType,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where  1=1
        <if test="id != null and id != ''">
        	and id = #{id,jdbcType=CHAR}
        </if>
    </update>

    <delete id="deleteByPrimaryKey" parameterType="OrderSourceVstoreConfig">
        delete from order_source_vstore_config
        where  1=1
        <if test="id != null and id != ''">
        	and id = #{id,jdbcType=CHAR}
        </if>
    </delete>

    <select id="selectCount" resultType="java.lang.Integer">
        select count(1) from order_source_vstore_config o
        Where 1 = 1
        <include refid="Condition1" />
    </select>

    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from order_source_vstore_config
        where 1 = 1
        <include refid="Condition" />
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT
        o.*,
        (
            CASE
            WHEN o.`groups` = 3 THEN
                o.shop_name
            ELSE
                i.vstore_name
            END
        ) vstore_name,
        s.third_channel_name
        FROM
        order_source_vstore_config o
        LEFT JOIN internet_virtual_warehouse_info i ON i.vstore_code = o.vstore_code
        LEFT JOIN internet_shop_company_setting s ON s.third_channel_no = o.order_source_no
        where
        1 = 1
        <include refid="Condition1" />
        GROUP BY
        o.id
        <if test="orderByField != null and ''!=orderByField" >
            order by ${orderByField}
            <if test="orderByField" >
                ${orderBy}
            </if>
        </if>
        <if test="orderByField == null or ''==orderByField" >
            ORDER BY o.create_time desc
        </if>
        LIMIT #{page.startRowNum} ,#{page.pageSize}
    </select>
</mapper>