<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetVirtualWarehouseInfoRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="InternetVirtualWarehouseInfo">
                
        <id column="id" property="id" jdbcType="VARCHAR" />

        <result column="qty_str" property="qtyStr" jdbcType="VARCHAR" />
        
        <result column="vstore_name" property="vstoreName" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="create_name" property="createName" jdbcType="VARCHAR" />
        
        <result column="interface_platform" property="interfacePlatform" jdbcType="VARCHAR" />
        
        <result column="is_sync" property="isSync" jdbcType="TINYINT" />
        
        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR" />
        
        <result column="parent_vstore_name" property="parentVstoreName" jdbcType="VARCHAR" />
        
        <result column="vstore_type" property="vstoreType" jdbcType="TINYINT" />
        
        <result column="business_type" property="businessType" jdbcType="TINYINT" />
        
        <result column="sub_index" property="subIndex" jdbcType="TINYINT" />
        
        <result column="parent_vstore_code" property="parentVstoreCode" jdbcType="VARCHAR" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
        <result column="online_type" property="onlineType" jdbcType="TINYINT" />
        
        <result column="is_calc" property="isCalc" jdbcType="TINYINT" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_name" property="updateName" jdbcType="VARCHAR" />

        <result column="vstore_mold" property="vstoreMold" jdbcType="TINYINT" />
        
    </resultMap>

    <sql id="column_list">
        `qty_str`,`vstore_name`,`create_time`,`create_name`,`interface_platform`,`is_sync`,`vstore_code`,`id`,`parent_vstore_name`,`vstore_type`,`business_type`,`sub_index`,`parent_vstore_code`,`organ_type_no`,`online_type`,`is_calc`,`update_time`,`update_name`,`vstore_mold`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.qtyStr  and ''!=params.qtyStr ">
                
                AND `qty_str`=#{params.qtyStr}
                
            </if>
            
            <if test="null!=params.vstoreName  and ''!=params.vstoreName ">
                
                AND `vstore_name` like CONCAT('%',#{params.vstoreName},'%') 
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.createName  and ''!=params.createName ">
                
                AND `create_name` like CONCAT('%',#{params.createName},'%') 
                
            </if>
            
            <if test="null!=params.interfacePlatform  and ''!=params.interfacePlatform ">
                
                AND `interface_platform`=#{params.interfacePlatform}
                
            </if>
            
            <if test="null!=params.isSync ">
                
                AND `is_sync`=#{params.isSync}
                
            </if>
            
            <if test="null!=params.vstoreCode  and ''!=params.vstoreCode ">
                
                AND `vstore_code`=#{params.vstoreCode}
                
            </if>
            
            <if test="null!=params.vstoreCodeList and params.vstoreCodeList.size()>0">
            	AND `vstore_code` IN
            	<foreach collection="params.vstoreCodeList" item="vstoreCode" separator="," open="(" close=")" >
            		#{vstoreCode}
            	</foreach>
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.parentVstoreName  and ''!=params.parentVstoreName ">
                
                AND `parent_vstore_name` like CONCAT('%',#{params.parentVstoreName},'%') 
                
            </if>
            
            <if test="null!=params.vstoreType ">
                
                AND `vstore_type`=#{params.vstoreType}
                
            </if>

            <if test="null!=params.vstoreMold ">

                AND `vstore_mold`=#{params.vstoreMold}

            </if>
            
            <if test="null!=params.businessType ">
                
                AND `business_type`=#{params.businessType}
                
            </if>
            
            <if test="null!=params.subIndex ">
                
                AND `sub_index`=#{params.subIndex}
                
            </if>
            
            <if test="null!=params.parentVstoreCode  and ''!=params.parentVstoreCode ">
                
                AND `parent_vstore_code`=#{params.parentVstoreCode}
                
            </if>
            
            <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                
                AND `organ_type_no`=#{params.organTypeNo}
                
            </if>
            
            <if test="null!=params.onlineType ">
                
                AND `online_type`=#{params.onlineType}
                
            </if>
            
            <if test="null!=params.isCalc ">
                
                AND `is_calc`=#{params.isCalc}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.updateName  and ''!=params.updateName ">
                
                AND `update_name` like CONCAT('%',#{params.updateName},'%') 
                
            </if>

            <if test="null!=params.vstoreCodeAndName  and ''!=params.vstoreCodeAndName ">

                AND (
                 `vstore_code` like CONCAT('%',#{params.vstoreCodeAndName},'%') or
                 `vstore_name` like CONCAT('%',#{params.vstoreCodeAndName},'%')
                )

            </if>

            <if test="(null!=params.storeNo and ''!=params.storeNo) or
                    (null != params.orderUnitNo and '' != params.orderUnitNo)">
                AND `vstore_code` in (
                    select `vstore_code` from internet_virtual_warehouse_scope
                    where 1=1
                    <if test="null != params.storeNo  and '' != params.storeNo">
                        AND `store_no`= #{params.storeNo}
                    </if>
                    <if test="null != params.orderUnitNo  and '' != params.orderUnitNo">
                        AND `order_unit_no`=  #{params.orderUnitNo}
                    </if>
                )

            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=vstoreCode and ''!=vstoreCode">
            AND `vstore_code`=#{vstoreCode}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_virtual_warehouse_info
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_virtual_warehouse_info
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_virtual_warehouse_info
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_virtual_warehouse_info
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_virtual_warehouse_info
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_virtual_warehouse_info
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_virtual_warehouse_info
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_virtual_warehouse_info
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_virtual_warehouse_info
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="InternetVirtualWarehouseInfo"  >
        INSERT INTO internet_virtual_warehouse_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="qtyStr != null">
                `qty_str`,
            </if>
            
            <if test="vstoreName != null">
                `vstore_name`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="createName != null">
                `create_name`,
            </if>
            
            <if test="interfacePlatform != null">
                `interface_platform`,
            </if>
            
            <if test="isSync != null">
                `is_sync`,
            </if>
            
            <if test="vstoreCode != null">
                `vstore_code`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="parentVstoreName != null">
                `parent_vstore_name`,
            </if>
            
            <if test="vstoreType != null">
                `vstore_type`,
            </if>

            <if test="vstoreMold != null">
                `vstore_mold`,
            </if>
            
            <if test="businessType != null">
                `business_type`,
            </if>
            
            <if test="subIndex != null">
                `sub_index`,
            </if>
            
            <if test="parentVstoreCode != null">
                `parent_vstore_code`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
            <if test="onlineType != null">
                `online_type`,
            </if>
            
            <if test="isCalc != null">
                `is_calc`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="updateName != null">
                `update_name`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="qtyStr != null">
                #{qtyStr},
            </if>
            
            <if test="vstoreName != null">
                #{vstoreName},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="createName != null">
                #{createName},
            </if>
            
            <if test="interfacePlatform != null">
                #{interfacePlatform},
            </if>
            
            <if test="isSync != null">
                #{isSync},
            </if>
            
            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="parentVstoreName != null">
                #{parentVstoreName},
            </if>
            
            <if test="vstoreType != null">
                #{vstoreType},
            </if>

            <if test="vstoreMold != null">
                #{vstoreMold},
            </if>
            
            <if test="businessType != null">
                #{businessType},
            </if>
            
            <if test="subIndex != null">
                #{subIndex},
            </if>
            
            <if test="parentVstoreCode != null">
                #{parentVstoreCode},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
            <if test="onlineType != null">
                #{onlineType},
            </if>
            
            <if test="isCalc != null">
                #{isCalc},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="updateName != null">
                #{updateName},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="InternetVirtualWarehouseInfo"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="InternetVirtualWarehouseInfo">
        UPDATE internet_virtual_warehouse_info
        <set>
            
            <if test="qtyStr != null">
                `qty_str` = #{qtyStr},
            </if> 
            <if test="vstoreName != null">
                `vstore_name` = #{vstoreName},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="createName != null">
                `create_name` = #{createName},
            </if> 
            <if test="interfacePlatform != null">
                `interface_platform` = #{interfacePlatform},
            </if> 
            <if test="isSync != null">
                `is_sync` = #{isSync},
            </if> 
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if> 
            <if test="parentVstoreName != null">
                `parent_vstore_name` = #{parentVstoreName},
            </if> 
            <if test="vstoreType != null">
                `vstore_type` = #{vstoreType},
            </if>
            <if test="vstoreMold != null">
                `vstore_mold` = #{vstoreMold},
            </if>
            <if test="businessType != null">
                `business_type` = #{businessType},
            </if> 
            <if test="subIndex != null">
                `sub_index` = #{subIndex},
            </if> 
            <if test="parentVstoreCode != null">
                `parent_vstore_code` = #{parentVstoreCode},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            <if test="onlineType != null">
                `online_type` = #{onlineType},
            </if> 
            <if test="isCalc != null">
                `is_calc` = #{isCalc},
            </if> 
            <if test="updateName != null">
                `update_name` = #{updateName},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE vstore_code = #{vstoreCode}
        
            
    </update>
        <!-- auto generate end-->


</mapper>