<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.CompanyPartConfigRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.CompanyPartConfig">
        <id column="company_no" property="companyNo" jdbcType="CHAR"/>

        <result column="time_seq" property="timeSeq" jdbcType="INTEGER"/>
        <result column="zone_alias" property="zoneAlias" jdbcType="VARCHAR"/>
        <result column="zone_no" property="zoneNo" jdbcType="CHAR"/>
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR"/>
        <result column="partition_no" property="partitionNo" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="column_list">
        `time_seq`,
        `zone_alias`,
        `zone_no`,
        `organ_type_no`,
        `partition_no`,
        `create_user`,
        `create_time`,
        `company_no`,
        `update_user`,
        `update_time`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.timeSeq">
                AND `time_seq`=#{params.timeSeq}
            </if>

            <if test="null != params.zoneAlias  and '' != params.zoneAlias">
                AND `zone_alias`=#{params.zoneAlias}
            </if>

            <if test="null != params.zoneNo  and '' != params.zoneNo">
                AND `zone_no`=#{params.zoneNo}
            </if>

            <if test="null != params.organTypeNo  and '' != params.organTypeNo">
                AND `organ_type_no`=#{params.organTypeNo}
            </if>

            <if test="null != params.partitionNo  and '' != params.partitionNo">
                AND `partition_no`=#{params.partitionNo}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.companyNo  and '' != params.companyNo">
                AND `company_no`=#{params.companyNo}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM company_part_config
        WHERE company_no = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM company_part_config
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM company_part_config
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM company_part_config
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM company_part_config
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM company_part_config
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM company_part_config
        WHERE company_no = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM company_part_config
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM company_part_config
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND company_no in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.CompanyPartConfig">
        INSERT INTO company_part_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timeSeq != null">
                `time_seq`,
            </if>

            <if test="zoneAlias != null">
                `zone_alias`,
            </if>

            <if test="zoneNo != null">
                `zone_no`,
            </if>

            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>

            <if test="partitionNo != null">
                `partition_no`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="companyNo != null">
                `company_no`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="timeSeq != null">
                #{timeSeq},
            </if>

            <if test="zoneAlias != null">
                #{zoneAlias},
            </if>

            <if test="zoneNo != null">
                #{zoneNo},
            </if>

            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>

            <if test="partitionNo != null">
                #{partitionNo},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="companyNo != null">
                #{companyNo},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.CompanyPartConfig" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO company_part_config (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            (#{item.timeSeq}, #{item.zoneAlias}, #{item.zoneNo}, #{item.organTypeNo}, #{item.partitionNo},
             #{item.createUser}, #{item.createTime}, #{item.companyNo}, #{item.updateUser}, #{item.updateTime})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.CompanyPartConfig">
        UPDATE company_part_config
        <set>
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if>
            <if test="zoneAlias != null">
                `zone_alias` = #{zoneAlias},
            </if>
            <if test="zoneNo != null">
                `zone_no` = #{zoneNo},
            </if>
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if>
            <if test="partitionNo != null">
                `partition_no` = #{partitionNo},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            update_time = now()
        </set>


        WHERE company_no = #{companyNo}
    </update>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM company_part_config
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- auto generate end-->



    <select id="getShardingFlagByStoreNo" resultMap="baseResultMap">
        SELECT
        r.company_no,
        c.partition_no,
        r.order_unit_no,
        r.order_unit_name
        FROM
        org_unit_brand_rel r,
        company_part_config c,
        brand b
        WHERE
        r.company_no = c.company_no
        AND r.STATUS = 1
        AND b.organ_type_no = c.organ_type_no
        AND r.store_no = #{storeNo}
        LIMIT 1
    </select>

    <select id="getShardingFlagByOrderUnitNo" resultType="java.lang.String">
        SELECT
        b.partition_no
        FROM
        order_unit a
        INNER JOIN company_part_config b ON a.company_no = b.company_no
        WHERE a.status = 1 AND
        a.order_unit_no = #{orderUnitNo}
        LIMIT 1
    </select>
</mapper>