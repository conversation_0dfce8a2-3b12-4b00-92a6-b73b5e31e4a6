<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InventoryActiveLockRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.InventoryActiveLock">
                
        <id column="id" property="id" jdbcType="CHAR" />	
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />	
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />	
        <result column="store_no" property="storeNo" jdbcType="CHAR" />	
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />	
        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR" />	
        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR" />	
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />	
        <result column="shop_name" property="shopName" jdbcType="VARCHAR" />	
        <result column="shop_no" property="shopNo" jdbcType="VARCHAR" />	
        <result column="vstore_name" property="vstoreName" jdbcType="VARCHAR" />	
        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR" />	
        <result column="active_name" property="activeName" jdbcType="VARCHAR" />	
        <result column="bill_no" property="billNo" jdbcType="CHAR" />	
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />	
        <result column="is_approve" property="isApprove" jdbcType="TINYINT" />	
        <result column="status" property="status" jdbcType="TINYINT" />	
        <result column="remark" property="remark" jdbcType="VARCHAR" />	
        <result column="channel_type" property="channelType" jdbcType="VARCHAR" />	
        <result column="channel_name" property="channelName" jdbcType="VARCHAR" />	
        <result column="source_platform" property="sourcePlatform" jdbcType="VARCHAR" />	
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />	
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="lock_type" property="lockType" jdbcType="TINYINT" />
        <result column="sync_status" property="syncStatus" jdbcType="TINYINT" />

        <result column="totalLockQty" property="totalLockQty" jdbcType="BIGINT" />
        <result column="totalBalanceLockQty" property="totalBalanceLockQty" jdbcType="BIGINT" />
    </resultMap>

    <sql id="column_list">
        `create_user`,`update_time`,`store_no`,`store_name`,`order_unit_no`,`order_unit_name`,`create_time`,`shop_name`,`shop_no`,`vstore_name`,
        `vstore_code`,`active_name`,`bill_no`,`id`,`end_time`,`is_approve`,`status`,`remark`,`channel_type`,`channel_name`,`source_platform`,
        `start_time`,`update_user`, `lock_type`, `sync_status`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
        
        	<if test="null!=params.createUser  and ''!=params.createUser ">
				 AND `create_user`=#{params.createUser}
            </if>	
        
        	<if test="null!=params.updateTime ">
				 AND `update_time`=#{params.updateTime}
            </if>	
        
        	<if test="null!=params.storeNo  and ''!=params.storeNo ">
				 AND `store_no`=#{params.storeNo}
            </if>	
        
        	<if test="null!=params.storeName  and ''!=params.storeName ">
				 AND `store_name`=#{params.storeName}
            </if>	
			<if test="null!=params.storeNameLike  and ''!=params.storeNameLike ">
                AND `store_name` like CONCAT('%',#{params.storeNameLike},'%') 
			</if>	
        
        	<if test="null!=params.orderUnitNo  and ''!=params.orderUnitNo ">
				 AND `order_unit_no`=#{params.orderUnitNo}
            </if>	
        
        	<if test="null!=params.orderUnitName  and ''!=params.orderUnitName ">
				 AND `order_unit_name`=#{params.orderUnitName}
            </if>	
			<if test="null!=params.orderUnitNameLike  and ''!=params.orderUnitNameLike ">
                AND `order_unit_name` like CONCAT('%',#{params.orderUnitNameLike},'%') 
			</if>	
        
        	<if test="null!=params.createTime ">
				 AND `create_time`=#{params.createTime}
            </if>	
        
        	<if test="null!=params.shopName  and ''!=params.shopName ">
				 AND `shop_name`=#{params.shopName}
            </if>	
			<if test="null!=params.shopNameLike  and ''!=params.shopNameLike ">
                AND `shop_name` like CONCAT('%',#{params.shopNameLike},'%') 
			</if>	
        
        	<if test="null!=params.shopNo  and ''!=params.shopNo ">
				 AND `shop_no`=#{params.shopNo}
            </if>	
        
        	<if test="null!=params.vstoreName  and ''!=params.vstoreName ">
				 AND `vstore_name`=#{params.vstoreName}
            </if>	
			<if test="null!=params.vstoreNameLike  and ''!=params.vstoreNameLike ">
                AND `vstore_name` like CONCAT('%',#{params.vstoreNameLike},'%') 
			</if>	
        
        	<if test="null!=params.vstoreCode  and ''!=params.vstoreCode ">
				 AND `vstore_code`=#{params.vstoreCode}
            </if>

            <if test="params.containsKey('vstoreCodeOrName') and '' != params.vstoreCodeOrName ">
                AND ( `vstore_code` like CONCAT('%',#{params.vstoreCodeOrName},'%') or `vstore_name` like CONCAT('%',#{params.vstoreCodeOrName},'%') )
            </if>
        
        	<if test="null!=params.activeName  and ''!=params.activeName ">
				 AND `active_name`=#{params.activeName}
            </if>	
			<if test="null!=params.activeNameLike  and ''!=params.activeNameLike ">
                AND `active_name` like CONCAT('%',#{params.activeNameLike},'%') 
			</if>

            <if test="params.activeNameOrNo != null and ''!= params.activeNameOrNo">
                AND ( `active_name` like CONCAT('%',#{params.activeNameOrNo},'%') or `bill_no` like CONCAT('%',#{params.activeNameOrNo},'%') )
            </if>
        
        	<if test="null!=params.billNo  and ''!=params.billNo ">
				 AND `bill_no`=#{params.billNo}
            </if>	
        
        	<if test="null!=params.id  and ''!=params.id ">
				 AND `id`=#{params.id}
            </if>	
        
        	<if test="null!=params.endTime ">
				 AND `end_time`=#{params.endTime}
            </if>
            <if test="null!=params.auditEndDate ">
                AND `end_time` >= #{params.auditEndDate}
            </if>
        
        	<if test="null!=params.isApprove and params.isApprove != -1 ">
				 AND `is_approve`=#{params.isApprove}
            </if>	
        
        	<if test="null!=params.status and params.status != -1">
				 AND `status`=#{params.status}
            </if>
        
        	<if test="null!=params.remark  and ''!=params.remark ">
				 AND `remark`=#{params.remark}
            </if>	
        
        	<if test="null!=params.channelType  and ''!=params.channelType ">
				 AND `channel_type`=#{params.channelType}
            </if>	
        
        	<if test="null!=params.channelName  and ''!=params.channelName ">
				 AND `channel_name`=#{params.channelName}
            </if>	
			<if test="null!=params.channelNameLike  and ''!=params.channelNameLike ">
                AND `channel_name` like CONCAT('%',#{params.channelNameLike},'%') 
			</if>	
        
        	<if test="null!=params.sourcePlatform  and ''!=params.sourcePlatform ">
				 AND `source_platform`=#{params.sourcePlatform}
            </if>	
        
        	<if test="null!=params.startTime ">
				 AND `start_time`=#{params.startTime}
            </if>	
        
        	<if test="null!=params.updateUser  and ''!=params.updateUser ">
				 AND `update_user`=#{params.updateUser}
            </if>	
        
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>

            <if test="null != params.filterBillNo and '' != params.filterBillNo">
                AND bill_no != #{params.filterBillNo}
            </if>
            <!--这里特别修改, 同一个商品不能存在于多个有效的锁库活动中-->
            <if test="null != params.filterStartDate and null != params.filterEndDate">
                AND (start_time <![CDATA[<=]]> #{params.filterEndDate} and end_time >= #{params.filterStartDate})
            </if>
            <if test="null != params.activeTime">
                AND (start_time <![CDATA[<=]]> #{params.activeTime} and end_time >= #{params.activeTime})
            </if>
            <if test="null!=params.filterShopNo and ''!=params.filterShopNo">
                AND shop_no != #{params.filterShopNo}
            </if>
            <if test="null!=params.lockType and params.lockType != -1 ">
                AND `lock_type`=#{params.lockType}
            </if>
            <if test="null!=params.syncStatus">
                AND sync_status != #{params.syncStatus}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=billNo and ''!=billNo">
            AND `bill_no`=#{billNo}
        </if>
    </sql>

    <sql id="adsfs_condition">
        <trim prefix="AND" prefixOverrides="AND|OR">
            and source_platform NOT IN ('ADSFS')
        </trim>
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM inventory_active_lock
        WHERE id = #{id}
        <include refid="adsfs_condition" />
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM inventory_active_lock
        <where>
            <include refid="uniqe_condition" />
            <include refid="adsfs_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM inventory_active_lock
        <where>
            <include refid="condition" />
            <include refid="adsfs_condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM inventory_active_lock
        <where>
            <include refid="condition" />
            <include refid="adsfs_condition" />
            <if test="params.containsKey('itemCode') and params.itemCode!=null and params.itemCode!=''">
                AND bill_no in (select distinct b.bill_no from inventory_active_lock_dtl b where b.item_code = #{params.itemCode})
            </if>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM inventory_active_lock
        <where>
            <include refid="condition" />
            <include refid="adsfs_condition" />
            <if test="params.containsKey('itemCode') and params.itemCode!=null and params.itemCode!=''">
                AND bill_no in (select distinct b.bill_no from inventory_active_lock_dtl b where b.item_code = #{params.itemCode})
            </if>
        </where>
        ORDER by update_time desc
        LIMIT ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        ,(select sum(ifnull(a.lock_qty, 0)) from inventory_active_lock_dtl a where a.bill_no = inventory_active_lock.bill_no) as totalLockQty
        ,(select sum(ifnull(a.balance_lock_qty, 0)) from inventory_active_lock_dtl a where a.bill_no = inventory_active_lock.bill_no) as totalBalanceLockQty
        FROM inventory_active_lock
        <where>
            <include refid="condition" />
            <include refid="adsfs_condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM inventory_active_lock
        WHERE id = #{id} and status = 1 and is_approve = 0
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM inventory_active_lock
        <where>
            <include refid="condition" />
            and status = 1 and is_approve = 0
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM inventory_active_lock
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
            and status = 1 and is_approve = 0
            <include refid="adsfs_condition" />
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLock"  >
        INSERT INTO inventory_active_lock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="storeNo != null">
                `store_no`,
            </if>
            
            <if test="storeName != null">
                `store_name`,
            </if>
            
            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>
            
            <if test="orderUnitName != null">
                `order_unit_name`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="shopName != null">
                `shop_name`,
            </if>
            
            <if test="shopNo != null">
                `shop_no`,
            </if>
            
            <if test="vstoreName != null">
                `vstore_name`,
            </if>
            
            <if test="vstoreCode != null">
                `vstore_code`,
            </if>
            
            <if test="activeName != null">
                `active_name`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="endTime != null">
                `end_time`,
            </if>
            
            <if test="isApprove != null">
                `is_approve`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="channelType != null">
                `channel_type`,
            </if>
            
            <if test="channelName != null">
                `channel_name`,
            </if>
            
            <if test="sourcePlatform != null">
                `source_platform`,
            </if>
            
            <if test="startTime != null">
                `start_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="lockType != null">
                `lock_type`,
            </if>
            <if test="syncStatus != null">
                `sync_status`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="storeNo != null">
                #{storeNo},
            </if>
            
            <if test="storeName != null">
                #{storeName},
            </if>
            
            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>
            
            <if test="orderUnitName != null">
                #{orderUnitName},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="shopName != null">
                #{shopName},
            </if>
            
            <if test="shopNo != null">
                #{shopNo},
            </if>
            
            <if test="vstoreName != null">
                #{vstoreName},
            </if>
            
            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>
            
            <if test="activeName != null">
                #{activeName},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="endTime != null">
                #{endTime},
            </if>
            
            <if test="isApprove != null">
                #{isApprove},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="channelType != null">
                #{channelType},
            </if>
            
            <if test="channelName != null">
                #{channelName},
            </if>
            
            <if test="sourcePlatform != null">
                #{sourcePlatform},
            </if>
            
            <if test="startTime != null">
                #{startTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="lockType != null">
                #{lockType},
            </if>

            <if test="syncStatus != null">
                #{syncStatus},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLock" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO inventory_active_lock (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.createUser}, #{item.updateTime}, #{item.storeNo}, #{item.storeName}, #{item.orderUnitNo}, #{item.orderUnitName}, #{item.createTime}, #{item.shopName}, #{item.shopNo}, #{item.vstoreName}, #{item.vstoreCode}, #{item.activeName}, #{item.billNo}, #{item.id}, #{item.endTime}, #{item.isApprove}, #{item.status}, #{item.remark}, #{item.channelType}, #{item.channelName}, #{item.sourcePlatform}, #{item.startTime}, #{item.updateUser}, #{item.lockType})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.InventoryActiveLock">
        UPDATE inventory_active_lock
        <set>
            
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if> 
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if> 
            <if test="orderUnitName != null">
                `order_unit_name` = #{orderUnitName},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="shopName != null">
                `shop_name` = #{shopName},
            </if> 
            <if test="shopNo != null">
                `shop_no` = #{shopNo},
            </if> 
            <if test="vstoreName != null">
                `vstore_name` = #{vstoreName},
            </if> 
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if> 
            <if test="activeName != null">
                `active_name` = #{activeName},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="endTime != null">
                `end_time` = #{endTime},
            </if> 
            <if test="isApprove != null">
                `is_approve` = #{isApprove},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="channelType != null">
                `channel_type` = #{channelType},
            </if> 
            <if test="channelName != null">
                `channel_name` = #{channelName},
            </if> 
            <if test="sourcePlatform != null">
                `source_platform` = #{sourcePlatform},
            </if> 
            <if test="startTime != null">
                `start_time` = #{startTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="lockType != null">
                `lock_type` = #{lockType},
            </if>
            <if test="syncStatus != null">
                `sync_status` = #{syncStatus},
            </if>
            update_time =  now() 
        </set>

        WHERE bill_no = #{billNo}
                
    </update>

    <select id="selectByUniques" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock 
        where bill_no in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
        <include refid="adsfs_condition" />
    </select>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM inventory_active_lock 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
        <include refid="adsfs_condition" />
    </select>


    <!-- auto generate end-->

</mapper>