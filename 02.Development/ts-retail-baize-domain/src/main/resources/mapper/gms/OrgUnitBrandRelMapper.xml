<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.OrgUnitBrandRelRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel">
                
        <id column="id" property="id" jdbcType="INTEGER" />
        
        
        <result column="org_search_code" property="orgSearchCode" jdbcType="VARCHAR" />
        
        <result column="order_search_code" property="orderSearchCode" jdbcType="VARCHAR" />
        
        <result column="company_no" property="companyNo" jdbcType="CHAR" />
        
        <result column="shop_store_no" property="shopStoreNo" jdbcType="VARCHAR" />
        
        <result column="shop_store_code" property="shopStoreCode" jdbcType="VARCHAR" />
        
        <result column="shop_store_name" property="shopStoreName" jdbcType="VARCHAR" />
        
        <result column="store_search_code" property="storeSearchCode" jdbcType="VARCHAR" />
        
        <result column="org_type" property="orgType" jdbcType="BIGINT" />
        
        <result column="zone_no" property="zoneNo" jdbcType="CHAR" />
        
        <result column="zone_name" property="zoneName" jdbcType="VARCHAR" />
        
        <result column="wms_store_name" property="wmsStoreName" jdbcType="VARCHAR" />
        
        <result column="store_no" property="storeNo" jdbcType="CHAR" />
        
        <result column="store_code" property="storeCode" jdbcType="VARCHAR" />
        
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        
        <result column="brand_no" property="brandNo" jdbcType="CHAR" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="store_type" property="storeType" jdbcType="TINYINT" />
        
        <result column="rel_status" property="relStatus" jdbcType="TINYINT" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="organ_no" property="organNo" jdbcType="CHAR" />
        
        <result column="wms_store_no" property="wmsStoreNo" jdbcType="VARCHAR" />
        
        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR" />
        
        <result column="order_unit_code" property="orderUnitCode" jdbcType="VARCHAR" />
        
        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR" />

        <!-- 查询字段 -->
        <result column="multi" property="multi" jdbcType="VARCHAR" />
        <result column="virt_phy_storage_type" property="virtPhyStorageType" jdbcType="TINYINT" />

    </resultMap>

    <sql id="column_list">
        `org_search_code`,`order_search_code`,`company_no`,`shop_store_no`,`shop_store_code`,`shop_store_name`,`store_search_code`,`org_type`,`zone_no`,`zone_name`,`wms_store_name`,`id`,`store_no`,`store_code`,`store_name`,`brand_no`,`status`,`store_type`,`rel_status`,`update_time`,`organ_no`,`wms_store_no`,`order_unit_no`,`order_unit_code`,`order_unit_name`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.orgSearchCode  and ''!=params.orgSearchCode ">
                
                AND `org_search_code`=#{params.orgSearchCode}
                
            </if>
            
            <if test="null!=params.orderSearchCode  and ''!=params.orderSearchCode ">
                
                AND `order_search_code`=#{params.orderSearchCode}
                
            </if>
            
            <if test="null!=params.companyNo  and ''!=params.companyNo ">
                
                AND `company_no`=#{params.companyNo}
                
            </if>
            
            <if test="null!=params.shopStoreNo  and ''!=params.shopStoreNo ">
                
                AND `shop_store_no`=#{params.shopStoreNo}
                
            </if>
            
            <if test="null!=params.shopStoreCode  and ''!=params.shopStoreCode ">
                
                AND `shop_store_code`=#{params.shopStoreCode}
                
            </if>
            
            <if test="null!=params.shopStoreName  and ''!=params.shopStoreName ">
                
                AND `shop_store_name` like CONCAT('%',#{params.shopStoreName},'%') 
                
            </if>
            
            <if test="null!=params.storeSearchCode  and ''!=params.storeSearchCode ">
                
                AND `store_search_code`=#{params.storeSearchCode}
                
            </if>
            
            <if test="null!=params.orgType ">
                
                AND `org_type`=#{params.orgType}
                
            </if>
            
            <if test="null!=params.zoneNo  and ''!=params.zoneNo ">
                
                AND `zone_no`=#{params.zoneNo}
                
            </if>
            
            <if test="null!=params.zoneName  and ''!=params.zoneName ">
                
                AND `zone_name` like CONCAT('%',#{params.zoneName},'%') 
                
            </if>
            
            <if test="null!=params.wmsStoreName  and ''!=params.wmsStoreName ">
                
                AND `wms_store_name` like CONCAT('%',#{params.wmsStoreName},'%') 
                
            </if>
            
            <if test="null!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.storeNo  and ''!=params.storeNo ">
                
                AND `store_no`=#{params.storeNo}
                
            </if>
            
            <if test="null!=params.storeCode  and ''!=params.storeCode ">
                
                AND `store_code`=#{params.storeCode}
                
            </if>
            
            <if test="null!=params.storeName  and ''!=params.storeName ">
                
                AND `store_name` like CONCAT('%',#{params.storeName},'%') 
                
            </if>
            
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                
                AND `brand_no`=#{params.brandNo}
                
            </if>
            
            <if test="null!=params.status ">
                
                AND `status`=#{params.status}
                
            </if>
            
            <if test="null!=params.storeType ">
                
                AND `store_type`=#{params.storeType}
                
            </if>
            
            <if test="null!=params.relStatus ">
                
                AND `rel_status`=#{params.relStatus}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.organNo  and ''!=params.organNo ">
                
                AND `organ_no`=#{params.organNo}
                
            </if>
            
            <if test="null!=params.wmsStoreNo  and ''!=params.wmsStoreNo ">
                
                AND `wms_store_no`=#{params.wmsStoreNo}
                
            </if>
            
            <if test="null!=params.orderUnitNo  and ''!=params.orderUnitNo ">
                
                AND `order_unit_no`=#{params.orderUnitNo}
                
            </if>
            
            <if test="null!=params.orderUnitCode  and ''!=params.orderUnitCode ">
                
                AND `order_unit_code`=#{params.orderUnitCode}
                
            </if>
            
            <if test="null!=params.orderUnitName  and ''!=params.orderUnitName ">
                
                AND `order_unit_name` like CONCAT('%',#{params.orderUnitName},'%') 
                
            </if>

        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM org_unit_brand_rel
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM org_unit_brand_rel
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM org_unit_brand_rel
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM org_unit_brand_rel
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM org_unit_brand_rel
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM org_unit_brand_rel
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM org_unit_brand_rel
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM org_unit_brand_rel
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM org_unit_brand_rel
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO org_unit_brand_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="orgSearchCode != null">
                `org_search_code`,
            </if>
            
            <if test="orderSearchCode != null">
                `order_search_code`,
            </if>
            
            <if test="companyNo != null">
                `company_no`,
            </if>
            
            <if test="shopStoreNo != null">
                `shop_store_no`,
            </if>
            
            <if test="shopStoreCode != null">
                `shop_store_code`,
            </if>
            
            <if test="shopStoreName != null">
                `shop_store_name`,
            </if>
            
            <if test="storeSearchCode != null">
                `store_search_code`,
            </if>
            
            <if test="orgType != null">
                `org_type`,
            </if>
            
            <if test="zoneNo != null">
                `zone_no`,
            </if>
            
            <if test="zoneName != null">
                `zone_name`,
            </if>
            
            <if test="wmsStoreName != null">
                `wms_store_name`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="storeNo != null">
                `store_no`,
            </if>
            
            <if test="storeCode != null">
                `store_code`,
            </if>
            
            <if test="storeName != null">
                `store_name`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="storeType != null">
                `store_type`,
            </if>
            
            <if test="relStatus != null">
                `rel_status`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="organNo != null">
                `organ_no`,
            </if>
            
            <if test="wmsStoreNo != null">
                `wms_store_no`,
            </if>
            
            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>
            
            <if test="orderUnitCode != null">
                `order_unit_code`,
            </if>
            
            <if test="orderUnitName != null">
                `order_unit_name`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="orgSearchCode != null">
                #{orgSearchCode},
            </if>
            
            <if test="orderSearchCode != null">
                #{orderSearchCode},
            </if>
            
            <if test="companyNo != null">
                #{companyNo},
            </if>
            
            <if test="shopStoreNo != null">
                #{shopStoreNo},
            </if>
            
            <if test="shopStoreCode != null">
                #{shopStoreCode},
            </if>
            
            <if test="shopStoreName != null">
                #{shopStoreName},
            </if>
            
            <if test="storeSearchCode != null">
                #{storeSearchCode},
            </if>
            
            <if test="orgType != null">
                #{orgType},
            </if>
            
            <if test="zoneNo != null">
                #{zoneNo},
            </if>
            
            <if test="zoneName != null">
                #{zoneName},
            </if>
            
            <if test="wmsStoreName != null">
                #{wmsStoreName},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="storeNo != null">
                #{storeNo},
            </if>
            
            <if test="storeCode != null">
                #{storeCode},
            </if>
            
            <if test="storeName != null">
                #{storeName},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="storeType != null">
                #{storeType},
            </if>
            
            <if test="relStatus != null">
                #{relStatus},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="organNo != null">
                #{organNo},
            </if>
            
            <if test="wmsStoreNo != null">
                #{wmsStoreNo},
            </if>
            
            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>
            
            <if test="orderUnitCode != null">
                #{orderUnitCode},
            </if>
            
            <if test="orderUnitName != null">
                #{orderUnitName},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel">
        UPDATE org_unit_brand_rel
        <set>
            
            <if test="orgSearchCode != null">
                `org_search_code` = #{orgSearchCode},
            </if> 
            <if test="orderSearchCode != null">
                `order_search_code` = #{orderSearchCode},
            </if> 
            <if test="companyNo != null">
                `company_no` = #{companyNo},
            </if> 
            <if test="shopStoreNo != null">
                `shop_store_no` = #{shopStoreNo},
            </if> 
            <if test="shopStoreCode != null">
                `shop_store_code` = #{shopStoreCode},
            </if> 
            <if test="shopStoreName != null">
                `shop_store_name` = #{shopStoreName},
            </if> 
            <if test="storeSearchCode != null">
                `store_search_code` = #{storeSearchCode},
            </if> 
            <if test="orgType != null">
                `org_type` = #{orgType},
            </if> 
            <if test="zoneNo != null">
                `zone_no` = #{zoneNo},
            </if> 
            <if test="zoneName != null">
                `zone_name` = #{zoneName},
            </if> 
            <if test="wmsStoreName != null">
                `wms_store_name` = #{wmsStoreName},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            <if test="storeCode != null">
                `store_code` = #{storeCode},
            </if> 
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="storeType != null">
                `store_type` = #{storeType},
            </if> 
            <if test="relStatus != null">
                `rel_status` = #{relStatus},
            </if> 
            <if test="organNo != null">
                `organ_no` = #{organNo},
            </if> 
            <if test="wmsStoreNo != null">
                `wms_store_no` = #{wmsStoreNo},
            </if> 
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if> 
            <if test="orderUnitCode != null">
                `order_unit_code` = #{orderUnitCode},
            </if> 
            <if test="orderUnitName != null">
                `order_unit_name` = #{orderUnitName},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE id = #{id}
        
            
    </update>

    <select id="selectStoreByUnitNo" resultMap="baseResultMap" parameterType="map">
        SELECT
        o.`store_no`,o.`store_code`,o.`store_name`,o.`order_unit_no`,o.`order_unit_code`,o.`order_unit_name`,o.`store_type`
        ,sp.`multi`,s.`virt_phy_storage_type`
        FROM
        org_unit_brand_rel o
        LEFT JOIN store s ON o.store_no = s.store_no
        LEFT JOIN shop sp ON o.store_no = sp.store_no
        WHERE
        1 = 1
        AND o.`status` = 1
        AND s.`status` = 1
        AND o.order_unit_no = #{orderUnitNo}
        GROUP BY
        o.store_no,
        o.order_unit_no
    </select>


    <select id="selectValidStoreNos" resultType="java.lang.String">
        select
            DISTINCT store_no
        from org_unit_brand_rel
        where`status` = 1
        AND store_no in
            <foreach collection="storeNos" item="storeNo"
                     open="(" close=")" separator=",">
                #{storeNo}
            </foreach>
            and zone_no = #{zoneNo}
    </select>


    <select id="selectValidStoreNoZones" resultMap="baseResultMap">
        select
            store_no, zone_no,MAX(zone_name) AS zone_name
        from org_unit_brand_rel
        where `status` = 1
        AND store_no in
        <foreach collection="storeNos" item="storeNo"
                 open="(" close=")" separator=",">
            #{storeNo}
        </foreach>
        group by store_no, zone_no
    </select>

    <select id="pageValidShop" resultMap="baseResultMap">
        select
            store_no,MAX(store_name) AS store_name
        from org_unit_brand_rel
        where `status` = 1
        and store_type=21
        AND brand_no in
        <foreach collection="brandNos" item="brandNo"
                 open="(" close=")" separator=",">
            #{brandNo}
        </foreach>
        AND zone_no in
        <foreach collection="zoneNos" item="zoneNo"
                open="(" close=")" separator=",">
            #{zoneNo}
        </foreach>
        <if test="null!=storeNoOrName  and ''!=storeNoOrName">
            AND (`store_name` like CONCAT('%',#{storeNoOrName},'%') or `store_no` like CONCAT('%',#{storeNoOrName},'%') )
        </if>
        group by store_no
        order by store_no
        LIMIT ${startRowNum},${pageSize}
    </select>

    <select id="countValidShop" resultType="java.lang.Integer">
        select
            count(distinct(store_no))
        from org_unit_brand_rel
        where `status` = 1
        and store_type=21
        AND brand_no in
        <foreach collection="brandNos" item="brandNo"
                 open="(" close=")" separator=",">
            #{brandNo}
        </foreach>
        AND zone_no in
        <foreach collection="zoneNos" item="zoneNo"
                 open="(" close=")" separator=",">
            #{zoneNo}
        </foreach>
        <if test="null!=storeNoOrName  and ''!=storeNoOrName">
            AND (`store_name` like CONCAT('%',#{storeNoOrName},'%') or `store_no` like CONCAT('%',#{storeNoOrName},'%') )
        </if>

    </select>


</mapper>