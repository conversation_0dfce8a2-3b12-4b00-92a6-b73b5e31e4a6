<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.BrandInventoryRangeRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.BrandInventoryRange">
        <id column="id" property="id" jdbcType="CHAR"/>


        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>

        <result column="channel" property="channel" jdbcType="TINYINT"/>

        <result column="bussiness_type" property="bussinessType" jdbcType="VARCHAR"/>
        <result column="bussiness_type_name" property="bussinessTypeName" jdbcType="VARCHAR"/>

        <result column="safety_stock" property="safetyStock" jdbcType="INTEGER"/>

        <result column="sharing_ratio" property="sharingRatio" jdbcType="TINYINT"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="purchase_season" property="purchaseSeason" jdbcType="VARCHAR"/>

        <result column="years" property="years" jdbcType="VARCHAR"/>

        <result column="brand_no" property="brandNo" jdbcType="CHAR"/>

        <result column="brand_name" property="brandName" jdbcType="VARCHAR"/>

        <result column="years_name" property="yearsName" jdbcType="VARCHAR"/>
        <result column="purchase_season_name" property="purchaseSeasonName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `id`,
        `update_user`,
        `create_user`,
        `channel`,
        `bussiness_type`,
        `safety_stock`,
        `sharing_ratio`,
        `update_time`,
        `create_time`,
        `purchase_season`,
        `years`,
        `brand_no`,
        `brand_name`,
        (SELECT info.vstore_name
         FROM internet_virtual_warehouse_info info
         WHERE info.vstore_code = bussiness_type)                                                              as bussiness_type_name,
        (SELECT entry.name FROM lookup_entry entry WHERE entry.code = years)                                   as years_name,
        (SELECT entry.name
         FROM lookup_entry entry
         WHERE entry.code = purchase_season
           and entry.lookup_id = 6)                                                                            as purchase_season_name
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.id  and '' != params.id">
                AND `id`=#{params.id}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.channel">
                AND `channel`=#{params.channel}
            </if>

            <if test="null != params.bussinessType  and '' != params.bussinessType">
                AND `bussiness_type`=#{params.bussinessType}
            </if>

            <if test="null != params.safetyStock">
                AND `safety_stock`=#{params.safetyStock}
            </if>

            <if test="null != params.sharingRatio">
                AND `sharing_ratio`=#{params.sharingRatio}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.purchaseSeason  and '' != params.purchaseSeason">
                AND `purchase_season`=#{params.purchaseSeason}
            </if>

            <if test="null != params.purchaseSeasonList and params.purchaseSeasonList.size() > 0">
                AND `purchase_season` in
                <foreach collection="params.purchaseSeasonList"  item="item" separator="," open="(" close=")">
                #{item}
                </foreach>
            </if>

            <if test="null != params.years  and '' != params.years">
                AND `years`=#{params.years}
            </if>

            <if test="null != params.yearsList and params.yearsList.size() > 0">
                AND `years` in
                <foreach collection="params.yearsList"  item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="null != params.brandNo  and '' != params.brandNo">
                AND `brand_no`=#{params.brandNo}
            </if>

            <if test="null != params.brandName  and '' != params.brandName">
                AND `brand_name` like CONCAT('%',
                    #{params.brandName}, '%')
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;=
                    #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM brand_inventory_range
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM brand_inventory_range
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM brand_inventory_range
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM brand_inventory_range
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM brand_inventory_range
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM brand_inventory_range
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM brand_inventory_range
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM brand_inventory_range
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM brand_inventory_range
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.BrandInventoryRange">
        INSERT INTO brand_inventory_range
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="channel != null">
                `channel`,
            </if>

            <if test="bussinessType != null">
                `bussiness_type`,
            </if>

            <if test="safetyStock != null">
                `safety_stock`,
            </if>

            <if test="sharingRatio != null">
                `sharing_ratio`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="purchaseSeason != null">
                `purchase_season`,
            </if>

            <if test="years != null">
                `years`,
            </if>

            <if test="brandNo != null">
                `brand_no`,
            </if>

            <if test="brandName != null">
                `brand_name`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="channel != null">
                #{channel},
            </if>

            <if test="bussinessType != null">
                #{bussinessType},
            </if>

            <if test="safetyStock != null">
                #{safetyStock},
            </if>

            <if test="sharingRatio != null">
                #{sharingRatio},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="purchaseSeason != null">
                #{purchaseSeason},
            </if>

            <if test="years != null">
                #{years},
            </if>

            <if test="brandNo != null">
                #{brandNo},
            </if>

            <if test="brandName != null">
                #{brandName},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.BrandInventoryRange"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO brand_inventory_range (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.updateUser}, #{item.createUser}, #{item.channel}, #{item.bussinessType},
             #{item.safetyStock}, #{item.sharingRatio}, #{item.updateTime}, #{item.createTime}, #{item.purchaseSeason},
             #{item.years}, #{item.brandNo}, #{item.brandName})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.BrandInventoryRange">
        UPDATE brand_inventory_range
        <set>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="channel != null">
                `channel` = #{channel},
            </if>
            <if test="bussinessType != null">
                `bussiness_type` = #{bussinessType},
            </if>
            <if test="safetyStock != null">
                `safety_stock` = #{safetyStock},
            </if>
            <if test="sharingRatio != null">
                `sharing_ratio` = #{sharingRatio},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="purchaseSeason != null">
                `purchase_season` = #{purchaseSeason},
            </if>
            <if test="years != null">
                `years` = #{years},
            </if>
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if>
            <if test="brandName != null">
                `brand_name` = #{brandName},
            </if>
            update_time = now()
        </set>


        WHERE id = #{id}
    </update>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM brand_inventory_range
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- auto generate end-->


    <update id="updateShare" parameterType="map">
        update brand_inventory_range
        set sharing_ratio = #{sharingRatio,jdbcType=INTEGER},
            safety_stock  = #{safetystock,jdbcType=INTEGER},
            update_time = now()
        <if test="updateUser != null and !''.equals(updateUser)">
            ,update_user= #{updateUser,jdbcType=VARCHAR}
        </if>

        where brand_no = #{brandNo,jdbcType=CHAR}
          and years = #{years,jdbcType=VARCHAR}
          and purchase_season = #{purchaseSeason,jdbcType=VARCHAR}
        <if test="bussinessTypeExport != null and !''.equals(bussinessTypeExport)">
            and bussiness_type = #{bussinessTypeExport}
        </if>
    </update>


    <select id="selectShareNoExist" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM brand_inventory_range
        WHERE 1 = 1
        <if test="params.brandNo != null and !''.equals(params.brandNo)">
            and brand_no = #{params.brandNo}
        </if>
        <if test="params.years != null and !''.equals(params.years)">
            and years = #{params.years}
        </if>
        <if test="params.purchaseSeason != null and !''.equals(params.purchaseSeason)">
            and purchase_season = #{params.purchaseSeason}
        </if>
        <if test="params.bussinessTypeExport != null and !''.equals(params.bussinessTypeExport)">
            and bussiness_type = #{params.bussinessTypeExport}
        </if>
    </select>
</mapper>