<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.IcsShopCreditScoreRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore">
        <id column="id" property="id" jdbcType="CHAR"/>

        <result column="credit_date" property="creditDate" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="credit_score" property="creditScore" jdbcType="INTEGER"/>
        <result column="shop_name" property="shopName" jdbcType="VARCHAR"/>
        <result column="shop_no" property="shopNo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `credit_date`,
        `update_time`,
        `update_user`,
        `create_time`,
        `create_user`,
        `remark`,
        `status`,
        `credit_score`,
        `shop_name`,
        `shop_no`,
        `id`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.creditDate">
                AND `credit_date`=#{params.creditDate}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.remark  and '' != params.remark">
                AND `remark`=#{params.remark}
            </if>

            <if test="null != params.status">
                AND `status`=#{params.status}
            </if>

            <if test="null != params.creditScore">
                AND `credit_score`=#{params.creditScore}
            </if>

            <if test="null != params.shopNoOrName and '' != params.shopNoOrName ">
                AND (shop_name like CONCAT('%', #{params.shopNoOrName}, '%')
                    or shop_no like CONCAT('%', #{params.shopNoOrName}, '%')
                )
            </if>

            <if test="null != params.shopName  and '' != params.shopName">
                AND `shop_name`=#{params.shopName}
            </if>
            <if test="null != params.shopNameLike  and '' != params.shopNameLike">
                AND `shop_name` like CONCAT('%',
                    #{params.shopNameLike}, '%')
            </if>

            <if test="null != params.shopNo  and '' != params.shopNo">
                AND `shop_no`= #{params.shopNo}
            </if>

            <if test="null != params.id  and '' != params.id">
                AND `id`=#{params.id}
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_shop_credit_score
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_shop_credit_score
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_shop_credit_score
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM ics_shop_credit_score
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_shop_credit_score
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM ics_shop_credit_score
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM ics_shop_credit_score
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM ics_shop_credit_score
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM ics_shop_credit_score
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore">
        INSERT INTO ics_shop_credit_score
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="creditDate != null">
                `credit_date`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="status != null">
                `status`,
            </if>

            <if test="creditScore != null">
                `credit_score`,
            </if>

            <if test="shopName != null">
                `shop_name`,
            </if>

            <if test="shopNo != null">
                `shop_no`,
            </if>

            <if test="id != null">
                `id`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="creditDate != null">
                #{creditDate},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="remark != null">
                #{remark},
            </if>

            <if test="status != null">
                #{status},
            </if>

            <if test="creditScore != null">
                #{creditScore},
            </if>

            <if test="shopName != null">
                #{shopName},
            </if>

            <if test="shopNo != null">
                #{shopNo},
            </if>

            <if test="id != null">
                #{id},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ics_shop_credit_score (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            (#{item.creditDate}, #{item.updateTime}, #{item.updateUser}, #{item.createTime}, #{item.createUser},
             #{item.remark}, #{item.status}, #{item.creditScore}, #{item.shopName}, #{item.shopNo}, #{item.id})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore">
        UPDATE ics_shop_credit_score
        <set>
            <if test="creditDate != null">
                `credit_date` = #{creditDate},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="creditScore != null">
                `credit_score` = #{creditScore},
            </if>
            <if test="shopName != null">
                `shop_name` = #{shopName},
            </if>
            <if test="shopNo != null">
                `shop_no` = #{shopNo},
            </if>
            update_time = now()
        </set>


        WHERE id = #{id}
    </update>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_shop_credit_score
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <sql id="baseSelectShopCreditScore">
        SELECT s.* FROM ics_shop_credit_score s
        LEFT JOIN org_unit_brand_rel r
        ON s.shop_no = r.store_no
        AND r.store_type = 21
        WHERE 1=1
        <choose>
            <when test="params.brandNo != null and '' != params.brandNo">
                AND r.brand_no = #{params.brandNo}
            </when>
            <otherwise>
                -- AND @r.brand_no
            </otherwise>
        </choose>
        <if test="null != params.shopNoOrName and '' != params.shopNoOrName ">
            AND (shop_name like CONCAT('%', #{params.shopNoOrName}, '%')
            or shop_no like CONCAT('%', #{params.shopNoOrName}, '%')
            )
        </if>
        GROUP BY s.shop_no
    </sql>

    <select id="selectShopCreditScoreByCount" resultType="int">
        SELECT COUNT(1) FROM (
            <include refid="baseSelectShopCreditScore"/>
         ) t
    </select>

    <select id="selectShopCreditScoreByPage" resultMap="baseResultMap">
        SELECT <include refid="column_list"/> FROM (
            <include refid="baseSelectShopCreditScore"/>
        ) t
        ORDER BY t.update_time DESC, t.shop_no
        LIMIT ${page.startRowNum}, ${page.pageSize}
    </select>

    <select id="selectShopCreditScoreByParams" resultMap="baseResultMap">
        SELECT <include refid="column_list"/> FROM (
            <include refid="baseSelectShopCreditScore"/>
        ) t
        ORDER BY t.update_time DESC, t.shop_no
    </select>

    <!-- auto generate end-->
</mapper>