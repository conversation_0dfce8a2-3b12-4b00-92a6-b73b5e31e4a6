<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.IcsInventorySyncConfigRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        <result column="channel_no" property="channelNo" jdbcType="VARCHAR" />
        
        <result column="channel_name" property="channelName" jdbcType="VARCHAR" />
        
        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR" />
        
        <result column="vstore_name" property="vstoreName" jdbcType="VARCHAR" />
        
        <result column="channel_type" property="channelType" jdbcType="TINYINT" />
        
        <result column="sharing_ratio" property="sharingRatio" jdbcType="TINYINT" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />

        <result column="third_channel_no" property="thirdChannelNo" jdbcType="VARCHAR" />

        <result column="vstore_mold" property="vstoreMold" jdbcType="TINYINT" />

    </resultMap>

    <sql id="column_list">
        `id`,`channel_no`,`channel_name`,`vstore_code`,`vstore_name`,`channel_type`,`sharing_ratio`,`status`,`remark`,`create_user`,`create_time`,`update_user`,`update_time`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            <if test="null!=params.id  and ''!=params.id ">
                AND `id`=#{params.id}
            </if>
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                AND `channel_no`=#{params.channelNo}
            </if>
            <if test="null!=params.channelName  and ''!=params.channelName ">
                AND `channel_name` like CONCAT('%',#{params.channelName},'%')
            </if>
            <if test="null!=params.vstoreCode  and ''!=params.vstoreCode ">
                AND `vstore_code`=#{params.vstoreCode}
            </if>
            <if test="null!=params.vstoreName  and ''!=params.vstoreName ">
                AND `vstore_name` like CONCAT('%',#{params.vstoreName},'%')
            </if>
            <if test="null!=params.channelType ">
                AND `channel_type`=#{params.channelType}
            </if>
            <if test="null!=params.sharingRatio ">
                AND `sharing_ratio`=#{params.sharingRatio}
            </if>
            <if test="null!=params.status ">
                AND `status`=#{params.status}
            </if>
            <if test="null!=params.remark  and ''!=params.remark ">
                AND `remark`=#{params.remark}
            </if>
            <if test="null!=params.createUser  and ''!=params.createUser ">
                AND `create_user`=#{params.createUser}
            </if>
            <if test="null!=params.createTime ">
                AND `create_time`=#{params.createTime}
            </if>
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                AND `update_user`=#{params.updateUser}
            </if>
            <if test="null!=params.updateTime ">
                AND `update_time`=#{params.updateTime}
            </if>
            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
				AND `create_time` &gt;= #{params.createTimeStart}
			</if>
        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
				AND `create_time` &lt;= #{params.createTimeEnd}
			</if>
			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
				AND `update_time` &gt;= #{params.updateTimeStart}
			</if>
        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
				AND `update_time` &lt;= #{params.updateTimeEnd}
			</if>
            <if test="null!=params.channelNoList  and params.channelNoList.size() > 0 ">
                AND `channel_no` IN
                <foreach collection="params.channelNoList" item="channelNo" open="(" close=")" separator=",">
                    #{channelNo}
                </foreach>
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM ics_inventory_sync_config
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM ics_inventory_sync_config
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM ics_inventory_sync_config
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM ics_inventory_sync_config
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM ics_inventory_sync_config
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM ics_inventory_sync_config
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM ics_inventory_sync_config
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM ics_inventory_sync_config
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM ics_inventory_sync_config
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig"  >
        INSERT INTO ics_inventory_sync_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="channelNo != null">
                `channel_no`,
            </if>
            
            <if test="channelName != null">
                `channel_name`,
            </if>
            
            <if test="vstoreCode != null">
                `vstore_code`,
            </if>
            
            <if test="vstoreName != null">
                `vstore_name`,
            </if>
            
            <if test="channelType != null">
                `channel_type`,
            </if>
            
            <if test="sharingRatio != null">
                `sharing_ratio`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="channelNo != null">
                #{channelNo},
            </if>
            
            <if test="channelName != null">
                #{channelName},
            </if>
            
            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>
            
            <if test="vstoreName != null">
                #{vstoreName},
            </if>
            
            <if test="channelType != null">
                #{channelType},
            </if>
            
            <if test="sharingRatio != null">
                #{sharingRatio},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ics_inventory_sync_config (<include refid="column_list"></include>)
        values 
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.channelNo}, #{item.channelName}, #{item.vstoreCode}, #{item.vstoreName}, #{item.channelType}, #{item.sharingRatio}, #{item.status}, #{item.remark}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig">
        UPDATE ics_inventory_sync_config
        <set>

            <if test="channelNo != null">
                `channel_no` = #{channelNo},
            </if> 
            <if test="channelName != null">
                `channel_name` = #{channelName},
            </if> 
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if> 
            <if test="vstoreName != null">
                `vstore_name` = #{vstoreName},
            </if> 
            <if test="channelType != null">
                `channel_type` = #{channelType},
            </if> 
            <if test="sharingRatio != null">
                `sharing_ratio` = #{sharingRatio},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            update_time =  now() 
        </set>

        
        
        WHERE id = #{id}
                
    </update>

	<select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM ics_inventory_sync_config 
        where id in 
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
          #{item}
      	</foreach>
    </select>

    <select id="selectShopCountByParams" resultType="java.lang.Integer">
        select count(distinct channel_no) from ics_inventory_sync_config
        <where>
            <include refid="condition" />
            <if test="params.queryParam!=null and params.queryParam!='' ">
                AND (channel_no like CONCAT('%',#{params.queryParam},'%')
                or channel_name like CONCAT('%',#{params.queryParam},'%'))
            </if>
        </where>
    </select>

    <select id="selectShopPageByParams" resultMap="baseResultMap">
        select distinct channel_no, channel_name from ics_inventory_sync_config
        <where>
            1=1
            <include refid="condition" />
            <if test="params.queryParam!=null and params.queryParam!='' ">
                AND (channel_no like CONCAT('%',#{params.queryParam},'%')
                or channel_name like CONCAT('%',#{params.queryParam},'%'))
            </if>
        </where>
        order by channel_no
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectVstoreListByParams" resultType="cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo">
        select distinct vstore_code, vstore_name from ics_inventory_sync_config
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectWithDistinct" resultType="cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig">
        SELECT
        vstore_code as vstoreCode,
        vstore_name as vstoreName,
        channel_no as channelNo,
        channel_name as channelName,
        sharing_ratio as sharingRatio
        FROM ics_inventory_sync_config
        <where>
            <if test="params.warehouseCode != null">
                AND (vstore_code = #{params.warehouseCode} or vstore_name like CONCAT('%',#{params.warehouseCode},'%'))
            </if>
            <if test="params.vstoreName != null">
                AND vstore_name = #{params.vstoreName}
            </if>
            <if test="params.channelType != null">
                AND channel_type = #{params.channelType}
            </if>
            <if test="params.sharingRatio != null">
                AND sharing_ratio = #{params.sharingRatio}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.remark != null">
                AND remark = #{params.remark}
            </if>
        </where>
        GROUP BY vstore_code
    </select>

    <select id="selectByPageWithDistinct" resultType="cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig">
        SELECT
        vstore_code as vstoreCode,
        vstore_name as vstoreName,
        channel_no as channelNo,
        channel_name as channelName,
        sharing_ratio as sharingRatio
        FROM ics_inventory_sync_config
        <where>
            <if test="params.warehouseCode != null">
                AND (vstore_code = #{params.warehouseCode} or vstore_name like CONCAT('%',#{params.warehouseCode},'%'))
            </if>
            <if test="params.vstoreName != null">
                AND vstore_name = #{params.vstoreName}
            </if>
            <if test="params.channelType != null">
                AND channel_type = #{params.channelType}
            </if>
            <if test="params.sharingRatio != null">
                AND sharing_ratio = #{params.sharingRatio}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.remark != null">
                AND remark = #{params.remark}
            </if>
        </where>
        GROUP BY vstore_code
        LIMIT ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByPageWithDistinctCount" resultType="integer">
        select count(1) from(
        SELECT
        vstore_code
        FROM ics_inventory_sync_config
        <where>
            <if test="params.warehouseCode != null">
                AND vstore_code = #{params.warehouseCode}
            </if>
            <if test="params.vstoreName != null">
                AND vstore_name = #{params.vstoreName}
            </if>
            <if test="params.channelType != null">
                AND channel_type = #{params.channelType}
            </if>
            <if test="params.sharingRatio != null">
                AND sharing_ratio = #{params.sharingRatio}
            </if>
            <if test="params.status != null">
                AND status = #{params.status}
            </if>
            <if test="params.remark != null">
                AND remark = #{params.remark}
            </if>
        </where>
        GROUP BY vstore_code) t
    </select>


    <select id="countByChannel" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM (
            SELECT 1
            FROM ics_inventory_sync_config a LEFT JOIN order_source_terminal_config b ON a.channel_no=b.third_platform
            <where>
                1=1
                <if test="null!=params.channelType ">
                    AND a.`channel_type`=#{params.channelType}
                </if>
                <if test="null!=params.channelNo  and ''!=params.channelNo ">
                    AND a.`channel_no` like CONCAT('%',#{params.channelNo},'%')
                </if>
                <if test="null!=params.channelName  and ''!=params.channelName ">
                    AND a.`channel_name` like CONCAT('%',#{params.channelName},'%')
                </if>
                <if test="null!=params.thirdChannelNo  and ''!=params.thirdChannelNo ">
                    AND b.`third_channel_no` like CONCAT('%',#{params.thirdChannelNo},'%')
                </if>
            </where>
            GROUP BY a.channel_no
        ) t
    </select>

    <select id="pageByChannel" resultMap="baseResultMap" parameterType="map">
        SELECT
        a.id,a.channel_type,a.channel_no,a.channel_name,a.create_user,a.create_time,a.update_user,max(a.update_time) as update_time,b.third_channel_no
        FROM ics_inventory_sync_config a LEFT JOIN order_source_terminal_config b ON a.channel_no=b.third_platform
        <where>
            1=1
            <if test="null!=params.channelType ">
                AND a.`channel_type`=#{params.channelType}
            </if>
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                AND a.`channel_no` like CONCAT('%',#{params.channelNo},'%')
            </if>
            <if test="null!=params.channelName  and ''!=params.channelName ">
                AND a.`channel_name` like CONCAT('%',#{params.channelName},'%')
            </if>
            <if test="null!=params.thirdChannelNo  and ''!=params.thirdChannelNo ">
                AND b.`third_channel_no` like CONCAT('%',#{params.thirdChannelNo},'%')
            </if>
        </where>
        GROUP BY a.channel_no
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>


    <select id="selectChannelVstoreInfo" resultMap="baseResultMap" parameterType="map">
        SELECT
        a.channel_type,a.channel_no,a.channel_name,a.vstore_code,b.vstore_mold
        FROM ics_inventory_sync_config a LEFT JOIN internet_virtual_warehouse_info b ON a.vstore_code=b.vstore_code
        <where>
            1=1
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                AND a.`channel_no` = #{params.channelNo}
            </if>
            <if test="null!=params.channelType ">
                AND a.`channel_type`=#{params.channelType}
            </if>
        </where>
    </select>

    <!-- auto generate end-->
</mapper>