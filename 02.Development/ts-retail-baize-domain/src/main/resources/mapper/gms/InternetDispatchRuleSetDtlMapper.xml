<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.InternetDispatchRuleSetDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetDtl">
                
        <id column="id" property="id" jdbcType="BIGINT" />
        
        
        <result column="rule_no" property="ruleNo" jdbcType="CHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="channel_no" property="channelNo" jdbcType="VARCHAR" />
        
        <result column="channel_name" property="channelName" jdbcType="VARCHAR" />
        
        <result column="more_shop_flag" property="moreShopFlag" jdbcType="TINYINT" />
        
        <result column="order_source_no" property="orderSourceNo" jdbcType="VARCHAR" />
        
        <result column="order_source_name" property="orderSourceName" jdbcType="VARCHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `rule_no`,`create_time`,`id`,`channel_no`,`channel_name`,`more_shop_flag`,`order_source_no`,`order_source_name`,`create_user`,`update_time`,`update_user`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.ruleNo  and ''!=params.ruleNo ">
                
                AND `rule_no`=#{params.ruleNo}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                
                AND `channel_no`=#{params.channelNo}
                
            </if>
            
            <if test="null!=params.channelName  and ''!=params.channelName ">
                
                AND `channel_name` like CONCAT('%',#{params.channelName},'%') 
                
            </if>
            
            <if test="null!=params.moreShopFlag ">
                
                AND `more_shop_flag`=#{params.moreShopFlag}
                
            </if>
            
            <if test="null!=params.orderSourceNo  and ''!=params.orderSourceNo ">
                
                AND `order_source_no`=#{params.orderSourceNo}
                
            </if>
            
            <if test="null!=params.orderSourceName  and ''!=params.orderSourceName ">
                
                AND `order_source_name` like CONCAT('%',#{params.orderSourceName},'%') 
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_dtl
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_dispatch_rule_set_dtl
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_dispatch_rule_set_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_dispatch_rule_set_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_dispatch_rule_set_dtl
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_dispatch_rule_set_dtl
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetDtl"  useGeneratedKeys="true" keyProperty="id"  >
        INSERT INTO internet_dispatch_rule_set_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="ruleNo != null">
                `rule_no`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="channelNo != null">
                `channel_no`,
            </if>
            
            <if test="channelName != null">
                `channel_name`,
            </if>
            
            <if test="moreShopFlag != null">
                `more_shop_flag`,
            </if>
            
            <if test="orderSourceNo != null">
                `order_source_no`,
            </if>
            
            <if test="orderSourceName != null">
                `order_source_name`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="ruleNo != null">
                #{ruleNo},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="channelNo != null">
                #{channelNo},
            </if>
            
            <if test="channelName != null">
                #{channelName},
            </if>
            
            <if test="moreShopFlag != null">
                #{moreShopFlag},
            </if>
            
            <if test="orderSourceNo != null">
                #{orderSourceNo},
            </if>
            
            <if test="orderSourceName != null">
                #{orderSourceName},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetDtl">
        UPDATE internet_dispatch_rule_set_dtl
        <set>
            
            <if test="ruleNo != null">
                `rule_no` = #{ruleNo},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="channelNo != null">
                `channel_no` = #{channelNo},
            </if> 
            <if test="channelName != null">
                `channel_name` = #{channelName},
            </if> 
            <if test="moreShopFlag != null">
                `more_shop_flag` = #{moreShopFlag},
            </if> 
            <if test="orderSourceNo != null">
                `order_source_no` = #{orderSourceNo},
            </if> 
            <if test="orderSourceName != null">
                `order_source_name` = #{orderSourceName},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>