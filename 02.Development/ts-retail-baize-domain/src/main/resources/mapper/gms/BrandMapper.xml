<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.gms.BrandRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.Brand">
        <id column="id" property="id" jdbcType="INTEGER"/>


        <result column="category" property="category" jdbcType="VARCHAR"/>

        <result column="parent_brand_id" property="parentBrandId" jdbcType="INTEGER"/>

        <result column="name" property="name" jdbcType="VARCHAR"/>

        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR"/>

        <result column="time_seq" property="timeSeq" jdbcType="BIGINT"/>

        <result column="remark" property="remark" jdbcType="VARCHAR"/>

        <result column="logo_url" property="logoUrl" jdbcType="VARCHAR"/>

        <result column="en_short_name" property="enShortName" jdbcType="VARCHAR"/>

        <result column="opcode" property="opcode" jdbcType="CHAR"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="belonger" property="belonger" jdbcType="VARCHAR"/>

        <result column="status" property="status" jdbcType="TINYINT"/>

        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>

        <result column="sys_no" property="sysNo" jdbcType="VARCHAR"/>

        <result column="search_code" property="searchCode" jdbcType="VARCHAR"/>

        <result column="en_name" property="enName" jdbcType="VARCHAR"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>

        <result column="brand_no" property="brandNo" jdbcType="CHAR"/>
    </resultMap>

    <sql id="column_list">
        `category`,
        `parent_brand_id`,
        `name`,
        `organ_type_no`,
        `time_seq`,
        `remark`,
        `logo_url`,
        `en_short_name`,
        `opcode`,
        `update_time`,
        `belonger`,
        `status`,
        `update_user`,
        `sys_no`,
        `search_code`,
        `en_name`,
        `create_time`,
        `create_user`,
        `brand_no`,
        `id`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.category  and '' != params.category">
                AND `category`=#{params.category}
            </if>

            <if test="null != params.parentBrandId">
                AND `parent_brand_id`=#{params.parentBrandId}
            </if>

            <if test="null != params.name  and '' != params.name">
                AND `name` like CONCAT('%',
                    #{params.name}, '%')
            </if>

            <if test="null != params.organTypeNo  and '' != params.organTypeNo">
                AND `organ_type_no`=
                    #{params.organTypeNo}
            </if>

            <if test="null != params.timeSeq">
                AND `time_seq`=#{params.timeSeq}
            </if>

            <if test="null != params.remark  and '' != params.remark">
                AND `remark`=#{params.remark}
            </if>

            <if test="null != params.logoUrl  and '' != params.logoUrl">
                AND `logo_url`=#{params.logoUrl}
            </if>

            <if test="null != params.enShortName  and '' != params.enShortName">
                AND `en_short_name` like CONCAT('%',
                    #{params.enShortName}, '%')
            </if>

            <if test="null != params.opcode  and '' != params.opcode">
                AND `opcode`=
                    #{params.opcode}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.belonger  and '' != params.belonger">
                AND `belonger`=#{params.belonger}
            </if>

            <if test="null != params.status">
                AND `status`=#{params.status}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>

            <if test="null != params.sysNo  and '' != params.sysNo">
                AND `sys_no`=#{params.sysNo}
            </if>

            <if test="null != params.searchCode  and '' != params.searchCode">
                AND `search_code`=#{params.searchCode}
            </if>

            <if test="null != params.enName  and '' != params.enName">
                AND `en_name` like CONCAT('%',
                    #{params.enName}, '%')
            </if>

            <if test="null != params.createTime">
                AND `create_time`=
                    #{params.createTime}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.brandNo  and '' != params.brandNo">
                AND `brand_no`=#{params.brandNo}
            </if>

            <if test="null != params.id">
                AND `id`=#{params.id}
            </if>

            <if test="params.createTimeStart != null  and '' != params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd != null  and '' != params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart != null  and '' != params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd != null  and '' != params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        <if test="null != brandNo and '' != brandNo">
            AND `brand_no`=#{brandNo}
        </if>
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM brand
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM brand
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM brand
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s
        FROM brand
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM brand
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM brand
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM brand
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM brand
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM brand
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.Brand" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO brand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="category != null">
                `category`,
            </if>

            <if test="parentBrandId != null">
                `parent_brand_id`,
            </if>

            <if test="name != null">
                `name`,
            </if>

            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>

            <if test="timeSeq != null">
                `time_seq`,
            </if>

            <if test="remark != null">
                `remark`,
            </if>

            <if test="logoUrl != null">
                `logo_url`,
            </if>

            <if test="enShortName != null">
                `en_short_name`,
            </if>

            <if test="opcode != null">
                `opcode`,
            </if>

            <if test="updateTime != null">
                `update_time`,
            </if>

            <if test="belonger != null">
                `belonger`,
            </if>

            <if test="status != null">
                `status`,
            </if>

            <if test="updateUser != null">
                `update_user`,
            </if>

            <if test="sysNo != null">
                `sys_no`,
            </if>

            <if test="searchCode != null">
                `search_code`,
            </if>

            <if test="enName != null">
                `en_name`,
            </if>

            <if test="createTime != null">
                `create_time`,
            </if>

            <if test="createUser != null">
                `create_user`,
            </if>

            <if test="brandNo != null">
                `brand_no`,
            </if>

            <if test="id != null">
                `id`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="category != null">
                #{category},
            </if>

            <if test="parentBrandId != null">
                #{parentBrandId},
            </if>

            <if test="name != null">
                #{name},
            </if>

            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>

            <if test="timeSeq != null">
                #{timeSeq},
            </if>

            <if test="remark != null">
                #{remark},
            </if>

            <if test="logoUrl != null">
                #{logoUrl},
            </if>

            <if test="enShortName != null">
                #{enShortName},
            </if>

            <if test="opcode != null">
                #{opcode},
            </if>

            <if test="updateTime != null">
                #{updateTime},
            </if>

            <if test="belonger != null">
                #{belonger},
            </if>

            <if test="status != null">
                #{status},
            </if>

            <if test="updateUser != null">
                #{updateUser},
            </if>

            <if test="sysNo != null">
                #{sysNo},
            </if>

            <if test="searchCode != null">
                #{searchCode},
            </if>

            <if test="enName != null">
                #{enName},
            </if>

            <if test="createTime != null">
                #{createTime},
            </if>

            <if test="createUser != null">
                #{createUser},
            </if>

            <if test="brandNo != null">
                #{brandNo},
            </if>

            <if test="id != null">
                #{id},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.Brand" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO brand (<include refid="column_list">
    </include>)
    values
        <foreach collection="list" item="item" separator=",">
            (#{item.category}, #{item.parentBrandId}, #{item.name}, #{item.organTypeNo}, #{item.timeSeq},
             #{item.remark}, #{item.logoUrl}, #{item.enShortName}, #{item.opcode}, #{item.updateTime}, #{item.belonger},
             #{item.status}, #{item.updateUser}, #{item.sysNo}, #{item.searchCode}, #{item.enName}, #{item.createTime},
             #{item.createUser}, #{item.brandNo}, #{item.id})
        </foreach>
    </insert>
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.Brand">
        UPDATE brand
        <set>
            <if test="category != null">
                `category` = #{category},
            </if>
            <if test="parentBrandId != null">
                `parent_brand_id` = #{parentBrandId},
            </if>
            <if test="name != null">
                `name` = #{name},
            </if>
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if>
            <if test="timeSeq != null">
                `time_seq` = #{timeSeq},
            </if>
            <if test="remark != null">
                `remark` = #{remark},
            </if>
            <if test="logoUrl != null">
                `logo_url` = #{logoUrl},
            </if>
            <if test="enShortName != null">
                `en_short_name` = #{enShortName},
            </if>
            <if test="opcode != null">
                `opcode` = #{opcode},
            </if>
            <if test="belonger != null">
                `belonger` = #{belonger},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="sysNo != null">
                `sys_no` = #{sysNo},
            </if>
            <if test="searchCode != null">
                `search_code` = #{searchCode},
            </if>
            <if test="enName != null">
                `en_name` = #{enName},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if>
            update_time = now()
        </set>


        WHERE brand_no = #{brandNo}
    </update>

    <select id="selectByUniques" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM brand
        where brand_no in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByIds" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM brand
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- auto generate end-->
</mapper>