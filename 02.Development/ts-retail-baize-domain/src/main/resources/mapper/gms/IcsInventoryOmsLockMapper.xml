<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.wonhigh.baize.repository.gms.IcsInventoryOmsLockRepository">
    <!-- auto generate -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.IcsInventoryOmsLock" >
        <result  column="id" property="id" jdbcType="VARCHAR"/>
        <result  column="vstore_code" property="vstoreCode" jdbcType="VARCHAR"/>
        <result  column="vstore_name" property="vstoreName" jdbcType="VARCHAR"/>
        <result  column="sku_no" property="skuNo" jdbcType="VARCHAR"/>
        <result  column="lock_qty" property="lockQty" jdbcType="INTEGER"/>
        <result  column="status" property="status" jdbcType="INTEGER"/>
        <result  column="remark" property="remark" jdbcType="VARCHAR"/>
        <result  column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result  column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result  column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result  column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="column_list">
    id,
    vstore_code,
    vstore_name,
    sku_no,
    lock_qty,
    status,
    remark,
    create_user,
    create_time,
    update_user,
    update_time
    </sql>

    <sql id="condition">
        <if test="null!=params.queryCondition and ''!=params.queryCondition">
            And ${params.queryCondition}
        </if>
        <if test="params.id != null and ''!=params.id ">
            And `id` = #{params.id}
        </if>
        <if test="params.vstoreCode != null and ''!=params.vstoreCode ">
            And `vstore_code` = #{params.vstoreCode}
        </if>
        <if test="params.vstoreName != null and ''!=params.vstoreName ">
            And `vstore_name` = #{params.vstoreName}
        </if>
        <if test="params.skuNo != null and ''!=params.skuNo ">
            And `sku_no` = #{params.skuNo}
        </if>
        <if test="params.lockQty != null">
            And `lock_qty` = #{params.lockQty}
        </if>
        <if test="params.status != null">
            And `status` = #{params.status}
        </if>
        <if test="params.remark != null and ''!=params.remark ">
            And `remark` = #{params.remark}
        </if>
        <if test="params.createUser != null and ''!=params.createUser ">
            And `create_user` = #{params.createUser}
        </if>
        <if test="params.createTime != null">
            And `create_time` = #{params.createTime}
        </if>
        <if test="params.updateUser != null and ''!=params.updateUser ">
            And `update_user` = #{params.updateUser}
        </if>
        <if test="params.updateTime != null">
            And `update_time` = #{params.updateTime}
        </if>
    </sql>
    <!--查询单个-->
    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        Select
        <include refid="column_list" />
        From `ics_inventory_oms_lock`
        Where id = #{id}

    </select>

    <!--查询指定行数据-->
    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `ics_inventory_oms_lock`
        <where>
            <include refid="condition" />
        </where>
        Limit 1
    </select>

    <!--统计总行数-->
    <select id="selectCount" resultType="java.lang.Integer" parameterType="map">
        Select Count(1)
        From `ics_inventory_oms_lock`
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `ics_inventory_oms_lock`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
        Limit ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        Select
        <include refid="column_list" />
        From `ics_inventory_oms_lock`
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            Order By ${orderby}
        </if>
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        Insert Into `ics_inventory_oms_lock` (
            `id` ,
            `vstore_code` ,
            `vstore_name` ,
            `sku_no` ,
            `lock_qty` ,
            `status` ,
            `remark` ,
            `create_user` ,
            `create_time` ,
            `update_user` ,
            `update_time` 
        )
        Values (
            #{id} ,
            #{vstoreCode} ,
            #{vstoreName} ,
            #{skuNo} ,
            #{lockQty} ,
            #{status} ,
            #{remark} ,
            #{createUser} ,
            #{createTime} ,
            #{updateUser} ,
            #{updateTime} 
        )
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        Insert Into `ics_inventory_oms_lock` (
           `id`,
           `vstore_code`,
           `vstore_name`,
           `sku_no`,
           `lock_qty`,
           `status`,
           `remark`,
           `create_user`,
           `create_time`,
           `update_user`,
           `update_time`
        ) Values
        <foreach collection="list" item="entity" separator=",">
        (
            #{entity.id},
            #{entity.vstoreCode},
            #{entity.vstoreName},
            #{entity.skuNo},
            #{entity.lockQty},
            #{entity.status},
            #{entity.remark},
            #{entity.createUser},
            #{entity.createTime},
            #{entity.updateUser},
            #{entity.updateTime}
        )
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        Insert Into `ics_inventory_oms_lock` (
        `id` ,
        `vstore_code` ,
        `vstore_name` ,
        `sku_no` ,
        `lock_qty` ,
        `status` ,
        `remark` ,
        `create_user` ,
        `create_time` ,
        `update_user` ,
        `update_time` 
       )Values
        <foreach collection="list" item="entity" separator=",">
         (
         #{entity.id},
         #{entity.vstoreCode},
         #{entity.vstoreName},
         #{entity.skuNo},
         #{entity.lockQty},
         #{entity.status},
         #{entity.remark},
         #{entity.createUser},
         #{entity.createTime},
         #{entity.updateUser},
         #{entity.updateTime}
 )
        </foreach>
        On Duplicate Key Update
        `vstore_code` = values(vstore_code) ,
        `vstore_name` = values(vstore_name) ,
        `sku_no` = values(sku_no) ,
        `lock_qty` = values(lock_qty) ,
        `status` = values(status) ,
        `remark` = values(remark) ,
        `create_user` = values(create_user) ,
        `create_time` = values(create_time) ,
        `update_user` = values(update_user) ,
        `update_time` = values(update_time) 

    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        Update `ics_inventory_oms_lock`
        <set>
            <if test="vstoreCode != null and vstoreCode != ''">
                `vstore_code` = #{vstoreCode},
            </if>
            <if test="vstoreName != null and vstoreName != ''">
                `vstore_name` = #{vstoreName},
            </if>
            <if test="skuNo != null and skuNo != ''">
                `sku_no` = #{skuNo},
            </if>
            <if test="lockQty != null">
                `lock_qty` = #{lockQty},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="remark != null and remark != ''">
                `remark` = #{remark},
            </if>
            <if test="createUser != null and createUser != ''">
                `create_user` = #{createUser},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="updateUser != null and updateUser != ''">
                `update_user` = #{updateUser},
            </if>
            <if test="updateTime != null">
                `update_time` = #{updateTime},
            </if>
        </set>
        Where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteByPrimaryKey">
        Delete From `ics_inventory_oms_lock` Where id = #{id}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        Delete From `ics_inventory_oms_lock`
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                And In ( ${params.ids} )
            </if>
        </where>
    </delete>

    <!-- auto generate end -->

    <update id="updateStock" >
        insert into `ics_inventory_oms_lock`
        (`vstore_code`,`vstore_name`,`sku_no`,`lock_qty`)
        values
        <foreach collection="params" item="entity" separator=",">
            (
            #{entity.vstoreCode},
            #{entity.vstoreName},
            #{entity.skuNo},
            #{entity.lockQty}
            )
            ON DUPLICATE KEY UPDATE lock_qty=#{entity.lockQty};
        </foreach>
    </update>
</mapper>
