<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.RetailOrderReturnRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.RetailOrderReturn">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="is_payment" property="isPayment" jdbcType="TINYINT" />
        
        <result column="original_order_no" property="originalOrderNo" jdbcType="VARCHAR" />
        
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        
        <result column="auditor" property="auditor" jdbcType="VARCHAR" />
        
        <result column="quality_date" property="qualityDate" jdbcType="TIMESTAMP" />
        
        <result column="quality_info" property="qualityInfo" jdbcType="VARCHAR" />
        
        <result column="bug_type" property="bugType" jdbcType="CHAR" />
        
        <result column="untread_type" property="untreadType" jdbcType="CHAR" />
        
        <result column="rejectionType" property="rejectiontype" jdbcType="TINYINT" />
        
        <result column="channel_no" property="channelNo" jdbcType="CHAR" />
        
        <result column="package_no" property="packageNo" jdbcType="VARCHAR" />
        
        <result column="storage_no" property="storageNo" jdbcType="VARCHAR" />
        
        <result column="has_order" property="hasOrder" jdbcType="TINYINT" />
        
        <result column="record_code" property="recordCode" jdbcType="VARCHAR" />
        
        <result column="is_vip" property="isVip" jdbcType="TINYINT" />
        
        <result column="theater_schedule" property="theaterSchedule" jdbcType="VARCHAR" />
        
        <result column="box_code" property="boxCode" jdbcType="VARCHAR" />
        
        <result column="store_no" property="storeNo" jdbcType="VARCHAR" />
        
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="total_price" property="totalPrice" jdbcType="DECIMAL" />
        
        <result column="send_detail_total" property="sendDetailTotal" jdbcType="INTEGER" />
        
        <result column="parcel_weight" property="parcelWeight" jdbcType="DECIMAL" />
        
        <result column="express_price" property="expressPrice" jdbcType="DECIMAL" />
        
        <result column="express_code" property="expressCode" jdbcType="VARCHAR" />
        
        <result column="logistics_company_name" property="logisticsCompanyName" jdbcType="VARCHAR" />
        
        <result column="logistic_company_code" property="logisticCompanyCode" jdbcType="VARCHAR" />
        
        <result column="origin_platform_name" property="originPlatformName" jdbcType="VARCHAR" />
        
        <result column="origin_platform" property="originPlatform" jdbcType="VARCHAR" />
        
        <result column="customer_name" property="customerName" jdbcType="VARCHAR" />
        
        <result column="customer_no" property="customerNo" jdbcType="VARCHAR" />
        
        <result column="message" property="message" jdbcType="VARCHAR" />
        
        <result column="send_out_date" property="sendOutDate" jdbcType="TIMESTAMP" />
        
        <result column="send_store_type" property="sendStoreType" jdbcType="TINYINT" />
        
        <result column="send_company_no" property="sendCompanyNo" jdbcType="CHAR" />
        
        <result column="send_order_unit_name" property="sendOrderUnitName" jdbcType="VARCHAR" />
        
        <result column="send_order_unit_no" property="sendOrderUnitNo" jdbcType="CHAR" />
        
        <result column="send_store_name" property="sendStoreName" jdbcType="VARCHAR" />
        
        <result column="send_store_no" property="sendStoreNo" jdbcType="CHAR" />
        
        <result column="sale_company_no" property="saleCompanyNo" jdbcType="CHAR" />
        
        <result column="sale_order_unit_name" property="saleOrderUnitName" jdbcType="VARCHAR" />
        
        <result column="sale_order_unit_no" property="saleOrderUnitNo" jdbcType="CHAR" />
        
        <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
        
        <result column="shop_no" property="shopNo" jdbcType="CHAR" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="return_type" property="returnType" jdbcType="TINYINT" />
        
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
        <result column="sell_return_code" property="sellReturnCode" jdbcType="VARCHAR" />
        
        <result column="ref_bill_no" property="refBillNo" jdbcType="CHAR" />
        
        <result column="bill_no" property="billNo" jdbcType="CHAR" />
        
        <result column="interface_platform" property="interfacePlatform" jdbcType="CHAR" />
        
        <result column="retail_flag" property="retailFlag" jdbcType="CHAR" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="share_flag" property="shareFlag" jdbcType="TINYINT" />
        
    </resultMap>

    <sql id="column_list">
        `is_payment`,`original_order_no`,`audit_time`,`auditor`,`quality_date`,`quality_info`,`bug_type`,`untread_type`,`rejectionType`,`channel_no`,`package_no`,`storage_no`,`has_order`,`record_code`,`is_vip`,`theater_schedule`,`box_code`,`store_no`,`store_name`,`create_time`,`create_user`,`update_time`,`update_user`,`total_price`,`send_detail_total`,`parcel_weight`,`id`,`express_price`,`express_code`,`logistics_company_name`,`logistic_company_code`,`origin_platform_name`,`origin_platform`,`customer_name`,`customer_no`,`message`,`send_out_date`,`send_store_type`,`send_company_no`,`send_order_unit_name`,`send_order_unit_no`,`send_store_name`,`send_store_no`,`sale_company_no`,`sale_order_unit_name`,`sale_order_unit_no`,`shop_name`,`shop_no`,`status`,`return_type`,`business_type`,`remark`,`order_sub_no`,`sell_return_code`,`ref_bill_no`,`bill_no`,`interface_platform`,`retail_flag`,`sharding_flag`,`share_flag`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.isPayment ">
                
                AND `is_payment`=#{params.isPayment}
                
            </if>
            
            <if test="null!=params.originalOrderNo  and ''!=params.originalOrderNo ">
                
                AND `original_order_no`=#{params.originalOrderNo}
                
            </if>
            
            <if test="null!=params.auditTime ">
                
                AND `audit_time`=#{params.auditTime}
                
            </if>
            
            <if test="null!=params.auditor  and ''!=params.auditor ">
                
                AND `auditor`=#{params.auditor}
                
            </if>
            
            <if test="null!=params.qualityDate ">
                
                AND `quality_date`=#{params.qualityDate}
                
            </if>
            
            <if test="null!=params.qualityInfo  and ''!=params.qualityInfo ">
                
                AND `quality_info`=#{params.qualityInfo}
                
            </if>
            
            <if test="null!=params.bugType  and ''!=params.bugType ">
                
                AND `bug_type`=#{params.bugType}
                
            </if>
            
            <if test="null!=params.untreadType  and ''!=params.untreadType ">
                
                AND `untread_type`=#{params.untreadType}
                
            </if>
            
            <if test="null!=params.rejectiontype ">
                
                AND `rejectionType`=#{params.rejectiontype}
                
            </if>
            
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                
                AND `channel_no`=#{params.channelNo}
                
            </if>
            
            <if test="null!=params.packageNo  and ''!=params.packageNo ">
                
                AND `package_no`=#{params.packageNo}
                
            </if>
            
            <if test="null!=params.storageNo  and ''!=params.storageNo ">
                
                AND `storage_no`=#{params.storageNo}
                
            </if>
            
            <if test="null!=params.hasOrder ">
                
                AND `has_order`=#{params.hasOrder}
                
            </if>
            
            <if test="null!=params.recordCode  and ''!=params.recordCode ">
                
                AND `record_code`=#{params.recordCode}
                
            </if>
            
            <if test="null!=params.isVip ">
                
                AND `is_vip`=#{params.isVip}
                
            </if>
            
            <if test="null!=params.theaterSchedule  and ''!=params.theaterSchedule ">
                
                AND `theater_schedule`=#{params.theaterSchedule}
                
            </if>
            
            <if test="null!=params.boxCode  and ''!=params.boxCode ">
                
                AND `box_code`=#{params.boxCode}
                
            </if>
            
            <if test="null!=params.storeNo  and ''!=params.storeNo ">
                
                AND `store_no`=#{params.storeNo}
                
            </if>
            
            <if test="null!=params.storeName  and ''!=params.storeName ">
                
                AND `store_name` like CONCAT('%',#{params.storeName},'%') 
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.totalPrice ">
                
                AND `total_price`=#{params.totalPrice}
                
            </if>
            
            <if test="null!=params.sendDetailTotal ">
                
                AND `send_detail_total`=#{params.sendDetailTotal}
                
            </if>
            
            <if test="null!=params.parcelWeight ">
                
                AND `parcel_weight`=#{params.parcelWeight}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.expressPrice ">
                
                AND `express_price`=#{params.expressPrice}
                
            </if>
            
            <if test="null!=params.expressCode  and ''!=params.expressCode ">
                
                AND `express_code`=#{params.expressCode}
                
            </if>
            
            <if test="null!=params.logisticsCompanyName  and ''!=params.logisticsCompanyName ">
                
                AND `logistics_company_name` like CONCAT('%',#{params.logisticsCompanyName},'%') 
                
            </if>
            
            <if test="null!=params.logisticCompanyCode  and ''!=params.logisticCompanyCode ">
                
                AND `logistic_company_code`=#{params.logisticCompanyCode}
                
            </if>
            
            <if test="null!=params.originPlatformName  and ''!=params.originPlatformName ">
                
                AND `origin_platform_name` like CONCAT('%',#{params.originPlatformName},'%') 
                
            </if>
            
            <if test="null!=params.originPlatform  and ''!=params.originPlatform ">
                
                AND `origin_platform`=#{params.originPlatform}
                
            </if>
            
            <if test="null!=params.customerName  and ''!=params.customerName ">
                
                AND `customer_name` like CONCAT('%',#{params.customerName},'%') 
                
            </if>
            
            <if test="null!=params.customerNo  and ''!=params.customerNo ">
                
                AND `customer_no`=#{params.customerNo}
                
            </if>
            
            <if test="null!=params.message  and ''!=params.message ">
                
                AND `message`=#{params.message}
                
            </if>
            
            <if test="null!=params.sendOutDate ">
                
                AND `send_out_date`=#{params.sendOutDate}
                
            </if>
            
            <if test="null!=params.sendStoreType ">
                
                AND `send_store_type`=#{params.sendStoreType}
                
            </if>
            
            <if test="null!=params.sendCompanyNo  and ''!=params.sendCompanyNo ">
                
                AND `send_company_no`=#{params.sendCompanyNo}
                
            </if>
            
            <if test="null!=params.sendOrderUnitName  and ''!=params.sendOrderUnitName ">
                
                AND `send_order_unit_name` like CONCAT('%',#{params.sendOrderUnitName},'%') 
                
            </if>
            
            <if test="null!=params.sendOrderUnitNo  and ''!=params.sendOrderUnitNo ">
                
                AND `send_order_unit_no`=#{params.sendOrderUnitNo}
                
            </if>
            
            <if test="null!=params.sendStoreName  and ''!=params.sendStoreName ">
                
                AND `send_store_name` like CONCAT('%',#{params.sendStoreName},'%') 
                
            </if>
            
            <if test="null!=params.sendStoreNo  and ''!=params.sendStoreNo ">
                
                AND `send_store_no`=#{params.sendStoreNo}
                
            </if>
            
            <if test="null!=params.saleCompanyNo  and ''!=params.saleCompanyNo ">
                
                AND `sale_company_no`=#{params.saleCompanyNo}
                
            </if>
            
            <if test="null!=params.saleOrderUnitName  and ''!=params.saleOrderUnitName ">
                
                AND `sale_order_unit_name` like CONCAT('%',#{params.saleOrderUnitName},'%') 
                
            </if>
            
            <if test="null!=params.saleOrderUnitNo  and ''!=params.saleOrderUnitNo ">
                
                AND `sale_order_unit_no`=#{params.saleOrderUnitNo}
                
            </if>
            
            <if test="null!=params.shopName  and ''!=params.shopName ">
                
                AND `shop_name` like CONCAT('%',#{params.shopName},'%') 
                
            </if>
            
            <if test="null!=params.shopNo  and ''!=params.shopNo ">
                
                AND `shop_no`=#{params.shopNo}
                
            </if>
            
            <if test="null!=params.status ">
                
                AND `status`=#{params.status}
                
            </if>
            
            <if test="null!=params.returnType ">
                
                AND `return_type`=#{params.returnType}
                
            </if>
            
            <if test="null!=params.businessType  and ''!=params.businessType ">
                
                AND `business_type`=#{params.businessType}
                
            </if>
            
            <if test="null!=params.remark  and ''!=params.remark ">
                
                AND `remark`=#{params.remark}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
            <if test="null!=params.sellReturnCode  and ''!=params.sellReturnCode ">
                
                AND `sell_return_code`=#{params.sellReturnCode}
                
            </if>
            
            <if test="null!=params.refBillNo  and ''!=params.refBillNo ">
                
                AND `ref_bill_no`=#{params.refBillNo}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
            <if test="null!=params.interfacePlatform  and ''!=params.interfacePlatform ">
                
                AND `interface_platform`=#{params.interfacePlatform}
                
            </if>
            
            <if test="null!=params.retailFlag  and ''!=params.retailFlag ">
                
                AND `retail_flag`=#{params.retailFlag}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.shareFlag ">
                
                AND `share_flag`=#{params.shareFlag}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=billNo and ''!=billNo">
            AND `bill_no`=#{billNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_return
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_return
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_return
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM retail_order_return
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM retail_order_return
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_return
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM retail_order_return
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM retail_order_return
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM retail_order_return
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderReturn"  >
        INSERT INTO retail_order_return
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="isPayment != null">
                `is_payment`,
            </if>
            
            <if test="originalOrderNo != null">
                `original_order_no`,
            </if>
            
            <if test="auditTime != null">
                `audit_time`,
            </if>
            
            <if test="auditor != null">
                `auditor`,
            </if>
            
            <if test="qualityDate != null">
                `quality_date`,
            </if>
            
            <if test="qualityInfo != null">
                `quality_info`,
            </if>
            
            <if test="bugType != null">
                `bug_type`,
            </if>
            
            <if test="untreadType != null">
                `untread_type`,
            </if>
            
            <if test="rejectiontype != null">
                `rejectionType`,
            </if>
            
            <if test="channelNo != null">
                `channel_no`,
            </if>
            
            <if test="packageNo != null">
                `package_no`,
            </if>
            
            <if test="storageNo != null">
                `storage_no`,
            </if>
            
            <if test="hasOrder != null">
                `has_order`,
            </if>
            
            <if test="recordCode != null">
                `record_code`,
            </if>
            
            <if test="isVip != null">
                `is_vip`,
            </if>
            
            <if test="theaterSchedule != null">
                `theater_schedule`,
            </if>
            
            <if test="boxCode != null">
                `box_code`,
            </if>
            
            <if test="storeNo != null">
                `store_no`,
            </if>
            
            <if test="storeName != null">
                `store_name`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="totalPrice != null">
                `total_price`,
            </if>
            
            <if test="sendDetailTotal != null">
                `send_detail_total`,
            </if>
            
            <if test="parcelWeight != null">
                `parcel_weight`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="expressPrice != null">
                `express_price`,
            </if>
            
            <if test="expressCode != null">
                `express_code`,
            </if>
            
            <if test="logisticsCompanyName != null">
                `logistics_company_name`,
            </if>
            
            <if test="logisticCompanyCode != null">
                `logistic_company_code`,
            </if>
            
            <if test="originPlatformName != null">
                `origin_platform_name`,
            </if>
            
            <if test="originPlatform != null">
                `origin_platform`,
            </if>
            
            <if test="customerName != null">
                `customer_name`,
            </if>
            
            <if test="customerNo != null">
                `customer_no`,
            </if>
            
            <if test="message != null">
                `message`,
            </if>
            
            <if test="sendOutDate != null">
                `send_out_date`,
            </if>
            
            <if test="sendStoreType != null">
                `send_store_type`,
            </if>
            
            <if test="sendCompanyNo != null">
                `send_company_no`,
            </if>
            
            <if test="sendOrderUnitName != null">
                `send_order_unit_name`,
            </if>
            
            <if test="sendOrderUnitNo != null">
                `send_order_unit_no`,
            </if>
            
            <if test="sendStoreName != null">
                `send_store_name`,
            </if>
            
            <if test="sendStoreNo != null">
                `send_store_no`,
            </if>
            
            <if test="saleCompanyNo != null">
                `sale_company_no`,
            </if>
            
            <if test="saleOrderUnitName != null">
                `sale_order_unit_name`,
            </if>
            
            <if test="saleOrderUnitNo != null">
                `sale_order_unit_no`,
            </if>
            
            <if test="shopName != null">
                `shop_name`,
            </if>
            
            <if test="shopNo != null">
                `shop_no`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="returnType != null">
                `return_type`,
            </if>
            
            <if test="businessType != null">
                `business_type`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="sellReturnCode != null">
                `sell_return_code`,
            </if>
            
            <if test="refBillNo != null">
                `ref_bill_no`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="interfacePlatform != null">
                `interface_platform`,
            </if>
            
            <if test="retailFlag != null">
                `retail_flag`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="shareFlag != null">
                `share_flag`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="isPayment != null">
                #{isPayment},
            </if>
            
            <if test="originalOrderNo != null">
                #{originalOrderNo},
            </if>
            
            <if test="auditTime != null">
                #{auditTime},
            </if>
            
            <if test="auditor != null">
                #{auditor},
            </if>
            
            <if test="qualityDate != null">
                #{qualityDate},
            </if>
            
            <if test="qualityInfo != null">
                #{qualityInfo},
            </if>
            
            <if test="bugType != null">
                #{bugType},
            </if>
            
            <if test="untreadType != null">
                #{untreadType},
            </if>
            
            <if test="rejectiontype != null">
                #{rejectiontype},
            </if>
            
            <if test="channelNo != null">
                #{channelNo},
            </if>
            
            <if test="packageNo != null">
                #{packageNo},
            </if>
            
            <if test="storageNo != null">
                #{storageNo},
            </if>
            
            <if test="hasOrder != null">
                #{hasOrder},
            </if>
            
            <if test="recordCode != null">
                #{recordCode},
            </if>
            
            <if test="isVip != null">
                #{isVip},
            </if>
            
            <if test="theaterSchedule != null">
                #{theaterSchedule},
            </if>
            
            <if test="boxCode != null">
                #{boxCode},
            </if>
            
            <if test="storeNo != null">
                #{storeNo},
            </if>
            
            <if test="storeName != null">
                #{storeName},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="totalPrice != null">
                #{totalPrice},
            </if>
            
            <if test="sendDetailTotal != null">
                #{sendDetailTotal},
            </if>
            
            <if test="parcelWeight != null">
                #{parcelWeight},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="expressPrice != null">
                #{expressPrice},
            </if>
            
            <if test="expressCode != null">
                #{expressCode},
            </if>
            
            <if test="logisticsCompanyName != null">
                #{logisticsCompanyName},
            </if>
            
            <if test="logisticCompanyCode != null">
                #{logisticCompanyCode},
            </if>
            
            <if test="originPlatformName != null">
                #{originPlatformName},
            </if>
            
            <if test="originPlatform != null">
                #{originPlatform},
            </if>
            
            <if test="customerName != null">
                #{customerName},
            </if>
            
            <if test="customerNo != null">
                #{customerNo},
            </if>
            
            <if test="message != null">
                #{message},
            </if>
            
            <if test="sendOutDate != null">
                #{sendOutDate},
            </if>
            
            <if test="sendStoreType != null">
                #{sendStoreType},
            </if>
            
            <if test="sendCompanyNo != null">
                #{sendCompanyNo},
            </if>
            
            <if test="sendOrderUnitName != null">
                #{sendOrderUnitName},
            </if>
            
            <if test="sendOrderUnitNo != null">
                #{sendOrderUnitNo},
            </if>
            
            <if test="sendStoreName != null">
                #{sendStoreName},
            </if>
            
            <if test="sendStoreNo != null">
                #{sendStoreNo},
            </if>
            
            <if test="saleCompanyNo != null">
                #{saleCompanyNo},
            </if>
            
            <if test="saleOrderUnitName != null">
                #{saleOrderUnitName},
            </if>
            
            <if test="saleOrderUnitNo != null">
                #{saleOrderUnitNo},
            </if>
            
            <if test="shopName != null">
                #{shopName},
            </if>
            
            <if test="shopNo != null">
                #{shopNo},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="returnType != null">
                #{returnType},
            </if>
            
            <if test="businessType != null">
                #{businessType},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="sellReturnCode != null">
                #{sellReturnCode},
            </if>
            
            <if test="refBillNo != null">
                #{refBillNo},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="interfacePlatform != null">
                #{interfacePlatform},
            </if>
            
            <if test="retailFlag != null">
                #{retailFlag},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="shareFlag != null">
                #{shareFlag},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderReturn"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderReturn">
        UPDATE retail_order_return
        <set>
            
            <if test="isPayment != null">
                `is_payment` = #{isPayment},
            </if> 
            <if test="originalOrderNo != null">
                `original_order_no` = #{originalOrderNo},
            </if> 
            <if test="auditTime != null">
                `audit_time` = #{auditTime},
            </if> 
            <if test="auditor != null">
                `auditor` = #{auditor},
            </if> 
            <if test="qualityDate != null">
                `quality_date` = #{qualityDate},
            </if> 
            <if test="qualityInfo != null">
                `quality_info` = #{qualityInfo},
            </if> 
            <if test="bugType != null">
                `bug_type` = #{bugType},
            </if> 
            <if test="untreadType != null">
                `untread_type` = #{untreadType},
            </if> 
            <if test="rejectiontype != null">
                `rejectionType` = #{rejectiontype},
            </if> 
            <if test="channelNo != null">
                `channel_no` = #{channelNo},
            </if> 
            <if test="packageNo != null">
                `package_no` = #{packageNo},
            </if> 
            <if test="storageNo != null">
                `storage_no` = #{storageNo},
            </if> 
            <if test="hasOrder != null">
                `has_order` = #{hasOrder},
            </if> 
            <if test="recordCode != null">
                `record_code` = #{recordCode},
            </if> 
            <if test="isVip != null">
                `is_vip` = #{isVip},
            </if> 
            <if test="theaterSchedule != null">
                `theater_schedule` = #{theaterSchedule},
            </if> 
            <if test="boxCode != null">
                `box_code` = #{boxCode},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="totalPrice != null">
                `total_price` = #{totalPrice},
            </if> 
            <if test="sendDetailTotal != null">
                `send_detail_total` = #{sendDetailTotal},
            </if> 
            <if test="parcelWeight != null">
                `parcel_weight` = #{parcelWeight},
            </if> 
            <if test="expressPrice != null">
                `express_price` = #{expressPrice},
            </if> 
            <if test="expressCode != null">
                `express_code` = #{expressCode},
            </if> 
            <if test="logisticsCompanyName != null">
                `logistics_company_name` = #{logisticsCompanyName},
            </if> 
            <if test="logisticCompanyCode != null">
                `logistic_company_code` = #{logisticCompanyCode},
            </if> 
            <if test="originPlatformName != null">
                `origin_platform_name` = #{originPlatformName},
            </if> 
            <if test="originPlatform != null">
                `origin_platform` = #{originPlatform},
            </if> 
            <if test="customerName != null">
                `customer_name` = #{customerName},
            </if> 
            <if test="customerNo != null">
                `customer_no` = #{customerNo},
            </if> 
            <if test="message != null">
                `message` = #{message},
            </if> 
            <if test="sendOutDate != null">
                `send_out_date` = #{sendOutDate},
            </if> 
            <if test="sendStoreType != null">
                `send_store_type` = #{sendStoreType},
            </if> 
            <if test="sendCompanyNo != null">
                `send_company_no` = #{sendCompanyNo},
            </if> 
            <if test="sendOrderUnitName != null">
                `send_order_unit_name` = #{sendOrderUnitName},
            </if> 
            <if test="sendOrderUnitNo != null">
                `send_order_unit_no` = #{sendOrderUnitNo},
            </if> 
            <if test="sendStoreName != null">
                `send_store_name` = #{sendStoreName},
            </if> 
            <if test="sendStoreNo != null">
                `send_store_no` = #{sendStoreNo},
            </if> 
            <if test="saleCompanyNo != null">
                `sale_company_no` = #{saleCompanyNo},
            </if> 
            <if test="saleOrderUnitName != null">
                `sale_order_unit_name` = #{saleOrderUnitName},
            </if> 
            <if test="saleOrderUnitNo != null">
                `sale_order_unit_no` = #{saleOrderUnitNo},
            </if> 
            <if test="shopName != null">
                `shop_name` = #{shopName},
            </if> 
            <if test="shopNo != null">
                `shop_no` = #{shopNo},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="returnType != null">
                `return_type` = #{returnType},
            </if> 
            <if test="businessType != null">
                `business_type` = #{businessType},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="sellReturnCode != null">
                `sell_return_code` = #{sellReturnCode},
            </if> 
            <if test="refBillNo != null">
                `ref_bill_no` = #{refBillNo},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="interfacePlatform != null">
                `interface_platform` = #{interfacePlatform},
            </if> 
            <if test="retailFlag != null">
                `retail_flag` = #{retailFlag},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="shareFlag != null">
                `share_flag` = #{shareFlag},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id} OR bill_no = #{billNo}
    </update>
        <!-- auto generate end-->


</mapper>