<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.RetailOrderReturnDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.RetailOrderReturnDtl">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="sale_price" property="salePrice" jdbcType="DECIMAL" />
        
        <result column="item_name_forshow" property="itemNameForshow" jdbcType="VARCHAR" />
        
        <result column="in_storage_type" property="inStorageType" jdbcType="CHAR" />
        
        <result column="item_no" property="itemNo" jdbcType="CHAR" />
        
        <result column="barcode" property="barcode" jdbcType="VARCHAR" />
        
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
        
        <result column="item_name" property="itemName" jdbcType="VARCHAR" />
        
        <result column="schedule" property="schedule" jdbcType="TINYINT" />
        
        <result column="color_no" property="colorNo" jdbcType="CHAR" />
        
        <result column="color_name" property="colorName" jdbcType="VARCHAR" />
        
        <result column="brand_no" property="brandNo" jdbcType="CHAR" />
        
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        
        <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />
        
        <result column="size_kind" property="sizeKind" jdbcType="CHAR" />
        
        <result column="commodity_specification_str" property="commoditySpecificationStr" jdbcType="VARCHAR" />
        
        <result column="discount" property="discount" jdbcType="DECIMAL" />
        
        <result column="ask_qty" property="askQty" jdbcType="INTEGER" />
        
        <result column="send_out_qty" property="sendOutQty" jdbcType="INTEGER" />
        
        <result column="quote_price" property="quotePrice" jdbcType="DECIMAL" />
        
        <result column="category_no" property="categoryNo" jdbcType="CHAR" />
        
        <result column="bill_no" property="billNo" jdbcType="CHAR" />
        
        <result column="sku_no" property="skuNo" jdbcType="CHAR" />
        
        <result column="out_order_id" property="outOrderId" jdbcType="VARCHAR" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="retail_flag" property="retailFlag" jdbcType="CHAR" />
        
        <result column="question_reason" property="questionReason" jdbcType="VARCHAR" />
        
        <result column="question_type" property="questionType" jdbcType="VARCHAR" />
        
        <result column="delevir_size_no" property="delevirSizeNo" jdbcType="VARCHAR" />
        
        <result column="delevir_brand_name" property="delevirBrandName" jdbcType="VARCHAR" />
        
        <result column="delevir_brand_no" property="delevirBrandNo" jdbcType="CHAR" />
        
        <result column="delevir_item_code" property="delevirItemCode" jdbcType="VARCHAR" />
        
        <result column="delevir_barcode" property="delevirBarcode" jdbcType="VARCHAR" />
        
        <result column="delevir_item_no" property="delevirItemNo" jdbcType="CHAR" />
        
        <result column="delevir_sku_no" property="delevirSkuNo" jdbcType="CHAR" />
        
        <result column="po_no" property="poNo" jdbcType="VARCHAR" />
        
        <result column="box_no" property="boxNo" jdbcType="VARCHAR" />
        
        <result column="image_remark" property="imageRemark" jdbcType="VARCHAR" />
        
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR" />
        
        <result column="pos_dtl_id" property="posDtlId" jdbcType="CHAR" />
        
        <result column="commodity_type" property="commodityType" jdbcType="TINYINT" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
    </resultMap>

    <sql id="column_list">
        `sale_price`,`item_name_forshow`,`in_storage_type`,`item_no`,`barcode`,`item_code`,`item_name`,`schedule`,`color_no`,`color_name`,`brand_no`,`brand_name`,`id`,`size_no`,`size_kind`,`commodity_specification_str`,`discount`,`ask_qty`,`send_out_qty`,`quote_price`,`category_no`,`bill_no`,`sku_no`,`out_order_id`,`sharding_flag`,`retail_flag`,`question_reason`,`question_type`,`delevir_size_no`,`delevir_brand_name`,`delevir_brand_no`,`delevir_item_code`,`delevir_barcode`,`delevir_item_no`,`delevir_sku_no`,`po_no`,`box_no`,`image_remark`,`image_url`,`pos_dtl_id`,`commodity_type`,`update_time`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.salePrice ">
                
                AND `sale_price`=#{params.salePrice}
                
            </if>
            
            <if test="null!=params.itemNameForshow  and ''!=params.itemNameForshow ">
                
                AND `item_name_forshow` like CONCAT('%',#{params.itemNameForshow},'%') 
                
            </if>
            
            <if test="null!=params.inStorageType  and ''!=params.inStorageType ">
                
                AND `in_storage_type`=#{params.inStorageType}
                
            </if>
            
            <if test="null!=params.itemNo  and ''!=params.itemNo ">
                
                AND `item_no`=#{params.itemNo}
                
            </if>
            
            <if test="null!=params.barcode  and ''!=params.barcode ">
                
                AND `barcode`=#{params.barcode}
                
            </if>
            
            <if test="null!=params.itemCode  and ''!=params.itemCode ">
                
                AND `item_code`=#{params.itemCode}
                
            </if>
            
            <if test="null!=params.itemName  and ''!=params.itemName ">
                
                AND `item_name` like CONCAT('%',#{params.itemName},'%') 
                
            </if>
            
            <if test="null!=params.schedule ">
                
                AND `schedule`=#{params.schedule}
                
            </if>
            
            <if test="null!=params.colorNo  and ''!=params.colorNo ">
                
                AND `color_no`=#{params.colorNo}
                
            </if>
            
            <if test="null!=params.colorName  and ''!=params.colorName ">
                
                AND `color_name` like CONCAT('%',#{params.colorName},'%') 
                
            </if>
            
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                
                AND `brand_no`=#{params.brandNo}
                
            </if>
            
            <if test="null!=params.brandName  and ''!=params.brandName ">
                
                AND `brand_name` like CONCAT('%',#{params.brandName},'%') 
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.sizeNo  and ''!=params.sizeNo ">
                
                AND `size_no`=#{params.sizeNo}
                
            </if>
            
            <if test="null!=params.sizeKind  and ''!=params.sizeKind ">
                
                AND `size_kind`=#{params.sizeKind}
                
            </if>
            
            <if test="null!=params.commoditySpecificationStr  and ''!=params.commoditySpecificationStr ">
                
                AND `commodity_specification_str`=#{params.commoditySpecificationStr}
                
            </if>
            
            <if test="null!=params.discount ">
                
                AND `discount`=#{params.discount}
                
            </if>
            
            <if test="null!=params.askQty ">
                
                AND `ask_qty`=#{params.askQty}
                
            </if>
            
            <if test="null!=params.sendOutQty ">
                
                AND `send_out_qty`=#{params.sendOutQty}
                
            </if>
            
            <if test="null!=params.quotePrice ">
                
                AND `quote_price`=#{params.quotePrice}
                
            </if>
            
            <if test="null!=params.categoryNo  and ''!=params.categoryNo ">
                
                AND `category_no`=#{params.categoryNo}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
            <if test="null!=params.skuNo  and ''!=params.skuNo ">
                
                AND `sku_no`=#{params.skuNo}
                
            </if>
            
            <if test="null!=params.outOrderId  and ''!=params.outOrderId ">
                
                AND `out_order_id`=#{params.outOrderId}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.retailFlag  and ''!=params.retailFlag ">
                
                AND `retail_flag`=#{params.retailFlag}
                
            </if>
            
            <if test="null!=params.questionReason  and ''!=params.questionReason ">
                
                AND `question_reason`=#{params.questionReason}
                
            </if>
            
            <if test="null!=params.questionType  and ''!=params.questionType ">
                
                AND `question_type`=#{params.questionType}
                
            </if>
            
            <if test="null!=params.delevirSizeNo  and ''!=params.delevirSizeNo ">
                
                AND `delevir_size_no`=#{params.delevirSizeNo}
                
            </if>
            
            <if test="null!=params.delevirBrandName  and ''!=params.delevirBrandName ">
                
                AND `delevir_brand_name` like CONCAT('%',#{params.delevirBrandName},'%') 
                
            </if>
            
            <if test="null!=params.delevirBrandNo  and ''!=params.delevirBrandNo ">
                
                AND `delevir_brand_no`=#{params.delevirBrandNo}
                
            </if>
            
            <if test="null!=params.delevirItemCode  and ''!=params.delevirItemCode ">
                
                AND `delevir_item_code`=#{params.delevirItemCode}
                
            </if>
            
            <if test="null!=params.delevirBarcode  and ''!=params.delevirBarcode ">
                
                AND `delevir_barcode`=#{params.delevirBarcode}
                
            </if>
            
            <if test="null!=params.delevirItemNo  and ''!=params.delevirItemNo ">
                
                AND `delevir_item_no`=#{params.delevirItemNo}
                
            </if>
            
            <if test="null!=params.delevirSkuNo  and ''!=params.delevirSkuNo ">
                
                AND `delevir_sku_no`=#{params.delevirSkuNo}
                
            </if>
            
            <if test="null!=params.poNo  and ''!=params.poNo ">
                
                AND `po_no`=#{params.poNo}
                
            </if>
            
            <if test="null!=params.boxNo  and ''!=params.boxNo ">
                
                AND `box_no`=#{params.boxNo}
                
            </if>
            
            <if test="null!=params.imageRemark  and ''!=params.imageRemark ">
                
                AND `image_remark`=#{params.imageRemark}
                
            </if>
            
            <if test="null!=params.imageUrl  and ''!=params.imageUrl ">
                
                AND `image_url`=#{params.imageUrl}
                
            </if>
            
            <if test="null!=params.posDtlId  and ''!=params.posDtlId ">
                
                AND `pos_dtl_id`=#{params.posDtlId}
                
            </if>
            
            <if test="null!=params.commodityType ">
                
                AND `commodity_type`=#{params.commodityType}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_return_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_return_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM retail_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM retail_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM retail_order_return_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM retail_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM retail_order_return_dtl
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderReturnDtl"  >
        INSERT INTO retail_order_return_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="salePrice != null">
                `sale_price`,
            </if>
            
            <if test="itemNameForshow != null">
                `item_name_forshow`,
            </if>
            
            <if test="inStorageType != null">
                `in_storage_type`,
            </if>
            
            <if test="itemNo != null">
                `item_no`,
            </if>
            
            <if test="barcode != null">
                `barcode`,
            </if>
            
            <if test="itemCode != null">
                `item_code`,
            </if>
            
            <if test="itemName != null">
                `item_name`,
            </if>
            
            <if test="schedule != null">
                `schedule`,
            </if>
            
            <if test="colorNo != null">
                `color_no`,
            </if>
            
            <if test="colorName != null">
                `color_name`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="brandName != null">
                `brand_name`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="sizeNo != null">
                `size_no`,
            </if>
            
            <if test="sizeKind != null">
                `size_kind`,
            </if>
            
            <if test="commoditySpecificationStr != null">
                `commodity_specification_str`,
            </if>
            
            <if test="discount != null">
                `discount`,
            </if>
            
            <if test="askQty != null">
                `ask_qty`,
            </if>
            
            <if test="sendOutQty != null">
                `send_out_qty`,
            </if>
            
            <if test="quotePrice != null">
                `quote_price`,
            </if>
            
            <if test="categoryNo != null">
                `category_no`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="skuNo != null">
                `sku_no`,
            </if>
            
            <if test="outOrderId != null">
                `out_order_id`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="retailFlag != null">
                `retail_flag`,
            </if>
            
            <if test="questionReason != null">
                `question_reason`,
            </if>
            
            <if test="questionType != null">
                `question_type`,
            </if>
            
            <if test="delevirSizeNo != null">
                `delevir_size_no`,
            </if>
            
            <if test="delevirBrandName != null">
                `delevir_brand_name`,
            </if>
            
            <if test="delevirBrandNo != null">
                `delevir_brand_no`,
            </if>
            
            <if test="delevirItemCode != null">
                `delevir_item_code`,
            </if>
            
            <if test="delevirBarcode != null">
                `delevir_barcode`,
            </if>
            
            <if test="delevirItemNo != null">
                `delevir_item_no`,
            </if>
            
            <if test="delevirSkuNo != null">
                `delevir_sku_no`,
            </if>
            
            <if test="poNo != null">
                `po_no`,
            </if>
            
            <if test="boxNo != null">
                `box_no`,
            </if>
            
            <if test="imageRemark != null">
                `image_remark`,
            </if>
            
            <if test="imageUrl != null">
                `image_url`,
            </if>
            
            <if test="posDtlId != null">
                `pos_dtl_id`,
            </if>
            
            <if test="commodityType != null">
                `commodity_type`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="salePrice != null">
                #{salePrice},
            </if>
            
            <if test="itemNameForshow != null">
                #{itemNameForshow},
            </if>
            
            <if test="inStorageType != null">
                #{inStorageType},
            </if>
            
            <if test="itemNo != null">
                #{itemNo},
            </if>
            
            <if test="barcode != null">
                #{barcode},
            </if>
            
            <if test="itemCode != null">
                #{itemCode},
            </if>
            
            <if test="itemName != null">
                #{itemName},
            </if>
            
            <if test="schedule != null">
                #{schedule},
            </if>
            
            <if test="colorNo != null">
                #{colorNo},
            </if>
            
            <if test="colorName != null">
                #{colorName},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="brandName != null">
                #{brandName},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="sizeNo != null">
                #{sizeNo},
            </if>
            
            <if test="sizeKind != null">
                #{sizeKind},
            </if>
            
            <if test="commoditySpecificationStr != null">
                #{commoditySpecificationStr},
            </if>
            
            <if test="discount != null">
                #{discount},
            </if>
            
            <if test="askQty != null">
                #{askQty},
            </if>
            
            <if test="sendOutQty != null">
                #{sendOutQty},
            </if>
            
            <if test="quotePrice != null">
                #{quotePrice},
            </if>
            
            <if test="categoryNo != null">
                #{categoryNo},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="skuNo != null">
                #{skuNo},
            </if>
            
            <if test="outOrderId != null">
                #{outOrderId},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="retailFlag != null">
                #{retailFlag},
            </if>
            
            <if test="questionReason != null">
                #{questionReason},
            </if>
            
            <if test="questionType != null">
                #{questionType},
            </if>
            
            <if test="delevirSizeNo != null">
                #{delevirSizeNo},
            </if>
            
            <if test="delevirBrandName != null">
                #{delevirBrandName},
            </if>
            
            <if test="delevirBrandNo != null">
                #{delevirBrandNo},
            </if>
            
            <if test="delevirItemCode != null">
                #{delevirItemCode},
            </if>
            
            <if test="delevirBarcode != null">
                #{delevirBarcode},
            </if>
            
            <if test="delevirItemNo != null">
                #{delevirItemNo},
            </if>
            
            <if test="delevirSkuNo != null">
                #{delevirSkuNo},
            </if>
            
            <if test="poNo != null">
                #{poNo},
            </if>
            
            <if test="boxNo != null">
                #{boxNo},
            </if>
            
            <if test="imageRemark != null">
                #{imageRemark},
            </if>
            
            <if test="imageUrl != null">
                #{imageUrl},
            </if>
            
            <if test="posDtlId != null">
                #{posDtlId},
            </if>
            
            <if test="commodityType != null">
                #{commodityType},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderReturnDtl">
        UPDATE retail_order_return_dtl
        <set>
            
            <if test="salePrice != null">
                `sale_price` = #{salePrice},
            </if> 
            <if test="itemNameForshow != null">
                `item_name_forshow` = #{itemNameForshow},
            </if> 
            <if test="inStorageType != null">
                `in_storage_type` = #{inStorageType},
            </if> 
            <if test="itemNo != null">
                `item_no` = #{itemNo},
            </if> 
            <if test="barcode != null">
                `barcode` = #{barcode},
            </if> 
            <if test="itemCode != null">
                `item_code` = #{itemCode},
            </if> 
            <if test="itemName != null">
                `item_name` = #{itemName},
            </if> 
            <if test="schedule != null">
                `schedule` = #{schedule},
            </if> 
            <if test="colorNo != null">
                `color_no` = #{colorNo},
            </if> 
            <if test="colorName != null">
                `color_name` = #{colorName},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="brandName != null">
                `brand_name` = #{brandName},
            </if> 
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},
            </if> 
            <if test="sizeKind != null">
                `size_kind` = #{sizeKind},
            </if> 
            <if test="commoditySpecificationStr != null">
                `commodity_specification_str` = #{commoditySpecificationStr},
            </if> 
            <if test="discount != null">
                `discount` = #{discount},
            </if> 
            <if test="askQty != null">
                `ask_qty` = #{askQty},
            </if> 
            <if test="sendOutQty != null">
                `send_out_qty` = #{sendOutQty},
            </if> 
            <if test="quotePrice != null">
                `quote_price` = #{quotePrice},
            </if> 
            <if test="categoryNo != null">
                `category_no` = #{categoryNo},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="skuNo != null">
                `sku_no` = #{skuNo},
            </if> 
            <if test="outOrderId != null">
                `out_order_id` = #{outOrderId},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="retailFlag != null">
                `retail_flag` = #{retailFlag},
            </if> 
            <if test="questionReason != null">
                `question_reason` = #{questionReason},
            </if> 
            <if test="questionType != null">
                `question_type` = #{questionType},
            </if> 
            <if test="delevirSizeNo != null">
                `delevir_size_no` = #{delevirSizeNo},
            </if> 
            <if test="delevirBrandName != null">
                `delevir_brand_name` = #{delevirBrandName},
            </if> 
            <if test="delevirBrandNo != null">
                `delevir_brand_no` = #{delevirBrandNo},
            </if> 
            <if test="delevirItemCode != null">
                `delevir_item_code` = #{delevirItemCode},
            </if> 
            <if test="delevirBarcode != null">
                `delevir_barcode` = #{delevirBarcode},
            </if> 
            <if test="delevirItemNo != null">
                `delevir_item_no` = #{delevirItemNo},
            </if> 
            <if test="delevirSkuNo != null">
                `delevir_sku_no` = #{delevirSkuNo},
            </if> 
            <if test="poNo != null">
                `po_no` = #{poNo},
            </if> 
            <if test="boxNo != null">
                `box_no` = #{boxNo},
            </if> 
            <if test="imageRemark != null">
                `image_remark` = #{imageRemark},
            </if> 
            <if test="imageUrl != null">
                `image_url` = #{imageUrl},
            </if> 
            <if test="posDtlId != null">
                `pos_dtl_id` = #{posDtlId},
            </if> 
            <if test="commodityType != null">
                `commodity_type` = #{commodityType},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>