<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetTakeShopItemRepository">
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetTakeShopItem">
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="shop_no" property="shopNo" jdbcType="VARCHAR" />
        <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
        <result column="channel_No" property="channelNo" jdbcType="VARCHAR" />
        <result column="channel_name" property="channelName" jdbcType="VARCHAR" />
        <result column="item_no" property="itemNo" jdbcType="VARCHAR" />
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
        <result column="brand_no" property="brandNo" jdbcType="VARCHAR" />
        <result column="take_sign" property="takeSign" jdbcType="TINYINT" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        <result column="stock_sync_time" property="stockSyncTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="column_list">
        `id`,`shop_no`,`shop_name`,`channel_No`,`channel_name`,`item_no`,`item_code`,`brand_no`,`take_sign`,`update_time`,`create_time`,`create_user`,`update_user`,`stock_sync_time`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.id and ''!=params.id">
                AND `id`=#{params.id}
            </if>
            <if test="params.queryParam != null and params.queryParam != ''">
                AND (shop_no like CONCAT('%',#{params.queryParam},'%') or shop_name like CONCAT('%',#{params.queryParam},'%'))
            </if>
            <if test="null!=params.shopNo and ''!=params.shopNo">
                AND `shop_no`=#{params.shopNo}
            </if>
            <if test="null!=params.shopName and ''!=params.shopName">
                AND `shop_name` like CONCAT('%',#{params.shopName},'%')
            </if>
            <if test="null!=params.channelNo and ''!=params.channelNo">
                AND `channel_No`=#{params.channelNo}
            </if>
            <if test="null!=params.channelName and ''!=params.channelName">
                AND `channel_name` like CONCAT('%',#{params.channelName},'%')
            </if>
            <if test="null!=params.itemNo and ''!=params.itemNo">
                AND `item_no`=#{params.itemNo}
            </if>
            <if test="null!=params.itemCode and ''!=params.itemCode">
                AND `item_code`=#{params.itemCode}
            </if>
            <if test="null!=params.brandNo and ''!=params.brandNo">
                AND `brand_no`=#{params.brandNo}
            </if>
            <if test="null!=params.takeSign">
                AND `take_sign`=#{params.takeSign}
            </if>
            <if test="params.createTimeStart!=null and ''!=params.createTimeStart">
                AND `create_time` &gt;= #{params.createTimeStart}
            </if>
            <if test="params.createTimeEnd!=null and ''!=params.createTimeEnd">
                AND `create_time` &lt;= #{params.createTimeEnd}
            </if>
            <if test="params.updateTimeStart!=null and ''!=params.updateTimeStart">
                AND `update_time` &gt;= #{params.updateTimeStart}
            </if>
            <if test="params.updateTimeEnd!=null and ''!=params.updateTimeEnd">
                AND `update_time` &lt;= #{params.updateTimeEnd}
            </if>
        </if>
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list" />
        FROM internet_take_shop_item
        WHERE id = #{id}
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_take_shop_item
        <where>
            <include refid="condition" />
        </where>
        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_take_shop_item
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_take_shop_item
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_take_shop_item
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_take_shop_item
        WHERE id = #{id}
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_take_shop_item
        <where>
            <include refid="condition" />
            <if test="params.id!=null and ''!=params.id">
                AND id in ( ${params.id} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetTakeShopItem" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO internet_take_shop_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>
            <if test="shopNo != null">
                `shop_no`,
            </if>
            <if test="shopName != null">
                `shop_name`,
            </if>
            <if test="channelNo != null">
                `channel_No`,
            </if>
            <if test="channelName != null">
                `channel_name`,
            </if>
            <if test="itemNo != null">
                `item_no`,
            </if>
            <if test="itemCode != null">
                `item_code`,
            </if>
            <if test="brandNo != null">
                `brand_no`,
            </if>
            <if test="takeSign != null">
                `take_sign`,
            </if>
            <if test="createTime != null">
                `create_time`,
            </if>
            <if test="updateTime != null">
                `update_time`,
            </if>
            <if test="createUser != null">
                `create_user`,
            </if>
            <if test="updateUser != null">
                `update_user`,
            </if>
            <if test="stockSyncTime != null">
                `stock_sync_time`,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="shopNo != null">
                #{shopNo},
            </if>
            <if test="shopName != null">
                #{shopName},
            </if>
            <if test="channelNo != null">
                #{channelNo},
            </if>
            <if test="channelName != null">
                #{channelName},
            </if>
            <if test="itemNo != null">
                #{itemNo},
            </if>
            <if test="itemCode != null">
                #{itemCode},
            </if>
            <if test="brandNo != null">
                #{brandNo},
            </if>
            <if test="takeSign != null">
                #{takeSign},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createUser != null">
                #{createUser},
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="stockSyncTime != null">
                `stockSyncTime`,
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO internet_take_shop_item (<include refid="column_list"/>)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.shopNo}, #{item.shopName}, #{item.channelNo}, #{item.channelName}, #{item.itemNo}, #{item.itemCode}, #{item.brandNo}, #{item.takeSign}, #{item.updateTime}, #{item.createTime}, #{item.createUser}, #{item.updateUser})
        </foreach>
    </insert>

    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetTakeShopItem">
        UPDATE internet_take_shop_item
        <set>
            <if test="shopNo != null">
                `shop_no` = #{shopNo},
            </if>
            <if test="shopName != null">
                `shop_name` = #{shopName},
            </if>
            <if test="channelNo != null">
                `channel_No` = #{channelNo},
            </if>
            <if test="channelName != null">
                `channel_name` = #{channelName},
            </if>
            <if test="itemNo != null">
                `item_no` = #{itemNo},
            </if>
            <if test="itemCode != null">
                `item_code` = #{itemCode},
            </if>
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if>
            <if test="takeSign != null">
                `take_sign` = #{takeSign},
            </if>
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if>
            <if test="updateTime != null">
                `update_time` = #{updateTime},
            </if>
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if>
            <if test="updateUser != null">
                `stock_sync_time` = #{stockSyncTime},
            </if>
        </set>
        WHERE id = #{id}
    </update>
</mapper>