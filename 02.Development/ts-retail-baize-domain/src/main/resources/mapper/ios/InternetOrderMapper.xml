<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetOrderRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetOrder">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="send_store" property="sendStore" jdbcType="VARCHAR" />
        
        <result column="actual_postage" property="actualPostage" jdbcType="DECIMAL" />
        
        <result column="original_order_no" property="originalOrderNo" jdbcType="VARCHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
        <result column="origin_platform" property="originPlatform" jdbcType="VARCHAR" />
        
        <result column="origin_platform_name" property="originPlatformName" jdbcType="VARCHAR" />
        
        <result column="guide_order_flag" property="guideOrderFlag" jdbcType="TINYINT" />
        
        <result column="system_source" property="systemSource" jdbcType="TINYINT" />
        
        <result column="close_code" property="closeCode" jdbcType="VARCHAR" />
        
        <result column="order_timestamp" property="orderTimestamp" jdbcType="VARCHAR" />
        
        <result column="original_out_id" property="originalOutId" jdbcType="VARCHAR" />
        
        <result column="defective_shop" property="defectiveShop" jdbcType="VARCHAR" />
        
        <result column="custom_sign" property="customSign" jdbcType="TINYINT" />
        
        <result column="po_no" property="poNo" jdbcType="VARCHAR" />
        
        <result column="balance_due_date" property="balanceDueDate" jdbcType="TIMESTAMP" />
        
        <result column="verification_status" property="verificationStatus" jdbcType="TINYINT" />
        
        <result column="validate_batch" property="validateBatch" jdbcType="CHAR" />
        
        <result column="app_secret" property="appSecret" jdbcType="VARCHAR" />
        
        <result column="app_key" property="appKey" jdbcType="VARCHAR" />
        
        <result column="order_source_no" property="orderSourceNo" jdbcType="VARCHAR" />
        
        <result column="interface_platform" property="interfacePlatform" jdbcType="CHAR" />
        
        <result column="out_order_status_name" property="outOrderStatusName" jdbcType="CHAR" />
        
        <result column="out_order_status" property="outOrderStatus" jdbcType="TINYINT" />
        
        <result column="order_status_name" property="orderStatusName" jdbcType="CHAR" />
        
        <result column="order_status" property="orderStatus" jdbcType="TINYINT" />
        
        <result column="business_type" property="businessType" jdbcType="TINYINT" />
        
        <result column="source_type" property="sourceType" jdbcType="TINYINT" />
        
        <result column="order_style" property="orderStyle" jdbcType="TINYINT" />
        
        <result column="is_vip" property="isVip" jdbcType="TINYINT" />
        
        <result column="is_converted" property="isConverted" jdbcType="TINYINT" />
        
        <result column="order_main_no" property="orderMainNo" jdbcType="CHAR" />
        
        <result column="sale_shop_no" property="saleShopNo" jdbcType="VARCHAR" />
        
        <result column="company_name" property="companyName" jdbcType="VARCHAR" />
        
        <result column="company_code" property="companyCode" jdbcType="VARCHAR" />
        
        <result column="retail_province" property="retailProvince" jdbcType="VARCHAR" />
        
        <result column="retail_province_name" property="retailProvinceName" jdbcType="VARCHAR" />
        
        <result column="retail_city" property="retailCity" jdbcType="VARCHAR" />
        
        <result column="retail_city_name" property="retailCityName" jdbcType="VARCHAR" />
        
        <result column="retail_area" property="retailArea" jdbcType="VARCHAR" />
        
        <result column="retail_area_name" property="retailAreaName" jdbcType="VARCHAR" />
        
        <result column="stock_type" property="stockType" jdbcType="TINYINT" />
        
        <result column="undeliverable" property="undeliverable" jdbcType="TINYINT" />
        
        <result column="oa_id" property="oaId" jdbcType="CHAR" />
        
        <result column="product_total_quantity" property="productTotalQuantity" jdbcType="INTEGER" />
        
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        
        <result column="order_pay_total_amont" property="orderPayTotalAmont" jdbcType="DECIMAL" />
        
        <result column="ship_time" property="shipTime" jdbcType="TIMESTAMP" />
        
        <result column="online_pay_time" property="onlinePayTime" jdbcType="TIMESTAMP" />
        
        <result column="arrive_time" property="arriveTime" jdbcType="TIMESTAMP" />
        
        <result column="sync_time_stamp" property="syncTimeStamp" jdbcType="BIGINT" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="retail_flag" property="retailFlag" jdbcType="CHAR" />
        
        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR" />
        
        <result column="vstore_type" property="vstoreType" jdbcType="TINYINT" />
        
        <result column="merchant_code" property="merchantCode" jdbcType="VARCHAR" />
        
        <result column="is_match" property="isMatch" jdbcType="TINYINT" />
        
        <result column="out_order_id" property="outOrderId" jdbcType="VARCHAR" />
        
        <result column="is_guide_order" property="isGuideOrder" jdbcType="TINYINT" />
        
        <result column="consignee_address_latitude" property="consigneeAddressLatitude" jdbcType="VARCHAR" />
        
        <result column="consignee_address_longitude" property="consigneeAddressLongitude" jdbcType="VARCHAR" />
        
        <result column="order_modify_time" property="orderModifyTime" jdbcType="TIMESTAMP" />
        
        <result column="order_create_time" property="orderCreateTime" jdbcType="TIMESTAMP" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="organ_type_no" property="organTypeNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `send_store`,`actual_postage`,`id`,`original_order_no`,`order_sub_no`,`origin_platform`,`origin_platform_name`,`guide_order_flag`,`system_source`,`close_code`,`order_timestamp`,`original_out_id`,`defective_shop`,`custom_sign`,`po_no`,`balance_due_date`,`verification_status`,`validate_batch`,`app_secret`,`app_key`,`order_source_no`,`interface_platform`,`out_order_status_name`,`out_order_status`,`order_status_name`,`order_status`,`business_type`,`source_type`,`order_style`,`is_vip`,`is_converted`,`order_main_no`,`sale_shop_no`,`company_name`,`company_code`,`retail_province`,`retail_province_name`,`retail_city`,`retail_city_name`,`retail_area`,`retail_area_name`,`stock_type`,`undeliverable`,`oa_id`,`product_total_quantity`,`order_amount`,`order_pay_total_amont`,`ship_time`,`online_pay_time`,`arrive_time`,`sync_time_stamp`,`sharding_flag`,`retail_flag`,`vstore_code`,`vstore_type`,`merchant_code`,`is_match`,`out_order_id`,`is_guide_order`,`consignee_address_latitude`,`consignee_address_longitude`,`order_modify_time`,`order_create_time`,`update_user`,`update_time`,`create_user`,`create_time`,`organ_type_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.sendStore  and ''!=params.sendStore ">
                
                AND `send_store`=#{params.sendStore}
                
            </if>
            
            <if test="null!=params.actualPostage ">
                
                AND `actual_postage`=#{params.actualPostage}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.originalOrderNo  and ''!=params.originalOrderNo ">
                
                AND `original_order_no`=#{params.originalOrderNo}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>

            <if test="null!=params.prefixOrderSubNo  and ''!=params.prefixOrderSubNo ">

                AND `order_sub_no` like CONCAT(#{params.prefixOrderSubNo},'%')

            </if>
            
            <if test="null!=params.originPlatform  and ''!=params.originPlatform ">
                
                AND `origin_platform`=#{params.originPlatform}
                
            </if>
            
            <if test="null!=params.originPlatformName  and ''!=params.originPlatformName ">
                
                AND `origin_platform_name` like CONCAT('%',#{params.originPlatformName},'%') 
                
            </if>
            
            <if test="null!=params.guideOrderFlag ">
                
                AND `guide_order_flag`=#{params.guideOrderFlag}
                
            </if>
            
            <if test="null!=params.systemSource ">
                
                AND `system_source`=#{params.systemSource}
                
            </if>
            
            <if test="null!=params.closeCode  and ''!=params.closeCode ">
                
                AND `close_code`=#{params.closeCode}
                
            </if>
            
            <if test="null!=params.orderTimestamp  and ''!=params.orderTimestamp ">
                
                AND `order_timestamp`=#{params.orderTimestamp}
                
            </if>
            
            <if test="null!=params.originalOutId  and ''!=params.originalOutId ">
                
                AND `original_out_id`=#{params.originalOutId}
                
            </if>
            
            <if test="null!=params.defectiveShop  and ''!=params.defectiveShop ">
                
                AND `defective_shop`=#{params.defectiveShop}
                
            </if>
            
            <if test="null!=params.customSign ">
                
                AND `custom_sign`=#{params.customSign}
                
            </if>
            
            <if test="null!=params.poNo  and ''!=params.poNo ">
                
                AND `po_no`=#{params.poNo}
                
            </if>
            
            <if test="null!=params.balanceDueDate ">
                
                AND `balance_due_date`=#{params.balanceDueDate}
                
            </if>
            
            <if test="null!=params.verificationStatus ">
                
                AND `verification_status`=#{params.verificationStatus}
                
            </if>
            
            <if test="null!=params.validateBatch  and ''!=params.validateBatch ">
                
                AND `validate_batch`=#{params.validateBatch}
                
            </if>
            
            <if test="null!=params.appSecret  and ''!=params.appSecret ">
                
                AND `app_secret`=#{params.appSecret}
                
            </if>
            
            <if test="null!=params.appKey  and ''!=params.appKey ">
                
                AND `app_key`=#{params.appKey}
                
            </if>
            
            <if test="null!=params.orderSourceNo  and ''!=params.orderSourceNo ">
                
                AND `order_source_no`=#{params.orderSourceNo}
                
            </if>
            
            <if test="null!=params.interfacePlatform  and ''!=params.interfacePlatform ">
                
                AND `interface_platform`=#{params.interfacePlatform}
                
            </if>
            
            <if test="null!=params.outOrderStatusName  and ''!=params.outOrderStatusName ">
                
                AND `out_order_status_name` like CONCAT('%',#{params.outOrderStatusName},'%') 
                
            </if>
            
            <if test="null!=params.outOrderStatus ">
                
                AND `out_order_status`=#{params.outOrderStatus}
                
            </if>
            
            <if test="null!=params.orderStatusName  and ''!=params.orderStatusName ">
                
                AND `order_status_name` like CONCAT('%',#{params.orderStatusName},'%') 
                
            </if>
            
            <if test="null!=params.orderStatus ">
                
                AND `order_status`=#{params.orderStatus}
                
            </if>
            
            <if test="null!=params.businessType ">
                
                AND `business_type`=#{params.businessType}
                
            </if>
            
            <if test="null!=params.sourceType ">
                
                AND `source_type`=#{params.sourceType}
                
            </if>
            
            <if test="null!=params.orderStyle ">
                
                AND `order_style`=#{params.orderStyle}
                
            </if>
            
            <if test="null!=params.isVip ">
                
                AND `is_vip`=#{params.isVip}
                
            </if>
            
            <if test="null!=params.isConverted ">
                
                AND `is_converted`=#{params.isConverted}
                
            </if>
            
            <if test="null!=params.orderMainNo  and ''!=params.orderMainNo ">
                
                AND `order_main_no`=#{params.orderMainNo}
                
            </if>
            
            <if test="null!=params.saleShopNo  and ''!=params.saleShopNo ">
                
                AND `sale_shop_no`=#{params.saleShopNo}
                
            </if>
            
            <if test="null!=params.companyName  and ''!=params.companyName ">
                
                AND `company_name` like CONCAT('%',#{params.companyName},'%') 
                
            </if>
            
            <if test="null!=params.companyCode  and ''!=params.companyCode ">
                
                AND `company_code`=#{params.companyCode}
                
            </if>
            
            <if test="null!=params.retailProvince  and ''!=params.retailProvince ">
                
                AND `retail_province`=#{params.retailProvince}
                
            </if>
            
            <if test="null!=params.retailProvinceName  and ''!=params.retailProvinceName ">
                
                AND `retail_province_name` like CONCAT('%',#{params.retailProvinceName},'%') 
                
            </if>
            
            <if test="null!=params.retailCity  and ''!=params.retailCity ">
                
                AND `retail_city`=#{params.retailCity}
                
            </if>
            
            <if test="null!=params.retailCityName  and ''!=params.retailCityName ">
                
                AND `retail_city_name` like CONCAT('%',#{params.retailCityName},'%') 
                
            </if>
            
            <if test="null!=params.retailArea  and ''!=params.retailArea ">
                
                AND `retail_area`=#{params.retailArea}
                
            </if>
            
            <if test="null!=params.retailAreaName  and ''!=params.retailAreaName ">
                
                AND `retail_area_name` like CONCAT('%',#{params.retailAreaName},'%') 
                
            </if>
            
            <if test="null!=params.stockType ">
                
                AND `stock_type`=#{params.stockType}
                
            </if>
            
            <if test="null!=params.undeliverable ">
                
                AND `undeliverable`=#{params.undeliverable}
                
            </if>
            
            <if test="null!=params.oaId  and ''!=params.oaId ">
                
                AND `oa_id`=#{params.oaId}
                
            </if>
            
            <if test="null!=params.productTotalQuantity ">
                
                AND `product_total_quantity`=#{params.productTotalQuantity}
                
            </if>
            
            <if test="null!=params.orderAmount ">
                
                AND `order_amount`=#{params.orderAmount}
                
            </if>
            
            <if test="null!=params.orderPayTotalAmont ">
                
                AND `order_pay_total_amont`=#{params.orderPayTotalAmont}
                
            </if>
            
            <if test="null!=params.shipTime ">
                
                AND `ship_time`=#{params.shipTime}
                
            </if>
            
            <if test="null!=params.onlinePayTime ">
                
                AND `online_pay_time`=#{params.onlinePayTime}
                
            </if>
            
            <if test="null!=params.arriveTime ">
                
                AND `arrive_time`=#{params.arriveTime}
                
            </if>
            
            <if test="null!=params.syncTimeStamp ">
                
                AND `sync_time_stamp`=#{params.syncTimeStamp}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.retailFlag  and ''!=params.retailFlag ">
                
                AND `retail_flag`=#{params.retailFlag}
                
            </if>
            
            <if test="null!=params.vstoreCode  and ''!=params.vstoreCode ">
                
                AND `vstore_code`=#{params.vstoreCode}
                
            </if>
            
            <if test="null!=params.vstoreType ">
                
                AND `vstore_type`=#{params.vstoreType}
                
            </if>
            
            <if test="null!=params.merchantCode  and ''!=params.merchantCode ">
                
                AND `merchant_code`=#{params.merchantCode}
                
            </if>
            
            <if test="null!=params.isMatch ">
                
                AND `is_match`=#{params.isMatch}
                
            </if>
            
            <if test="null!=params.outOrderId  and ''!=params.outOrderId ">
                
                AND `out_order_id`=#{params.outOrderId}
                
            </if>
            
            <if test="null!=params.isGuideOrder ">
                
                AND `is_guide_order`=#{params.isGuideOrder}
                
            </if>
            
            <if test="null!=params.consigneeAddressLatitude  and ''!=params.consigneeAddressLatitude ">
                
                AND `consignee_address_latitude`=#{params.consigneeAddressLatitude}
                
            </if>
            
            <if test="null!=params.consigneeAddressLongitude  and ''!=params.consigneeAddressLongitude ">
                
                AND `consignee_address_longitude`=#{params.consigneeAddressLongitude}
                
            </if>
            
            <if test="null!=params.orderModifyTime ">
                
                AND `order_modify_time`=#{params.orderModifyTime}
                
            </if>
            
            <if test="null!=params.orderCreateTime ">
                
                AND `order_create_time`=#{params.orderCreateTime}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.organTypeNo  and ''!=params.organTypeNo ">
                
                AND `organ_type_no`=#{params.organTypeNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=orderSubNo and ''!=orderSubNo">
            AND `order_sub_no`=#{orderSubNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_order
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_order
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_order
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_order
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_order
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrder"  >
        INSERT INTO internet_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="sendStore != null">
                `send_store`,
            </if>
            
            <if test="actualPostage != null">
                `actual_postage`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="originalOrderNo != null">
                `original_order_no`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="originPlatform != null">
                `origin_platform`,
            </if>
            
            <if test="originPlatformName != null">
                `origin_platform_name`,
            </if>
            
            <if test="guideOrderFlag != null">
                `guide_order_flag`,
            </if>
            
            <if test="systemSource != null">
                `system_source`,
            </if>
            
            <if test="closeCode != null">
                `close_code`,
            </if>
            
            <if test="orderTimestamp != null">
                `order_timestamp`,
            </if>
            
            <if test="originalOutId != null">
                `original_out_id`,
            </if>
            
            <if test="defectiveShop != null">
                `defective_shop`,
            </if>
            
            <if test="customSign != null">
                `custom_sign`,
            </if>
            
            <if test="poNo != null">
                `po_no`,
            </if>
            
            <if test="balanceDueDate != null">
                `balance_due_date`,
            </if>
            
            <if test="verificationStatus != null">
                `verification_status`,
            </if>
            
            <if test="validateBatch != null">
                `validate_batch`,
            </if>
            
            <if test="appSecret != null">
                `app_secret`,
            </if>
            
            <if test="appKey != null">
                `app_key`,
            </if>
            
            <if test="orderSourceNo != null">
                `order_source_no`,
            </if>
            
            <if test="interfacePlatform != null">
                `interface_platform`,
            </if>
            
            <if test="outOrderStatusName != null">
                `out_order_status_name`,
            </if>
            
            <if test="outOrderStatus != null">
                `out_order_status`,
            </if>
            
            <if test="orderStatusName != null">
                `order_status_name`,
            </if>
            
            <if test="orderStatus != null">
                `order_status`,
            </if>
            
            <if test="businessType != null">
                `business_type`,
            </if>
            
            <if test="sourceType != null">
                `source_type`,
            </if>
            
            <if test="orderStyle != null">
                `order_style`,
            </if>
            
            <if test="isVip != null">
                `is_vip`,
            </if>
            
            <if test="isConverted != null">
                `is_converted`,
            </if>
            
            <if test="orderMainNo != null">
                `order_main_no`,
            </if>
            
            <if test="saleShopNo != null">
                `sale_shop_no`,
            </if>
            
            <if test="companyName != null">
                `company_name`,
            </if>
            
            <if test="companyCode != null">
                `company_code`,
            </if>
            
            <if test="retailProvince != null">
                `retail_province`,
            </if>
            
            <if test="retailProvinceName != null">
                `retail_province_name`,
            </if>
            
            <if test="retailCity != null">
                `retail_city`,
            </if>
            
            <if test="retailCityName != null">
                `retail_city_name`,
            </if>
            
            <if test="retailArea != null">
                `retail_area`,
            </if>
            
            <if test="retailAreaName != null">
                `retail_area_name`,
            </if>
            
            <if test="stockType != null">
                `stock_type`,
            </if>
            
            <if test="undeliverable != null">
                `undeliverable`,
            </if>
            
            <if test="oaId != null">
                `oa_id`,
            </if>
            
            <if test="productTotalQuantity != null">
                `product_total_quantity`,
            </if>
            
            <if test="orderAmount != null">
                `order_amount`,
            </if>
            
            <if test="orderPayTotalAmont != null">
                `order_pay_total_amont`,
            </if>
            
            <if test="shipTime != null">
                `ship_time`,
            </if>
            
            <if test="onlinePayTime != null">
                `online_pay_time`,
            </if>
            
            <if test="arriveTime != null">
                `arrive_time`,
            </if>
            
            <if test="syncTimeStamp != null">
                `sync_time_stamp`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="retailFlag != null">
                `retail_flag`,
            </if>
            
            <if test="vstoreCode != null">
                `vstore_code`,
            </if>
            
            <if test="vstoreType != null">
                `vstore_type`,
            </if>
            
            <if test="merchantCode != null">
                `merchant_code`,
            </if>
            
            <if test="isMatch != null">
                `is_match`,
            </if>
            
            <if test="outOrderId != null">
                `out_order_id`,
            </if>
            
            <if test="isGuideOrder != null">
                `is_guide_order`,
            </if>
            
            <if test="consigneeAddressLatitude != null">
                `consignee_address_latitude`,
            </if>
            
            <if test="consigneeAddressLongitude != null">
                `consignee_address_longitude`,
            </if>
            
            <if test="orderModifyTime != null">
                `order_modify_time`,
            </if>
            
            <if test="orderCreateTime != null">
                `order_create_time`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="organTypeNo != null">
                `organ_type_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="sendStore != null">
                #{sendStore},
            </if>
            
            <if test="actualPostage != null">
                #{actualPostage},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="originalOrderNo != null">
                #{originalOrderNo},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="originPlatform != null">
                #{originPlatform},
            </if>
            
            <if test="originPlatformName != null">
                #{originPlatformName},
            </if>
            
            <if test="guideOrderFlag != null">
                #{guideOrderFlag},
            </if>
            
            <if test="systemSource != null">
                #{systemSource},
            </if>
            
            <if test="closeCode != null">
                #{closeCode},
            </if>
            
            <if test="orderTimestamp != null">
                #{orderTimestamp},
            </if>
            
            <if test="originalOutId != null">
                #{originalOutId},
            </if>
            
            <if test="defectiveShop != null">
                #{defectiveShop},
            </if>
            
            <if test="customSign != null">
                #{customSign},
            </if>
            
            <if test="poNo != null">
                #{poNo},
            </if>
            
            <if test="balanceDueDate != null">
                #{balanceDueDate},
            </if>
            
            <if test="verificationStatus != null">
                #{verificationStatus},
            </if>
            
            <if test="validateBatch != null">
                #{validateBatch},
            </if>
            
            <if test="appSecret != null">
                #{appSecret},
            </if>
            
            <if test="appKey != null">
                #{appKey},
            </if>
            
            <if test="orderSourceNo != null">
                #{orderSourceNo},
            </if>
            
            <if test="interfacePlatform != null">
                #{interfacePlatform},
            </if>
            
            <if test="outOrderStatusName != null">
                #{outOrderStatusName},
            </if>
            
            <if test="outOrderStatus != null">
                #{outOrderStatus},
            </if>
            
            <if test="orderStatusName != null">
                #{orderStatusName},
            </if>
            
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            
            <if test="businessType != null">
                #{businessType},
            </if>
            
            <if test="sourceType != null">
                #{sourceType},
            </if>
            
            <if test="orderStyle != null">
                #{orderStyle},
            </if>
            
            <if test="isVip != null">
                #{isVip},
            </if>
            
            <if test="isConverted != null">
                #{isConverted},
            </if>
            
            <if test="orderMainNo != null">
                #{orderMainNo},
            </if>
            
            <if test="saleShopNo != null">
                #{saleShopNo},
            </if>
            
            <if test="companyName != null">
                #{companyName},
            </if>
            
            <if test="companyCode != null">
                #{companyCode},
            </if>
            
            <if test="retailProvince != null">
                #{retailProvince},
            </if>
            
            <if test="retailProvinceName != null">
                #{retailProvinceName},
            </if>
            
            <if test="retailCity != null">
                #{retailCity},
            </if>
            
            <if test="retailCityName != null">
                #{retailCityName},
            </if>
            
            <if test="retailArea != null">
                #{retailArea},
            </if>
            
            <if test="retailAreaName != null">
                #{retailAreaName},
            </if>
            
            <if test="stockType != null">
                #{stockType},
            </if>
            
            <if test="undeliverable != null">
                #{undeliverable},
            </if>
            
            <if test="oaId != null">
                #{oaId},
            </if>
            
            <if test="productTotalQuantity != null">
                #{productTotalQuantity},
            </if>
            
            <if test="orderAmount != null">
                #{orderAmount},
            </if>
            
            <if test="orderPayTotalAmont != null">
                #{orderPayTotalAmont},
            </if>
            
            <if test="shipTime != null">
                #{shipTime},
            </if>
            
            <if test="onlinePayTime != null">
                #{onlinePayTime},
            </if>
            
            <if test="arriveTime != null">
                #{arriveTime},
            </if>
            
            <if test="syncTimeStamp != null">
                #{syncTimeStamp},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="retailFlag != null">
                #{retailFlag},
            </if>
            
            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>
            
            <if test="vstoreType != null">
                #{vstoreType},
            </if>
            
            <if test="merchantCode != null">
                #{merchantCode},
            </if>
            
            <if test="isMatch != null">
                #{isMatch},
            </if>
            
            <if test="outOrderId != null">
                #{outOrderId},
            </if>
            
            <if test="isGuideOrder != null">
                #{isGuideOrder},
            </if>
            
            <if test="consigneeAddressLatitude != null">
                #{consigneeAddressLatitude},
            </if>
            
            <if test="consigneeAddressLongitude != null">
                #{consigneeAddressLongitude},
            </if>
            
            <if test="orderModifyTime != null">
                #{orderModifyTime},
            </if>
            
            <if test="orderCreateTime != null">
                #{orderCreateTime},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="organTypeNo != null">
                #{organTypeNo},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrder"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrder">
        UPDATE internet_order
        <set>
            
            <if test="sendStore != null">
                `send_store` = #{sendStore},
            </if> 
            <if test="actualPostage != null">
                `actual_postage` = #{actualPostage},
            </if> 
            <if test="originalOrderNo != null">
                `original_order_no` = #{originalOrderNo},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="originPlatform != null">
                `origin_platform` = #{originPlatform},
            </if> 
            <if test="originPlatformName != null">
                `origin_platform_name` = #{originPlatformName},
            </if> 
            <if test="guideOrderFlag != null">
                `guide_order_flag` = #{guideOrderFlag},
            </if> 
            <if test="systemSource != null">
                `system_source` = #{systemSource},
            </if> 
            <if test="closeCode != null">
                `close_code` = #{closeCode},
            </if> 
            <if test="orderTimestamp != null">
                `order_timestamp` = #{orderTimestamp},
            </if> 
            <if test="originalOutId != null">
                `original_out_id` = #{originalOutId},
            </if> 
            <if test="defectiveShop != null">
                `defective_shop` = #{defectiveShop},
            </if> 
            <if test="customSign != null">
                `custom_sign` = #{customSign},
            </if> 
            <if test="poNo != null">
                `po_no` = #{poNo},
            </if> 
            <if test="balanceDueDate != null">
                `balance_due_date` = #{balanceDueDate},
            </if> 
            <if test="verificationStatus != null">
                `verification_status` = #{verificationStatus},
            </if> 
            <if test="validateBatch != null">
                `validate_batch` = #{validateBatch},
            </if> 
            <if test="appSecret != null">
                `app_secret` = #{appSecret},
            </if> 
            <if test="appKey != null">
                `app_key` = #{appKey},
            </if> 
            <if test="orderSourceNo != null">
                `order_source_no` = #{orderSourceNo},
            </if> 
            <if test="interfacePlatform != null">
                `interface_platform` = #{interfacePlatform},
            </if> 
            <if test="outOrderStatusName != null">
                `out_order_status_name` = #{outOrderStatusName},
            </if> 
            <if test="outOrderStatus != null">
                `out_order_status` = #{outOrderStatus},
            </if> 
            <if test="orderStatusName != null">
                `order_status_name` = #{orderStatusName},
            </if> 
            <if test="orderStatus != null">
                `order_status` = #{orderStatus},
            </if> 
            <if test="businessType != null">
                `business_type` = #{businessType},
            </if> 
            <if test="sourceType != null">
                `source_type` = #{sourceType},
            </if> 
            <if test="orderStyle != null">
                `order_style` = #{orderStyle},
            </if> 
            <if test="isVip != null">
                `is_vip` = #{isVip},
            </if> 
            <if test="isConverted != null">
                `is_converted` = #{isConverted},
            </if> 
            <if test="orderMainNo != null">
                `order_main_no` = #{orderMainNo},
            </if> 
            <if test="saleShopNo != null">
                `sale_shop_no` = #{saleShopNo},
            </if> 
            <if test="companyName != null">
                `company_name` = #{companyName},
            </if> 
            <if test="companyCode != null">
                `company_code` = #{companyCode},
            </if> 
            <if test="retailProvince != null">
                `retail_province` = #{retailProvince},
            </if> 
            <if test="retailProvinceName != null">
                `retail_province_name` = #{retailProvinceName},
            </if> 
            <if test="retailCity != null">
                `retail_city` = #{retailCity},
            </if> 
            <if test="retailCityName != null">
                `retail_city_name` = #{retailCityName},
            </if> 
            <if test="retailArea != null">
                `retail_area` = #{retailArea},
            </if> 
            <if test="retailAreaName != null">
                `retail_area_name` = #{retailAreaName},
            </if> 
            <if test="stockType != null">
                `stock_type` = #{stockType},
            </if> 
            <if test="undeliverable != null">
                `undeliverable` = #{undeliverable},
            </if> 
            <if test="oaId != null">
                `oa_id` = #{oaId},
            </if> 
            <if test="productTotalQuantity != null">
                `product_total_quantity` = #{productTotalQuantity},
            </if> 
            <if test="orderAmount != null">
                `order_amount` = #{orderAmount},
            </if> 
            <if test="orderPayTotalAmont != null">
                `order_pay_total_amont` = #{orderPayTotalAmont},
            </if> 
            <if test="shipTime != null">
                `ship_time` = #{shipTime},
            </if> 
            <if test="onlinePayTime != null">
                `online_pay_time` = #{onlinePayTime},
            </if> 
            <if test="arriveTime != null">
                `arrive_time` = #{arriveTime},
            </if> 
            <if test="syncTimeStamp != null">
                `sync_time_stamp` = #{syncTimeStamp},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="retailFlag != null">
                `retail_flag` = #{retailFlag},
            </if> 
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if> 
            <if test="vstoreType != null">
                `vstore_type` = #{vstoreType},
            </if> 
            <if test="merchantCode != null">
                `merchant_code` = #{merchantCode},
            </if> 
            <if test="isMatch != null">
                `is_match` = #{isMatch},
            </if> 
            <if test="outOrderId != null">
                `out_order_id` = #{outOrderId},
            </if> 
            <if test="isGuideOrder != null">
                `is_guide_order` = #{isGuideOrder},
            </if> 
            <if test="consigneeAddressLatitude != null">
                `consignee_address_latitude` = #{consigneeAddressLatitude},
            </if> 
            <if test="consigneeAddressLongitude != null">
                `consignee_address_longitude` = #{consigneeAddressLongitude},
            </if> 
            <if test="orderModifyTime != null">
                `order_modify_time` = #{orderModifyTime},
            </if> 
            <if test="orderCreateTime != null">
                `order_create_time` = #{orderCreateTime},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="organTypeNo != null">
                `organ_type_no` = #{organTypeNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id} OR order_sub_no = #{orderSubNo}
    </update>
        <!-- auto generate end-->


</mapper>