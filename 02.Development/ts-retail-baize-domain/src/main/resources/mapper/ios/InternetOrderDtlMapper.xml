<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetOrderDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetOrderDtl">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />
        
        <result column="coupon_pref_amount" property="couponPrefAmount" jdbcType="DECIMAL" />
        
        <result column="member_pref_amount" property="memberPrefAmount" jdbcType="DECIMAL" />
        
        <result column="should_postage" property="shouldPostage" jdbcType="DECIMAL" />
        
        <result column="buy_reduction_pref_amount" property="buyReductionPrefAmount" jdbcType="DECIMAL" />
        
        <result column="postage_cost" property="postageCost" jdbcType="DECIMAL" />
        
        <result column="payment_pref_amount" property="paymentPrefAmount" jdbcType="DECIMAL" />
        
        <result column="active_pref_amount" property="activePrefAmount" jdbcType="DECIMAL" />
        
        <result column="cpercent1" property="cpercent1" jdbcType="DECIMAL" />
        
        <result column="cpercent2" property="cpercent2" jdbcType="DECIMAL" />
        
        <result column="pos_dtl_id" property="posDtlId" jdbcType="CHAR" />
        
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        
        <result column="send_shop_no" property="sendShopNo" jdbcType="CHAR" />
        
        <result column="basescore" property="basescore" jdbcType="INTEGER" />
        
        <result column="prefAmountOfVip" property="prefamountofvip" jdbcType="DECIMAL" />
        
        <result column="itemActualPayPrice" property="itemactualpayprice" jdbcType="DECIMAL" />
        
        <result column="itemActualPayAmount" property="itemactualpayamount" jdbcType="DECIMAL" />
        
        <result column="deal_code" property="dealCode" jdbcType="VARCHAR" />
        
        <result column="brand_no" property="brandNo" jdbcType="CHAR" />
        
        <result column="gift_card_amount" property="giftCardAmount" jdbcType="DECIMAL" />
        
        <result column="customer_settle_price" property="customerSettlePrice" jdbcType="DECIMAL" />
        
        <result column="pro_score" property="proScore" jdbcType="INTEGER" />
        
        <result column="cost_score" property="costScore" jdbcType="INTEGER" />
        
        <result column="yougou_order_dtl_id" property="yougouOrderDtlId" jdbcType="VARCHAR" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="item_no" property="itemNo" jdbcType="CHAR" />
        
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
        
        <result column="style_no" property="styleNo" jdbcType="VARCHAR" />
        
        <result column="red_ink_amount" property="redInkAmount" jdbcType="DECIMAL" />
        
        <result column="red_ink_price" property="redInkPrice" jdbcType="DECIMAL" />
        
        <result column="settle_price" property="settlePrice" jdbcType="DECIMAL" />
        
        <result column="settle_amount" property="settleAmount" jdbcType="DECIMAL" />
        
        <result column="platform_bear_amount" property="platformBearAmount" jdbcType="DECIMAL" />
        
        <result column="yougou_price" property="yougouPrice" jdbcType="DECIMAL" />
        
        <result column="is_defective_sent" property="isDefectiveSent" jdbcType="TINYINT" />
        
        <result column="flight_flag" property="flightFlag" jdbcType="TINYINT" />
        
        <result column="market_price" property="marketPrice" jdbcType="DECIMAL" />
        
        <result column="delivery_line" property="deliveryLine" jdbcType="INTEGER" />
        
        <result column="article_no" property="articleNo" jdbcType="VARCHAR" />
        
        <result column="exchange_dispute_id" property="exchangeDisputeId" jdbcType="VARCHAR" />
        
        <result column="refund_status" property="refundStatus" jdbcType="TINYINT" />
        
        <result column="streamer_id" property="streamerId" jdbcType="VARCHAR" />
        
        <result column="streamer_name" property="streamerName" jdbcType="VARCHAR" />
        
        <result column="commodityType" property="commoditytype" jdbcType="TINYINT" />
        
        <result column="supplier_code" property="supplierCode" jdbcType="VARCHAR" />
        
        <result column="commodity_specification_str" property="commoditySpecificationStr" jdbcType="VARCHAR" />
        
        <result column="commodity_num" property="commodityNum" jdbcType="INTEGER" />
        
        <result column="commodity_no" property="commodityNo" jdbcType="VARCHAR" />
        
        <result column="level_code" property="levelCode" jdbcType="VARCHAR" />
        
        <result column="prod_total_amt" property="prodTotalAmt" jdbcType="DECIMAL" />
        
        <result column="prod_unit_price" property="prodUnitPrice" jdbcType="DECIMAL" />
        
        <result column="prod_name" property="prodName" jdbcType="VARCHAR" />
        
        <result column="prod_no" property="prodNo" jdbcType="VARCHAR" />
        
        <result column="prod_discount_amount" property="prodDiscountAmount" jdbcType="DECIMAL" />
        
        <result column="commodity_image" property="commodityImage" jdbcType="VARCHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `size_no`,`id`,`coupon_pref_amount`,`member_pref_amount`,`should_postage`,`buy_reduction_pref_amount`,`postage_cost`,`payment_pref_amount`,`active_pref_amount`,`cpercent1`,`cpercent2`,`pos_dtl_id`,`sku_no`,`send_shop_no`,`basescore`,`prefAmountOfVip`,`itemActualPayPrice`,`itemActualPayAmount`,`deal_code`,`brand_no`,`gift_card_amount`,`customer_settle_price`,`pro_score`,`cost_score`,`yougou_order_dtl_id`,`sharding_flag`,`create_user`,`update_user`,`create_time`,`update_time`,`item_no`,`item_code`,`style_no`,`red_ink_amount`,`red_ink_price`,`settle_price`,`settle_amount`,`platform_bear_amount`,`yougou_price`,`is_defective_sent`,`flight_flag`,`market_price`,`delivery_line`,`article_no`,`exchange_dispute_id`,`refund_status`,`streamer_id`,`streamer_name`,`commodityType`,`supplier_code`,`commodity_specification_str`,`commodity_num`,`commodity_no`,`level_code`,`prod_total_amt`,`prod_unit_price`,`prod_name`,`prod_no`,`prod_discount_amount`,`commodity_image`,`order_sub_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.sizeNo  and ''!=params.sizeNo ">
                
                AND `size_no`=#{params.sizeNo}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.couponPrefAmount ">
                
                AND `coupon_pref_amount`=#{params.couponPrefAmount}
                
            </if>
            
            <if test="null!=params.memberPrefAmount ">
                
                AND `member_pref_amount`=#{params.memberPrefAmount}
                
            </if>
            
            <if test="null!=params.shouldPostage ">
                
                AND `should_postage`=#{params.shouldPostage}
                
            </if>
            
            <if test="null!=params.buyReductionPrefAmount ">
                
                AND `buy_reduction_pref_amount`=#{params.buyReductionPrefAmount}
                
            </if>
            
            <if test="null!=params.postageCost ">
                
                AND `postage_cost`=#{params.postageCost}
                
            </if>
            
            <if test="null!=params.paymentPrefAmount ">
                
                AND `payment_pref_amount`=#{params.paymentPrefAmount}
                
            </if>
            
            <if test="null!=params.activePrefAmount ">
                
                AND `active_pref_amount`=#{params.activePrefAmount}
                
            </if>
            
            <if test="null!=params.cpercent1 ">
                
                AND `cpercent1`=#{params.cpercent1}
                
            </if>
            
            <if test="null!=params.cpercent2 ">
                
                AND `cpercent2`=#{params.cpercent2}
                
            </if>
            
            <if test="null!=params.posDtlId  and ''!=params.posDtlId ">
                
                AND `pos_dtl_id`=#{params.posDtlId}
                
            </if>
            
            <if test="null!=params.skuNo  and ''!=params.skuNo ">
                
                AND `sku_no`=#{params.skuNo}
                
            </if>
            
            <if test="null!=params.sendShopNo  and ''!=params.sendShopNo ">
                
                AND `send_shop_no`=#{params.sendShopNo}
                
            </if>
            
            <if test="null!=params.basescore ">
                
                AND `basescore`=#{params.basescore}
                
            </if>
            
            <if test="null!=params.prefamountofvip ">
                
                AND `prefAmountOfVip`=#{params.prefamountofvip}
                
            </if>
            
            <if test="null!=params.itemactualpayprice ">
                
                AND `itemActualPayPrice`=#{params.itemactualpayprice}
                
            </if>
            
            <if test="null!=params.itemactualpayamount ">
                
                AND `itemActualPayAmount`=#{params.itemactualpayamount}
                
            </if>
            
            <if test="null!=params.dealCode  and ''!=params.dealCode ">
                
                AND `deal_code`=#{params.dealCode}
                
            </if>
            
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                
                AND `brand_no`=#{params.brandNo}
                
            </if>
            
            <if test="null!=params.giftCardAmount ">
                
                AND `gift_card_amount`=#{params.giftCardAmount}
                
            </if>
            
            <if test="null!=params.customerSettlePrice ">
                
                AND `customer_settle_price`=#{params.customerSettlePrice}
                
            </if>
            
            <if test="null!=params.proScore ">
                
                AND `pro_score`=#{params.proScore}
                
            </if>
            
            <if test="null!=params.costScore ">
                
                AND `cost_score`=#{params.costScore}
                
            </if>
            
            <if test="null!=params.yougouOrderDtlId  and ''!=params.yougouOrderDtlId ">
                
                AND `yougou_order_dtl_id`=#{params.yougouOrderDtlId}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.itemNo  and ''!=params.itemNo ">
                
                AND `item_no`=#{params.itemNo}
                
            </if>
            
            <if test="null!=params.itemCode  and ''!=params.itemCode ">
                
                AND `item_code`=#{params.itemCode}
                
            </if>
            
            <if test="null!=params.styleNo  and ''!=params.styleNo ">
                
                AND `style_no`=#{params.styleNo}
                
            </if>
            
            <if test="null!=params.redInkAmount ">
                
                AND `red_ink_amount`=#{params.redInkAmount}
                
            </if>
            
            <if test="null!=params.redInkPrice ">
                
                AND `red_ink_price`=#{params.redInkPrice}
                
            </if>
            
            <if test="null!=params.settlePrice ">
                
                AND `settle_price`=#{params.settlePrice}
                
            </if>
            
            <if test="null!=params.settleAmount ">
                
                AND `settle_amount`=#{params.settleAmount}
                
            </if>
            
            <if test="null!=params.platformBearAmount ">
                
                AND `platform_bear_amount`=#{params.platformBearAmount}
                
            </if>
            
            <if test="null!=params.yougouPrice ">
                
                AND `yougou_price`=#{params.yougouPrice}
                
            </if>
            
            <if test="null!=params.isDefectiveSent ">
                
                AND `is_defective_sent`=#{params.isDefectiveSent}
                
            </if>
            
            <if test="null!=params.flightFlag ">
                
                AND `flight_flag`=#{params.flightFlag}
                
            </if>
            
            <if test="null!=params.marketPrice ">
                
                AND `market_price`=#{params.marketPrice}
                
            </if>
            
            <if test="null!=params.deliveryLine ">
                
                AND `delivery_line`=#{params.deliveryLine}
                
            </if>
            
            <if test="null!=params.articleNo  and ''!=params.articleNo ">
                
                AND `article_no`=#{params.articleNo}
                
            </if>
            
            <if test="null!=params.exchangeDisputeId  and ''!=params.exchangeDisputeId ">
                
                AND `exchange_dispute_id`=#{params.exchangeDisputeId}
                
            </if>
            
            <if test="null!=params.refundStatus ">
                
                AND `refund_status`=#{params.refundStatus}
                
            </if>
            
            <if test="null!=params.streamerId  and ''!=params.streamerId ">
                
                AND `streamer_id`=#{params.streamerId}
                
            </if>
            
            <if test="null!=params.streamerName  and ''!=params.streamerName ">
                
                AND `streamer_name` like CONCAT('%',#{params.streamerName},'%') 
                
            </if>
            
            <if test="null!=params.commoditytype ">
                
                AND `commodityType`=#{params.commoditytype}
                
            </if>
            
            <if test="null!=params.supplierCode  and ''!=params.supplierCode ">
                
                AND `supplier_code`=#{params.supplierCode}
                
            </if>
            
            <if test="null!=params.commoditySpecificationStr  and ''!=params.commoditySpecificationStr ">
                
                AND `commodity_specification_str`=#{params.commoditySpecificationStr}
                
            </if>
            
            <if test="null!=params.commodityNum ">
                
                AND `commodity_num`=#{params.commodityNum}
                
            </if>
            
            <if test="null!=params.commodityNo  and ''!=params.commodityNo ">
                
                AND `commodity_no`=#{params.commodityNo}
                
            </if>
            
            <if test="null!=params.levelCode  and ''!=params.levelCode ">
                
                AND `level_code`=#{params.levelCode}
                
            </if>
            
            <if test="null!=params.prodTotalAmt ">
                
                AND `prod_total_amt`=#{params.prodTotalAmt}
                
            </if>
            
            <if test="null!=params.prodUnitPrice ">
                
                AND `prod_unit_price`=#{params.prodUnitPrice}
                
            </if>
            
            <if test="null!=params.prodName  and ''!=params.prodName ">
                
                AND `prod_name` like CONCAT('%',#{params.prodName},'%') 
                
            </if>
            
            <if test="null!=params.prodNo  and ''!=params.prodNo ">
                
                AND `prod_no`=#{params.prodNo}
                
            </if>
            
            <if test="null!=params.prodDiscountAmount ">
                
                AND `prod_discount_amount`=#{params.prodDiscountAmount}
                
            </if>
            
            <if test="null!=params.commodityImage  and ''!=params.commodityImage ">
                
                AND `commodity_image`=#{params.commodityImage}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_dtl
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_order_dtl
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_order_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_order_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_order_dtl
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_order_dtl
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderDtl"  >
        INSERT INTO internet_order_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="sizeNo != null">
                `size_no`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="couponPrefAmount != null">
                `coupon_pref_amount`,
            </if>
            
            <if test="memberPrefAmount != null">
                `member_pref_amount`,
            </if>
            
            <if test="shouldPostage != null">
                `should_postage`,
            </if>
            
            <if test="buyReductionPrefAmount != null">
                `buy_reduction_pref_amount`,
            </if>
            
            <if test="postageCost != null">
                `postage_cost`,
            </if>
            
            <if test="paymentPrefAmount != null">
                `payment_pref_amount`,
            </if>
            
            <if test="activePrefAmount != null">
                `active_pref_amount`,
            </if>
            
            <if test="cpercent1 != null">
                `cpercent1`,
            </if>
            
            <if test="cpercent2 != null">
                `cpercent2`,
            </if>
            
            <if test="posDtlId != null">
                `pos_dtl_id`,
            </if>
            
            <if test="skuNo != null">
                `sku_no`,
            </if>
            
            <if test="sendShopNo != null">
                `send_shop_no`,
            </if>
            
            <if test="basescore != null">
                `basescore`,
            </if>
            
            <if test="prefamountofvip != null">
                `prefAmountOfVip`,
            </if>
            
            <if test="itemactualpayprice != null">
                `itemActualPayPrice`,
            </if>
            
            <if test="itemactualpayamount != null">
                `itemActualPayAmount`,
            </if>
            
            <if test="dealCode != null">
                `deal_code`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="giftCardAmount != null">
                `gift_card_amount`,
            </if>
            
            <if test="customerSettlePrice != null">
                `customer_settle_price`,
            </if>
            
            <if test="proScore != null">
                `pro_score`,
            </if>
            
            <if test="costScore != null">
                `cost_score`,
            </if>
            
            <if test="yougouOrderDtlId != null">
                `yougou_order_dtl_id`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="itemNo != null">
                `item_no`,
            </if>
            
            <if test="itemCode != null">
                `item_code`,
            </if>
            
            <if test="styleNo != null">
                `style_no`,
            </if>
            
            <if test="redInkAmount != null">
                `red_ink_amount`,
            </if>
            
            <if test="redInkPrice != null">
                `red_ink_price`,
            </if>
            
            <if test="settlePrice != null">
                `settle_price`,
            </if>
            
            <if test="settleAmount != null">
                `settle_amount`,
            </if>
            
            <if test="platformBearAmount != null">
                `platform_bear_amount`,
            </if>
            
            <if test="yougouPrice != null">
                `yougou_price`,
            </if>
            
            <if test="isDefectiveSent != null">
                `is_defective_sent`,
            </if>
            
            <if test="flightFlag != null">
                `flight_flag`,
            </if>
            
            <if test="marketPrice != null">
                `market_price`,
            </if>
            
            <if test="deliveryLine != null">
                `delivery_line`,
            </if>
            
            <if test="articleNo != null">
                `article_no`,
            </if>
            
            <if test="exchangeDisputeId != null">
                `exchange_dispute_id`,
            </if>
            
            <if test="refundStatus != null">
                `refund_status`,
            </if>
            
            <if test="streamerId != null">
                `streamer_id`,
            </if>
            
            <if test="streamerName != null">
                `streamer_name`,
            </if>
            
            <if test="commoditytype != null">
                `commodityType`,
            </if>
            
            <if test="supplierCode != null">
                `supplier_code`,
            </if>
            
            <if test="commoditySpecificationStr != null">
                `commodity_specification_str`,
            </if>
            
            <if test="commodityNum != null">
                `commodity_num`,
            </if>
            
            <if test="commodityNo != null">
                `commodity_no`,
            </if>
            
            <if test="levelCode != null">
                `level_code`,
            </if>
            
            <if test="prodTotalAmt != null">
                `prod_total_amt`,
            </if>
            
            <if test="prodUnitPrice != null">
                `prod_unit_price`,
            </if>
            
            <if test="prodName != null">
                `prod_name`,
            </if>
            
            <if test="prodNo != null">
                `prod_no`,
            </if>
            
            <if test="prodDiscountAmount != null">
                `prod_discount_amount`,
            </if>
            
            <if test="commodityImage != null">
                `commodity_image`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="sizeNo != null">
                #{sizeNo},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="couponPrefAmount != null">
                #{couponPrefAmount},
            </if>
            
            <if test="memberPrefAmount != null">
                #{memberPrefAmount},
            </if>
            
            <if test="shouldPostage != null">
                #{shouldPostage},
            </if>
            
            <if test="buyReductionPrefAmount != null">
                #{buyReductionPrefAmount},
            </if>
            
            <if test="postageCost != null">
                #{postageCost},
            </if>
            
            <if test="paymentPrefAmount != null">
                #{paymentPrefAmount},
            </if>
            
            <if test="activePrefAmount != null">
                #{activePrefAmount},
            </if>
            
            <if test="cpercent1 != null">
                #{cpercent1},
            </if>
            
            <if test="cpercent2 != null">
                #{cpercent2},
            </if>
            
            <if test="posDtlId != null">
                #{posDtlId},
            </if>
            
            <if test="skuNo != null">
                #{skuNo},
            </if>
            
            <if test="sendShopNo != null">
                #{sendShopNo},
            </if>
            
            <if test="basescore != null">
                #{basescore},
            </if>
            
            <if test="prefamountofvip != null">
                #{prefamountofvip},
            </if>
            
            <if test="itemactualpayprice != null">
                #{itemactualpayprice},
            </if>
            
            <if test="itemactualpayamount != null">
                #{itemactualpayamount},
            </if>
            
            <if test="dealCode != null">
                #{dealCode},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="giftCardAmount != null">
                #{giftCardAmount},
            </if>
            
            <if test="customerSettlePrice != null">
                #{customerSettlePrice},
            </if>
            
            <if test="proScore != null">
                #{proScore},
            </if>
            
            <if test="costScore != null">
                #{costScore},
            </if>
            
            <if test="yougouOrderDtlId != null">
                #{yougouOrderDtlId},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="itemNo != null">
                #{itemNo},
            </if>
            
            <if test="itemCode != null">
                #{itemCode},
            </if>
            
            <if test="styleNo != null">
                #{styleNo},
            </if>
            
            <if test="redInkAmount != null">
                #{redInkAmount},
            </if>
            
            <if test="redInkPrice != null">
                #{redInkPrice},
            </if>
            
            <if test="settlePrice != null">
                #{settlePrice},
            </if>
            
            <if test="settleAmount != null">
                #{settleAmount},
            </if>
            
            <if test="platformBearAmount != null">
                #{platformBearAmount},
            </if>
            
            <if test="yougouPrice != null">
                #{yougouPrice},
            </if>
            
            <if test="isDefectiveSent != null">
                #{isDefectiveSent},
            </if>
            
            <if test="flightFlag != null">
                #{flightFlag},
            </if>
            
            <if test="marketPrice != null">
                #{marketPrice},
            </if>
            
            <if test="deliveryLine != null">
                #{deliveryLine},
            </if>
            
            <if test="articleNo != null">
                #{articleNo},
            </if>
            
            <if test="exchangeDisputeId != null">
                #{exchangeDisputeId},
            </if>
            
            <if test="refundStatus != null">
                #{refundStatus},
            </if>
            
            <if test="streamerId != null">
                #{streamerId},
            </if>
            
            <if test="streamerName != null">
                #{streamerName},
            </if>
            
            <if test="commoditytype != null">
                #{commoditytype},
            </if>
            
            <if test="supplierCode != null">
                #{supplierCode},
            </if>
            
            <if test="commoditySpecificationStr != null">
                #{commoditySpecificationStr},
            </if>
            
            <if test="commodityNum != null">
                #{commodityNum},
            </if>
            
            <if test="commodityNo != null">
                #{commodityNo},
            </if>
            
            <if test="levelCode != null">
                #{levelCode},
            </if>
            
            <if test="prodTotalAmt != null">
                #{prodTotalAmt},
            </if>
            
            <if test="prodUnitPrice != null">
                #{prodUnitPrice},
            </if>
            
            <if test="prodName != null">
                #{prodName},
            </if>
            
            <if test="prodNo != null">
                #{prodNo},
            </if>
            
            <if test="prodDiscountAmount != null">
                #{prodDiscountAmount},
            </if>
            
            <if test="commodityImage != null">
                #{commodityImage},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderDtl">
        UPDATE internet_order_dtl
        <set>
            
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},
            </if> 
            <if test="couponPrefAmount != null">
                `coupon_pref_amount` = #{couponPrefAmount},
            </if> 
            <if test="memberPrefAmount != null">
                `member_pref_amount` = #{memberPrefAmount},
            </if> 
            <if test="shouldPostage != null">
                `should_postage` = #{shouldPostage},
            </if> 
            <if test="buyReductionPrefAmount != null">
                `buy_reduction_pref_amount` = #{buyReductionPrefAmount},
            </if> 
            <if test="postageCost != null">
                `postage_cost` = #{postageCost},
            </if> 
            <if test="paymentPrefAmount != null">
                `payment_pref_amount` = #{paymentPrefAmount},
            </if> 
            <if test="activePrefAmount != null">
                `active_pref_amount` = #{activePrefAmount},
            </if> 
            <if test="cpercent1 != null">
                `cpercent1` = #{cpercent1},
            </if> 
            <if test="cpercent2 != null">
                `cpercent2` = #{cpercent2},
            </if> 
            <if test="posDtlId != null">
                `pos_dtl_id` = #{posDtlId},
            </if> 
            <if test="skuNo != null">
                `sku_no` = #{skuNo},
            </if> 
            <if test="sendShopNo != null">
                `send_shop_no` = #{sendShopNo},
            </if> 
            <if test="basescore != null">
                `basescore` = #{basescore},
            </if> 
            <if test="prefamountofvip != null">
                `prefAmountOfVip` = #{prefamountofvip},
            </if> 
            <if test="itemactualpayprice != null">
                `itemActualPayPrice` = #{itemactualpayprice},
            </if> 
            <if test="itemactualpayamount != null">
                `itemActualPayAmount` = #{itemactualpayamount},
            </if> 
            <if test="dealCode != null">
                `deal_code` = #{dealCode},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="giftCardAmount != null">
                `gift_card_amount` = #{giftCardAmount},
            </if> 
            <if test="customerSettlePrice != null">
                `customer_settle_price` = #{customerSettlePrice},
            </if> 
            <if test="proScore != null">
                `pro_score` = #{proScore},
            </if> 
            <if test="costScore != null">
                `cost_score` = #{costScore},
            </if> 
            <if test="yougouOrderDtlId != null">
                `yougou_order_dtl_id` = #{yougouOrderDtlId},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="itemNo != null">
                `item_no` = #{itemNo},
            </if> 
            <if test="itemCode != null">
                `item_code` = #{itemCode},
            </if> 
            <if test="styleNo != null">
                `style_no` = #{styleNo},
            </if> 
            <if test="redInkAmount != null">
                `red_ink_amount` = #{redInkAmount},
            </if> 
            <if test="redInkPrice != null">
                `red_ink_price` = #{redInkPrice},
            </if> 
            <if test="settlePrice != null">
                `settle_price` = #{settlePrice},
            </if> 
            <if test="settleAmount != null">
                `settle_amount` = #{settleAmount},
            </if> 
            <if test="platformBearAmount != null">
                `platform_bear_amount` = #{platformBearAmount},
            </if> 
            <if test="yougouPrice != null">
                `yougou_price` = #{yougouPrice},
            </if> 
            <if test="isDefectiveSent != null">
                `is_defective_sent` = #{isDefectiveSent},
            </if> 
            <if test="flightFlag != null">
                `flight_flag` = #{flightFlag},
            </if> 
            <if test="marketPrice != null">
                `market_price` = #{marketPrice},
            </if> 
            <if test="deliveryLine != null">
                `delivery_line` = #{deliveryLine},
            </if> 
            <if test="articleNo != null">
                `article_no` = #{articleNo},
            </if> 
            <if test="exchangeDisputeId != null">
                `exchange_dispute_id` = #{exchangeDisputeId},
            </if> 
            <if test="refundStatus != null">
                `refund_status` = #{refundStatus},
            </if> 
            <if test="streamerId != null">
                `streamer_id` = #{streamerId},
            </if> 
            <if test="streamerName != null">
                `streamer_name` = #{streamerName},
            </if> 
            <if test="commoditytype != null">
                `commodityType` = #{commoditytype},
            </if> 
            <if test="supplierCode != null">
                `supplier_code` = #{supplierCode},
            </if> 
            <if test="commoditySpecificationStr != null">
                `commodity_specification_str` = #{commoditySpecificationStr},
            </if> 
            <if test="commodityNum != null">
                `commodity_num` = #{commodityNum},
            </if> 
            <if test="commodityNo != null">
                `commodity_no` = #{commodityNo},
            </if> 
            <if test="levelCode != null">
                `level_code` = #{levelCode},
            </if> 
            <if test="prodTotalAmt != null">
                `prod_total_amt` = #{prodTotalAmt},
            </if> 
            <if test="prodUnitPrice != null">
                `prod_unit_price` = #{prodUnitPrice},
            </if> 
            <if test="prodName != null">
                `prod_name` = #{prodName},
            </if> 
            <if test="prodNo != null">
                `prod_no` = #{prodNo},
            </if> 
            <if test="prodDiscountAmount != null">
                `prod_discount_amount` = #{prodDiscountAmount},
            </if> 
            <if test="commodityImage != null">
                `commodity_image` = #{commodityImage},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>