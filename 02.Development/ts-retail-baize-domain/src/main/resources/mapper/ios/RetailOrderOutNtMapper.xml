<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.RetailOrderOutNtRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.RetailOrderOutNt">
        <id column="id" property="id" jdbcType="CHAR"/>
        <result column="bill_no" property="billNo" jdbcType="CHAR"/>
        <result column="bill_type" property="billType" jdbcType="INTEGER"/>
        <result column="ref_bill_no" property="refBillNo" jdbcType="CHAR"/>
        <result column="ref_bill_type" property="refBillType" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" jdbcType="INTEGER"/>
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR"/>
        <result column="original_order_no" property="originalOrderNo" jdbcType="VARCHAR"/>
        <result column="origin_platform" property="originPlatform" jdbcType="VARCHAR"/>
        <result column="send_store_type" property="sendStoreType" jdbcType="TINYINT"/>
        <result column="send_store_no" property="sendStoreNo" jdbcType="CHAR"/>
        <result column="send_store_name" property="sendStoreName" jdbcType="VARCHAR"/>
        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR"/>
        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR"/>
        <result column="notice_date" property="noticeDate" jdbcType="DATE"/>
        <result column="send_detail_total" property="sendDetailTotal" jdbcType="INTEGER"/>
        <result column="total_price" property="totalPrice" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR"/>
        <result column="retail_flag" property="retailFlag" jdbcType="CHAR"/>
        <result column="order_pay_total_amont" property="orderPayTotalAmount" jdbcType="DECIMAL"/>
        <result column="origin_platform_name" property="originPlatformName" jdbcType="VARCHAR"/>
        <result column="shop_name" property="shopName" jdbcType="VARCHAR"/>
        <result column="shop_no" property="shopNo" jdbcType="VARCHAR"/>
        <result column="order_create_time" property="orderCreateTime" jdbcType="TIMESTAMP"/>
        <result column="company_no" property="companyNo" jdbcType="CHAR"/>
        <result column="address_id" property="addressId" jdbcType="INTEGER"/>
        <result column="buyer_storeid" property="buyerStoreid" jdbcType="VARCHAR"/>
        <result column="interface_platform" property="interfacePlatform" jdbcType="CHAR"/>
        <result column="basescore" property="basescore" jdbcType="INTEGER"/>
        <result column="is_vip" property="isVip" jdbcType="TINYINT"/>
        <result column="sale_company_no" property="saleCompanyNo" jdbcType="CHAR"/>
        <result column="transport_type" property="transportType" jdbcType="VARCHAR"/>
        <result column="arrive_time" property="arriveTime" jdbcType="TIMESTAMP"/>
        <result column="out_store_time" property="outStoreTime" jdbcType="TIMESTAMP"/>
        <result column="close_code" property="closeCode" jdbcType="VARCHAR"/>
        <result column="channel_occupied_no" property="channelOccupiedNo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `id`,
        `bill_no`,
        `bill_type`,
        `ref_bill_no`,
        `ref_bill_type`,
        `STATUS`,
        `order_sub_no`,
        `original_order_no`,
        `origin_platform`,
        `send_store_type`,
        `send_store_no`,
        `send_store_name`,
        `order_unit_no`,
        `order_unit_name`,
        `notice_date`,
        `send_detail_total`,
        `total_price`,
        `create_time`,
        `create_user`,
        `update_time`,
        `remark`,
        `sharding_flag`,
        `retail_flag`,
        `order_pay_total_amont`,
        `origin_platform_name`,
        `shop_name`,
        `shop_no`,
        `order_create_time`,
        `company_no`,
        `address_id`,
        `buyer_storeid`,
        `interface_platform`,
        `basescore`,
        `is_vip`,
        `sale_company_no`,
        `transport_type`,
        `arrive_time`,
        `out_store_time`,
        `close_code`,
        `channel_occupied_no`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>

            <if test="null != params.id and '' != params.id">
                AND id = #{params.id}
            </if>
            <if test="null != params.billNo and '' != params.billNo">
                AND bill_no = #{params.billNo}
            </if>
            <if test="null != params.billType">
                AND bill_type = #{params.billType}
            </if>
            <if test="null != params.refBillNo and '' != params.refBillNo">
                AND ref_bill_no = #{params.refBillNo}
            </if>
            <if test="null != params.refBillType">
                AND ref_bill_type = #{params.refBillType}
            </if>
            <if test="null != params.status">
                AND status = #{params.status}
            </if>
            <if test="null != params.orderSubNo and '' != params.orderSubNo">
                AND order_sub_no = #{params.orderSubNo}
            </if>
            <if test="null != params.originalOrderNo and '' != params.originalOrderNo">
                AND original_order_no LIKE CONCAT('%', #{params.originalOrderNo}, '%')
            </if>
            <if test="null != params.originPlatform and '' != params.originPlatform">
                AND origin_platform = #{params.originPlatform}
            </if>
            <if test="null != params.sendStoreType">
                AND send_store_type = #{params.sendStoreType}
            </if>
            <if test="null != params.sendStoreNo and '' != params.sendStoreNo">
                AND send_store_no = #{params.sendStoreNo}
            </if>
            <if test="null != params.sendStoreName and '' != params.sendStoreName">
                AND send_store_name LIKE CONCAT('%',#{params.sendStoreName}, '%')
            </if>
            <if test="null != params.orderUnitNo and '' != params.orderUnitNo">
                AND order_unit_no = #{params.orderUnitNo}
            </if>
            <if test="null != params.orderUnitName and '' != params.orderUnitName">
                AND order_unit_name LIKE CONCAT('%',#{params.orderUnitName}, '%')
            </if>
            <if test="null != params.noticeDate">
                AND notice_date = #{params.noticeDate}
            </if>
            <if test="null != params.sendDetailTotal">
                AND send_detail_total = #{params.sendDetailTotal}
            </if>
            <if test="null != params.totalPrice">
                AND total_price = #{params.totalPrice}
            </if>
            <if test="null != params.createTime">
                AND create_time = #{params.createTime}
            </if>
            <if test="null != params.createUser and '' != params.createUser">
                AND create_user LIKE CONCAT('%',#{params.createUser}, '%')
            </if>
            <if test="null != params.updateTime">
                AND update_time = #{params.updateTime}
            </if>
            <if test="null != params.remark and '' != params.remark">
                AND remark LIKE CONCAT('%', #{params.remark},'%')
            </if>
            <if test="null != params.shardingFlag and '' != params.shardingFlag">
                AND sharding_flag = #{params.shardingFlag}
            </if>
            <if test="null != params.retailFlag and '' != params.retailFlag">
                AND retail_flag = #{params.retailFlag}
            </if>
            <if test="null != params.orderPayTotalAmount">
                AND order_pay_total_amont = #{params.orderPayTotalAmount}
            </if>
            <if test="null != params.originPlatformName and '' != params.originPlatformName">
                AND origin_platform_name LIKE CONCAT('%', #{params.originPlatformName}, '%')
            </if>
            <if test="null != params.shopName and '' != params.shopName">
                AND shop_name LIKE CONCAT('%',#{params.shopName}, '%')
            </if>
            <if test="null != params.shopNo and '' != params.shopNo">
                AND shop_no = #{params.shopNo}
            </if>
            <if test="null != params.orderCreateTime">
                AND order_create_time = #{params.orderCreateTime}
            </if>
            <if test="null != params.companyNo and '' != params.companyNo">
                AND company_no = #{params.companyNo}
            </if>
            <if test="null != params.addressId">
                AND address_id = #{params.addressId}
            </if>
            <if test="null != params.buyerStoreid and '' != params.buyerStoreid">
                AND buyer_storeid = #{params.buyerStoreid}
            </if>
            <if test="null != params.interfacePlatform and '' != params.interfacePlatform">
                AND interface_platform = #{params.interfacePlatform}
            </if>
            <if test="null != params.basescore">
                AND basescore = #{params.basescore}
            </if>
            <if test="null != params.isVip">
                AND is_vip = #{params.isVip}
            </if>
            <if test="null != params.saleCompanyNo and '' != params.saleCompanyNo">
                AND sale_company_no = #{params.saleCompanyNo}
            </if>
            <if test="null != params.transportType and '' != params.transportType">
                AND transport_type LIKE CONCAT('%', #{params.transportType}, '%')
            </if>
            <if test="null != params.arriveTime">
                AND arrive_time = #{params.arriveTime}
            </if>
            <if test="null != params.outStoreTime">
                AND out_store_time = #{params.outStoreTime}
            </if>
            <if test="null != params.closeCode and '' != params.closeCode">
                AND close_code = #{params.closeCode}
            </if>
            <if test="null != params.channelOccupiedNo and '' != params.channelOccupiedNo">
                AND channel_occupied_no = #{params.channelOccupiedNo}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        `bill_no`= #{billNo}
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_out_nt
        WHERE id = #{id}
    </select>

    <select id="findByBillNo" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_out_nt
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_out_nt
        <where>
            <include refid="condition" />
        </where>

        LIMIT 1

    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM retail_order_out_nt
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">

        SELECT
        <include refid="column_list" />
        FROM retail_order_out_nt
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_out_nt
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>


    <!-- auto generate end-->


</mapper>