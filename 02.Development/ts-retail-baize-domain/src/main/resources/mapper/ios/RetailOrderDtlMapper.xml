<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.RetailOrderDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.RetailOrderDtl">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="flight_flag" property="flightFlag" jdbcType="TINYINT" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="deal_code" property="dealCode" jdbcType="VARCHAR" />
        
        <result column="itemActualPayAmount" property="itemactualpayamount" jdbcType="DECIMAL" />
        
        <result column="itemActualPayPrice" property="itemactualpayprice" jdbcType="DECIMAL" />
        
        <result column="prefAmountOfVip" property="prefamountofvip" jdbcType="DECIMAL" />
        
        <result column="item_name_forshow" property="itemNameForshow" jdbcType="VARCHAR" />
        
        <result column="basescore" property="basescore" jdbcType="INTEGER" />
        
        <result column="send_shop_no" property="sendShopNo" jdbcType="VARCHAR" />
        
        <result column="pos_dtl_id" property="posDtlId" jdbcType="VARCHAR" />
        
        <result column="cpercent2" property="cpercent2" jdbcType="DECIMAL" />
        
        <result column="cpercent1" property="cpercent1" jdbcType="DECIMAL" />
        
        <result column="active_pref_amount" property="activePrefAmount" jdbcType="DECIMAL" />
        
        <result column="payment_pref_amount" property="paymentPrefAmount" jdbcType="DECIMAL" />
        
        <result column="postage_cost" property="postageCost" jdbcType="DECIMAL" />
        
        <result column="buy_reduction_pref_amount" property="buyReductionPrefAmount" jdbcType="DECIMAL" />
        
        <result column="should_postage" property="shouldPostage" jdbcType="DECIMAL" />
        
        <result column="member_pref_amount" property="memberPrefAmount" jdbcType="DECIMAL" />
        
        <result column="coupon_pref_amount" property="couponPrefAmount" jdbcType="DECIMAL" />
        
        <result column="supplier_code" property="supplierCode" jdbcType="VARCHAR" />
        
        <result column="customer_settle_price" property="customerSettlePrice" jdbcType="DECIMAL" />
        
        <result column="pro_score" property="proScore" jdbcType="INTEGER" />
        
        <result column="cost_score" property="costScore" jdbcType="INTEGER" />
        
        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR" />
        
        <result column="yougou_order_dtl_id" property="yougouOrderDtlId" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="gift_card_amount" property="giftCardAmount" jdbcType="DECIMAL" />
        
        <result column="red_ink_amount" property="redInkAmount" jdbcType="DECIMAL" />
        
        <result column="red_ink_price" property="redInkPrice" jdbcType="DECIMAL" />
        
        <result column="settle_price" property="settlePrice" jdbcType="DECIMAL" />
        
        <result column="settle_amount" property="settleAmount" jdbcType="DECIMAL" />
        
        <result column="platform_bear_amount" property="platformBearAmount" jdbcType="DECIMAL" />
        
        <result column="yougou_price" property="yougouPrice" jdbcType="DECIMAL" />
        
        <result column="market_price" property="marketPrice" jdbcType="DECIMAL" />
        
        <result column="bill_no" property="billNo" jdbcType="CHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="CHAR" />
        
        <result column="commodity_image" property="commodityImage" jdbcType="VARCHAR" />
        
        <result column="prod_discount_amount" property="prodDiscountAmount" jdbcType="DECIMAL" />
        
        <result column="item_no" property="itemNo" jdbcType="VARCHAR" />
        
        <result column="item_name" property="itemName" jdbcType="VARCHAR" />
        
        <result column="prod_unit_price" property="prodUnitPrice" jdbcType="DECIMAL" />
        
        <result column="prod_total_amt" property="prodTotalAmt" jdbcType="DECIMAL" />
        
        <result column="barcode" property="barcode" jdbcType="VARCHAR" />
        
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        
        <result column="brand_no" property="brandNo" jdbcType="VARCHAR" />
        
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        
        <result column="category_no" property="categoryNo" jdbcType="VARCHAR" />
        
        <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />
        
        <result column="size_kind" property="sizeKind" jdbcType="VARCHAR" />
        
        <result column="commodity_num" property="commodityNum" jdbcType="INTEGER" />
        
        <result column="commodity_specification_str" property="commoditySpecificationStr" jdbcType="VARCHAR" />
        
        <result column="commodityType" property="commoditytype" jdbcType="TINYINT" />
        
        <result column="style_no" property="styleNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `flight_flag`,`sharding_flag`,`deal_code`,`itemActualPayAmount`,`itemActualPayPrice`,`prefAmountOfVip`,`item_name_forshow`,`basescore`,`send_shop_no`,`pos_dtl_id`,`cpercent2`,`cpercent1`,`active_pref_amount`,`payment_pref_amount`,`postage_cost`,`buy_reduction_pref_amount`,`should_postage`,`member_pref_amount`,`coupon_pref_amount`,`supplier_code`,`customer_settle_price`,`pro_score`,`cost_score`,`order_unit_no`,`yougou_order_dtl_id`,`create_time`,`update_time`,`gift_card_amount`,`red_ink_amount`,`red_ink_price`,`settle_price`,`settle_amount`,`platform_bear_amount`,`yougou_price`,`market_price`,`id`,`bill_no`,`order_sub_no`,`commodity_image`,`prod_discount_amount`,`item_no`,`item_name`,`prod_unit_price`,`prod_total_amt`,`barcode`,`sku_no`,`brand_no`,`brand_name`,`category_no`,`size_no`,`size_kind`,`commodity_num`,`commodity_specification_str`,`commodityType`,`style_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.flightFlag ">
                
                AND `flight_flag`=#{params.flightFlag}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.dealCode  and ''!=params.dealCode ">
                
                AND `deal_code`=#{params.dealCode}
                
            </if>
            
            <if test="null!=params.itemactualpayamount ">
                
                AND `itemActualPayAmount`=#{params.itemactualpayamount}
                
            </if>
            
            <if test="null!=params.itemactualpayprice ">
                
                AND `itemActualPayPrice`=#{params.itemactualpayprice}
                
            </if>
            
            <if test="null!=params.prefamountofvip ">
                
                AND `prefAmountOfVip`=#{params.prefamountofvip}
                
            </if>
            
            <if test="null!=params.itemNameForshow  and ''!=params.itemNameForshow ">
                
                AND `item_name_forshow` like CONCAT('%',#{params.itemNameForshow},'%') 
                
            </if>
            
            <if test="null!=params.basescore ">
                
                AND `basescore`=#{params.basescore}
                
            </if>
            
            <if test="null!=params.sendShopNo  and ''!=params.sendShopNo ">
                
                AND `send_shop_no`=#{params.sendShopNo}
                
            </if>
            
            <if test="null!=params.posDtlId  and ''!=params.posDtlId ">
                
                AND `pos_dtl_id`=#{params.posDtlId}
                
            </if>
            
            <if test="null!=params.cpercent2 ">
                
                AND `cpercent2`=#{params.cpercent2}
                
            </if>
            
            <if test="null!=params.cpercent1 ">
                
                AND `cpercent1`=#{params.cpercent1}
                
            </if>
            
            <if test="null!=params.activePrefAmount ">
                
                AND `active_pref_amount`=#{params.activePrefAmount}
                
            </if>
            
            <if test="null!=params.paymentPrefAmount ">
                
                AND `payment_pref_amount`=#{params.paymentPrefAmount}
                
            </if>
            
            <if test="null!=params.postageCost ">
                
                AND `postage_cost`=#{params.postageCost}
                
            </if>
            
            <if test="null!=params.buyReductionPrefAmount ">
                
                AND `buy_reduction_pref_amount`=#{params.buyReductionPrefAmount}
                
            </if>
            
            <if test="null!=params.shouldPostage ">
                
                AND `should_postage`=#{params.shouldPostage}
                
            </if>
            
            <if test="null!=params.memberPrefAmount ">
                
                AND `member_pref_amount`=#{params.memberPrefAmount}
                
            </if>
            
            <if test="null!=params.couponPrefAmount ">
                
                AND `coupon_pref_amount`=#{params.couponPrefAmount}
                
            </if>
            
            <if test="null!=params.supplierCode  and ''!=params.supplierCode ">
                
                AND `supplier_code`=#{params.supplierCode}
                
            </if>
            
            <if test="null!=params.customerSettlePrice ">
                
                AND `customer_settle_price`=#{params.customerSettlePrice}
                
            </if>
            
            <if test="null!=params.proScore ">
                
                AND `pro_score`=#{params.proScore}
                
            </if>
            
            <if test="null!=params.costScore ">
                
                AND `cost_score`=#{params.costScore}
                
            </if>
            
            <if test="null!=params.orderUnitNo  and ''!=params.orderUnitNo ">
                
                AND `order_unit_no`=#{params.orderUnitNo}
                
            </if>
            
            <if test="null!=params.yougouOrderDtlId  and ''!=params.yougouOrderDtlId ">
                
                AND `yougou_order_dtl_id`=#{params.yougouOrderDtlId}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.giftCardAmount ">
                
                AND `gift_card_amount`=#{params.giftCardAmount}
                
            </if>
            
            <if test="null!=params.redInkAmount ">
                
                AND `red_ink_amount`=#{params.redInkAmount}
                
            </if>
            
            <if test="null!=params.redInkPrice ">
                
                AND `red_ink_price`=#{params.redInkPrice}
                
            </if>
            
            <if test="null!=params.settlePrice ">
                
                AND `settle_price`=#{params.settlePrice}
                
            </if>
            
            <if test="null!=params.settleAmount ">
                
                AND `settle_amount`=#{params.settleAmount}
                
            </if>
            
            <if test="null!=params.platformBearAmount ">
                
                AND `platform_bear_amount`=#{params.platformBearAmount}
                
            </if>
            
            <if test="null!=params.yougouPrice ">
                
                AND `yougou_price`=#{params.yougouPrice}
                
            </if>
            
            <if test="null!=params.marketPrice ">
                
                AND `market_price`=#{params.marketPrice}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
            <if test="null!=params.commodityImage  and ''!=params.commodityImage ">
                
                AND `commodity_image`=#{params.commodityImage}
                
            </if>
            
            <if test="null!=params.prodDiscountAmount ">
                
                AND `prod_discount_amount`=#{params.prodDiscountAmount}
                
            </if>
            
            <if test="null!=params.itemNo  and ''!=params.itemNo ">
                
                AND `item_no`=#{params.itemNo}
                
            </if>
            
            <if test="null!=params.itemName  and ''!=params.itemName ">
                
                AND `item_name` like CONCAT('%',#{params.itemName},'%') 
                
            </if>
            
            <if test="null!=params.prodUnitPrice ">
                
                AND `prod_unit_price`=#{params.prodUnitPrice}
                
            </if>
            
            <if test="null!=params.prodTotalAmt ">
                
                AND `prod_total_amt`=#{params.prodTotalAmt}
                
            </if>
            
            <if test="null!=params.barcode  and ''!=params.barcode ">
                
                AND `barcode`=#{params.barcode}
                
            </if>
            
            <if test="null!=params.skuNo  and ''!=params.skuNo ">
                
                AND `sku_no`=#{params.skuNo}
                
            </if>
            
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                
                AND `brand_no`=#{params.brandNo}
                
            </if>
            
            <if test="null!=params.brandName  and ''!=params.brandName ">
                
                AND `brand_name` like CONCAT('%',#{params.brandName},'%') 
                
            </if>
            
            <if test="null!=params.categoryNo  and ''!=params.categoryNo ">
                
                AND `category_no`=#{params.categoryNo}
                
            </if>
            
            <if test="null!=params.sizeNo  and ''!=params.sizeNo ">
                
                AND `size_no`=#{params.sizeNo}
                
            </if>
            
            <if test="null!=params.sizeKind  and ''!=params.sizeKind ">
                
                AND `size_kind`=#{params.sizeKind}
                
            </if>
            
            <if test="null!=params.commodityNum ">
                
                AND `commodity_num`=#{params.commodityNum}
                
            </if>
            
            <if test="null!=params.commoditySpecificationStr  and ''!=params.commoditySpecificationStr ">
                
                AND `commodity_specification_str`=#{params.commoditySpecificationStr}
                
            </if>
            
            <if test="null!=params.commoditytype ">
                
                AND `commodityType`=#{params.commoditytype}
                
            </if>
            
            <if test="null!=params.styleNo  and ''!=params.styleNo ">
                
                AND `style_no`=#{params.styleNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_dtl
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM retail_order_dtl
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM retail_order_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM retail_order_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM retail_order_dtl
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM retail_order_dtl
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderDtl"  >
        INSERT INTO retail_order_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="flightFlag != null">
                `flight_flag`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="dealCode != null">
                `deal_code`,
            </if>
            
            <if test="itemactualpayamount != null">
                `itemActualPayAmount`,
            </if>
            
            <if test="itemactualpayprice != null">
                `itemActualPayPrice`,
            </if>
            
            <if test="prefamountofvip != null">
                `prefAmountOfVip`,
            </if>
            
            <if test="itemNameForshow != null">
                `item_name_forshow`,
            </if>
            
            <if test="basescore != null">
                `basescore`,
            </if>
            
            <if test="sendShopNo != null">
                `send_shop_no`,
            </if>
            
            <if test="posDtlId != null">
                `pos_dtl_id`,
            </if>
            
            <if test="cpercent2 != null">
                `cpercent2`,
            </if>
            
            <if test="cpercent1 != null">
                `cpercent1`,
            </if>
            
            <if test="activePrefAmount != null">
                `active_pref_amount`,
            </if>
            
            <if test="paymentPrefAmount != null">
                `payment_pref_amount`,
            </if>
            
            <if test="postageCost != null">
                `postage_cost`,
            </if>
            
            <if test="buyReductionPrefAmount != null">
                `buy_reduction_pref_amount`,
            </if>
            
            <if test="shouldPostage != null">
                `should_postage`,
            </if>
            
            <if test="memberPrefAmount != null">
                `member_pref_amount`,
            </if>
            
            <if test="couponPrefAmount != null">
                `coupon_pref_amount`,
            </if>
            
            <if test="supplierCode != null">
                `supplier_code`,
            </if>
            
            <if test="customerSettlePrice != null">
                `customer_settle_price`,
            </if>
            
            <if test="proScore != null">
                `pro_score`,
            </if>
            
            <if test="costScore != null">
                `cost_score`,
            </if>
            
            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>
            
            <if test="yougouOrderDtlId != null">
                `yougou_order_dtl_id`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="giftCardAmount != null">
                `gift_card_amount`,
            </if>
            
            <if test="redInkAmount != null">
                `red_ink_amount`,
            </if>
            
            <if test="redInkPrice != null">
                `red_ink_price`,
            </if>
            
            <if test="settlePrice != null">
                `settle_price`,
            </if>
            
            <if test="settleAmount != null">
                `settle_amount`,
            </if>
            
            <if test="platformBearAmount != null">
                `platform_bear_amount`,
            </if>
            
            <if test="yougouPrice != null">
                `yougou_price`,
            </if>
            
            <if test="marketPrice != null">
                `market_price`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="commodityImage != null">
                `commodity_image`,
            </if>
            
            <if test="prodDiscountAmount != null">
                `prod_discount_amount`,
            </if>
            
            <if test="itemNo != null">
                `item_no`,
            </if>
            
            <if test="itemName != null">
                `item_name`,
            </if>
            
            <if test="prodUnitPrice != null">
                `prod_unit_price`,
            </if>
            
            <if test="prodTotalAmt != null">
                `prod_total_amt`,
            </if>
            
            <if test="barcode != null">
                `barcode`,
            </if>
            
            <if test="skuNo != null">
                `sku_no`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="brandName != null">
                `brand_name`,
            </if>
            
            <if test="categoryNo != null">
                `category_no`,
            </if>
            
            <if test="sizeNo != null">
                `size_no`,
            </if>
            
            <if test="sizeKind != null">
                `size_kind`,
            </if>
            
            <if test="commodityNum != null">
                `commodity_num`,
            </if>
            
            <if test="commoditySpecificationStr != null">
                `commodity_specification_str`,
            </if>
            
            <if test="commoditytype != null">
                `commodityType`,
            </if>
            
            <if test="styleNo != null">
                `style_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="flightFlag != null">
                #{flightFlag},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="dealCode != null">
                #{dealCode},
            </if>
            
            <if test="itemactualpayamount != null">
                #{itemactualpayamount},
            </if>
            
            <if test="itemactualpayprice != null">
                #{itemactualpayprice},
            </if>
            
            <if test="prefamountofvip != null">
                #{prefamountofvip},
            </if>
            
            <if test="itemNameForshow != null">
                #{itemNameForshow},
            </if>
            
            <if test="basescore != null">
                #{basescore},
            </if>
            
            <if test="sendShopNo != null">
                #{sendShopNo},
            </if>
            
            <if test="posDtlId != null">
                #{posDtlId},
            </if>
            
            <if test="cpercent2 != null">
                #{cpercent2},
            </if>
            
            <if test="cpercent1 != null">
                #{cpercent1},
            </if>
            
            <if test="activePrefAmount != null">
                #{activePrefAmount},
            </if>
            
            <if test="paymentPrefAmount != null">
                #{paymentPrefAmount},
            </if>
            
            <if test="postageCost != null">
                #{postageCost},
            </if>
            
            <if test="buyReductionPrefAmount != null">
                #{buyReductionPrefAmount},
            </if>
            
            <if test="shouldPostage != null">
                #{shouldPostage},
            </if>
            
            <if test="memberPrefAmount != null">
                #{memberPrefAmount},
            </if>
            
            <if test="couponPrefAmount != null">
                #{couponPrefAmount},
            </if>
            
            <if test="supplierCode != null">
                #{supplierCode},
            </if>
            
            <if test="customerSettlePrice != null">
                #{customerSettlePrice},
            </if>
            
            <if test="proScore != null">
                #{proScore},
            </if>
            
            <if test="costScore != null">
                #{costScore},
            </if>
            
            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>
            
            <if test="yougouOrderDtlId != null">
                #{yougouOrderDtlId},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="giftCardAmount != null">
                #{giftCardAmount},
            </if>
            
            <if test="redInkAmount != null">
                #{redInkAmount},
            </if>
            
            <if test="redInkPrice != null">
                #{redInkPrice},
            </if>
            
            <if test="settlePrice != null">
                #{settlePrice},
            </if>
            
            <if test="settleAmount != null">
                #{settleAmount},
            </if>
            
            <if test="platformBearAmount != null">
                #{platformBearAmount},
            </if>
            
            <if test="yougouPrice != null">
                #{yougouPrice},
            </if>
            
            <if test="marketPrice != null">
                #{marketPrice},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="commodityImage != null">
                #{commodityImage},
            </if>
            
            <if test="prodDiscountAmount != null">
                #{prodDiscountAmount},
            </if>
            
            <if test="itemNo != null">
                #{itemNo},
            </if>
            
            <if test="itemName != null">
                #{itemName},
            </if>
            
            <if test="prodUnitPrice != null">
                #{prodUnitPrice},
            </if>
            
            <if test="prodTotalAmt != null">
                #{prodTotalAmt},
            </if>
            
            <if test="barcode != null">
                #{barcode},
            </if>
            
            <if test="skuNo != null">
                #{skuNo},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="brandName != null">
                #{brandName},
            </if>
            
            <if test="categoryNo != null">
                #{categoryNo},
            </if>
            
            <if test="sizeNo != null">
                #{sizeNo},
            </if>
            
            <if test="sizeKind != null">
                #{sizeKind},
            </if>
            
            <if test="commodityNum != null">
                #{commodityNum},
            </if>
            
            <if test="commoditySpecificationStr != null">
                #{commoditySpecificationStr},
            </if>
            
            <if test="commoditytype != null">
                #{commoditytype},
            </if>
            
            <if test="styleNo != null">
                #{styleNo},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderDtl">
        UPDATE retail_order_dtl
        <set>
            
            <if test="flightFlag != null">
                `flight_flag` = #{flightFlag},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="dealCode != null">
                `deal_code` = #{dealCode},
            </if> 
            <if test="itemactualpayamount != null">
                `itemActualPayAmount` = #{itemactualpayamount},
            </if> 
            <if test="itemactualpayprice != null">
                `itemActualPayPrice` = #{itemactualpayprice},
            </if> 
            <if test="prefamountofvip != null">
                `prefAmountOfVip` = #{prefamountofvip},
            </if> 
            <if test="itemNameForshow != null">
                `item_name_forshow` = #{itemNameForshow},
            </if> 
            <if test="basescore != null">
                `basescore` = #{basescore},
            </if> 
            <if test="sendShopNo != null">
                `send_shop_no` = #{sendShopNo},
            </if> 
            <if test="posDtlId != null">
                `pos_dtl_id` = #{posDtlId},
            </if> 
            <if test="cpercent2 != null">
                `cpercent2` = #{cpercent2},
            </if> 
            <if test="cpercent1 != null">
                `cpercent1` = #{cpercent1},
            </if> 
            <if test="activePrefAmount != null">
                `active_pref_amount` = #{activePrefAmount},
            </if> 
            <if test="paymentPrefAmount != null">
                `payment_pref_amount` = #{paymentPrefAmount},
            </if> 
            <if test="postageCost != null">
                `postage_cost` = #{postageCost},
            </if> 
            <if test="buyReductionPrefAmount != null">
                `buy_reduction_pref_amount` = #{buyReductionPrefAmount},
            </if> 
            <if test="shouldPostage != null">
                `should_postage` = #{shouldPostage},
            </if> 
            <if test="memberPrefAmount != null">
                `member_pref_amount` = #{memberPrefAmount},
            </if> 
            <if test="couponPrefAmount != null">
                `coupon_pref_amount` = #{couponPrefAmount},
            </if> 
            <if test="supplierCode != null">
                `supplier_code` = #{supplierCode},
            </if> 
            <if test="customerSettlePrice != null">
                `customer_settle_price` = #{customerSettlePrice},
            </if> 
            <if test="proScore != null">
                `pro_score` = #{proScore},
            </if> 
            <if test="costScore != null">
                `cost_score` = #{costScore},
            </if> 
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if> 
            <if test="yougouOrderDtlId != null">
                `yougou_order_dtl_id` = #{yougouOrderDtlId},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="giftCardAmount != null">
                `gift_card_amount` = #{giftCardAmount},
            </if> 
            <if test="redInkAmount != null">
                `red_ink_amount` = #{redInkAmount},
            </if> 
            <if test="redInkPrice != null">
                `red_ink_price` = #{redInkPrice},
            </if> 
            <if test="settlePrice != null">
                `settle_price` = #{settlePrice},
            </if> 
            <if test="settleAmount != null">
                `settle_amount` = #{settleAmount},
            </if> 
            <if test="platformBearAmount != null">
                `platform_bear_amount` = #{platformBearAmount},
            </if> 
            <if test="yougouPrice != null">
                `yougou_price` = #{yougouPrice},
            </if> 
            <if test="marketPrice != null">
                `market_price` = #{marketPrice},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="commodityImage != null">
                `commodity_image` = #{commodityImage},
            </if> 
            <if test="prodDiscountAmount != null">
                `prod_discount_amount` = #{prodDiscountAmount},
            </if> 
            <if test="itemNo != null">
                `item_no` = #{itemNo},
            </if> 
            <if test="itemName != null">
                `item_name` = #{itemName},
            </if> 
            <if test="prodUnitPrice != null">
                `prod_unit_price` = #{prodUnitPrice},
            </if> 
            <if test="prodTotalAmt != null">
                `prod_total_amt` = #{prodTotalAmt},
            </if> 
            <if test="barcode != null">
                `barcode` = #{barcode},
            </if> 
            <if test="skuNo != null">
                `sku_no` = #{skuNo},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="brandName != null">
                `brand_name` = #{brandName},
            </if> 
            <if test="categoryNo != null">
                `category_no` = #{categoryNo},
            </if> 
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},
            </if> 
            <if test="sizeKind != null">
                `size_kind` = #{sizeKind},
            </if> 
            <if test="commodityNum != null">
                `commodity_num` = #{commodityNum},
            </if> 
            <if test="commoditySpecificationStr != null">
                `commodity_specification_str` = #{commoditySpecificationStr},
            </if> 
            <if test="commoditytype != null">
                `commodityType` = #{commoditytype},
            </if> 
            <if test="styleNo != null">
                `style_no` = #{styleNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>