<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetOrderReturnDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetOrderReturnDtl">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="otherDeductAmount" property="otherdeductamount" jdbcType="DECIMAL" />
        
        <result column="schedule" property="schedule" jdbcType="TINYINT" />
        
        <result column="freight" property="freight" jdbcType="DECIMAL" />
        
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        
        <result column="brand_no" property="brandNo" jdbcType="CHAR" />
        
        <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
        
        <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
        
        <result column="size_no" property="sizeNo" jdbcType="VARCHAR" />
        
        <result column="item_name" property="itemName" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="goods_price" property="goodsPrice" jdbcType="DECIMAL" />
        
        <result column="box_no" property="boxNo" jdbcType="VARCHAR" />
        
        <result column="po_no" property="poNo" jdbcType="VARCHAR" />
        
        <result column="storage_type" property="storageType" jdbcType="TINYINT" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="po_start_date" property="poStartDate" jdbcType="TIMESTAMP" />
        
        <result column="sell_return_code" property="sellReturnCode" jdbcType="VARCHAR" />
        
        <result column="out_order_id" property="outOrderId" jdbcType="VARCHAR" />
        
        <result column="prod_total_amt" property="prodTotalAmt" jdbcType="DECIMAL" />
        
        <result column="gift_card_amount" property="giftCardAmount" jdbcType="DECIMAL" />
        
        <result column="promotion_amount" property="promotionAmount" jdbcType="DECIMAL" />
        
        <result column="commodity_type" property="commodityType" jdbcType="TINYINT" />
        
        <result column="actualRefundAmount" property="actualrefundamount" jdbcType="DECIMAL" />
        
        <result column="applyRefundAmount" property="applyrefundamount" jdbcType="DECIMAL" />
        
        <result column="cpercent2" property="cpercent2" jdbcType="DECIMAL" />
        
        <result column="cpercent1" property="cpercent1" jdbcType="DECIMAL" />
        
        <result column="basescore" property="basescore" jdbcType="INTEGER" />
        
        <result column="size_name" property="sizeName" jdbcType="VARCHAR" />
        
        <result column="color_name" property="colorName" jdbcType="VARCHAR" />
        
        <result column="coupon_amount" property="couponAmount" jdbcType="DECIMAL" />
        
        <result column="size_code" property="sizeCode" jdbcType="VARCHAR" />
        
        <result column="color_code" property="colorCode" jdbcType="VARCHAR" />
        
        <result column="commodity_num" property="commodityNum" jdbcType="INTEGER" />
        
        <result column="barcode" property="barcode" jdbcType="VARCHAR" />
        
        <result column="new_goods_price" property="newGoodsPrice" jdbcType="DECIMAL" />
        
        <result column="new_total_amt" property="newTotalAmt" jdbcType="DECIMAL" />
        
    </resultMap>

    <sql id="column_list">
        `otherDeductAmount`,`schedule`,`freight`,`sku_no`,`brand_no`,`bill_no`,`item_code`,`size_no`,`item_name`,`update_time`,`goods_price`,`box_no`,`po_no`,`storage_type`,`sharding_flag`,`po_start_date`,`id`,`sell_return_code`,`out_order_id`,`prod_total_amt`,`gift_card_amount`,`promotion_amount`,`commodity_type`,`actualRefundAmount`,`applyRefundAmount`,`cpercent2`,`cpercent1`,`basescore`,`size_name`,`color_name`,`coupon_amount`,`size_code`,`color_code`,`commodity_num`,`barcode`,`new_goods_price`,`new_total_amt`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.otherdeductamount ">
                
                AND `otherDeductAmount`=#{params.otherdeductamount}
                
            </if>
            
            <if test="null!=params.schedule ">
                
                AND `schedule`=#{params.schedule}
                
            </if>
            
            <if test="null!=params.freight ">
                
                AND `freight`=#{params.freight}
                
            </if>
            
            <if test="null!=params.skuNo  and ''!=params.skuNo ">
                
                AND `sku_no`=#{params.skuNo}
                
            </if>
            
            <if test="null!=params.brandNo  and ''!=params.brandNo ">
                
                AND `brand_no`=#{params.brandNo}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
            <if test="null!=params.itemCode  and ''!=params.itemCode ">
                
                AND `item_code`=#{params.itemCode}
                
            </if>
            
            <if test="null!=params.sizeNo  and ''!=params.sizeNo ">
                
                AND `size_no`=#{params.sizeNo}
                
            </if>
            
            <if test="null!=params.itemName  and ''!=params.itemName ">
                
                AND `item_name` like CONCAT('%',#{params.itemName},'%') 
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.goodsPrice ">
                
                AND `goods_price`=#{params.goodsPrice}
                
            </if>
            
            <if test="null!=params.boxNo  and ''!=params.boxNo ">
                
                AND `box_no`=#{params.boxNo}
                
            </if>
            
            <if test="null!=params.poNo  and ''!=params.poNo ">
                
                AND `po_no`=#{params.poNo}
                
            </if>
            
            <if test="null!=params.storageType ">
                
                AND `storage_type`=#{params.storageType}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.poStartDate ">
                
                AND `po_start_date`=#{params.poStartDate}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.sellReturnCode  and ''!=params.sellReturnCode ">
                
                AND `sell_return_code`=#{params.sellReturnCode}
                
            </if>
            
            <if test="null!=params.outOrderId  and ''!=params.outOrderId ">
                
                AND `out_order_id`=#{params.outOrderId}
                
            </if>
            
            <if test="null!=params.prodTotalAmt ">
                
                AND `prod_total_amt`=#{params.prodTotalAmt}
                
            </if>
            
            <if test="null!=params.giftCardAmount ">
                
                AND `gift_card_amount`=#{params.giftCardAmount}
                
            </if>
            
            <if test="null!=params.promotionAmount ">
                
                AND `promotion_amount`=#{params.promotionAmount}
                
            </if>
            
            <if test="null!=params.commodityType ">
                
                AND `commodity_type`=#{params.commodityType}
                
            </if>
            
            <if test="null!=params.actualrefundamount ">
                
                AND `actualRefundAmount`=#{params.actualrefundamount}
                
            </if>
            
            <if test="null!=params.applyrefundamount ">
                
                AND `applyRefundAmount`=#{params.applyrefundamount}
                
            </if>
            
            <if test="null!=params.cpercent2 ">
                
                AND `cpercent2`=#{params.cpercent2}
                
            </if>
            
            <if test="null!=params.cpercent1 ">
                
                AND `cpercent1`=#{params.cpercent1}
                
            </if>
            
            <if test="null!=params.basescore ">
                
                AND `basescore`=#{params.basescore}
                
            </if>
            
            <if test="null!=params.sizeName  and ''!=params.sizeName ">
                
                AND `size_name` like CONCAT('%',#{params.sizeName},'%') 
                
            </if>
            
            <if test="null!=params.colorName  and ''!=params.colorName ">
                
                AND `color_name` like CONCAT('%',#{params.colorName},'%') 
                
            </if>
            
            <if test="null!=params.couponAmount ">
                
                AND `coupon_amount`=#{params.couponAmount}
                
            </if>
            
            <if test="null!=params.sizeCode  and ''!=params.sizeCode ">
                
                AND `size_code`=#{params.sizeCode}
                
            </if>
            
            <if test="null!=params.colorCode  and ''!=params.colorCode ">
                
                AND `color_code`=#{params.colorCode}
                
            </if>
            
            <if test="null!=params.commodityNum ">
                
                AND `commodity_num`=#{params.commodityNum}
                
            </if>
            
            <if test="null!=params.barcode  and ''!=params.barcode ">
                
                AND `barcode`=#{params.barcode}
                
            </if>
            
            <if test="null!=params.newGoodsPrice ">
                
                AND `new_goods_price`=#{params.newGoodsPrice}
                
            </if>
            
            <if test="null!=params.newTotalAmt ">
                
                AND `new_total_amt`=#{params.newTotalAmt}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_return_dtl
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_return_dtl
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_order_return_dtl
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_order_return_dtl
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_order_return_dtl
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderReturnDtl"  >
        INSERT INTO internet_order_return_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="otherdeductamount != null">
                `otherDeductAmount`,
            </if>
            
            <if test="schedule != null">
                `schedule`,
            </if>
            
            <if test="freight != null">
                `freight`,
            </if>
            
            <if test="skuNo != null">
                `sku_no`,
            </if>
            
            <if test="brandNo != null">
                `brand_no`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="itemCode != null">
                `item_code`,
            </if>
            
            <if test="sizeNo != null">
                `size_no`,
            </if>
            
            <if test="itemName != null">
                `item_name`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="goodsPrice != null">
                `goods_price`,
            </if>
            
            <if test="boxNo != null">
                `box_no`,
            </if>
            
            <if test="poNo != null">
                `po_no`,
            </if>
            
            <if test="storageType != null">
                `storage_type`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="poStartDate != null">
                `po_start_date`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="sellReturnCode != null">
                `sell_return_code`,
            </if>
            
            <if test="outOrderId != null">
                `out_order_id`,
            </if>
            
            <if test="prodTotalAmt != null">
                `prod_total_amt`,
            </if>
            
            <if test="giftCardAmount != null">
                `gift_card_amount`,
            </if>
            
            <if test="promotionAmount != null">
                `promotion_amount`,
            </if>
            
            <if test="commodityType != null">
                `commodity_type`,
            </if>
            
            <if test="actualrefundamount != null">
                `actualRefundAmount`,
            </if>
            
            <if test="applyrefundamount != null">
                `applyRefundAmount`,
            </if>
            
            <if test="cpercent2 != null">
                `cpercent2`,
            </if>
            
            <if test="cpercent1 != null">
                `cpercent1`,
            </if>
            
            <if test="basescore != null">
                `basescore`,
            </if>
            
            <if test="sizeName != null">
                `size_name`,
            </if>
            
            <if test="colorName != null">
                `color_name`,
            </if>
            
            <if test="couponAmount != null">
                `coupon_amount`,
            </if>
            
            <if test="sizeCode != null">
                `size_code`,
            </if>
            
            <if test="colorCode != null">
                `color_code`,
            </if>
            
            <if test="commodityNum != null">
                `commodity_num`,
            </if>
            
            <if test="barcode != null">
                `barcode`,
            </if>
            
            <if test="newGoodsPrice != null">
                `new_goods_price`,
            </if>
            
            <if test="newTotalAmt != null">
                `new_total_amt`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="otherdeductamount != null">
                #{otherdeductamount},
            </if>
            
            <if test="schedule != null">
                #{schedule},
            </if>
            
            <if test="freight != null">
                #{freight},
            </if>
            
            <if test="skuNo != null">
                #{skuNo},
            </if>
            
            <if test="brandNo != null">
                #{brandNo},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="itemCode != null">
                #{itemCode},
            </if>
            
            <if test="sizeNo != null">
                #{sizeNo},
            </if>
            
            <if test="itemName != null">
                #{itemName},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="goodsPrice != null">
                #{goodsPrice},
            </if>
            
            <if test="boxNo != null">
                #{boxNo},
            </if>
            
            <if test="poNo != null">
                #{poNo},
            </if>
            
            <if test="storageType != null">
                #{storageType},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="poStartDate != null">
                #{poStartDate},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="sellReturnCode != null">
                #{sellReturnCode},
            </if>
            
            <if test="outOrderId != null">
                #{outOrderId},
            </if>
            
            <if test="prodTotalAmt != null">
                #{prodTotalAmt},
            </if>
            
            <if test="giftCardAmount != null">
                #{giftCardAmount},
            </if>
            
            <if test="promotionAmount != null">
                #{promotionAmount},
            </if>
            
            <if test="commodityType != null">
                #{commodityType},
            </if>
            
            <if test="actualrefundamount != null">
                #{actualrefundamount},
            </if>
            
            <if test="applyrefundamount != null">
                #{applyrefundamount},
            </if>
            
            <if test="cpercent2 != null">
                #{cpercent2},
            </if>
            
            <if test="cpercent1 != null">
                #{cpercent1},
            </if>
            
            <if test="basescore != null">
                #{basescore},
            </if>
            
            <if test="sizeName != null">
                #{sizeName},
            </if>
            
            <if test="colorName != null">
                #{colorName},
            </if>
            
            <if test="couponAmount != null">
                #{couponAmount},
            </if>
            
            <if test="sizeCode != null">
                #{sizeCode},
            </if>
            
            <if test="colorCode != null">
                #{colorCode},
            </if>
            
            <if test="commodityNum != null">
                #{commodityNum},
            </if>
            
            <if test="barcode != null">
                #{barcode},
            </if>
            
            <if test="newGoodsPrice != null">
                #{newGoodsPrice},
            </if>
            
            <if test="newTotalAmt != null">
                #{newTotalAmt},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderReturnDtl">
        UPDATE internet_order_return_dtl
        <set>
            
            <if test="otherdeductamount != null">
                `otherDeductAmount` = #{otherdeductamount},
            </if> 
            <if test="schedule != null">
                `schedule` = #{schedule},
            </if> 
            <if test="freight != null">
                `freight` = #{freight},
            </if> 
            <if test="skuNo != null">
                `sku_no` = #{skuNo},
            </if> 
            <if test="brandNo != null">
                `brand_no` = #{brandNo},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="itemCode != null">
                `item_code` = #{itemCode},
            </if> 
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},
            </if> 
            <if test="itemName != null">
                `item_name` = #{itemName},
            </if> 
            <if test="goodsPrice != null">
                `goods_price` = #{goodsPrice},
            </if> 
            <if test="boxNo != null">
                `box_no` = #{boxNo},
            </if> 
            <if test="poNo != null">
                `po_no` = #{poNo},
            </if> 
            <if test="storageType != null">
                `storage_type` = #{storageType},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="poStartDate != null">
                `po_start_date` = #{poStartDate},
            </if> 
            <if test="sellReturnCode != null">
                `sell_return_code` = #{sellReturnCode},
            </if> 
            <if test="outOrderId != null">
                `out_order_id` = #{outOrderId},
            </if> 
            <if test="prodTotalAmt != null">
                `prod_total_amt` = #{prodTotalAmt},
            </if> 
            <if test="giftCardAmount != null">
                `gift_card_amount` = #{giftCardAmount},
            </if> 
            <if test="promotionAmount != null">
                `promotion_amount` = #{promotionAmount},
            </if> 
            <if test="commodityType != null">
                `commodity_type` = #{commodityType},
            </if> 
            <if test="actualrefundamount != null">
                `actualRefundAmount` = #{actualrefundamount},
            </if> 
            <if test="applyrefundamount != null">
                `applyRefundAmount` = #{applyrefundamount},
            </if> 
            <if test="cpercent2 != null">
                `cpercent2` = #{cpercent2},
            </if> 
            <if test="cpercent1 != null">
                `cpercent1` = #{cpercent1},
            </if> 
            <if test="basescore != null">
                `basescore` = #{basescore},
            </if> 
            <if test="sizeName != null">
                `size_name` = #{sizeName},
            </if> 
            <if test="colorName != null">
                `color_name` = #{colorName},
            </if> 
            <if test="couponAmount != null">
                `coupon_amount` = #{couponAmount},
            </if> 
            <if test="sizeCode != null">
                `size_code` = #{sizeCode},
            </if> 
            <if test="colorCode != null">
                `color_code` = #{colorCode},
            </if> 
            <if test="commodityNum != null">
                `commodity_num` = #{commodityNum},
            </if> 
            <if test="barcode != null">
                `barcode` = #{barcode},
            </if> 
            <if test="newGoodsPrice != null">
                `new_goods_price` = #{newGoodsPrice},
            </if> 
            <if test="newTotalAmt != null">
                `new_total_amt` = #{newTotalAmt},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>