<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.OcsOrderSourceConfigRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig">
        <id column="id" property="id" jdbcType="CHAR"/>

        <result column="shop_name" property="shopName" jdbcType="VARCHAR"/>

        <result column="status" property="status" jdbcType="TINYINT"/>

        <result column="charge" property="charge" jdbcType="VARCHAR"/>

        <result column="charge_phone" property="chargePhone" jdbcType="VARCHAR"/>

        <result column="return_constact_phone" property="returnConstactPhone" jdbcType="VARCHAR"/>

        <result column="open_export" property="openExport" jdbcType="TINYINT"/>

        <result column="open_update_address" property="openUpdateAddress" jdbcType="TINYINT"/>

        <result column="open_intercepter" property="openIntercepter" jdbcType="TINYINT"/>

        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>

        <result column="shop_no" property="shopNo" jdbcType="VARCHAR"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="merchant_code" property="merchantCode" jdbcType="VARCHAR"/>

        <result column="channel_type" property="channelType" jdbcType="VARCHAR"/>

        <result column="interface_platform" property="interfacePlatform" jdbcType="VARCHAR"/>

        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `shop_name`,
            `status`,
            `charge`,
            `charge_phone`,
            `return_constact_phone`,
            `open_export`,
            `open_update_address`,
            `open_intercepter`,
            `create_time`,
            `create_user`,
            `shop_no`,
            `update_time`,
            `id`,
            `merchant_code`,
            `channel_type`,
            `interface_platform`,
            `update_user`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND
                    ${params.queryCondition}
            </if>

            <if test="null != params.shopName  and '' != params.shopName">
                AND `shop_name` like CONCAT(
                    '%',
                    #{params.shopName}, '%')
            </if>

            <if test="null != params.status">
                AND `status`=
                    #{params.status}
            </if>

            <if test="null != params.charge  and '' != params.charge">
                AND `charge`=#{params.charge}
            </if>

            <if test="null != params.chargePhone  and '' != params.chargePhone">
                AND `charge_phone`=#{params.chargePhone}
            </if>

            <if test="null != params.returnConstactPhone  and '' != params.returnConstactPhone">
                AND `return_constact_phone`=#{params.returnConstactPhone}
            </if>

            <if test="null != params.openExport">
                AND `open_export`=#{params.openExport}
            </if>

            <if test="null != params.openUpdateAddress">
                AND `open_update_address`=#{params.openUpdateAddress}
            </if>

            <if test="null != params.openIntercepter">
                AND `open_intercepter`=#{params.openIntercepter}
            </if>

            <if test="null != params.createTime">
                AND `create_time`=#{params.createTime}
            </if>

            <if test="null != params.createUser  and '' != params.createUser">
                AND `create_user`=#{params.createUser}
            </if>

            <if test="null != params.shopNo  and '' != params.shopNo">
                AND `shop_no`=#{params.shopNo}
            </if>

            <if test="null != params.updateTime">
                AND `update_time`=#{params.updateTime}
            </if>

            <if test="null != params.id  and '' != params.id">
                AND `id`=#{params.id}
            </if>

            <if test="null != params.merchantCode  and '' != params.merchantCode">
                AND `merchant_code`=#{params.merchantCode}
            </if>

            <if test="null != params.channelType  and '' != params.channelType">
                AND `channel_type`=#{params.channelType}
            </if>

            <if test="null != params.interfacePlatform  and '' != params.interfacePlatform">
                AND `interface_platform`=#{params.interfacePlatform}
            </if>

            <if test="null != params.updateUser  and '' != params.updateUser">
                AND `update_user`=#{params.updateUser}
            </if>
        </if>
    </sql>

    <sql id="uniqe_condition">
        <if test="null != shopNo and '' != shopNo">AND `shop_no`=
            #{shopNo}</if>
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM
            ocs_order_source_config
        WHERE
            id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM
            ocs_order_source_config
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM
            ocs_order_source_config
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as s
        FROM
            ocs_order_source_config
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM
            ocs_order_source_config
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY
                ${orderby}</if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM
            ocs_order_source_config
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY
                ${orderby}</if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM
            ocs_order_source_config
        WHERE
            id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM
            ocs_order_source_config
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM
            ocs_order_source_config
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in (
                    ${params.ids} )</if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig">
        INSERT INTO ocs_order_source_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopName != null">
                `shop_name`,</if>

            <if test="status != null">`status`,</if>

            <if test="charge != null">`charge`,</if>

            <if test="chargePhone != null">`charge_phone`,</if>

            <if test="returnConstactPhone != null">`return_constact_phone`,</if>

            <if test="openExport != null">`open_export`,</if>

            <if test="openUpdateAddress != null">`open_update_address`,</if>

            <if test="openIntercepter != null">`open_intercepter`,</if>

            <if test="createTime != null">`create_time`,</if>

            <if test="createUser != null">`create_user`,</if>

            <if test="shopNo != null">`shop_no`,</if>

            <if test="updateTime != null">`update_time`,</if>

            <if test="id != null">`id`,</if>

            <if test="merchantCode != null">`merchant_code`,</if>

            <if test="channelType != null">`channel_type`,</if>

            <if test="interfacePlatform != null">`interface_platform`,</if>

            <if test="updateUser != null">`update_user`,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="shopName != null">
                #{shopName},</if>

            <if test="status != null">#{status},</if>

            <if test="charge != null">#{charge},</if>

            <if test="chargePhone != null">#{chargePhone},</if>

            <if test="returnConstactPhone != null">#{returnConstactPhone},</if>

            <if test="openExport != null">#{openExport},</if>

            <if test="openUpdateAddress != null">#{openUpdateAddress},</if>

            <if test="openIntercepter != null">#{openIntercepter},</if>

            <if test="createTime != null">#{createTime},</if>

            <if test="createUser != null">#{createUser},</if>

            <if test="shopNo != null">#{shopNo},</if>

            <if test="updateTime != null">#{updateTime},</if>

            <if test="id != null">#{id},</if>

            <if test="merchantCode != null">#{merchantCode},</if>

            <if test="channelType != null">#{channelType},</if>

            <if test="interfacePlatform != null">#{interfacePlatform},</if>

            <if test="updateUser != null">#{updateUser},</if>
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig">
        <!-- 未实现 -->
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig">
        UPDATE ocs_order_source_config
        <set>
            <if test="shopName != null">
                `shop_name` = #{shopName},</if>
            <if test="status != null">
                `status` = #{status},</if>
            <if test="charge != null">
                `charge` = #{charge},</if>
            <if test="chargePhone != null">
                `charge_phone` = #{chargePhone},</if>
            <if test="returnConstactPhone != null">
                `return_constact_phone` = #{returnConstactPhone},
            </if>
            <if test="openExport != null">
                `open_export` = #{openExport},</if>
            <if test="openUpdateAddress != null">
                `open_update_address` = #{openUpdateAddress},</if>
            <if test="openIntercepter != null">
                `open_intercepter` = #{openIntercepter},</if>
            <if test="createTime != null">
                `create_time` = #{createTime},</if>
            <if test="createUser != null">
                `create_user` = #{createUser},</if>
            <if test="shopNo != null">
                `shop_no` = #{shopNo},</if>
            <if test="merchantCode != null">
                `merchant_code` = #{merchantCode},</if>
            <if test="channelType != null">
                `channel_type` = #{channelType},</if>
            <if test="interfacePlatform != null">
                `interface_platform` = #{interfacePlatform},</if>
            <if test="updateUser != null">
                `update_user` = #{updateUser},</if>
            update_time = now()
        </set>
        WHERE
            shop_no = #{shopNo}
            OR id = #{id}
    </update>
    <!-- auto generate end-->
</mapper>