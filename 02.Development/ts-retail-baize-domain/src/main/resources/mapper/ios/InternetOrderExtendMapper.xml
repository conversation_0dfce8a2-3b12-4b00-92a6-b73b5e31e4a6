<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetOrderExtendRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetOrderExtend">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="coupon_pref_amount5" property="couponPrefAmount5" jdbcType="DECIMAL" />
        
        <result column="payable_money" property="payableMoney" jdbcType="DECIMAL" />
        
        <result column="third_party_member" property="thirdPartyMember" jdbcType="VARCHAR" />
        
        <result column="sale_store" property="saleStore" jdbcType="VARCHAR" />
        
        <result column="sale_store_name" property="saleStoreName" jdbcType="VARCHAR" />
        
        <result column="express_code" property="expressCode" jdbcType="VARCHAR" />
        
        <result column="chain_order_no" property="chainOrderNo" jdbcType="VARCHAR" />
        
        <result column="delivery_demand" property="deliveryDemand" jdbcType="VARCHAR" />
        
        <result column="express_pro_type" property="expressProType" jdbcType="TINYINT" />
        
        <result column="fetch_code" property="fetchCode" jdbcType="VARCHAR" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="jit_warehouse" property="jitWarehouse" jdbcType="VARCHAR" />
        
        <result column="jit_warehouse_name" property="jitWarehouseName" jdbcType="VARCHAR" />
        
        <result column="warehouse_code" property="warehouseCode" jdbcType="VARCHAR" />
        
        <result column="opt_type" property="optType" jdbcType="TINYINT" />
        
        <result column="exchange_order_id" property="exchangeOrderId" jdbcType="VARCHAR" />
        
        <result column="sales_organization" property="salesOrganization" jdbcType="VARCHAR" />
        
        <result column="extra_consignee_name" property="extraConsigneeName" jdbcType="VARCHAR" />
        
        <result column="extra_mobile_phone" property="extraMobilePhone" jdbcType="VARCHAR" />
        
        <result column="extra_consignee_address" property="extraConsigneeAddress" jdbcType="VARCHAR" />
        
        <result column="handle" property="handle" jdbcType="VARCHAR" />
        
        <result column="theater_schedule" property="theaterSchedule" jdbcType="VARCHAR" />
        
        <result column="delivery_id" property="deliveryId" jdbcType="VARCHAR" />
        
        <result column="pay_method" property="payMethod" jdbcType="TINYINT" />
        
        <result column="cost_score" property="costScore" jdbcType="INTEGER" />
        
        <result column="pro_score" property="proScore" jdbcType="INTEGER" />
        
        <result column="customer_settle_price_amount" property="customerSettlePriceAmount" jdbcType="DECIMAL" />
        
        <result column="pref_amount_of_vip" property="prefAmountOfVip" jdbcType="DECIMAL" />
        
        <result column="prod_total_amount" property="prodTotalAmount" jdbcType="DECIMAL" />
        
        <result column="prod_total_tag_amount" property="prodTotalTagAmount" jdbcType="DECIMAL" />
        
        <result column="Total_pref_amount_of_pro" property="totalPrefAmountOfPro" jdbcType="DECIMAL" />
        
        <result column="order_postage_insurance" property="orderPostageInsurance" jdbcType="DECIMAL" />
        
        <result column="good_avg_amount" property="goodAvgAmount" jdbcType="DECIMAL" />
        
        <result column="good_amount" property="goodAmount" jdbcType="DECIMAL" />
        
        <result column="order_weight" property="orderWeight" jdbcType="DECIMAL" />
        
        <result column="has_invoice" property="hasInvoice" jdbcType="TINYINT" />
        
        <result column="base_score" property="baseScore" jdbcType="INTEGER" />
        
        <result column="original_order_code" property="originalOrderCode" jdbcType="VARCHAR" />
        
        <result column="coupon_pref_amount" property="couponPrefAmount" jdbcType="DECIMAL" />
        
        <result column="payment_name" property="paymentName" jdbcType="VARCHAR" />
        
        <result column="discount_amount" property="discountAmount" jdbcType="DECIMAL" />
        
        <result column="payment" property="payment" jdbcType="CHAR" />
        
        <result column="sync_discount" property="syncDiscount" jdbcType="TINYINT" />
        
        <result column="is_postage_free" property="isPostageFree" jdbcType="TINYINT" />
        
        <result column="seller_message" property="sellerMessage" jdbcType="VARCHAR" />
        
        <result column="buyer_message" property="buyerMessage" jdbcType="VARCHAR" />
        
        <result column="buyer_storeid" property="buyerStoreid" jdbcType="VARCHAR" />
        
        <result column="deal_code_list" property="dealCodeList" jdbcType="VARCHAR" />
        
        <result column="store_code" property="storeCode" jdbcType="CHAR" />
        
        <result column="consignee_name" property="consigneeName" jdbcType="VARCHAR" />
        
        <result column="package_no" property="packageNo" jdbcType="VARCHAR" />
        
        <result column="return_constact_phone" property="returnConstactPhone" jdbcType="VARCHAR" />
        
        <result column="return_address" property="returnAddress" jdbcType="VARCHAR" />
        
        <result column="return_zipcode" property="returnZipcode" jdbcType="VARCHAR" />
        
        <result column="employee_code" property="employeeCode" jdbcType="VARCHAR" />
        
        <result column="clerk_name" property="clerkName" jdbcType="VARCHAR" />
        
        <result column="clerk_number" property="clerkNumber" jdbcType="VARCHAR" />
        
        <result column="mobile_number" property="mobileNumber" jdbcType="VARCHAR" />
        
        <result column="member_id" property="memberId" jdbcType="VARCHAR" />
        
        <result column="card_no2" property="cardNo2" jdbcType="VARCHAR" />
        
        <result column="shop_card_no" property="shopCardNo" jdbcType="VARCHAR" />
        
        <result column="mobile_phone" property="mobilePhone" jdbcType="VARCHAR" />
        
        <result column="member_name" property="memberName" jdbcType="VARCHAR" />
        
        <result column="consignee_address" property="consigneeAddress" jdbcType="VARCHAR" />
        
        <result column="email" property="email" jdbcType="VARCHAR" />
        
        <result column="buyer_name" property="buyerName" jdbcType="VARCHAR" />
        
        <result column="constact_phone" property="constactPhone" jdbcType="VARCHAR" />
        
        <result column="logistics_name" property="logisticsName" jdbcType="VARCHAR" />
        
        <result column="logistics_code" property="logisticsCode" jdbcType="CHAR" />
        
        <result column="return_consignee_name" property="returnConsigneeName" jdbcType="VARCHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
        <result column="city" property="city" jdbcType="VARCHAR" />
        
        <result column="city_name" property="cityName" jdbcType="VARCHAR" />
        
        <result column="area" property="area" jdbcType="VARCHAR" />
        
        <result column="area_name" property="areaName" jdbcType="VARCHAR" />
        
        <result column="province" property="province" jdbcType="VARCHAR" />
        
        <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
        
        <result column="zipcode" property="zipcode" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `coupon_pref_amount5`,`payable_money`,`third_party_member`,`sale_store`,`sale_store_name`,`express_code`,`chain_order_no`,`delivery_demand`,`express_pro_type`,`fetch_code`,`sharding_flag`,`update_user`,`update_time`,`create_user`,`create_time`,`jit_warehouse`,`jit_warehouse_name`,`warehouse_code`,`opt_type`,`exchange_order_id`,`sales_organization`,`extra_consignee_name`,`extra_mobile_phone`,`extra_consignee_address`,`handle`,`theater_schedule`,`delivery_id`,`pay_method`,`cost_score`,`pro_score`,`customer_settle_price_amount`,`pref_amount_of_vip`,`prod_total_amount`,`prod_total_tag_amount`,`Total_pref_amount_of_pro`,`order_postage_insurance`,`good_avg_amount`,`good_amount`,`order_weight`,`has_invoice`,`base_score`,`original_order_code`,`coupon_pref_amount`,`payment_name`,`discount_amount`,`payment`,`sync_discount`,`is_postage_free`,`seller_message`,`buyer_message`,`buyer_storeid`,`deal_code_list`,`store_code`,`consignee_name`,`package_no`,`return_constact_phone`,`return_address`,`return_zipcode`,`employee_code`,`clerk_name`,`clerk_number`,`mobile_number`,`member_id`,`card_no2`,`shop_card_no`,`mobile_phone`,`member_name`,`consignee_address`,`email`,`buyer_name`,`constact_phone`,`logistics_name`,`logistics_code`,`return_consignee_name`,`id`,`order_sub_no`,`city`,`city_name`,`area`,`area_name`,`province`,`province_name`,`zipcode`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.couponPrefAmount5 ">
                
                AND `coupon_pref_amount5`=#{params.couponPrefAmount5}
                
            </if>
            
            <if test="null!=params.payableMoney ">
                
                AND `payable_money`=#{params.payableMoney}
                
            </if>
            
            <if test="null!=params.thirdPartyMember  and ''!=params.thirdPartyMember ">
                
                AND `third_party_member`=#{params.thirdPartyMember}
                
            </if>
            
            <if test="null!=params.saleStore  and ''!=params.saleStore ">
                
                AND `sale_store`=#{params.saleStore}
                
            </if>
            
            <if test="null!=params.saleStoreName  and ''!=params.saleStoreName ">
                
                AND `sale_store_name` like CONCAT('%',#{params.saleStoreName},'%') 
                
            </if>
            
            <if test="null!=params.expressCode  and ''!=params.expressCode ">
                
                AND `express_code`=#{params.expressCode}
                
            </if>
            
            <if test="null!=params.chainOrderNo  and ''!=params.chainOrderNo ">
                
                AND `chain_order_no`=#{params.chainOrderNo}
                
            </if>
            
            <if test="null!=params.deliveryDemand  and ''!=params.deliveryDemand ">
                
                AND `delivery_demand`=#{params.deliveryDemand}
                
            </if>
            
            <if test="null!=params.expressProType ">
                
                AND `express_pro_type`=#{params.expressProType}
                
            </if>
            
            <if test="null!=params.fetchCode  and ''!=params.fetchCode ">
                
                AND `fetch_code`=#{params.fetchCode}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.updateUser  and ''!=params.updateUser ">
                
                AND `update_user`=#{params.updateUser}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.jitWarehouse  and ''!=params.jitWarehouse ">
                
                AND `jit_warehouse`=#{params.jitWarehouse}
                
            </if>
            
            <if test="null!=params.jitWarehouseName  and ''!=params.jitWarehouseName ">
                
                AND `jit_warehouse_name` like CONCAT('%',#{params.jitWarehouseName},'%') 
                
            </if>
            
            <if test="null!=params.warehouseCode  and ''!=params.warehouseCode ">
                
                AND `warehouse_code`=#{params.warehouseCode}
                
            </if>
            
            <if test="null!=params.optType ">
                
                AND `opt_type`=#{params.optType}
                
            </if>
            
            <if test="null!=params.exchangeOrderId  and ''!=params.exchangeOrderId ">
                
                AND `exchange_order_id`=#{params.exchangeOrderId}
                
            </if>
            
            <if test="null!=params.salesOrganization  and ''!=params.salesOrganization ">
                
                AND `sales_organization`=#{params.salesOrganization}
                
            </if>
            
            <if test="null!=params.extraConsigneeName  and ''!=params.extraConsigneeName ">
                
                AND `extra_consignee_name` like CONCAT('%',#{params.extraConsigneeName},'%') 
                
            </if>
            
            <if test="null!=params.extraMobilePhone  and ''!=params.extraMobilePhone ">
                
                AND `extra_mobile_phone`=#{params.extraMobilePhone}
                
            </if>
            
            <if test="null!=params.extraConsigneeAddress  and ''!=params.extraConsigneeAddress ">
                
                AND `extra_consignee_address`=#{params.extraConsigneeAddress}
                
            </if>
            
            <if test="null!=params.handle  and ''!=params.handle ">
                
                AND `handle`=#{params.handle}
                
            </if>
            
            <if test="null!=params.theaterSchedule  and ''!=params.theaterSchedule ">
                
                AND `theater_schedule`=#{params.theaterSchedule}
                
            </if>
            
            <if test="null!=params.deliveryId  and ''!=params.deliveryId ">
                
                AND `delivery_id`=#{params.deliveryId}
                
            </if>
            
            <if test="null!=params.payMethod ">
                
                AND `pay_method`=#{params.payMethod}
                
            </if>
            
            <if test="null!=params.costScore ">
                
                AND `cost_score`=#{params.costScore}
                
            </if>
            
            <if test="null!=params.proScore ">
                
                AND `pro_score`=#{params.proScore}
                
            </if>
            
            <if test="null!=params.customerSettlePriceAmount ">
                
                AND `customer_settle_price_amount`=#{params.customerSettlePriceAmount}
                
            </if>
            
            <if test="null!=params.prefAmountOfVip ">
                
                AND `pref_amount_of_vip`=#{params.prefAmountOfVip}
                
            </if>
            
            <if test="null!=params.prodTotalAmount ">
                
                AND `prod_total_amount`=#{params.prodTotalAmount}
                
            </if>
            
            <if test="null!=params.prodTotalTagAmount ">
                
                AND `prod_total_tag_amount`=#{params.prodTotalTagAmount}
                
            </if>
            
            <if test="null!=params.totalPrefAmountOfPro ">
                
                AND `Total_pref_amount_of_pro`=#{params.totalPrefAmountOfPro}
                
            </if>
            
            <if test="null!=params.orderPostageInsurance ">
                
                AND `order_postage_insurance`=#{params.orderPostageInsurance}
                
            </if>
            
            <if test="null!=params.goodAvgAmount ">
                
                AND `good_avg_amount`=#{params.goodAvgAmount}
                
            </if>
            
            <if test="null!=params.goodAmount ">
                
                AND `good_amount`=#{params.goodAmount}
                
            </if>
            
            <if test="null!=params.orderWeight ">
                
                AND `order_weight`=#{params.orderWeight}
                
            </if>
            
            <if test="null!=params.hasInvoice ">
                
                AND `has_invoice`=#{params.hasInvoice}
                
            </if>
            
            <if test="null!=params.baseScore ">
                
                AND `base_score`=#{params.baseScore}
                
            </if>
            
            <if test="null!=params.originalOrderCode  and ''!=params.originalOrderCode ">
                
                AND `original_order_code`=#{params.originalOrderCode}
                
            </if>
            
            <if test="null!=params.couponPrefAmount ">
                
                AND `coupon_pref_amount`=#{params.couponPrefAmount}
                
            </if>
            
            <if test="null!=params.paymentName  and ''!=params.paymentName ">
                
                AND `payment_name` like CONCAT('%',#{params.paymentName},'%') 
                
            </if>
            
            <if test="null!=params.discountAmount ">
                
                AND `discount_amount`=#{params.discountAmount}
                
            </if>
            
            <if test="null!=params.payment  and ''!=params.payment ">
                
                AND `payment`=#{params.payment}
                
            </if>
            
            <if test="null!=params.syncDiscount ">
                
                AND `sync_discount`=#{params.syncDiscount}
                
            </if>
            
            <if test="null!=params.isPostageFree ">
                
                AND `is_postage_free`=#{params.isPostageFree}
                
            </if>
            
            <if test="null!=params.sellerMessage  and ''!=params.sellerMessage ">
                
                AND `seller_message`=#{params.sellerMessage}
                
            </if>
            
            <if test="null!=params.buyerMessage  and ''!=params.buyerMessage ">
                
                AND `buyer_message`=#{params.buyerMessage}
                
            </if>
            
            <if test="null!=params.buyerStoreid  and ''!=params.buyerStoreid ">
                
                AND `buyer_storeid`=#{params.buyerStoreid}
                
            </if>
            
            <if test="null!=params.dealCodeList  and ''!=params.dealCodeList ">
                
                AND `deal_code_list`=#{params.dealCodeList}
                
            </if>
            
            <if test="null!=params.storeCode  and ''!=params.storeCode ">
                
                AND `store_code`=#{params.storeCode}
                
            </if>
            
            <if test="null!=params.consigneeName  and ''!=params.consigneeName ">
                
                AND `consignee_name` like CONCAT('%',#{params.consigneeName},'%') 
                
            </if>
            
            <if test="null!=params.packageNo  and ''!=params.packageNo ">
                
                AND `package_no`=#{params.packageNo}
                
            </if>
            
            <if test="null!=params.returnConstactPhone  and ''!=params.returnConstactPhone ">
                
                AND `return_constact_phone`=#{params.returnConstactPhone}
                
            </if>
            
            <if test="null!=params.returnAddress  and ''!=params.returnAddress ">
                
                AND `return_address`=#{params.returnAddress}
                
            </if>
            
            <if test="null!=params.returnZipcode  and ''!=params.returnZipcode ">
                
                AND `return_zipcode`=#{params.returnZipcode}
                
            </if>
            
            <if test="null!=params.employeeCode  and ''!=params.employeeCode ">
                
                AND `employee_code`=#{params.employeeCode}
                
            </if>
            
            <if test="null!=params.clerkName  and ''!=params.clerkName ">
                
                AND `clerk_name` like CONCAT('%',#{params.clerkName},'%') 
                
            </if>
            
            <if test="null!=params.clerkNumber  and ''!=params.clerkNumber ">
                
                AND `clerk_number`=#{params.clerkNumber}
                
            </if>
            
            <if test="null!=params.mobileNumber  and ''!=params.mobileNumber ">
                
                AND `mobile_number`=#{params.mobileNumber}
                
            </if>
            
            <if test="null!=params.memberId  and ''!=params.memberId ">
                
                AND `member_id`=#{params.memberId}
                
            </if>
            
            <if test="null!=params.cardNo2  and ''!=params.cardNo2 ">
                
                AND `card_no2`=#{params.cardNo2}
                
            </if>
            
            <if test="null!=params.shopCardNo  and ''!=params.shopCardNo ">
                
                AND `shop_card_no`=#{params.shopCardNo}
                
            </if>
            
            <if test="null!=params.mobilePhone  and ''!=params.mobilePhone ">
                
                AND `mobile_phone`=#{params.mobilePhone}
                
            </if>
            
            <if test="null!=params.memberName  and ''!=params.memberName ">
                
                AND `member_name` like CONCAT('%',#{params.memberName},'%') 
                
            </if>
            
            <if test="null!=params.consigneeAddress  and ''!=params.consigneeAddress ">
                
                AND `consignee_address`=#{params.consigneeAddress}
                
            </if>
            
            <if test="null!=params.email  and ''!=params.email ">
                
                AND `email`=#{params.email}
                
            </if>
            
            <if test="null!=params.buyerName  and ''!=params.buyerName ">
                
                AND `buyer_name` like CONCAT('%',#{params.buyerName},'%') 
                
            </if>
            
            <if test="null!=params.constactPhone  and ''!=params.constactPhone ">
                
                AND `constact_phone`=#{params.constactPhone}
                
            </if>
            
            <if test="null!=params.logisticsName  and ''!=params.logisticsName ">
                
                AND `logistics_name` like CONCAT('%',#{params.logisticsName},'%') 
                
            </if>
            
            <if test="null!=params.logisticsCode  and ''!=params.logisticsCode ">
                
                AND `logistics_code`=#{params.logisticsCode}
                
            </if>
            
            <if test="null!=params.returnConsigneeName  and ''!=params.returnConsigneeName ">
                
                AND `return_consignee_name` like CONCAT('%',#{params.returnConsigneeName},'%') 
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
            <if test="null!=params.city  and ''!=params.city ">
                
                AND `city`=#{params.city}
                
            </if>
            
            <if test="null!=params.cityName  and ''!=params.cityName ">
                
                AND `city_name` like CONCAT('%',#{params.cityName},'%') 
                
            </if>
            
            <if test="null!=params.area  and ''!=params.area ">
                
                AND `area`=#{params.area}
                
            </if>
            
            <if test="null!=params.areaName  and ''!=params.areaName ">
                
                AND `area_name` like CONCAT('%',#{params.areaName},'%') 
                
            </if>
            
            <if test="null!=params.province  and ''!=params.province ">
                
                AND `province`=#{params.province}
                
            </if>
            
            <if test="null!=params.provinceName  and ''!=params.provinceName ">
                
                AND `province_name` like CONCAT('%',#{params.provinceName},'%') 
                
            </if>
            
            <if test="null!=params.zipcode  and ''!=params.zipcode ">
                
                AND `zipcode`=#{params.zipcode}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=orderSubNo and ''!=orderSubNo">
            AND `order_sub_no`=#{orderSubNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_extend
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_extend
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_extend
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_order_extend
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_order_extend
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_extend
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_order_extend
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_order_extend
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_order_extend
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderExtend"  >
        INSERT INTO internet_order_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="couponPrefAmount5 != null">
                `coupon_pref_amount5`,
            </if>
            
            <if test="payableMoney != null">
                `payable_money`,
            </if>
            
            <if test="thirdPartyMember != null">
                `third_party_member`,
            </if>
            
            <if test="saleStore != null">
                `sale_store`,
            </if>
            
            <if test="saleStoreName != null">
                `sale_store_name`,
            </if>
            
            <if test="expressCode != null">
                `express_code`,
            </if>
            
            <if test="chainOrderNo != null">
                `chain_order_no`,
            </if>
            
            <if test="deliveryDemand != null">
                `delivery_demand`,
            </if>
            
            <if test="expressProType != null">
                `express_pro_type`,
            </if>
            
            <if test="fetchCode != null">
                `fetch_code`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="updateUser != null">
                `update_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="jitWarehouse != null">
                `jit_warehouse`,
            </if>
            
            <if test="jitWarehouseName != null">
                `jit_warehouse_name`,
            </if>
            
            <if test="warehouseCode != null">
                `warehouse_code`,
            </if>
            
            <if test="optType != null">
                `opt_type`,
            </if>
            
            <if test="exchangeOrderId != null">
                `exchange_order_id`,
            </if>
            
            <if test="salesOrganization != null">
                `sales_organization`,
            </if>
            
            <if test="extraConsigneeName != null">
                `extra_consignee_name`,
            </if>
            
            <if test="extraMobilePhone != null">
                `extra_mobile_phone`,
            </if>
            
            <if test="extraConsigneeAddress != null">
                `extra_consignee_address`,
            </if>
            
            <if test="handle != null">
                `handle`,
            </if>
            
            <if test="theaterSchedule != null">
                `theater_schedule`,
            </if>
            
            <if test="deliveryId != null">
                `delivery_id`,
            </if>
            
            <if test="payMethod != null">
                `pay_method`,
            </if>
            
            <if test="costScore != null">
                `cost_score`,
            </if>
            
            <if test="proScore != null">
                `pro_score`,
            </if>
            
            <if test="customerSettlePriceAmount != null">
                `customer_settle_price_amount`,
            </if>
            
            <if test="prefAmountOfVip != null">
                `pref_amount_of_vip`,
            </if>
            
            <if test="prodTotalAmount != null">
                `prod_total_amount`,
            </if>
            
            <if test="prodTotalTagAmount != null">
                `prod_total_tag_amount`,
            </if>
            
            <if test="totalPrefAmountOfPro != null">
                `Total_pref_amount_of_pro`,
            </if>
            
            <if test="orderPostageInsurance != null">
                `order_postage_insurance`,
            </if>
            
            <if test="goodAvgAmount != null">
                `good_avg_amount`,
            </if>
            
            <if test="goodAmount != null">
                `good_amount`,
            </if>
            
            <if test="orderWeight != null">
                `order_weight`,
            </if>
            
            <if test="hasInvoice != null">
                `has_invoice`,
            </if>
            
            <if test="baseScore != null">
                `base_score`,
            </if>
            
            <if test="originalOrderCode != null">
                `original_order_code`,
            </if>
            
            <if test="couponPrefAmount != null">
                `coupon_pref_amount`,
            </if>
            
            <if test="paymentName != null">
                `payment_name`,
            </if>
            
            <if test="discountAmount != null">
                `discount_amount`,
            </if>
            
            <if test="payment != null">
                `payment`,
            </if>
            
            <if test="syncDiscount != null">
                `sync_discount`,
            </if>
            
            <if test="isPostageFree != null">
                `is_postage_free`,
            </if>
            
            <if test="sellerMessage != null">
                `seller_message`,
            </if>
            
            <if test="buyerMessage != null">
                `buyer_message`,
            </if>
            
            <if test="buyerStoreid != null">
                `buyer_storeid`,
            </if>
            
            <if test="dealCodeList != null">
                `deal_code_list`,
            </if>
            
            <if test="storeCode != null">
                `store_code`,
            </if>
            
            <if test="consigneeName != null">
                `consignee_name`,
            </if>
            
            <if test="packageNo != null">
                `package_no`,
            </if>
            
            <if test="returnConstactPhone != null">
                `return_constact_phone`,
            </if>
            
            <if test="returnAddress != null">
                `return_address`,
            </if>
            
            <if test="returnZipcode != null">
                `return_zipcode`,
            </if>
            
            <if test="employeeCode != null">
                `employee_code`,
            </if>
            
            <if test="clerkName != null">
                `clerk_name`,
            </if>
            
            <if test="clerkNumber != null">
                `clerk_number`,
            </if>
            
            <if test="mobileNumber != null">
                `mobile_number`,
            </if>
            
            <if test="memberId != null">
                `member_id`,
            </if>
            
            <if test="cardNo2 != null">
                `card_no2`,
            </if>
            
            <if test="shopCardNo != null">
                `shop_card_no`,
            </if>
            
            <if test="mobilePhone != null">
                `mobile_phone`,
            </if>
            
            <if test="memberName != null">
                `member_name`,
            </if>
            
            <if test="consigneeAddress != null">
                `consignee_address`,
            </if>
            
            <if test="email != null">
                `email`,
            </if>
            
            <if test="buyerName != null">
                `buyer_name`,
            </if>
            
            <if test="constactPhone != null">
                `constact_phone`,
            </if>
            
            <if test="logisticsName != null">
                `logistics_name`,
            </if>
            
            <if test="logisticsCode != null">
                `logistics_code`,
            </if>
            
            <if test="returnConsigneeName != null">
                `return_consignee_name`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="city != null">
                `city`,
            </if>
            
            <if test="cityName != null">
                `city_name`,
            </if>
            
            <if test="area != null">
                `area`,
            </if>
            
            <if test="areaName != null">
                `area_name`,
            </if>
            
            <if test="province != null">
                `province`,
            </if>
            
            <if test="provinceName != null">
                `province_name`,
            </if>
            
            <if test="zipcode != null">
                `zipcode`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="couponPrefAmount5 != null">
                #{couponPrefAmount5},
            </if>
            
            <if test="payableMoney != null">
                #{payableMoney},
            </if>
            
            <if test="thirdPartyMember != null">
                #{thirdPartyMember},
            </if>
            
            <if test="saleStore != null">
                #{saleStore},
            </if>
            
            <if test="saleStoreName != null">
                #{saleStoreName},
            </if>
            
            <if test="expressCode != null">
                #{expressCode},
            </if>
            
            <if test="chainOrderNo != null">
                #{chainOrderNo},
            </if>
            
            <if test="deliveryDemand != null">
                #{deliveryDemand},
            </if>
            
            <if test="expressProType != null">
                #{expressProType},
            </if>
            
            <if test="fetchCode != null">
                #{fetchCode},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="updateUser != null">
                #{updateUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="jitWarehouse != null">
                #{jitWarehouse},
            </if>
            
            <if test="jitWarehouseName != null">
                #{jitWarehouseName},
            </if>
            
            <if test="warehouseCode != null">
                #{warehouseCode},
            </if>
            
            <if test="optType != null">
                #{optType},
            </if>
            
            <if test="exchangeOrderId != null">
                #{exchangeOrderId},
            </if>
            
            <if test="salesOrganization != null">
                #{salesOrganization},
            </if>
            
            <if test="extraConsigneeName != null">
                #{extraConsigneeName},
            </if>
            
            <if test="extraMobilePhone != null">
                #{extraMobilePhone},
            </if>
            
            <if test="extraConsigneeAddress != null">
                #{extraConsigneeAddress},
            </if>
            
            <if test="handle != null">
                #{handle},
            </if>
            
            <if test="theaterSchedule != null">
                #{theaterSchedule},
            </if>
            
            <if test="deliveryId != null">
                #{deliveryId},
            </if>
            
            <if test="payMethod != null">
                #{payMethod},
            </if>
            
            <if test="costScore != null">
                #{costScore},
            </if>
            
            <if test="proScore != null">
                #{proScore},
            </if>
            
            <if test="customerSettlePriceAmount != null">
                #{customerSettlePriceAmount},
            </if>
            
            <if test="prefAmountOfVip != null">
                #{prefAmountOfVip},
            </if>
            
            <if test="prodTotalAmount != null">
                #{prodTotalAmount},
            </if>
            
            <if test="prodTotalTagAmount != null">
                #{prodTotalTagAmount},
            </if>
            
            <if test="totalPrefAmountOfPro != null">
                #{totalPrefAmountOfPro},
            </if>
            
            <if test="orderPostageInsurance != null">
                #{orderPostageInsurance},
            </if>
            
            <if test="goodAvgAmount != null">
                #{goodAvgAmount},
            </if>
            
            <if test="goodAmount != null">
                #{goodAmount},
            </if>
            
            <if test="orderWeight != null">
                #{orderWeight},
            </if>
            
            <if test="hasInvoice != null">
                #{hasInvoice},
            </if>
            
            <if test="baseScore != null">
                #{baseScore},
            </if>
            
            <if test="originalOrderCode != null">
                #{originalOrderCode},
            </if>
            
            <if test="couponPrefAmount != null">
                #{couponPrefAmount},
            </if>
            
            <if test="paymentName != null">
                #{paymentName},
            </if>
            
            <if test="discountAmount != null">
                #{discountAmount},
            </if>
            
            <if test="payment != null">
                #{payment},
            </if>
            
            <if test="syncDiscount != null">
                #{syncDiscount},
            </if>
            
            <if test="isPostageFree != null">
                #{isPostageFree},
            </if>
            
            <if test="sellerMessage != null">
                #{sellerMessage},
            </if>
            
            <if test="buyerMessage != null">
                #{buyerMessage},
            </if>
            
            <if test="buyerStoreid != null">
                #{buyerStoreid},
            </if>
            
            <if test="dealCodeList != null">
                #{dealCodeList},
            </if>
            
            <if test="storeCode != null">
                #{storeCode},
            </if>
            
            <if test="consigneeName != null">
                #{consigneeName},
            </if>
            
            <if test="packageNo != null">
                #{packageNo},
            </if>
            
            <if test="returnConstactPhone != null">
                #{returnConstactPhone},
            </if>
            
            <if test="returnAddress != null">
                #{returnAddress},
            </if>
            
            <if test="returnZipcode != null">
                #{returnZipcode},
            </if>
            
            <if test="employeeCode != null">
                #{employeeCode},
            </if>
            
            <if test="clerkName != null">
                #{clerkName},
            </if>
            
            <if test="clerkNumber != null">
                #{clerkNumber},
            </if>
            
            <if test="mobileNumber != null">
                #{mobileNumber},
            </if>
            
            <if test="memberId != null">
                #{memberId},
            </if>
            
            <if test="cardNo2 != null">
                #{cardNo2},
            </if>
            
            <if test="shopCardNo != null">
                #{shopCardNo},
            </if>
            
            <if test="mobilePhone != null">
                #{mobilePhone},
            </if>
            
            <if test="memberName != null">
                #{memberName},
            </if>
            
            <if test="consigneeAddress != null">
                #{consigneeAddress},
            </if>
            
            <if test="email != null">
                #{email},
            </if>
            
            <if test="buyerName != null">
                #{buyerName},
            </if>
            
            <if test="constactPhone != null">
                #{constactPhone},
            </if>
            
            <if test="logisticsName != null">
                #{logisticsName},
            </if>
            
            <if test="logisticsCode != null">
                #{logisticsCode},
            </if>
            
            <if test="returnConsigneeName != null">
                #{returnConsigneeName},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="city != null">
                #{city},
            </if>
            
            <if test="cityName != null">
                #{cityName},
            </if>
            
            <if test="area != null">
                #{area},
            </if>
            
            <if test="areaName != null">
                #{areaName},
            </if>
            
            <if test="province != null">
                #{province},
            </if>
            
            <if test="provinceName != null">
                #{provinceName},
            </if>
            
            <if test="zipcode != null">
                #{zipcode},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderExtend"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderExtend">
        UPDATE internet_order_extend
        <set>
            
            <if test="couponPrefAmount5 != null">
                `coupon_pref_amount5` = #{couponPrefAmount5},
            </if> 
            <if test="payableMoney != null">
                `payable_money` = #{payableMoney},
            </if> 
            <if test="thirdPartyMember != null">
                `third_party_member` = #{thirdPartyMember},
            </if> 
            <if test="saleStore != null">
                `sale_store` = #{saleStore},
            </if> 
            <if test="saleStoreName != null">
                `sale_store_name` = #{saleStoreName},
            </if> 
            <if test="expressCode != null">
                `express_code` = #{expressCode},
            </if> 
            <if test="chainOrderNo != null">
                `chain_order_no` = #{chainOrderNo},
            </if> 
            <if test="deliveryDemand != null">
                `delivery_demand` = #{deliveryDemand},
            </if> 
            <if test="expressProType != null">
                `express_pro_type` = #{expressProType},
            </if> 
            <if test="fetchCode != null">
                `fetch_code` = #{fetchCode},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="updateUser != null">
                `update_user` = #{updateUser},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="jitWarehouse != null">
                `jit_warehouse` = #{jitWarehouse},
            </if> 
            <if test="jitWarehouseName != null">
                `jit_warehouse_name` = #{jitWarehouseName},
            </if> 
            <if test="warehouseCode != null">
                `warehouse_code` = #{warehouseCode},
            </if> 
            <if test="optType != null">
                `opt_type` = #{optType},
            </if> 
            <if test="exchangeOrderId != null">
                `exchange_order_id` = #{exchangeOrderId},
            </if> 
            <if test="salesOrganization != null">
                `sales_organization` = #{salesOrganization},
            </if> 
            <if test="extraConsigneeName != null">
                `extra_consignee_name` = #{extraConsigneeName},
            </if> 
            <if test="extraMobilePhone != null">
                `extra_mobile_phone` = #{extraMobilePhone},
            </if> 
            <if test="extraConsigneeAddress != null">
                `extra_consignee_address` = #{extraConsigneeAddress},
            </if> 
            <if test="handle != null">
                `handle` = #{handle},
            </if> 
            <if test="theaterSchedule != null">
                `theater_schedule` = #{theaterSchedule},
            </if> 
            <if test="deliveryId != null">
                `delivery_id` = #{deliveryId},
            </if> 
            <if test="payMethod != null">
                `pay_method` = #{payMethod},
            </if> 
            <if test="costScore != null">
                `cost_score` = #{costScore},
            </if> 
            <if test="proScore != null">
                `pro_score` = #{proScore},
            </if> 
            <if test="customerSettlePriceAmount != null">
                `customer_settle_price_amount` = #{customerSettlePriceAmount},
            </if> 
            <if test="prefAmountOfVip != null">
                `pref_amount_of_vip` = #{prefAmountOfVip},
            </if> 
            <if test="prodTotalAmount != null">
                `prod_total_amount` = #{prodTotalAmount},
            </if> 
            <if test="prodTotalTagAmount != null">
                `prod_total_tag_amount` = #{prodTotalTagAmount},
            </if> 
            <if test="totalPrefAmountOfPro != null">
                `Total_pref_amount_of_pro` = #{totalPrefAmountOfPro},
            </if> 
            <if test="orderPostageInsurance != null">
                `order_postage_insurance` = #{orderPostageInsurance},
            </if> 
            <if test="goodAvgAmount != null">
                `good_avg_amount` = #{goodAvgAmount},
            </if> 
            <if test="goodAmount != null">
                `good_amount` = #{goodAmount},
            </if> 
            <if test="orderWeight != null">
                `order_weight` = #{orderWeight},
            </if> 
            <if test="hasInvoice != null">
                `has_invoice` = #{hasInvoice},
            </if> 
            <if test="baseScore != null">
                `base_score` = #{baseScore},
            </if> 
            <if test="originalOrderCode != null">
                `original_order_code` = #{originalOrderCode},
            </if> 
            <if test="couponPrefAmount != null">
                `coupon_pref_amount` = #{couponPrefAmount},
            </if> 
            <if test="paymentName != null">
                `payment_name` = #{paymentName},
            </if> 
            <if test="discountAmount != null">
                `discount_amount` = #{discountAmount},
            </if> 
            <if test="payment != null">
                `payment` = #{payment},
            </if> 
            <if test="syncDiscount != null">
                `sync_discount` = #{syncDiscount},
            </if> 
            <if test="isPostageFree != null">
                `is_postage_free` = #{isPostageFree},
            </if> 
            <if test="sellerMessage != null">
                `seller_message` = #{sellerMessage},
            </if> 
            <if test="buyerMessage != null">
                `buyer_message` = #{buyerMessage},
            </if> 
            <if test="buyerStoreid != null">
                `buyer_storeid` = #{buyerStoreid},
            </if> 
            <if test="dealCodeList != null">
                `deal_code_list` = #{dealCodeList},
            </if> 
            <if test="storeCode != null">
                `store_code` = #{storeCode},
            </if> 
            <if test="consigneeName != null">
                `consignee_name` = #{consigneeName},
            </if> 
            <if test="packageNo != null">
                `package_no` = #{packageNo},
            </if> 
            <if test="returnConstactPhone != null">
                `return_constact_phone` = #{returnConstactPhone},
            </if> 
            <if test="returnAddress != null">
                `return_address` = #{returnAddress},
            </if> 
            <if test="returnZipcode != null">
                `return_zipcode` = #{returnZipcode},
            </if> 
            <if test="employeeCode != null">
                `employee_code` = #{employeeCode},
            </if> 
            <if test="clerkName != null">
                `clerk_name` = #{clerkName},
            </if> 
            <if test="clerkNumber != null">
                `clerk_number` = #{clerkNumber},
            </if> 
            <if test="mobileNumber != null">
                `mobile_number` = #{mobileNumber},
            </if> 
            <if test="memberId != null">
                `member_id` = #{memberId},
            </if> 
            <if test="cardNo2 != null">
                `card_no2` = #{cardNo2},
            </if> 
            <if test="shopCardNo != null">
                `shop_card_no` = #{shopCardNo},
            </if> 
            <if test="mobilePhone != null">
                `mobile_phone` = #{mobilePhone},
            </if> 
            <if test="memberName != null">
                `member_name` = #{memberName},
            </if> 
            <if test="consigneeAddress != null">
                `consignee_address` = #{consigneeAddress},
            </if> 
            <if test="email != null">
                `email` = #{email},
            </if> 
            <if test="buyerName != null">
                `buyer_name` = #{buyerName},
            </if> 
            <if test="constactPhone != null">
                `constact_phone` = #{constactPhone},
            </if> 
            <if test="logisticsName != null">
                `logistics_name` = #{logisticsName},
            </if> 
            <if test="logisticsCode != null">
                `logistics_code` = #{logisticsCode},
            </if> 
            <if test="returnConsigneeName != null">
                `return_consignee_name` = #{returnConsigneeName},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="city != null">
                `city` = #{city},
            </if> 
            <if test="cityName != null">
                `city_name` = #{cityName},
            </if> 
            <if test="area != null">
                `area` = #{area},
            </if> 
            <if test="areaName != null">
                `area_name` = #{areaName},
            </if> 
            <if test="province != null">
                `province` = #{province},
            </if> 
            <if test="provinceName != null">
                `province_name` = #{provinceName},
            </if> 
            <if test="zipcode != null">
                `zipcode` = #{zipcode},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id} OR order_sub_no = #{orderSubNo}
    </update>
        <!-- auto generate end-->


</mapper>