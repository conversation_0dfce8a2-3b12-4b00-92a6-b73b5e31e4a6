<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.RetailOrderOutDtlRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl">
        <id column="id" property="id" jdbcType="CHAR"/>


        <result column="quote_price" property="quotePrice" jdbcType="DECIMAL"/>

        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>

        <result column="logistics_company_name" property="logisticsCompanyName" jdbcType="VARCHAR"/>

        <result column="logistic_company_code" property="logisticCompanyCode" jdbcType="VARCHAR"/>

        <result column="express_no" property="expressNo" jdbcType="VARCHAR"/>

        <result column="box_no" property="boxNo" jdbcType="VARCHAR"/>

        <result column="retail_flag" property="retailFlag" jdbcType="CHAR"/>

        <result column="image_remark" property="imageRemark" jdbcType="VARCHAR"/>

        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>

        <result column="pos_dtl_id" property="posDtlId" jdbcType="CHAR"/>

        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR"/>

        <result column="remark" property="remark" jdbcType="VARCHAR"/>

        <result column="bill_no" property="billNo" jdbcType="CHAR"/>

        <result column="sku_no" property="skuNo" jdbcType="CHAR"/>

        <result column="item_no" property="itemNo" jdbcType="CHAR"/>

        <result column="barcode" property="barcode" jdbcType="VARCHAR"/>

        <result column="item_code" property="itemCode" jdbcType="VARCHAR"/>

        <result column="item_name" property="itemName" jdbcType="VARCHAR"/>

        <result column="item_name_forshow" property="itemNameForshow" jdbcType="VARCHAR"/>

        <result column="color_no" property="colorNo" jdbcType="CHAR"/>

        <result column="color_name" property="colorName" jdbcType="VARCHAR"/>

        <result column="brand_no" property="brandNo" jdbcType="CHAR"/>

        <result column="brand_name" property="brandName" jdbcType="VARCHAR"/>

        <result column="category_no" property="categoryNo" jdbcType="CHAR"/>

        <result column="size_no" property="sizeNo" jdbcType="VARCHAR"/>

        <result column="size_kind" property="sizeKind" jdbcType="CHAR"/>

        <result column="commodity_specification_str" property="commoditySpecificationStr" jdbcType="VARCHAR"/>

        <result column="discount" property="discount" jdbcType="DECIMAL"/>

        <result column="ask_qty" property="askQty" jdbcType="INTEGER"/>

        <result column="send_out_qty" property="sendOutQty" jdbcType="INTEGER"/>

        <result column="commodity_type" property="commodityType" jdbcType="TINYINT"/>

        <result column="sale_price" property="salePrice" jdbcType="DECIMAL"/>

        <association property="retailOrderOut" javaType="cn.wonhigh.baize.model.entity.ios.RetailOrderOut"
                     columnPrefix="roo_"
                     resultMap="cn.wonhigh.baize.repository.ios.RetailOrderOutRepository.baseResultMap"/>

        <association property="internetOrder" javaType="cn.wonhigh.baize.model.entity.ios.InternetOrder"
                     columnPrefix="io_"
                     resultMap="cn.wonhigh.baize.repository.ios.InternetOrderRepository.baseResultMap"/>
    </resultMap>

    <sql id="column_list">
        `quote_price`,
            `update_time`,
            `logistics_company_name`,
            `logistic_company_code`,
            `express_no`,
            `box_no`,
            `retail_flag`,
            `image_remark`,
            `image_url`,
            `pos_dtl_id`,
            `sharding_flag`,
            `remark`,
            `id`,
            `bill_no`,
            `sku_no`,
            `item_no`,
            `barcode`,
            `item_code`,
            `item_name`,
            `item_name_forshow`,
            `color_no`,
            `color_name`,
            `brand_no`,
            `brand_name`,
            `category_no`,
            `size_no`,
            `size_kind`,
            `commodity_specification_str`,
            `discount`,
            `ask_qty`,
            `send_out_qty`,
            `commodity_type`,
            `sale_price`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND
                    ${params.queryCondition}
            </if>

            <if test="null != params.quotePrice">
                AND `quote_price`=
                    #{params.quotePrice}
            </if>

            <if test="null != params.updateTime">AND `update_time`=#{params.updateTime}</if>

            <if test="null != params.logisticsCompanyName  and '' != params.logisticsCompanyName">
                AND `logistics_company_name` like CONCAT('%',
                    #{params.logisticsCompanyName}, '%')
            </if>

            <if test="null != params.logisticCompanyCode  and '' != params.logisticCompanyCode">
                AND `logistic_company_code`=
                    #{params.logisticCompanyCode}
            </if>

            <if test="null != params.expressNo  and '' != params.expressNo">
                AND `express_no`=#{params.expressNo}
            </if>

            <if test="null != params.boxNo  and '' != params.boxNo">AND `box_no`=#{params.boxNo}</if>

            <if test="null != params.retailFlag  and '' != params.retailFlag">
                AND `retail_flag`=#{params.retailFlag}
            </if>

            <if test="null != params.imageRemark  and '' != params.imageRemark">
                AND `image_remark`=#{params.imageRemark}
            </if>

            <if test="null != params.imageUrl  and '' != params.imageUrl">
                AND `image_url`=#{params.imageUrl}
            </if>

            <if test="null != params.posDtlId  and '' != params.posDtlId">
                AND `pos_dtl_id`=#{params.posDtlId}
            </if>

            <if test="null != params.shardingFlag  and '' != params.shardingFlag">
                AND `sharding_flag`=#{params.shardingFlag}
            </if>

            <if test="null != params.remark  and '' != params.remark">
                AND `remark`=#{params.remark}
            </if>

            <if test="null != params.id  and '' != params.id">AND `id`=#{params.id}</if>

            <if test="null != params.billNo  and '' != params.billNo">
                AND `bill_no`=#{params.billNo}
            </if>

            <if test="null != params.skuNo  and '' != params.skuNo">AND `sku_no`=#{params.skuNo}</if>

            <if test="null != params.itemNo  and '' != params.itemNo">
                AND `item_no`=#{params.itemNo}
            </if>

            <if test="null != params.barcode  and '' != params.barcode">
                AND `barcode`=#{params.barcode}
            </if>

            <if test="null != params.itemCode  and '' != params.itemCode">
                AND `item_code`=#{params.itemCode}
            </if>

            <if test="null != params.itemName  and '' != params.itemName">
                AND `item_name` like CONCAT('%',
                    #{params.itemName}, '%')
            </if>

            <if test="null != params.itemNameForshow  and '' != params.itemNameForshow">
                AND `item_name_forshow` like CONCAT(
                    '%',
                    #{params.itemNameForshow}, '%')
            </if>

            <if test="null != params.colorNo  and '' != params.colorNo">
                AND `color_no`=
                    #{params.colorNo}
            </if>

            <if test="null != params.colorName  and '' != params.colorName">
                AND `color_name` like CONCAT('%',
                    #{params.colorName}, '%')
            </if>

            <if test="null != params.brandNo  and '' != params.brandNo">
                AND `brand_no`=
                    #{params.brandNo}
            </if>

            <if test="null != params.brandName  and '' != params.brandName">
                AND `brand_name` like CONCAT('%',
                    #{params.brandName}, '%')
            </if>

            <if test="null != params.categoryNo  and '' != params.categoryNo">
                AND `category_no`=
                    #{params.categoryNo}
            </if>

            <if test="null != params.sizeNo  and '' != params.sizeNo">
                AND `size_no`=#{params.sizeNo}
            </if>

            <if test="null != params.sizeKind  and '' != params.sizeKind">
                AND `size_kind`=#{params.sizeKind}
            </if>

            <if test="null != params.commoditySpecificationStr  and '' != params.commoditySpecificationStr">
                AND `commodity_specification_str`=#{params.commoditySpecificationStr}
            </if>

            <if test="null != params.discount">AND `discount`=#{params.discount}</if>

            <if test="null != params.askQty">AND `ask_qty`=#{params.askQty}</if>

            <if test="null != params.sendOutQty">AND `send_out_qty`=#{params.sendOutQty}</if>

            <if test="null != params.commodityType">AND `commodity_type`=#{params.commodityType}</if>

            <if test="null != params.salePrice">AND `sale_price`=#{params.salePrice}</if>
        </if>
    </sql>

    <sql id="uniqe_condition">
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM
            retail_order_out_dtl
        WHERE
            id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap">
        SELECT
        <include refid="column_list"/>
        FROM
            retail_order_out_dtl
        <where>
            <include refid="uniqe_condition"/>
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM
            retail_order_out_dtl
        <where>
            <include refid="condition"/>
        </where>

        LIMIT 1
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1) as s
        FROM
            retail_order_out_dtl
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM
            retail_order_out_dtl
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY
                ${orderby}</if>
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list"/>
        FROM
            retail_order_out_dtl
        <where>
            <include refid="condition"/>
        </where>
        <if test="orderby != null and '' != orderby">
            ORDER BY
                ${orderby}</if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE
        FROM
            retail_order_out_dtl
        WHERE
            id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE
        FROM
            retail_order_out_dtl
        <where>
            <include refid="condition"/>
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM
            retail_order_out_dtl
        <where>
            <include refid="condition"/>
            <if test="params.ids != null and '' != params.ids">
                AND id in (
                    ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl">
        INSERT INTO retail_order_out_dtl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quotePrice != null">
                `quote_price`,</if>

            <if test="updateTime != null">`update_time`,</if>

            <if test="logisticsCompanyName != null">`logistics_company_name`,</if>

            <if test="logisticCompanyCode != null">`logistic_company_code`,</if>

            <if test="expressNo != null">`express_no`,</if>

            <if test="boxNo != null">`box_no`,</if>

            <if test="retailFlag != null">`retail_flag`,</if>

            <if test="imageRemark != null">`image_remark`,</if>

            <if test="imageUrl != null">`image_url`,</if>

            <if test="posDtlId != null">`pos_dtl_id`,</if>

            <if test="shardingFlag != null">`sharding_flag`,</if>

            <if test="remark != null">`remark`,</if>

            <if test="id != null">`id`,</if>

            <if test="billNo != null">`bill_no`,</if>

            <if test="skuNo != null">`sku_no`,</if>

            <if test="itemNo != null">`item_no`,</if>

            <if test="barcode != null">`barcode`,</if>

            <if test="itemCode != null">`item_code`,</if>

            <if test="itemName != null">`item_name`,</if>

            <if test="itemNameForshow != null">`item_name_forshow`,</if>

            <if test="colorNo != null">`color_no`,</if>

            <if test="colorName != null">`color_name`,</if>

            <if test="brandNo != null">`brand_no`,</if>

            <if test="brandName != null">`brand_name`,</if>

            <if test="categoryNo != null">`category_no`,</if>

            <if test="sizeNo != null">`size_no`,</if>

            <if test="sizeKind != null">`size_kind`,</if>

            <if test="commoditySpecificationStr != null">`commodity_specification_str`,</if>

            <if test="discount != null">`discount`,</if>

            <if test="askQty != null">`ask_qty`,</if>

            <if test="sendOutQty != null">`send_out_qty`,</if>

            <if test="commodityType != null">`commodity_type`,</if>

            <if test="salePrice != null">`sale_price`,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="quotePrice != null">
                #{quotePrice},</if>

            <if test="updateTime != null">#{updateTime},</if>

            <if test="logisticsCompanyName != null">#{logisticsCompanyName},</if>

            <if test="logisticCompanyCode != null">#{logisticCompanyCode},</if>

            <if test="expressNo != null">#{expressNo},</if>

            <if test="boxNo != null">#{boxNo},</if>

            <if test="retailFlag != null">#{retailFlag},</if>

            <if test="imageRemark != null">#{imageRemark},</if>

            <if test="imageUrl != null">#{imageUrl},</if>

            <if test="posDtlId != null">#{posDtlId},</if>

            <if test="shardingFlag != null">#{shardingFlag},</if>

            <if test="remark != null">#{remark},</if>

            <if test="id != null">#{id},</if>

            <if test="billNo != null">#{billNo},</if>

            <if test="skuNo != null">#{skuNo},</if>

            <if test="itemNo != null">#{itemNo},</if>

            <if test="barcode != null">#{barcode},</if>

            <if test="itemCode != null">#{itemCode},</if>

            <if test="itemName != null">#{itemName},</if>

            <if test="itemNameForshow != null">#{itemNameForshow},</if>

            <if test="colorNo != null">#{colorNo},</if>

            <if test="colorName != null">#{colorName},</if>

            <if test="brandNo != null">#{brandNo},</if>

            <if test="brandName != null">#{brandName},</if>

            <if test="categoryNo != null">#{categoryNo},</if>

            <if test="sizeNo != null">#{sizeNo},</if>

            <if test="sizeKind != null">#{sizeKind},</if>

            <if test="commoditySpecificationStr != null">#{commoditySpecificationStr},</if>

            <if test="discount != null">#{discount},</if>

            <if test="askQty != null">#{askQty},</if>

            <if test="sendOutQty != null">#{sendOutQty},</if>

            <if test="commodityType != null">#{commodityType},</if>

            <if test="salePrice != null">#{salePrice},</if>
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl">
        UPDATE retail_order_out_dtl
        <set>
            <if test="quotePrice != null">
                `quote_price` = #{quotePrice},</if>
            <if test="logisticsCompanyName != null">
                `logistics_company_name` = #{logisticsCompanyName},
            </if>
            <if test="logisticCompanyCode != null">
                `logistic_company_code` = #{logisticCompanyCode},
            </if>
            <if test="expressNo != null">
                `express_no` = #{expressNo},</if>
            <if test="boxNo != null">
                `box_no` = #{boxNo},</if>
            <if test="retailFlag != null">
                `retail_flag` = #{retailFlag},</if>
            <if test="imageRemark != null">
                `image_remark` = #{imageRemark},</if>
            <if test="imageUrl != null">
                `image_url` = #{imageUrl},</if>
            <if test="posDtlId != null">
                `pos_dtl_id` = #{posDtlId},</if>
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},</if>
            <if test="remark != null">
                `remark` = #{remark},</if>
            <if test="billNo != null">
                `bill_no` = #{billNo},</if>
            <if test="skuNo != null">
                `sku_no` = #{skuNo},</if>
            <if test="itemNo != null">
                `item_no` = #{itemNo},</if>
            <if test="barcode != null">
                `barcode` = #{barcode},</if>
            <if test="itemCode != null">
                `item_code` = #{itemCode},</if>
            <if test="itemName != null">
                `item_name` = #{itemName},</if>
            <if test="itemNameForshow != null">
                `item_name_forshow` = #{itemNameForshow},</if>
            <if test="colorNo != null">
                `color_no` = #{colorNo},</if>
            <if test="colorName != null">
                `color_name` = #{colorName},</if>
            <if test="brandNo != null">
                `brand_no` = #{brandNo},</if>
            <if test="brandName != null">
                `brand_name` = #{brandName},</if>
            <if test="categoryNo != null">
                `category_no` = #{categoryNo},</if>
            <if test="sizeNo != null">
                `size_no` = #{sizeNo},</if>
            <if test="sizeKind != null">
                `size_kind` = #{sizeKind},</if>
            <if test="commoditySpecificationStr != null">
                `commodity_specification_str` = #{commoditySpecificationStr},
            </if>
            <if test="discount != null">
                `discount` = #{discount},</if>
            <if test="askQty != null">
                `ask_qty` = #{askQty},</if>
            <if test="sendOutQty != null">
                `send_out_qty` = #{sendOutQty},</if>
            <if test="commodityType != null">
                `commodity_type` = #{commodityType},</if>
            <if test="salePrice != null">
                `sale_price` = #{salePrice},</if>
            update_time = now()
        </set>
        WHERE
            id = #{id}
    </update>
    <!-- auto generate end-->


    <sql id="REPORT_CASE">
        <if test="params!=null">
            <if test="params.startDate!=null and  params.endDate!=null and  params.endDate!=null and params.endDate!=''">
                and (roo.create_time >= #{params.startDate} and roo.create_time <![CDATA[<=]]> #{params.endDate})
            </if>
            <if test="params.orderSubNos!=null and params.orderSubNos.size() > 0">
                and roo.order_sub_no in
                <foreach collection="params.orderSubNos" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.orderSourceNos!=null and params.orderSourceNos.size() > 0">
                and io.order_source_no in
                <foreach collection="params.orderSourceNos" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.outOrderIds!=null and params.outOrderIds.size() > 0">
                and io.out_order_id in
                <foreach collection="params.outOrderIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="selectPageForReport" resultMap="baseResultMap">
        select
            rood.id,
            rood.bill_no,
            rood.sku_no,
            rood.item_no,
            rood.id,
            rood.barcode,
            rood.item_code,
            rood.item_name,
            rood.brand_no,
            rood.brand_name,
            rood.size_no,
            rood.ask_qty,
            rood.send_out_qty,
            rood.quote_price,

            roo.origin_platform      as roo_origin_platform,
            roo.bill_no              as roo_bill_no,
            roo.ref_bill_no          as roo_ref_bill_no,
            roo.status               as roo_status,
            roo.original_order_no    as roo_original_order_no,
            roo.order_sub_no         as roo_order_sub_no,
            roo.send_out_date        as roo_send_out_date,
            roo.express_codes        as roo_express_codes,
            roo.create_time          as roo_create_time,
            roo.update_time          as roo_update_time,
            roo.order_unit_no        as roo_order_unit_no,
            roo.order_unit_name      as roo_order_unit_name,
            roo.send_store_name      as roo_send_store_name,
            roo.send_store_no        as roo_send_store_no,
            roo.origin_platform_name as roo_origin_platform_name,
            roo.shop_name            as roo_shop_name,
            roo.shop_no              as roo_shop_no,
            roo.company_no           as roo_company_no,
            roo.send_detail_total    as roo_send_detail_total,
            roo.total_price          as roo_total_price,

            io.original_order_no     as io_original_order_no,
            io.order_sub_no          as io_order_sub_no,
            io.order_source_no       as io_order_source_no,
            io.out_order_id          as io_out_order_id,
            io.origin_platform_name  as io_origin_platform_name
        from
            retail_order_out roo
            join retail_order_out_dtl rood on rood.bill_no = roo.bill_no
            join internet_order io on roo.order_sub_no = io.order_sub_no
        <where>
            <include refid="REPORT_CASE">
            </include>
        </where>
        order by
            roo.create_time,rood.bill_no, rood.id desc
        LIMIT ${page.startRowNum},${page.pageSize}
    </select>

    <select id="selectPageForReportCount" resultType="int">
        select
           count(*)
        from
            retail_order_out roo
            join retail_order_out_dtl rood on rood.bill_no = roo.bill_no
            join internet_order io on roo.order_sub_no = io.order_sub_no
       <where>
            <include refid="REPORT_CASE">
            </include>
        </where>
    </select>
</mapper>