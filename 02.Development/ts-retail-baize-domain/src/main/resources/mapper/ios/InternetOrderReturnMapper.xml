<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetOrderReturnRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetOrderReturn">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="sale_reason" property="saleReason" jdbcType="VARCHAR" />
        
        <result column="sell_return_code" property="sellReturnCode" jdbcType="VARCHAR" />
        
        <result column="order_status_name" property="orderStatusName" jdbcType="CHAR" />
        
        <result column="order_status" property="orderStatus" jdbcType="INTEGER" />
        
        <result column="before_refund_flag" property="beforeRefundFlag" jdbcType="TINYINT" />
        
        <result column="city" property="city" jdbcType="VARCHAR" />
        
        <result column="city_name" property="cityName" jdbcType="VARCHAR" />
        
        <result column="area" property="area" jdbcType="VARCHAR" />
        
        <result column="area_name" property="areaName" jdbcType="VARCHAR" />
        
        <result column="return_name" property="returnName" jdbcType="VARCHAR" />
        
        <result column="province" property="province" jdbcType="VARCHAR" />
        
        <result column="province_name" property="provinceName" jdbcType="VARCHAR" />
        
        <result column="buyer_express_money" property="buyerExpressMoney" jdbcType="DECIMAL" />
        
        <result column="seller_express_money" property="sellerExpressMoney" jdbcType="DECIMAL" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="logistics_name" property="logisticsName" jdbcType="VARCHAR" />
        
        <result column="receive_time" property="receiveTime" jdbcType="TIMESTAMP" />
        
        <result column="logistics_code" property="logisticsCode" jdbcType="VARCHAR" />
        
        <result column="zipcode" property="zipcode" jdbcType="CHAR" />
        
        <result column="constact_phone" property="constactPhone" jdbcType="VARCHAR" />
        
        <result column="buyer_name" property="buyerName" jdbcType="VARCHAR" />
        
        <result column="refund_total_fee" property="refundTotalFee" jdbcType="DECIMAL" />
        
        <result column="message" property="message" jdbcType="VARCHAR" />
        
        <result column="email" property="email" jdbcType="VARCHAR" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="consignee_address" property="consigneeAddress" jdbcType="VARCHAR" />
        
        <result column="member_name" property="memberName" jdbcType="VARCHAR" />
        
        <result column="mobile_phone" property="mobilePhone" jdbcType="VARCHAR" />
        
        <result column="origin_platform" property="originPlatform" jdbcType="VARCHAR" />
        
        <result column="origin_platform_name" property="originPlatformName" jdbcType="VARCHAR" />
        
        <result column="order_source_no" property="orderSourceNo" jdbcType="VARCHAR" />
        
        <result column="interface_platform" property="interfacePlatform" jdbcType="CHAR" />
        
        <result column="store_code" property="storeCode" jdbcType="CHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
        <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
        
        <result column="business_type" property="businessType" jdbcType="VARCHAR" />
        
        <result column="return_type" property="returnType" jdbcType="TINYINT" />
        
        <result column="product_total_quantity" property="productTotalQuantity" jdbcType="INTEGER" />
        
        <result column="return_shop_name" property="returnShopName" jdbcType="VARCHAR" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="yg_return_type" property="ygReturnType" jdbcType="INTEGER" />
        
        <result column="qa_return_code" property="qaReturnCode" jdbcType="VARCHAR" />
        
        <result column="out_store_date" property="outStoreDate" jdbcType="TIMESTAMP" />
        
        <result column="qa_status" property="qaStatus" jdbcType="TINYINT" />
        
        <result column="yg_second_warehouse" property="ygSecondWarehouse" jdbcType="VARCHAR" />
        
        <result column="yg_warehouse" property="ygWarehouse" jdbcType="VARCHAR" />
        
        <result column="exception_type" property="exceptionType" jdbcType="VARCHAR" />
        
        <result column="isquality" property="isquality" jdbcType="TINYINT" />
        
        <result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR" />
        
        <result column="theater_schedule" property="theaterSchedule" jdbcType="VARCHAR" />
        
        <result column="po_no" property="poNo" jdbcType="VARCHAR" />
        
        <result column="is_vip" property="isVip" jdbcType="TINYINT" />
        
        <result column="order_freight" property="orderFreight" jdbcType="DECIMAL" />
        
        <result column="order_coupon_amount" property="orderCouponAmount" jdbcType="DECIMAL" />
        
        <result column="order_gift_card_amount" property="orderGiftCardAmount" jdbcType="DECIMAL" />
        
        <result column="order_promotion_amount" property="orderPromotionAmount" jdbcType="DECIMAL" />
        
        <result column="mark_id" property="markId" jdbcType="VARCHAR" />
        
        <result column="refund_time" property="refundTime" jdbcType="TIMESTAMP" />
        
        <result column="orderAmount" property="orderamount" jdbcType="DECIMAL" />
        
        <result column="refundTypeOption" property="refundtypeoption" jdbcType="INTEGER" />
        
        <result column="basescore" property="basescore" jdbcType="INTEGER" />
        
        <result column="approver_date" property="approverDate" jdbcType="TIMESTAMP" />
        
        <result column="buyer_storeid" property="buyerStoreid" jdbcType="VARCHAR" />
        
        <result column="cardno2" property="cardno2" jdbcType="VARCHAR" />
        
        <result column="shopcardno" property="shopcardno" jdbcType="VARCHAR" />
        
        <result column="order_type" property="orderType" jdbcType="TINYINT" />
        
        <result column="express_no" property="expressNo" jdbcType="VARCHAR" />
        
        <result column="return_reason_name" property="returnReasonName" jdbcType="VARCHAR" />
        
        <result column="logistics_fee" property="logisticsFee" jdbcType="TINYINT" />
        
        <result column="claim_amount" property="claimAmount" jdbcType="DECIMAL" />
        
        <result column="follow_result_type" property="followResultType" jdbcType="TINYINT" />
        
        <result column="return_store_type" property="returnStoreType" jdbcType="VARCHAR" />
        
        <result column="operation_state" property="operationState" jdbcType="TINYINT" />
        
        <result column="transfer_shop" property="transferShop" jdbcType="VARCHAR" />
        
        <result column="update_name" property="updateName" jdbcType="VARCHAR" />
        
        <result column="vstore_code" property="vstoreCode" jdbcType="VARCHAR" />
        
        <result column="tertiary_source" property="tertiarySource" jdbcType="VARCHAR" />
        
        <result column="tertiary_source_name" property="tertiarySourceName" jdbcType="VARCHAR" />
        
        <result column="accounts_date" property="accountsDate" jdbcType="TIMESTAMP" />
        
        <result column="entry_order_code" property="entryOrderCode" jdbcType="VARCHAR" />
        
        <result column="return_shop_no" property="returnShopNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `sale_reason`,`sell_return_code`,`order_status_name`,`order_status`,`before_refund_flag`,`city`,`city_name`,`area`,`area_name`,`return_name`,`province`,`province_name`,`buyer_express_money`,`seller_express_money`,`update_time`,`logistics_name`,`receive_time`,`logistics_code`,`zipcode`,`constact_phone`,`buyer_name`,`refund_total_fee`,`message`,`email`,`create_time`,`consignee_address`,`member_name`,`mobile_phone`,`origin_platform`,`origin_platform_name`,`order_source_no`,`interface_platform`,`store_code`,`order_sub_no`,`bill_no`,`business_type`,`return_type`,`product_total_quantity`,`return_shop_name`,`sharding_flag`,`yg_return_type`,`qa_return_code`,`out_store_date`,`qa_status`,`yg_second_warehouse`,`yg_warehouse`,`exception_type`,`isquality`,`warehouse_name`,`theater_schedule`,`po_no`,`is_vip`,`order_freight`,`order_coupon_amount`,`order_gift_card_amount`,`order_promotion_amount`,`id`,`mark_id`,`refund_time`,`orderAmount`,`refundTypeOption`,`basescore`,`approver_date`,`buyer_storeid`,`cardno2`,`shopcardno`,`order_type`,`express_no`,`return_reason_name`,`logistics_fee`,`claim_amount`,`follow_result_type`,`return_store_type`,`operation_state`,`transfer_shop`,`update_name`,`vstore_code`,`tertiary_source`,`tertiary_source_name`,`accounts_date`,`entry_order_code`,`return_shop_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.saleReason  and ''!=params.saleReason ">
                
                AND `sale_reason`=#{params.saleReason}
                
            </if>
            
            <if test="null!=params.sellReturnCode  and ''!=params.sellReturnCode ">
                
                AND `sell_return_code`=#{params.sellReturnCode}
                
            </if>
            
            <if test="null!=params.orderStatusName  and ''!=params.orderStatusName ">
                
                AND `order_status_name` like CONCAT('%',#{params.orderStatusName},'%') 
                
            </if>
            
            <if test="null!=params.orderStatus ">
                
                AND `order_status`=#{params.orderStatus}
                
            </if>
            
            <if test="null!=params.beforeRefundFlag ">
                
                AND `before_refund_flag`=#{params.beforeRefundFlag}
                
            </if>
            
            <if test="null!=params.city  and ''!=params.city ">
                
                AND `city`=#{params.city}
                
            </if>
            
            <if test="null!=params.cityName  and ''!=params.cityName ">
                
                AND `city_name` like CONCAT('%',#{params.cityName},'%') 
                
            </if>
            
            <if test="null!=params.area  and ''!=params.area ">
                
                AND `area`=#{params.area}
                
            </if>
            
            <if test="null!=params.areaName  and ''!=params.areaName ">
                
                AND `area_name` like CONCAT('%',#{params.areaName},'%') 
                
            </if>
            
            <if test="null!=params.returnName  and ''!=params.returnName ">
                
                AND `return_name` like CONCAT('%',#{params.returnName},'%') 
                
            </if>
            
            <if test="null!=params.province  and ''!=params.province ">
                
                AND `province`=#{params.province}
                
            </if>
            
            <if test="null!=params.provinceName  and ''!=params.provinceName ">
                
                AND `province_name` like CONCAT('%',#{params.provinceName},'%') 
                
            </if>
            
            <if test="null!=params.buyerExpressMoney ">
                
                AND `buyer_express_money`=#{params.buyerExpressMoney}
                
            </if>
            
            <if test="null!=params.sellerExpressMoney ">
                
                AND `seller_express_money`=#{params.sellerExpressMoney}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.logisticsName  and ''!=params.logisticsName ">
                
                AND `logistics_name` like CONCAT('%',#{params.logisticsName},'%') 
                
            </if>
            
            <if test="null!=params.receiveTime ">
                
                AND `receive_time`=#{params.receiveTime}
                
            </if>
            
            <if test="null!=params.logisticsCode  and ''!=params.logisticsCode ">
                
                AND `logistics_code`=#{params.logisticsCode}
                
            </if>
            
            <if test="null!=params.zipcode  and ''!=params.zipcode ">
                
                AND `zipcode`=#{params.zipcode}
                
            </if>
            
            <if test="null!=params.constactPhone  and ''!=params.constactPhone ">
                
                AND `constact_phone`=#{params.constactPhone}
                
            </if>
            
            <if test="null!=params.buyerName  and ''!=params.buyerName ">
                
                AND `buyer_name` like CONCAT('%',#{params.buyerName},'%') 
                
            </if>
            
            <if test="null!=params.refundTotalFee ">
                
                AND `refund_total_fee`=#{params.refundTotalFee}
                
            </if>
            
            <if test="null!=params.message  and ''!=params.message ">
                
                AND `message`=#{params.message}
                
            </if>
            
            <if test="null!=params.email  and ''!=params.email ">
                
                AND `email`=#{params.email}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.consigneeAddress  and ''!=params.consigneeAddress ">
                
                AND `consignee_address`=#{params.consigneeAddress}
                
            </if>
            
            <if test="null!=params.memberName  and ''!=params.memberName ">
                
                AND `member_name` like CONCAT('%',#{params.memberName},'%') 
                
            </if>
            
            <if test="null!=params.mobilePhone  and ''!=params.mobilePhone ">
                
                AND `mobile_phone`=#{params.mobilePhone}
                
            </if>
            
            <if test="null!=params.originPlatform  and ''!=params.originPlatform ">
                
                AND `origin_platform`=#{params.originPlatform}
                
            </if>
            
            <if test="null!=params.originPlatformName  and ''!=params.originPlatformName ">
                
                AND `origin_platform_name` like CONCAT('%',#{params.originPlatformName},'%') 
                
            </if>
            
            <if test="null!=params.orderSourceNo  and ''!=params.orderSourceNo ">
                
                AND `order_source_no`=#{params.orderSourceNo}
                
            </if>
            
            <if test="null!=params.interfacePlatform  and ''!=params.interfacePlatform ">
                
                AND `interface_platform`=#{params.interfacePlatform}
                
            </if>
            
            <if test="null!=params.storeCode  and ''!=params.storeCode ">
                
                AND `store_code`=#{params.storeCode}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
            <if test="null!=params.businessType  and ''!=params.businessType ">
                
                AND `business_type`=#{params.businessType}
                
            </if>
            
            <if test="null!=params.returnType ">
                
                AND `return_type`=#{params.returnType}
                
            </if>
            
            <if test="null!=params.productTotalQuantity ">
                
                AND `product_total_quantity`=#{params.productTotalQuantity}
                
            </if>
            
            <if test="null!=params.returnShopName  and ''!=params.returnShopName ">
                
                AND `return_shop_name` like CONCAT('%',#{params.returnShopName},'%') 
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.ygReturnType ">
                
                AND `yg_return_type`=#{params.ygReturnType}
                
            </if>
            
            <if test="null!=params.qaReturnCode  and ''!=params.qaReturnCode ">
                
                AND `qa_return_code`=#{params.qaReturnCode}
                
            </if>
            
            <if test="null!=params.outStoreDate ">
                
                AND `out_store_date`=#{params.outStoreDate}
                
            </if>
            
            <if test="null!=params.qaStatus ">
                
                AND `qa_status`=#{params.qaStatus}
                
            </if>
            
            <if test="null!=params.ygSecondWarehouse  and ''!=params.ygSecondWarehouse ">
                
                AND `yg_second_warehouse`=#{params.ygSecondWarehouse}
                
            </if>
            
            <if test="null!=params.ygWarehouse  and ''!=params.ygWarehouse ">
                
                AND `yg_warehouse`=#{params.ygWarehouse}
                
            </if>
            
            <if test="null!=params.exceptionType  and ''!=params.exceptionType ">
                
                AND `exception_type`=#{params.exceptionType}
                
            </if>
            
            <if test="null!=params.isquality ">
                
                AND `isquality`=#{params.isquality}
                
            </if>
            
            <if test="null!=params.warehouseName  and ''!=params.warehouseName ">
                
                AND `warehouse_name` like CONCAT('%',#{params.warehouseName},'%') 
                
            </if>
            
            <if test="null!=params.theaterSchedule  and ''!=params.theaterSchedule ">
                
                AND `theater_schedule`=#{params.theaterSchedule}
                
            </if>
            
            <if test="null!=params.poNo  and ''!=params.poNo ">
                
                AND `po_no`=#{params.poNo}
                
            </if>
            
            <if test="null!=params.isVip ">
                
                AND `is_vip`=#{params.isVip}
                
            </if>
            
            <if test="null!=params.orderFreight ">
                
                AND `order_freight`=#{params.orderFreight}
                
            </if>
            
            <if test="null!=params.orderCouponAmount ">
                
                AND `order_coupon_amount`=#{params.orderCouponAmount}
                
            </if>
            
            <if test="null!=params.orderGiftCardAmount ">
                
                AND `order_gift_card_amount`=#{params.orderGiftCardAmount}
                
            </if>
            
            <if test="null!=params.orderPromotionAmount ">
                
                AND `order_promotion_amount`=#{params.orderPromotionAmount}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.markId  and ''!=params.markId ">
                
                AND `mark_id`=#{params.markId}
                
            </if>
            
            <if test="null!=params.refundTime ">
                
                AND `refund_time`=#{params.refundTime}
                
            </if>
            
            <if test="null!=params.orderamount ">
                
                AND `orderAmount`=#{params.orderamount}
                
            </if>
            
            <if test="null!=params.refundtypeoption ">
                
                AND `refundTypeOption`=#{params.refundtypeoption}
                
            </if>
            
            <if test="null!=params.basescore ">
                
                AND `basescore`=#{params.basescore}
                
            </if>
            
            <if test="null!=params.approverDate ">
                
                AND `approver_date`=#{params.approverDate}
                
            </if>
            
            <if test="null!=params.buyerStoreid  and ''!=params.buyerStoreid ">
                
                AND `buyer_storeid`=#{params.buyerStoreid}
                
            </if>
            
            <if test="null!=params.cardno2  and ''!=params.cardno2 ">
                
                AND `cardno2`=#{params.cardno2}
                
            </if>
            
            <if test="null!=params.shopcardno  and ''!=params.shopcardno ">
                
                AND `shopcardno`=#{params.shopcardno}
                
            </if>
            
            <if test="null!=params.orderType ">
                
                AND `order_type`=#{params.orderType}
                
            </if>
            
            <if test="null!=params.expressNo  and ''!=params.expressNo ">
                
                AND `express_no`=#{params.expressNo}
                
            </if>
            
            <if test="null!=params.returnReasonName  and ''!=params.returnReasonName ">
                
                AND `return_reason_name` like CONCAT('%',#{params.returnReasonName},'%') 
                
            </if>
            
            <if test="null!=params.logisticsFee ">
                
                AND `logistics_fee`=#{params.logisticsFee}
                
            </if>
            
            <if test="null!=params.claimAmount ">
                
                AND `claim_amount`=#{params.claimAmount}
                
            </if>
            
            <if test="null!=params.followResultType ">
                
                AND `follow_result_type`=#{params.followResultType}
                
            </if>
            
            <if test="null!=params.returnStoreType  and ''!=params.returnStoreType ">
                
                AND `return_store_type`=#{params.returnStoreType}
                
            </if>
            
            <if test="null!=params.operationState ">
                
                AND `operation_state`=#{params.operationState}
                
            </if>
            
            <if test="null!=params.transferShop  and ''!=params.transferShop ">
                
                AND `transfer_shop`=#{params.transferShop}
                
            </if>
            
            <if test="null!=params.updateName  and ''!=params.updateName ">
                
                AND `update_name` like CONCAT('%',#{params.updateName},'%') 
                
            </if>
            
            <if test="null!=params.vstoreCode  and ''!=params.vstoreCode ">
                
                AND `vstore_code`=#{params.vstoreCode}
                
            </if>
            
            <if test="null!=params.tertiarySource  and ''!=params.tertiarySource ">
                
                AND `tertiary_source`=#{params.tertiarySource}
                
            </if>
            
            <if test="null!=params.tertiarySourceName  and ''!=params.tertiarySourceName ">
                
                AND `tertiary_source_name` like CONCAT('%',#{params.tertiarySourceName},'%') 
                
            </if>
            
            <if test="null!=params.accountsDate ">
                
                AND `accounts_date`=#{params.accountsDate}
                
            </if>
            
            <if test="null!=params.entryOrderCode  and ''!=params.entryOrderCode ">
                
                AND `entry_order_code`=#{params.entryOrderCode}
                
            </if>
            
            <if test="null!=params.returnShopNo  and ''!=params.returnShopNo ">
                
                AND `return_shop_no`=#{params.returnShopNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_return
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_return
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_return
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_order_return
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_order_return
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_return
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_order_return
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_order_return
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_order_return
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderReturn"  >
        INSERT INTO internet_order_return
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="saleReason != null">
                `sale_reason`,
            </if>
            
            <if test="sellReturnCode != null">
                `sell_return_code`,
            </if>
            
            <if test="orderStatusName != null">
                `order_status_name`,
            </if>
            
            <if test="orderStatus != null">
                `order_status`,
            </if>
            
            <if test="beforeRefundFlag != null">
                `before_refund_flag`,
            </if>
            
            <if test="city != null">
                `city`,
            </if>
            
            <if test="cityName != null">
                `city_name`,
            </if>
            
            <if test="area != null">
                `area`,
            </if>
            
            <if test="areaName != null">
                `area_name`,
            </if>
            
            <if test="returnName != null">
                `return_name`,
            </if>
            
            <if test="province != null">
                `province`,
            </if>
            
            <if test="provinceName != null">
                `province_name`,
            </if>
            
            <if test="buyerExpressMoney != null">
                `buyer_express_money`,
            </if>
            
            <if test="sellerExpressMoney != null">
                `seller_express_money`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="logisticsName != null">
                `logistics_name`,
            </if>
            
            <if test="receiveTime != null">
                `receive_time`,
            </if>
            
            <if test="logisticsCode != null">
                `logistics_code`,
            </if>
            
            <if test="zipcode != null">
                `zipcode`,
            </if>
            
            <if test="constactPhone != null">
                `constact_phone`,
            </if>
            
            <if test="buyerName != null">
                `buyer_name`,
            </if>
            
            <if test="refundTotalFee != null">
                `refund_total_fee`,
            </if>
            
            <if test="message != null">
                `message`,
            </if>
            
            <if test="email != null">
                `email`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="consigneeAddress != null">
                `consignee_address`,
            </if>
            
            <if test="memberName != null">
                `member_name`,
            </if>
            
            <if test="mobilePhone != null">
                `mobile_phone`,
            </if>
            
            <if test="originPlatform != null">
                `origin_platform`,
            </if>
            
            <if test="originPlatformName != null">
                `origin_platform_name`,
            </if>
            
            <if test="orderSourceNo != null">
                `order_source_no`,
            </if>
            
            <if test="interfacePlatform != null">
                `interface_platform`,
            </if>
            
            <if test="storeCode != null">
                `store_code`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="businessType != null">
                `business_type`,
            </if>
            
            <if test="returnType != null">
                `return_type`,
            </if>
            
            <if test="productTotalQuantity != null">
                `product_total_quantity`,
            </if>
            
            <if test="returnShopName != null">
                `return_shop_name`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="ygReturnType != null">
                `yg_return_type`,
            </if>
            
            <if test="qaReturnCode != null">
                `qa_return_code`,
            </if>
            
            <if test="outStoreDate != null">
                `out_store_date`,
            </if>
            
            <if test="qaStatus != null">
                `qa_status`,
            </if>
            
            <if test="ygSecondWarehouse != null">
                `yg_second_warehouse`,
            </if>
            
            <if test="ygWarehouse != null">
                `yg_warehouse`,
            </if>
            
            <if test="exceptionType != null">
                `exception_type`,
            </if>
            
            <if test="isquality != null">
                `isquality`,
            </if>
            
            <if test="warehouseName != null">
                `warehouse_name`,
            </if>
            
            <if test="theaterSchedule != null">
                `theater_schedule`,
            </if>
            
            <if test="poNo != null">
                `po_no`,
            </if>
            
            <if test="isVip != null">
                `is_vip`,
            </if>
            
            <if test="orderFreight != null">
                `order_freight`,
            </if>
            
            <if test="orderCouponAmount != null">
                `order_coupon_amount`,
            </if>
            
            <if test="orderGiftCardAmount != null">
                `order_gift_card_amount`,
            </if>
            
            <if test="orderPromotionAmount != null">
                `order_promotion_amount`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="markId != null">
                `mark_id`,
            </if>
            
            <if test="refundTime != null">
                `refund_time`,
            </if>
            
            <if test="orderamount != null">
                `orderAmount`,
            </if>
            
            <if test="refundtypeoption != null">
                `refundTypeOption`,
            </if>
            
            <if test="basescore != null">
                `basescore`,
            </if>
            
            <if test="approverDate != null">
                `approver_date`,
            </if>
            
            <if test="buyerStoreid != null">
                `buyer_storeid`,
            </if>
            
            <if test="cardno2 != null">
                `cardno2`,
            </if>
            
            <if test="shopcardno != null">
                `shopcardno`,
            </if>
            
            <if test="orderType != null">
                `order_type`,
            </if>
            
            <if test="expressNo != null">
                `express_no`,
            </if>
            
            <if test="returnReasonName != null">
                `return_reason_name`,
            </if>
            
            <if test="logisticsFee != null">
                `logistics_fee`,
            </if>
            
            <if test="claimAmount != null">
                `claim_amount`,
            </if>
            
            <if test="followResultType != null">
                `follow_result_type`,
            </if>
            
            <if test="returnStoreType != null">
                `return_store_type`,
            </if>
            
            <if test="operationState != null">
                `operation_state`,
            </if>
            
            <if test="transferShop != null">
                `transfer_shop`,
            </if>
            
            <if test="updateName != null">
                `update_name`,
            </if>
            
            <if test="vstoreCode != null">
                `vstore_code`,
            </if>
            
            <if test="tertiarySource != null">
                `tertiary_source`,
            </if>
            
            <if test="tertiarySourceName != null">
                `tertiary_source_name`,
            </if>
            
            <if test="accountsDate != null">
                `accounts_date`,
            </if>
            
            <if test="entryOrderCode != null">
                `entry_order_code`,
            </if>
            
            <if test="returnShopNo != null">
                `return_shop_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="saleReason != null">
                #{saleReason},
            </if>
            
            <if test="sellReturnCode != null">
                #{sellReturnCode},
            </if>
            
            <if test="orderStatusName != null">
                #{orderStatusName},
            </if>
            
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            
            <if test="beforeRefundFlag != null">
                #{beforeRefundFlag},
            </if>
            
            <if test="city != null">
                #{city},
            </if>
            
            <if test="cityName != null">
                #{cityName},
            </if>
            
            <if test="area != null">
                #{area},
            </if>
            
            <if test="areaName != null">
                #{areaName},
            </if>
            
            <if test="returnName != null">
                #{returnName},
            </if>
            
            <if test="province != null">
                #{province},
            </if>
            
            <if test="provinceName != null">
                #{provinceName},
            </if>
            
            <if test="buyerExpressMoney != null">
                #{buyerExpressMoney},
            </if>
            
            <if test="sellerExpressMoney != null">
                #{sellerExpressMoney},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="logisticsName != null">
                #{logisticsName},
            </if>
            
            <if test="receiveTime != null">
                #{receiveTime},
            </if>
            
            <if test="logisticsCode != null">
                #{logisticsCode},
            </if>
            
            <if test="zipcode != null">
                #{zipcode},
            </if>
            
            <if test="constactPhone != null">
                #{constactPhone},
            </if>
            
            <if test="buyerName != null">
                #{buyerName},
            </if>
            
            <if test="refundTotalFee != null">
                #{refundTotalFee},
            </if>
            
            <if test="message != null">
                #{message},
            </if>
            
            <if test="email != null">
                #{email},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="consigneeAddress != null">
                #{consigneeAddress},
            </if>
            
            <if test="memberName != null">
                #{memberName},
            </if>
            
            <if test="mobilePhone != null">
                #{mobilePhone},
            </if>
            
            <if test="originPlatform != null">
                #{originPlatform},
            </if>
            
            <if test="originPlatformName != null">
                #{originPlatformName},
            </if>
            
            <if test="orderSourceNo != null">
                #{orderSourceNo},
            </if>
            
            <if test="interfacePlatform != null">
                #{interfacePlatform},
            </if>
            
            <if test="storeCode != null">
                #{storeCode},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="businessType != null">
                #{businessType},
            </if>
            
            <if test="returnType != null">
                #{returnType},
            </if>
            
            <if test="productTotalQuantity != null">
                #{productTotalQuantity},
            </if>
            
            <if test="returnShopName != null">
                #{returnShopName},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="ygReturnType != null">
                #{ygReturnType},
            </if>
            
            <if test="qaReturnCode != null">
                #{qaReturnCode},
            </if>
            
            <if test="outStoreDate != null">
                #{outStoreDate},
            </if>
            
            <if test="qaStatus != null">
                #{qaStatus},
            </if>
            
            <if test="ygSecondWarehouse != null">
                #{ygSecondWarehouse},
            </if>
            
            <if test="ygWarehouse != null">
                #{ygWarehouse},
            </if>
            
            <if test="exceptionType != null">
                #{exceptionType},
            </if>
            
            <if test="isquality != null">
                #{isquality},
            </if>
            
            <if test="warehouseName != null">
                #{warehouseName},
            </if>
            
            <if test="theaterSchedule != null">
                #{theaterSchedule},
            </if>
            
            <if test="poNo != null">
                #{poNo},
            </if>
            
            <if test="isVip != null">
                #{isVip},
            </if>
            
            <if test="orderFreight != null">
                #{orderFreight},
            </if>
            
            <if test="orderCouponAmount != null">
                #{orderCouponAmount},
            </if>
            
            <if test="orderGiftCardAmount != null">
                #{orderGiftCardAmount},
            </if>
            
            <if test="orderPromotionAmount != null">
                #{orderPromotionAmount},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="markId != null">
                #{markId},
            </if>
            
            <if test="refundTime != null">
                #{refundTime},
            </if>
            
            <if test="orderamount != null">
                #{orderamount},
            </if>
            
            <if test="refundtypeoption != null">
                #{refundtypeoption},
            </if>
            
            <if test="basescore != null">
                #{basescore},
            </if>
            
            <if test="approverDate != null">
                #{approverDate},
            </if>
            
            <if test="buyerStoreid != null">
                #{buyerStoreid},
            </if>
            
            <if test="cardno2 != null">
                #{cardno2},
            </if>
            
            <if test="shopcardno != null">
                #{shopcardno},
            </if>
            
            <if test="orderType != null">
                #{orderType},
            </if>
            
            <if test="expressNo != null">
                #{expressNo},
            </if>
            
            <if test="returnReasonName != null">
                #{returnReasonName},
            </if>
            
            <if test="logisticsFee != null">
                #{logisticsFee},
            </if>
            
            <if test="claimAmount != null">
                #{claimAmount},
            </if>
            
            <if test="followResultType != null">
                #{followResultType},
            </if>
            
            <if test="returnStoreType != null">
                #{returnStoreType},
            </if>
            
            <if test="operationState != null">
                #{operationState},
            </if>
            
            <if test="transferShop != null">
                #{transferShop},
            </if>
            
            <if test="updateName != null">
                #{updateName},
            </if>
            
            <if test="vstoreCode != null">
                #{vstoreCode},
            </if>
            
            <if test="tertiarySource != null">
                #{tertiarySource},
            </if>
            
            <if test="tertiarySourceName != null">
                #{tertiarySourceName},
            </if>
            
            <if test="accountsDate != null">
                #{accountsDate},
            </if>
            
            <if test="entryOrderCode != null">
                #{entryOrderCode},
            </if>
            
            <if test="returnShopNo != null">
                #{returnShopNo},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderReturn">
        UPDATE internet_order_return
        <set>
            
            <if test="saleReason != null">
                `sale_reason` = #{saleReason},
            </if> 
            <if test="sellReturnCode != null">
                `sell_return_code` = #{sellReturnCode},
            </if> 
            <if test="orderStatusName != null">
                `order_status_name` = #{orderStatusName},
            </if> 
            <if test="orderStatus != null">
                `order_status` = #{orderStatus},
            </if> 
            <if test="beforeRefundFlag != null">
                `before_refund_flag` = #{beforeRefundFlag},
            </if> 
            <if test="city != null">
                `city` = #{city},
            </if> 
            <if test="cityName != null">
                `city_name` = #{cityName},
            </if> 
            <if test="area != null">
                `area` = #{area},
            </if> 
            <if test="areaName != null">
                `area_name` = #{areaName},
            </if> 
            <if test="returnName != null">
                `return_name` = #{returnName},
            </if> 
            <if test="province != null">
                `province` = #{province},
            </if> 
            <if test="provinceName != null">
                `province_name` = #{provinceName},
            </if> 
            <if test="buyerExpressMoney != null">
                `buyer_express_money` = #{buyerExpressMoney},
            </if> 
            <if test="sellerExpressMoney != null">
                `seller_express_money` = #{sellerExpressMoney},
            </if> 
            <if test="logisticsName != null">
                `logistics_name` = #{logisticsName},
            </if> 
            <if test="receiveTime != null">
                `receive_time` = #{receiveTime},
            </if> 
            <if test="logisticsCode != null">
                `logistics_code` = #{logisticsCode},
            </if> 
            <if test="zipcode != null">
                `zipcode` = #{zipcode},
            </if> 
            <if test="constactPhone != null">
                `constact_phone` = #{constactPhone},
            </if> 
            <if test="buyerName != null">
                `buyer_name` = #{buyerName},
            </if> 
            <if test="refundTotalFee != null">
                `refund_total_fee` = #{refundTotalFee},
            </if> 
            <if test="message != null">
                `message` = #{message},
            </if> 
            <if test="email != null">
                `email` = #{email},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="consigneeAddress != null">
                `consignee_address` = #{consigneeAddress},
            </if> 
            <if test="memberName != null">
                `member_name` = #{memberName},
            </if> 
            <if test="mobilePhone != null">
                `mobile_phone` = #{mobilePhone},
            </if> 
            <if test="originPlatform != null">
                `origin_platform` = #{originPlatform},
            </if> 
            <if test="originPlatformName != null">
                `origin_platform_name` = #{originPlatformName},
            </if> 
            <if test="orderSourceNo != null">
                `order_source_no` = #{orderSourceNo},
            </if> 
            <if test="interfacePlatform != null">
                `interface_platform` = #{interfacePlatform},
            </if> 
            <if test="storeCode != null">
                `store_code` = #{storeCode},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="businessType != null">
                `business_type` = #{businessType},
            </if> 
            <if test="returnType != null">
                `return_type` = #{returnType},
            </if> 
            <if test="productTotalQuantity != null">
                `product_total_quantity` = #{productTotalQuantity},
            </if> 
            <if test="returnShopName != null">
                `return_shop_name` = #{returnShopName},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="ygReturnType != null">
                `yg_return_type` = #{ygReturnType},
            </if> 
            <if test="qaReturnCode != null">
                `qa_return_code` = #{qaReturnCode},
            </if> 
            <if test="outStoreDate != null">
                `out_store_date` = #{outStoreDate},
            </if> 
            <if test="qaStatus != null">
                `qa_status` = #{qaStatus},
            </if> 
            <if test="ygSecondWarehouse != null">
                `yg_second_warehouse` = #{ygSecondWarehouse},
            </if> 
            <if test="ygWarehouse != null">
                `yg_warehouse` = #{ygWarehouse},
            </if> 
            <if test="exceptionType != null">
                `exception_type` = #{exceptionType},
            </if> 
            <if test="isquality != null">
                `isquality` = #{isquality},
            </if> 
            <if test="warehouseName != null">
                `warehouse_name` = #{warehouseName},
            </if> 
            <if test="theaterSchedule != null">
                `theater_schedule` = #{theaterSchedule},
            </if> 
            <if test="poNo != null">
                `po_no` = #{poNo},
            </if> 
            <if test="isVip != null">
                `is_vip` = #{isVip},
            </if> 
            <if test="orderFreight != null">
                `order_freight` = #{orderFreight},
            </if> 
            <if test="orderCouponAmount != null">
                `order_coupon_amount` = #{orderCouponAmount},
            </if> 
            <if test="orderGiftCardAmount != null">
                `order_gift_card_amount` = #{orderGiftCardAmount},
            </if> 
            <if test="orderPromotionAmount != null">
                `order_promotion_amount` = #{orderPromotionAmount},
            </if> 
            <if test="markId != null">
                `mark_id` = #{markId},
            </if> 
            <if test="refundTime != null">
                `refund_time` = #{refundTime},
            </if> 
            <if test="orderamount != null">
                `orderAmount` = #{orderamount},
            </if> 
            <if test="refundtypeoption != null">
                `refundTypeOption` = #{refundtypeoption},
            </if> 
            <if test="basescore != null">
                `basescore` = #{basescore},
            </if> 
            <if test="approverDate != null">
                `approver_date` = #{approverDate},
            </if> 
            <if test="buyerStoreid != null">
                `buyer_storeid` = #{buyerStoreid},
            </if> 
            <if test="cardno2 != null">
                `cardno2` = #{cardno2},
            </if> 
            <if test="shopcardno != null">
                `shopcardno` = #{shopcardno},
            </if> 
            <if test="orderType != null">
                `order_type` = #{orderType},
            </if> 
            <if test="expressNo != null">
                `express_no` = #{expressNo},
            </if> 
            <if test="returnReasonName != null">
                `return_reason_name` = #{returnReasonName},
            </if> 
            <if test="logisticsFee != null">
                `logistics_fee` = #{logisticsFee},
            </if> 
            <if test="claimAmount != null">
                `claim_amount` = #{claimAmount},
            </if> 
            <if test="followResultType != null">
                `follow_result_type` = #{followResultType},
            </if> 
            <if test="returnStoreType != null">
                `return_store_type` = #{returnStoreType},
            </if> 
            <if test="operationState != null">
                `operation_state` = #{operationState},
            </if> 
            <if test="transferShop != null">
                `transfer_shop` = #{transferShop},
            </if> 
            <if test="updateName != null">
                `update_name` = #{updateName},
            </if> 
            <if test="vstoreCode != null">
                `vstore_code` = #{vstoreCode},
            </if> 
            <if test="tertiarySource != null">
                `tertiary_source` = #{tertiarySource},
            </if> 
            <if test="tertiarySourceName != null">
                `tertiary_source_name` = #{tertiarySourceName},
            </if> 
            <if test="accountsDate != null">
                `accounts_date` = #{accountsDate},
            </if> 
            <if test="entryOrderCode != null">
                `entry_order_code` = #{entryOrderCode},
            </if> 
            <if test="returnShopNo != null">
                `return_shop_no` = #{returnShopNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>