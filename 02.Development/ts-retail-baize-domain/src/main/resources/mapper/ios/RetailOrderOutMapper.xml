<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.RetailOrderOutRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.RetailOrderOut">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="original_order_no" property="originalOrderNo" jdbcType="VARCHAR" />
        
        <result column="arrive_time" property="arriveTime" jdbcType="TIMESTAMP" />
        
        <result column="bill_no" property="billNo" jdbcType="CHAR" />
        
        <result column="bill_type" property="billType" jdbcType="INTEGER" />
        
        <result column="out_biz_type" property="outBizType" jdbcType="TINYINT" />
        
        <result column="ref_bill_no" property="refBillNo" jdbcType="CHAR" />
        
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        
        <result column="store_no" property="storeNo" jdbcType="VARCHAR" />
        
        <result column="box_code" property="boxCode" jdbcType="VARCHAR" />
        
        <result column="parcel_volume" property="parcelVolume" jdbcType="DECIMAL" />
        
        <result column="parcel_weight" property="parcelWeight" jdbcType="DECIMAL" />
        
        <result column="ref_bill_type" property="refBillType" jdbcType="INTEGER" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
        <result column="sale_company_no" property="saleCompanyNo" jdbcType="CHAR" />
        
        <result column="theater_schedule" property="theaterSchedule" jdbcType="VARCHAR" />
        
        <result column="record_code" property="recordCode" jdbcType="VARCHAR" />
        
        <result column="has_order" property="hasOrder" jdbcType="TINYINT" />
        
        <result column="company_no" property="companyNo" jdbcType="CHAR" />
        
        <result column="storage_no" property="storageNo" jdbcType="VARCHAR" />
        
        <result column="origin_platform" property="originPlatform" jdbcType="VARCHAR" />
        
        <result column="send_store_type" property="sendStoreType" jdbcType="TINYINT" />
        
        <result column="message" property="message" jdbcType="VARCHAR" />
        
        <result column="customer_no" property="customerNo" jdbcType="VARCHAR" />
        
        <result column="customer_name" property="customerName" jdbcType="VARCHAR" />
        
        <result column="send_store_no" property="sendStoreNo" jdbcType="CHAR" />
        
        <result column="send_store_name" property="sendStoreName" jdbcType="VARCHAR" />
        
        <result column="order_unit_no" property="orderUnitNo" jdbcType="CHAR" />
        
        <result column="order_unit_name" property="orderUnitName" jdbcType="VARCHAR" />
        
        <result column="send_out_date" property="sendOutDate" jdbcType="TIMESTAMP" />
        
        <result column="logistic_company_code" property="logisticCompanyCode" jdbcType="VARCHAR" />
        
        <result column="logistics_company_name" property="logisticsCompanyName" jdbcType="VARCHAR" />
        
        <result column="express_codes" property="expressCodes" jdbcType="VARCHAR" />
        
        <result column="express_price" property="expressPrice" jdbcType="DECIMAL" />
        
        <result column="send_detail_total" property="sendDetailTotal" jdbcType="INTEGER" />
        
        <result column="total_price" property="totalPrice" jdbcType="DECIMAL" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="create_user" property="createUser" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="retail_flag" property="retailFlag" jdbcType="CHAR" />
        
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP" />
        
        <result column="origin_platform_name" property="originPlatformName" jdbcType="VARCHAR" />
        
        <result column="shop_no" property="shopNo" jdbcType="VARCHAR" />
        
        <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
        
        <result column="is_payment" property="isPayment" jdbcType="CHAR" />
        
        <result column="channel_no" property="channelNo" jdbcType="CHAR" />
        
        <result column="package_no" property="packageNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `original_order_no`,`arrive_time`,`bill_no`,`bill_type`,`out_biz_type`,`ref_bill_no`,`id`,`store_name`,`store_no`,`box_code`,`parcel_volume`,`parcel_weight`,`ref_bill_type`,`status`,`order_sub_no`,`sale_company_no`,`theater_schedule`,`record_code`,`has_order`,`company_no`,`storage_no`,`origin_platform`,`send_store_type`,`message`,`customer_no`,`customer_name`,`send_store_no`,`send_store_name`,`order_unit_no`,`order_unit_name`,`send_out_date`,`logistic_company_code`,`logistics_company_name`,`express_codes`,`express_price`,`send_detail_total`,`total_price`,`create_time`,`create_user`,`update_time`,`remark`,`sharding_flag`,`retail_flag`,`audit_time`,`origin_platform_name`,`shop_no`,`shop_name`,`is_payment`,`channel_no`,`package_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.originalOrderNo  and ''!=params.originalOrderNo ">
                
                AND `original_order_no`=#{params.originalOrderNo}
                
            </if>
            
            <if test="null!=params.arriveTime ">
                
                AND `arrive_time`=#{params.arriveTime}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
            <if test="null!=params.billType ">
                
                AND `bill_type`=#{params.billType}
                
            </if>
            
            <if test="null!=params.outBizType ">
                
                AND `out_biz_type`=#{params.outBizType}
                
            </if>
            
            <if test="null!=params.refBillNo  and ''!=params.refBillNo ">
                
                AND `ref_bill_no`=#{params.refBillNo}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.storeName  and ''!=params.storeName ">
                
                AND `store_name` like CONCAT('%',#{params.storeName},'%') 
                
            </if>
            
            <if test="null!=params.storeNo  and ''!=params.storeNo ">
                
                AND `store_no`=#{params.storeNo}
                
            </if>
            
            <if test="null!=params.boxCode  and ''!=params.boxCode ">
                
                AND `box_code`=#{params.boxCode}
                
            </if>
            
            <if test="null!=params.parcelVolume ">
                
                AND `parcel_volume`=#{params.parcelVolume}
                
            </if>
            
            <if test="null!=params.parcelWeight ">
                
                AND `parcel_weight`=#{params.parcelWeight}
                
            </if>
            
            <if test="null!=params.refBillType ">
                
                AND `ref_bill_type`=#{params.refBillType}
                
            </if>
            
            <if test="null!=params.status ">
                
                AND `status`=#{params.status}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
            <if test="null!=params.saleCompanyNo  and ''!=params.saleCompanyNo ">
                
                AND `sale_company_no`=#{params.saleCompanyNo}
                
            </if>
            
            <if test="null!=params.theaterSchedule  and ''!=params.theaterSchedule ">
                
                AND `theater_schedule`=#{params.theaterSchedule}
                
            </if>
            
            <if test="null!=params.recordCode  and ''!=params.recordCode ">
                
                AND `record_code`=#{params.recordCode}
                
            </if>
            
            <if test="null!=params.hasOrder ">
                
                AND `has_order`=#{params.hasOrder}
                
            </if>
            
            <if test="null!=params.companyNo  and ''!=params.companyNo ">
                
                AND `company_no`=#{params.companyNo}
                
            </if>
            
            <if test="null!=params.storageNo  and ''!=params.storageNo ">
                
                AND `storage_no`=#{params.storageNo}
                
            </if>
            
            <if test="null!=params.originPlatform  and ''!=params.originPlatform ">
                
                AND `origin_platform`=#{params.originPlatform}
                
            </if>
            
            <if test="null!=params.sendStoreType ">
                
                AND `send_store_type`=#{params.sendStoreType}
                
            </if>
            
            <if test="null!=params.message  and ''!=params.message ">
                
                AND `message`=#{params.message}
                
            </if>
            
            <if test="null!=params.customerNo  and ''!=params.customerNo ">
                
                AND `customer_no`=#{params.customerNo}
                
            </if>
            
            <if test="null!=params.customerName  and ''!=params.customerName ">
                
                AND `customer_name` like CONCAT('%',#{params.customerName},'%') 
                
            </if>
            
            <if test="null!=params.sendStoreNo  and ''!=params.sendStoreNo ">
                
                AND `send_store_no`=#{params.sendStoreNo}
                
            </if>
            
            <if test="null!=params.sendStoreName  and ''!=params.sendStoreName ">
                
                AND `send_store_name` like CONCAT('%',#{params.sendStoreName},'%') 
                
            </if>
            
            <if test="null!=params.orderUnitNo  and ''!=params.orderUnitNo ">
                
                AND `order_unit_no`=#{params.orderUnitNo}
                
            </if>
            
            <if test="null!=params.orderUnitName  and ''!=params.orderUnitName ">
                
                AND `order_unit_name` like CONCAT('%',#{params.orderUnitName},'%') 
                
            </if>
            
            <if test="null!=params.sendOutDate ">
                
                AND `send_out_date`=#{params.sendOutDate}
                
            </if>
            
            <if test="null!=params.logisticCompanyCode  and ''!=params.logisticCompanyCode ">
                
                AND `logistic_company_code`=#{params.logisticCompanyCode}
                
            </if>
            
            <if test="null!=params.logisticsCompanyName  and ''!=params.logisticsCompanyName ">
                
                AND `logistics_company_name` like CONCAT('%',#{params.logisticsCompanyName},'%') 
                
            </if>
            
            <if test="null!=params.expressCodes  and ''!=params.expressCodes ">
                
                AND `express_codes`=#{params.expressCodes}
                
            </if>
            
            <if test="null!=params.expressPrice ">
                
                AND `express_price`=#{params.expressPrice}
                
            </if>
            
            <if test="null!=params.sendDetailTotal ">
                
                AND `send_detail_total`=#{params.sendDetailTotal}
                
            </if>
            
            <if test="null!=params.totalPrice ">
                
                AND `total_price`=#{params.totalPrice}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.createUser  and ''!=params.createUser ">
                
                AND `create_user`=#{params.createUser}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.remark  and ''!=params.remark ">
                
                AND `remark`=#{params.remark}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.retailFlag  and ''!=params.retailFlag ">
                
                AND `retail_flag`=#{params.retailFlag}
                
            </if>
            
            <if test="null!=params.auditTime ">
                
                AND `audit_time`=#{params.auditTime}
                
            </if>
            
            <if test="null!=params.originPlatformName  and ''!=params.originPlatformName ">
                
                AND `origin_platform_name` like CONCAT('%',#{params.originPlatformName},'%') 
                
            </if>
            
            <if test="null!=params.shopNo  and ''!=params.shopNo ">
                
                AND `shop_no`=#{params.shopNo}
                
            </if>
            
            <if test="null!=params.shopName  and ''!=params.shopName ">
                
                AND `shop_name` like CONCAT('%',#{params.shopName},'%') 
                
            </if>
            
            <if test="null!=params.isPayment  and ''!=params.isPayment ">
                
                AND `is_payment`=#{params.isPayment}
                
            </if>
            
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                
                AND `channel_no`=#{params.channelNo}
                
            </if>
            
            <if test="null!=params.packageNo  and ''!=params.packageNo ">
                
                AND `package_no`=#{params.packageNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=billNo and ''!=billNo">
            AND `bill_no`=#{billNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_out
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order_out
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_out
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM retail_order_out
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM retail_order_out
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order_out
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM retail_order_out
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM retail_order_out
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM retail_order_out
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderOut"  >
        INSERT INTO retail_order_out
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="originalOrderNo != null">
                `original_order_no`,
            </if>
            
            <if test="arriveTime != null">
                `arrive_time`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="billType != null">
                `bill_type`,
            </if>
            
            <if test="outBizType != null">
                `out_biz_type`,
            </if>
            
            <if test="refBillNo != null">
                `ref_bill_no`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="storeName != null">
                `store_name`,
            </if>
            
            <if test="storeNo != null">
                `store_no`,
            </if>
            
            <if test="boxCode != null">
                `box_code`,
            </if>
            
            <if test="parcelVolume != null">
                `parcel_volume`,
            </if>
            
            <if test="parcelWeight != null">
                `parcel_weight`,
            </if>
            
            <if test="refBillType != null">
                `ref_bill_type`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="saleCompanyNo != null">
                `sale_company_no`,
            </if>
            
            <if test="theaterSchedule != null">
                `theater_schedule`,
            </if>
            
            <if test="recordCode != null">
                `record_code`,
            </if>
            
            <if test="hasOrder != null">
                `has_order`,
            </if>
            
            <if test="companyNo != null">
                `company_no`,
            </if>
            
            <if test="storageNo != null">
                `storage_no`,
            </if>
            
            <if test="originPlatform != null">
                `origin_platform`,
            </if>
            
            <if test="sendStoreType != null">
                `send_store_type`,
            </if>
            
            <if test="message != null">
                `message`,
            </if>
            
            <if test="customerNo != null">
                `customer_no`,
            </if>
            
            <if test="customerName != null">
                `customer_name`,
            </if>
            
            <if test="sendStoreNo != null">
                `send_store_no`,
            </if>
            
            <if test="sendStoreName != null">
                `send_store_name`,
            </if>
            
            <if test="orderUnitNo != null">
                `order_unit_no`,
            </if>
            
            <if test="orderUnitName != null">
                `order_unit_name`,
            </if>
            
            <if test="sendOutDate != null">
                `send_out_date`,
            </if>
            
            <if test="logisticCompanyCode != null">
                `logistic_company_code`,
            </if>
            
            <if test="logisticsCompanyName != null">
                `logistics_company_name`,
            </if>
            
            <if test="expressCodes != null">
                `express_codes`,
            </if>
            
            <if test="expressPrice != null">
                `express_price`,
            </if>
            
            <if test="sendDetailTotal != null">
                `send_detail_total`,
            </if>
            
            <if test="totalPrice != null">
                `total_price`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="createUser != null">
                `create_user`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="remark != null">
                `remark`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="retailFlag != null">
                `retail_flag`,
            </if>
            
            <if test="auditTime != null">
                `audit_time`,
            </if>
            
            <if test="originPlatformName != null">
                `origin_platform_name`,
            </if>
            
            <if test="shopNo != null">
                `shop_no`,
            </if>
            
            <if test="shopName != null">
                `shop_name`,
            </if>
            
            <if test="isPayment != null">
                `is_payment`,
            </if>
            
            <if test="channelNo != null">
                `channel_no`,
            </if>
            
            <if test="packageNo != null">
                `package_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="originalOrderNo != null">
                #{originalOrderNo},
            </if>
            
            <if test="arriveTime != null">
                #{arriveTime},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="billType != null">
                #{billType},
            </if>
            
            <if test="outBizType != null">
                #{outBizType},
            </if>
            
            <if test="refBillNo != null">
                #{refBillNo},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="storeName != null">
                #{storeName},
            </if>
            
            <if test="storeNo != null">
                #{storeNo},
            </if>
            
            <if test="boxCode != null">
                #{boxCode},
            </if>
            
            <if test="parcelVolume != null">
                #{parcelVolume},
            </if>
            
            <if test="parcelWeight != null">
                #{parcelWeight},
            </if>
            
            <if test="refBillType != null">
                #{refBillType},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="saleCompanyNo != null">
                #{saleCompanyNo},
            </if>
            
            <if test="theaterSchedule != null">
                #{theaterSchedule},
            </if>
            
            <if test="recordCode != null">
                #{recordCode},
            </if>
            
            <if test="hasOrder != null">
                #{hasOrder},
            </if>
            
            <if test="companyNo != null">
                #{companyNo},
            </if>
            
            <if test="storageNo != null">
                #{storageNo},
            </if>
            
            <if test="originPlatform != null">
                #{originPlatform},
            </if>
            
            <if test="sendStoreType != null">
                #{sendStoreType},
            </if>
            
            <if test="message != null">
                #{message},
            </if>
            
            <if test="customerNo != null">
                #{customerNo},
            </if>
            
            <if test="customerName != null">
                #{customerName},
            </if>
            
            <if test="sendStoreNo != null">
                #{sendStoreNo},
            </if>
            
            <if test="sendStoreName != null">
                #{sendStoreName},
            </if>
            
            <if test="orderUnitNo != null">
                #{orderUnitNo},
            </if>
            
            <if test="orderUnitName != null">
                #{orderUnitName},
            </if>
            
            <if test="sendOutDate != null">
                #{sendOutDate},
            </if>
            
            <if test="logisticCompanyCode != null">
                #{logisticCompanyCode},
            </if>
            
            <if test="logisticsCompanyName != null">
                #{logisticsCompanyName},
            </if>
            
            <if test="expressCodes != null">
                #{expressCodes},
            </if>
            
            <if test="expressPrice != null">
                #{expressPrice},
            </if>
            
            <if test="sendDetailTotal != null">
                #{sendDetailTotal},
            </if>
            
            <if test="totalPrice != null">
                #{totalPrice},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="createUser != null">
                #{createUser},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="remark != null">
                #{remark},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="retailFlag != null">
                #{retailFlag},
            </if>
            
            <if test="auditTime != null">
                #{auditTime},
            </if>
            
            <if test="originPlatformName != null">
                #{originPlatformName},
            </if>
            
            <if test="shopNo != null">
                #{shopNo},
            </if>
            
            <if test="shopName != null">
                #{shopName},
            </if>
            
            <if test="isPayment != null">
                #{isPayment},
            </if>
            
            <if test="channelNo != null">
                #{channelNo},
            </if>
            
            <if test="packageNo != null">
                #{packageNo},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderOut"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrderOut">
        UPDATE retail_order_out
        <set>
            
            <if test="originalOrderNo != null">
                `original_order_no` = #{originalOrderNo},
            </if> 
            <if test="arriveTime != null">
                `arrive_time` = #{arriveTime},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="billType != null">
                `bill_type` = #{billType},
            </if> 
            <if test="outBizType != null">
                `out_biz_type` = #{outBizType},
            </if> 
            <if test="refBillNo != null">
                `ref_bill_no` = #{refBillNo},
            </if> 
            <if test="storeName != null">
                `store_name` = #{storeName},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            <if test="boxCode != null">
                `box_code` = #{boxCode},
            </if> 
            <if test="parcelVolume != null">
                `parcel_volume` = #{parcelVolume},
            </if> 
            <if test="parcelWeight != null">
                `parcel_weight` = #{parcelWeight},
            </if> 
            <if test="refBillType != null">
                `ref_bill_type` = #{refBillType},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="saleCompanyNo != null">
                `sale_company_no` = #{saleCompanyNo},
            </if> 
            <if test="theaterSchedule != null">
                `theater_schedule` = #{theaterSchedule},
            </if> 
            <if test="recordCode != null">
                `record_code` = #{recordCode},
            </if> 
            <if test="hasOrder != null">
                `has_order` = #{hasOrder},
            </if> 
            <if test="companyNo != null">
                `company_no` = #{companyNo},
            </if> 
            <if test="storageNo != null">
                `storage_no` = #{storageNo},
            </if> 
            <if test="originPlatform != null">
                `origin_platform` = #{originPlatform},
            </if> 
            <if test="sendStoreType != null">
                `send_store_type` = #{sendStoreType},
            </if> 
            <if test="message != null">
                `message` = #{message},
            </if> 
            <if test="customerNo != null">
                `customer_no` = #{customerNo},
            </if> 
            <if test="customerName != null">
                `customer_name` = #{customerName},
            </if> 
            <if test="sendStoreNo != null">
                `send_store_no` = #{sendStoreNo},
            </if> 
            <if test="sendStoreName != null">
                `send_store_name` = #{sendStoreName},
            </if> 
            <if test="orderUnitNo != null">
                `order_unit_no` = #{orderUnitNo},
            </if> 
            <if test="orderUnitName != null">
                `order_unit_name` = #{orderUnitName},
            </if> 
            <if test="sendOutDate != null">
                `send_out_date` = #{sendOutDate},
            </if> 
            <if test="logisticCompanyCode != null">
                `logistic_company_code` = #{logisticCompanyCode},
            </if> 
            <if test="logisticsCompanyName != null">
                `logistics_company_name` = #{logisticsCompanyName},
            </if> 
            <if test="expressCodes != null">
                `express_codes` = #{expressCodes},
            </if> 
            <if test="expressPrice != null">
                `express_price` = #{expressPrice},
            </if> 
            <if test="sendDetailTotal != null">
                `send_detail_total` = #{sendDetailTotal},
            </if> 
            <if test="totalPrice != null">
                `total_price` = #{totalPrice},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="createUser != null">
                `create_user` = #{createUser},
            </if> 
            <if test="remark != null">
                `remark` = #{remark},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="retailFlag != null">
                `retail_flag` = #{retailFlag},
            </if> 
            <if test="auditTime != null">
                `audit_time` = #{auditTime},
            </if> 
            <if test="originPlatformName != null">
                `origin_platform_name` = #{originPlatformName},
            </if> 
            <if test="shopNo != null">
                `shop_no` = #{shopNo},
            </if> 
            <if test="shopName != null">
                `shop_name` = #{shopName},
            </if> 
            <if test="isPayment != null">
                `is_payment` = #{isPayment},
            </if> 
            <if test="channelNo != null">
                `channel_no` = #{channelNo},
            </if> 
            <if test="packageNo != null">
                `package_no` = #{packageNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE bill_no = #{billNo} OR id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>