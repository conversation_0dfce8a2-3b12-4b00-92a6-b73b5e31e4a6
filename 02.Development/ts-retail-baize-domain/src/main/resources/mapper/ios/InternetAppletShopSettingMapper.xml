<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetAppletShopSettingRepository">
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetAppletShopSetting">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="shop_no" jdbcType="VARCHAR" property="shopNo" />
        <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="groups" jdbcType="TINYINT" property="groups" />
        <result column="stock_type" jdbcType="VARCHAR" property="stockType" />
        <result column="sharing_ratio" jdbcType="TINYINT" property="sharingRatio" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    </resultMap>

    <sql id="column_list">
        id, shop_no, shop_name, type, `groups`, stock_type,`sharing_ratio`,`start_time`,`end_time`, update_time, create_time, create_user, update_user
    </sql>


    <sql id="condition">
        <if test="params != null">
            <if test="params.shopNo != null and params.shopNo != ''">
                AND shop_no = #{params.shopNo,jdbcType=VARCHAR}
            </if>
            <if test="params.shopName != null and params.shopName != ''">
                AND shop_name = #{params.shopName,jdbcType=VARCHAR}
            </if>
            <if test="params.type != null ">
                AND type = #{params.type,jdbcType=TINYINT}
            </if>
            <if test="params.groups != null ">
                AND `groups` = #{params.groups,jdbcType=TINYINT}
            </if>
            <if test="params.idList != null ">
                <foreach collection="params.idList" item="id" open="AND id in (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="params.shopNoList != null ">
                <foreach collection="params.shopNoList" item="shopNo" open="AND shop_no in (" close=")" separator=",">
                    #{shopNo}
                </foreach>
            </if>
        </if>
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_applet_shop_setting
        WHERE id = #{id}
    </select>


    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_applet_shop_setting
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_applet_shop_setting
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_applet_shop_setting
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_applet_shop_setting
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetAppletShopSetting"  >
        insert into internet_applet_shop_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="shopNo != null">
                shop_no,
            </if>
            <if test="shopName != null">
                shop_name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="groups != null">
                `groups`,
            </if>
            <if test="stockType != null">
                `stock_type`,
            </if>
            <if test="sharingRatio != null">
                `sharing_ratio`,
            </if>
            <if test="startTime != null">
                `start_time`,
            </if>
            <if test="endTime != null">
                `end_time`,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="shopNo != null">
                #{shopNo,jdbcType=VARCHAR},
            </if>
            <if test="shopName != null">
                #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="groups != null">
                #{groups,jdbcType=TINYINT},
            </if>
            <if test="stockType != null">
                #{stockType,jdbcType=VARCHAR},
            </if>
            <if test="sharingRatio != null">
                #{sharingRatio,jdbcType=TINYINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="cn.wonhigh.baize.model.entity.ios.InternetAppletShopSetting" useGeneratedKeys="true" keyProperty="id">
        insert into internet_applet_shop_setting (
        id, shop_no, shop_name, type, `groups`, stock_type,`sharing_ratio`,`start_time`,`end_time`, update_time, create_time, create_user, update_user
        ) values
        <foreach collection="list" item="data" separator=",">
            (#{data.id,jdbcType=VARCHAR}, #{data.shopNo,jdbcType=VARCHAR}, #{data.shopName,jdbcType=VARCHAR},
            #{data.type,jdbcType=TINYINT},#{data.groups,jdbcType=TINYINT},#{data.stockType,jdbcType=VARCHAR},
            #{data.sharingRatio,jdbcType=TINYINT},#{data.startTime,jdbcType=TIMESTAMP},#{data.endTime,jdbcType=TIMESTAMP},
            #{data.updateTime,jdbcType=TIMESTAMP}, #{data.createTime,jdbcType=TIMESTAMP},
            #{data.createUser,jdbcType=VARCHAR}, #{data.updateUser,jdbcType=VARCHAR})
        </foreach>
    </insert>
    
    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetAppletShopSetting">
        update internet_applet_shop_setting
        <set>
            <if test="shopNo != null">
                shop_no = #{shopNo,jdbcType=VARCHAR},
            </if>
            <if test="shopName != null">
                shop_name = #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="groups != null">
                `groups` = #{groups,jdbcType=TINYINT},
            </if>
            <if test="stockType != null">
                `stock_type` = #{stockType,jdbcType=VARCHAR},
            </if>
            <if test="sharingRatio != null">
                `sharing_ratio` = #{sharingRatio,jdbcType=TINYINT},
            </if>
            <if test="startTime != null">
                `start_time` = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                `end_time` = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id}
                
    </update>



    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_applet_shop_setting
        WHERE id = #{id}
    </delete>


    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_applet_shop_setting
        <where>
            <include refid="condition" />
        </where>
    </delete>


    <update id="updateByShopNo" parameterType="cn.wonhigh.baize.model.entity.ios.InternetAppletShopSetting">
        update internet_applet_shop_setting
        <set>
            <if test="shopName != null">
                shop_name = #{shopName,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="groups != null">
                `groups` = #{groups,jdbcType=TINYINT},
            </if>
            <if test="stockType != null">
                `stock_type` = #{stockType,jdbcType=VARCHAR},
            </if>
            <if test="sharingRatio != null">
                `sharing_ratio` = #{sharingRatio,jdbcType=TINYINT},
            </if>
            <if test="startTime != null">
                `start_time` = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                `end_time` = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE shop_no = #{shopNo}

    </update>


</mapper>