<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.AsyncTaskCompletedRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.AsyncTaskCompleted">
        <id column="id" property="id" jdbcType="CHAR"/>
        <result column="task_type" property="taskType" jdbcType="INTEGER"/>
        <result column="bill_no" property="billNo" jdbcType="CHAR"/>
        <result column="bill_type" property="billType" jdbcType="INTEGER"/>
        <result column="ref_bill_no" property="refBillNo" jdbcType="CHAR"/>
        <result column="ref_bill_type" property="refBillType" jdbcType="INTEGER"/>
        <result column="pre_task_type" property="preTaskType" jdbcType="INTEGER"/>
        <result column="pre_task_search_mode" property="preTaskSearchMode" jdbcType="TINYINT"/>
        <result column="validate_batch" property="validateBatch" jdbcType="CHAR"/>
        <result column="batch" property="batch" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR"/>
        <result column="error_msg" property="errorMsg" jdbcType="VARCHAR"/>
        <result column="host_info" property="hostInfo" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="sent_batch" property="sentBatch" jdbcType="CHAR"/>
        <result column="sent_type" property="sentType" jdbcType="VARCHAR"/>
        <result column="execute_time" property="executeTime" jdbcType="TIMESTAMP"/>
        <result column="elapsed_time" property="elapsedTime" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="column_list">
        `id`,
        `task_type`,
        `bill_no`,
        `bill_type`,
        `ref_bill_no`,
        `ref_bill_type`,
        `pre_task_type`,
        `pre_task_search_mode`,
        `validate_batch`,
        `batch`,
        `status`,
        `sharding_flag`,
        `error_msg`,
        `host_info`,
        `remark`,
        `sent_batch`,
        `sent_type`,
        `execute_time`,
        `elapsed_time`,
        `create_time`,
        `create_user`,
        `update_time`,
        `update_user`
    </sql>

    <sql id="condition">
        <if test="null != params">
            <if test="null != params.queryCondition and '' != params.queryCondition">
                AND ${params.queryCondition}
            </if>
            <if test="null != params.id and '' != params.id">
                AND id = #{params.id}
            </if>
            <if test="null != params.taskType">
                AND task_type = #{params.taskType}
            </if>
            <if test="null != params.billNo and '' != params.billNo">
                AND bill_no = #{params.billNo}
            </if>
            <if test="null != params.billType">
                AND bill_type = #{params.billType}
            </if>
            <if test="null != params.refBillNo and '' != params.refBillNo">
                AND ref_bill_no = #{params.refBillNo}
            </if>
            <if test="null != params.refBillType">
                AND ref_bill_type = #{params.refBillType}
            </if>
            <if test="null != params.preTaskType">
                AND pre_task_type = #{params.preTaskType}
            </if>
            <if test="null != params.preTaskSearchMode">
                AND pre_task_search_mode = #{params.preTaskSearchMode}
            </if>
            <if test="null != params.validateBatch and '' != params.validateBatch">
                AND validate_batch = #{params.validateBatch}
            </if>
            <if test="null != params.batch">
                AND batch = #{params.batch}
            </if>
            <if test="null != params.status">
                AND status = #{params.status}
            </if>
            <if test="null != params.shardingFlag and '' != params.shardingFlag">
                AND sharding_flag = #{params.shardingFlag}
            </if>
            <if test="null != params.errorMsg and '' != params.errorMsg">
                AND error_msg = #{params.errorMsg}
            </if>
            <if test="null != params.hostInfo and '' != params.hostInfo">
                AND host_info = #{params.hostInfo}
            </if>
            <if test="null != params.remark and '' != params.remark">
                AND remark = #{params.remark}
            </if>
            <if test="null != params.sentBatch and '' != params.sentBatch">
                AND sent_batch = #{params.sentBatch}
            </if>
            <if test="null != params.sentType and '' != params.sentType">
                AND sent_type = #{params.sentType}
            </if>
            <if test="null != params.executeTime">
                AND execute_time = #{params.executeTime}
            </if>
            <if test="null != params.elapsedTime">
                AND elapsed_time = #{params.elapsedTime}
            </if>
            <if test="null != params.createTime">
                AND create_time = #{params.createTime}
            </if>
            <if test="null != params.createUser and '' != params.createUser">
                AND create_user = #{params.createUser}
            </if>
            <if test="null != params.updateTime">
                AND update_time = #{params.updateTime}
            </if>
            <if test="null != params.updateUser and '' != params.updateUser">
                AND update_user = #{params.updateUser}
            </if>
        </if>
    </sql>


    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM async_task_completed
        WHERE id = #{id}
    </select>


    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM async_task_completed
        <where>
            <include refid="condition" />
        </where>

        LIMIT 1

    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM async_task_completed
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">

        SELECT
        <include refid="column_list" />
        FROM async_task_completed
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}

    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM async_task_completed
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <select id="getLastCreateByBillNos" resultMap="baseResultMap">
        SELECT
            <include refid="column_list" />
        FROM async_task_completed
        WHERE task_type = #{taskType}
        <foreach collection="billNos" item="billNO" open="AND bill_no in (" close=")" separator=",">
            #{billNO}
        </foreach>
        order by create_time desc limit 1
    </select>

    <!-- auto generate end-->


</mapper>