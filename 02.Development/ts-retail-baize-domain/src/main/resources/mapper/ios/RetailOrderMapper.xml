<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.RetailOrderRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.RetailOrder">
                
        <id column="id" property="id" jdbcType="CHAR" />
        
        
        <result column="business_type" property="businessType" jdbcType="TINYINT" />
        
        <result column="order_mark_name" property="orderMarkName" jdbcType="VARCHAR" />
        
        <result column="sync_time_stamp" property="syncTimeStamp" jdbcType="INTEGER" />
        
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="is_vip" property="isVip" jdbcType="TINYINT" />
        
        <result column="order_status_name" property="orderStatusName" jdbcType="VARCHAR" />
        
        <result column="return_bill_no" property="returnBillNo" jdbcType="CHAR" />
        
        <result column="ship_time" property="shipTime" jdbcType="TIMESTAMP" />
        
        <result column="follow_result_type" property="followResultType" jdbcType="TINYINT" />
        
        <result column="balance_due_date" property="balanceDueDate" jdbcType="TIMESTAMP" />
        
        <result column="po_no" property="poNo" jdbcType="VARCHAR" />
        
        <result column="close_code" property="closeCode" jdbcType="VARCHAR" />
        
        <result column="order_pay_total_amont" property="orderPayTotalAmont" jdbcType="DECIMAL" />
        
        <result column="order_amount" property="orderAmount" jdbcType="DECIMAL" />
        
        <result column="send_detail_total" property="sendDetailTotal" jdbcType="INTEGER" />
        
        <result column="actual_postage" property="actualPostage" jdbcType="DECIMAL" />
        
        <result column="logistics_name" property="logisticsName" jdbcType="VARCHAR" />
        
        <result column="logistics_code" property="logisticsCode" jdbcType="CHAR" />
        
        <result column="return_consignee_name" property="returnConsigneeName" jdbcType="VARCHAR" />
        
        <result column="order_type" property="orderType" jdbcType="TINYINT" />
        
        <result column="return_zipcode" property="returnZipcode" jdbcType="VARCHAR" />
        
        <result column="order_mark_id" property="orderMarkId" jdbcType="CHAR" />
        
        <result column="return_address" property="returnAddress" jdbcType="VARCHAR" />
        
        <result column="return_constact_phone" property="returnConstactPhone" jdbcType="VARCHAR" />
        
        <result column="need_grab" property="needGrab" jdbcType="TINYINT" />
        
        <result column="online_pay_time" property="onlinePayTime" jdbcType="TIMESTAMP" />
        
        <result column="problem_type_id" property="problemTypeId" jdbcType="CHAR" />
        
        <result column="problem_type_name" property="problemTypeName" jdbcType="VARCHAR" />
        
        <result column="user_store" property="userStore" jdbcType="TINYINT" />
        
        <result column="sale_company_no" property="saleCompanyNo" jdbcType="CHAR" />
        
        <result column="order_create_time" property="orderCreateTime" jdbcType="TIMESTAMP" />
        
        <result column="source_type" property="sourceType" jdbcType="TINYINT" />
        
        <result column="order_style" property="orderStyle" jdbcType="TINYINT" />
        
        <result column="bill_no" property="billNo" jdbcType="CHAR" />
        
        <result column="ref_bill_no" property="refBillNo" jdbcType="CHAR" />
        
        <result column="original_order_no" property="originalOrderNo" jdbcType="VARCHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
        <result column="out_order_id" property="outOrderId" jdbcType="VARCHAR" />
        
        <result column="order_status" property="orderStatus" jdbcType="TINYINT" />
        
        <result column="pay_status" property="payStatus" jdbcType="TINYINT" />
        
        <result column="pay_name" property="payName" jdbcType="VARCHAR" />
        
        <result column="payment_code" property="paymentCode" jdbcType="VARCHAR" />
        
        <result column="deliver_status" property="deliverStatus" jdbcType="TINYINT" />
        
        <result column="deliver_status_name" property="deliverStatusName" jdbcType="VARCHAR" />
        
        <result column="interface_platform" property="interfacePlatform" jdbcType="VARCHAR" />
        
        <result column="origin_platform" property="originPlatform" jdbcType="VARCHAR" />
        
        <result column="origin_platform_name" property="originPlatformName" jdbcType="VARCHAR" />
        
        <result column="shop_no" property="shopNo" jdbcType="VARCHAR" />
        
        <result column="validate_batch" property="validateBatch" jdbcType="CHAR" />
        
        <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
        
        <result column="channel_no" property="channelNo" jdbcType="VARCHAR" />
        
        <result column="store_no" property="storeNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `business_type`,`order_mark_name`,`sync_time_stamp`,`create_time`,`update_time`,`sharding_flag`,`is_vip`,`order_status_name`,`return_bill_no`,`ship_time`,`follow_result_type`,`balance_due_date`,`po_no`,`close_code`,`order_pay_total_amont`,`order_amount`,`send_detail_total`,`actual_postage`,`logistics_name`,`logistics_code`,`return_consignee_name`,`order_type`,`return_zipcode`,`order_mark_id`,`return_address`,`return_constact_phone`,`need_grab`,`online_pay_time`,`problem_type_id`,`problem_type_name`,`user_store`,`sale_company_no`,`order_create_time`,`source_type`,`order_style`,`id`,`bill_no`,`ref_bill_no`,`original_order_no`,`order_sub_no`,`out_order_id`,`order_status`,`pay_status`,`pay_name`,`payment_code`,`deliver_status`,`deliver_status_name`,`interface_platform`,`origin_platform`,`origin_platform_name`,`shop_no`,`validate_batch`,`shop_name`,`channel_no`,`store_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.businessType ">
                
                AND `business_type`=#{params.businessType}
                
            </if>
            
            <if test="null!=params.orderMarkName  and ''!=params.orderMarkName ">
                
                AND `order_mark_name` like CONCAT('%',#{params.orderMarkName},'%') 
                
            </if>
            
            <if test="null!=params.syncTimeStamp ">
                
                AND `sync_time_stamp`=#{params.syncTimeStamp}
                
            </if>
            
            <if test="null!=params.createTime ">
                
                AND `create_time`=#{params.createTime}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.isVip ">
                
                AND `is_vip`=#{params.isVip}
                
            </if>
            
            <if test="null!=params.orderStatusName  and ''!=params.orderStatusName ">
                
                AND `order_status_name` like CONCAT('%',#{params.orderStatusName},'%') 
                
            </if>
            
            <if test="null!=params.returnBillNo  and ''!=params.returnBillNo ">
                
                AND `return_bill_no`=#{params.returnBillNo}
                
            </if>
            
            <if test="null!=params.shipTime ">
                
                AND `ship_time`=#{params.shipTime}
                
            </if>
            
            <if test="null!=params.followResultType ">
                
                AND `follow_result_type`=#{params.followResultType}
                
            </if>
            
            <if test="null!=params.balanceDueDate ">
                
                AND `balance_due_date`=#{params.balanceDueDate}
                
            </if>
            
            <if test="null!=params.poNo  and ''!=params.poNo ">
                
                AND `po_no`=#{params.poNo}
                
            </if>
            
            <if test="null!=params.closeCode  and ''!=params.closeCode ">
                
                AND `close_code`=#{params.closeCode}
                
            </if>
            
            <if test="null!=params.orderPayTotalAmont ">
                
                AND `order_pay_total_amont`=#{params.orderPayTotalAmont}
                
            </if>
            
            <if test="null!=params.orderAmount ">
                
                AND `order_amount`=#{params.orderAmount}
                
            </if>
            
            <if test="null!=params.sendDetailTotal ">
                
                AND `send_detail_total`=#{params.sendDetailTotal}
                
            </if>
            
            <if test="null!=params.actualPostage ">
                
                AND `actual_postage`=#{params.actualPostage}
                
            </if>
            
            <if test="null!=params.logisticsName  and ''!=params.logisticsName ">
                
                AND `logistics_name` like CONCAT('%',#{params.logisticsName},'%') 
                
            </if>
            
            <if test="null!=params.logisticsCode  and ''!=params.logisticsCode ">
                
                AND `logistics_code`=#{params.logisticsCode}
                
            </if>
            
            <if test="null!=params.returnConsigneeName  and ''!=params.returnConsigneeName ">
                
                AND `return_consignee_name` like CONCAT('%',#{params.returnConsigneeName},'%') 
                
            </if>
            
            <if test="null!=params.orderType ">
                
                AND `order_type`=#{params.orderType}
                
            </if>
            
            <if test="null!=params.returnZipcode  and ''!=params.returnZipcode ">
                
                AND `return_zipcode`=#{params.returnZipcode}
                
            </if>
            
            <if test="null!=params.orderMarkId  and ''!=params.orderMarkId ">
                
                AND `order_mark_id`=#{params.orderMarkId}
                
            </if>
            
            <if test="null!=params.returnAddress  and ''!=params.returnAddress ">
                
                AND `return_address`=#{params.returnAddress}
                
            </if>
            
            <if test="null!=params.returnConstactPhone  and ''!=params.returnConstactPhone ">
                
                AND `return_constact_phone`=#{params.returnConstactPhone}
                
            </if>
            
            <if test="null!=params.needGrab ">
                
                AND `need_grab`=#{params.needGrab}
                
            </if>
            
            <if test="null!=params.onlinePayTime ">
                
                AND `online_pay_time`=#{params.onlinePayTime}
                
            </if>
            
            <if test="null!=params.problemTypeId  and ''!=params.problemTypeId ">
                
                AND `problem_type_id`=#{params.problemTypeId}
                
            </if>
            
            <if test="null!=params.problemTypeName  and ''!=params.problemTypeName ">
                
                AND `problem_type_name` like CONCAT('%',#{params.problemTypeName},'%') 
                
            </if>
            
            <if test="null!=params.userStore ">
                
                AND `user_store`=#{params.userStore}
                
            </if>
            
            <if test="null!=params.saleCompanyNo  and ''!=params.saleCompanyNo ">
                
                AND `sale_company_no`=#{params.saleCompanyNo}
                
            </if>
            
            <if test="null!=params.orderCreateTime ">
                
                AND `order_create_time`=#{params.orderCreateTime}
                
            </if>
            
            <if test="null!=params.sourceType ">
                
                AND `source_type`=#{params.sourceType}
                
            </if>
            
            <if test="null!=params.orderStyle ">
                
                AND `order_style`=#{params.orderStyle}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
            <if test="null!=params.refBillNo  and ''!=params.refBillNo ">
                
                AND `ref_bill_no`=#{params.refBillNo}
                
            </if>
            
            <if test="null!=params.originalOrderNo  and ''!=params.originalOrderNo ">
                
                AND `original_order_no`=#{params.originalOrderNo}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
            <if test="null!=params.outOrderId  and ''!=params.outOrderId ">
                
                AND `out_order_id`=#{params.outOrderId}
                
            </if>
            
            <if test="null!=params.orderStatus ">
                
                AND `order_status`=#{params.orderStatus}
                
            </if>
            
            <if test="null!=params.payStatus ">
                
                AND `pay_status`=#{params.payStatus}
                
            </if>
            
            <if test="null!=params.payName  and ''!=params.payName ">
                
                AND `pay_name` like CONCAT('%',#{params.payName},'%') 
                
            </if>
            
            <if test="null!=params.paymentCode  and ''!=params.paymentCode ">
                
                AND `payment_code`=#{params.paymentCode}
                
            </if>
            
            <if test="null!=params.deliverStatus ">
                
                AND `deliver_status`=#{params.deliverStatus}
                
            </if>
            
            <if test="null!=params.deliverStatusName  and ''!=params.deliverStatusName ">
                
                AND `deliver_status_name` like CONCAT('%',#{params.deliverStatusName},'%') 
                
            </if>
            
            <if test="null!=params.interfacePlatform  and ''!=params.interfacePlatform ">
                
                AND `interface_platform`=#{params.interfacePlatform}
                
            </if>
            
            <if test="null!=params.originPlatform  and ''!=params.originPlatform ">
                
                AND `origin_platform`=#{params.originPlatform}
                
            </if>
            
            <if test="null!=params.originPlatformName  and ''!=params.originPlatformName ">
                
                AND `origin_platform_name` like CONCAT('%',#{params.originPlatformName},'%') 
                
            </if>
            
            <if test="null!=params.shopNo  and ''!=params.shopNo ">
                
                AND `shop_no`=#{params.shopNo}
                
            </if>
            
            <if test="null!=params.validateBatch  and ''!=params.validateBatch ">
                
                AND `validate_batch`=#{params.validateBatch}
                
            </if>
            
            <if test="null!=params.shopName  and ''!=params.shopName ">
                
                AND `shop_name` like CONCAT('%',#{params.shopName},'%') 
                
            </if>
            
            <if test="null!=params.channelNo  and ''!=params.channelNo ">
                
                AND `channel_no`=#{params.channelNo}
                
            </if>
            
            <if test="null!=params.storeNo  and ''!=params.storeNo ">
                
                AND `store_no`=#{params.storeNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
        <if test="null!=billNo and ''!=billNo">
            AND `bill_no`=#{billNo}
        </if>
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM retail_order
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM retail_order
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM retail_order
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM retail_order
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM retail_order
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM retail_order
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM retail_order
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrder"  >
        INSERT INTO retail_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="businessType != null">
                `business_type`,
            </if>
            
            <if test="orderMarkName != null">
                `order_mark_name`,
            </if>
            
            <if test="syncTimeStamp != null">
                `sync_time_stamp`,
            </if>
            
            <if test="createTime != null">
                `create_time`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="isVip != null">
                `is_vip`,
            </if>
            
            <if test="orderStatusName != null">
                `order_status_name`,
            </if>
            
            <if test="returnBillNo != null">
                `return_bill_no`,
            </if>
            
            <if test="shipTime != null">
                `ship_time`,
            </if>
            
            <if test="followResultType != null">
                `follow_result_type`,
            </if>
            
            <if test="balanceDueDate != null">
                `balance_due_date`,
            </if>
            
            <if test="poNo != null">
                `po_no`,
            </if>
            
            <if test="closeCode != null">
                `close_code`,
            </if>
            
            <if test="orderPayTotalAmont != null">
                `order_pay_total_amont`,
            </if>
            
            <if test="orderAmount != null">
                `order_amount`,
            </if>
            
            <if test="sendDetailTotal != null">
                `send_detail_total`,
            </if>
            
            <if test="actualPostage != null">
                `actual_postage`,
            </if>
            
            <if test="logisticsName != null">
                `logistics_name`,
            </if>
            
            <if test="logisticsCode != null">
                `logistics_code`,
            </if>
            
            <if test="returnConsigneeName != null">
                `return_consignee_name`,
            </if>
            
            <if test="orderType != null">
                `order_type`,
            </if>
            
            <if test="returnZipcode != null">
                `return_zipcode`,
            </if>
            
            <if test="orderMarkId != null">
                `order_mark_id`,
            </if>
            
            <if test="returnAddress != null">
                `return_address`,
            </if>
            
            <if test="returnConstactPhone != null">
                `return_constact_phone`,
            </if>
            
            <if test="needGrab != null">
                `need_grab`,
            </if>
            
            <if test="onlinePayTime != null">
                `online_pay_time`,
            </if>
            
            <if test="problemTypeId != null">
                `problem_type_id`,
            </if>
            
            <if test="problemTypeName != null">
                `problem_type_name`,
            </if>
            
            <if test="userStore != null">
                `user_store`,
            </if>
            
            <if test="saleCompanyNo != null">
                `sale_company_no`,
            </if>
            
            <if test="orderCreateTime != null">
                `order_create_time`,
            </if>
            
            <if test="sourceType != null">
                `source_type`,
            </if>
            
            <if test="orderStyle != null">
                `order_style`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
            <if test="refBillNo != null">
                `ref_bill_no`,
            </if>
            
            <if test="originalOrderNo != null">
                `original_order_no`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="outOrderId != null">
                `out_order_id`,
            </if>
            
            <if test="orderStatus != null">
                `order_status`,
            </if>
            
            <if test="payStatus != null">
                `pay_status`,
            </if>
            
            <if test="payName != null">
                `pay_name`,
            </if>
            
            <if test="paymentCode != null">
                `payment_code`,
            </if>
            
            <if test="deliverStatus != null">
                `deliver_status`,
            </if>
            
            <if test="deliverStatusName != null">
                `deliver_status_name`,
            </if>
            
            <if test="interfacePlatform != null">
                `interface_platform`,
            </if>
            
            <if test="originPlatform != null">
                `origin_platform`,
            </if>
            
            <if test="originPlatformName != null">
                `origin_platform_name`,
            </if>
            
            <if test="shopNo != null">
                `shop_no`,
            </if>
            
            <if test="validateBatch != null">
                `validate_batch`,
            </if>
            
            <if test="shopName != null">
                `shop_name`,
            </if>
            
            <if test="channelNo != null">
                `channel_no`,
            </if>
            
            <if test="storeNo != null">
                `store_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="businessType != null">
                #{businessType},
            </if>
            
            <if test="orderMarkName != null">
                #{orderMarkName},
            </if>
            
            <if test="syncTimeStamp != null">
                #{syncTimeStamp},
            </if>
            
            <if test="createTime != null">
                #{createTime},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="isVip != null">
                #{isVip},
            </if>
            
            <if test="orderStatusName != null">
                #{orderStatusName},
            </if>
            
            <if test="returnBillNo != null">
                #{returnBillNo},
            </if>
            
            <if test="shipTime != null">
                #{shipTime},
            </if>
            
            <if test="followResultType != null">
                #{followResultType},
            </if>
            
            <if test="balanceDueDate != null">
                #{balanceDueDate},
            </if>
            
            <if test="poNo != null">
                #{poNo},
            </if>
            
            <if test="closeCode != null">
                #{closeCode},
            </if>
            
            <if test="orderPayTotalAmont != null">
                #{orderPayTotalAmont},
            </if>
            
            <if test="orderAmount != null">
                #{orderAmount},
            </if>
            
            <if test="sendDetailTotal != null">
                #{sendDetailTotal},
            </if>
            
            <if test="actualPostage != null">
                #{actualPostage},
            </if>
            
            <if test="logisticsName != null">
                #{logisticsName},
            </if>
            
            <if test="logisticsCode != null">
                #{logisticsCode},
            </if>
            
            <if test="returnConsigneeName != null">
                #{returnConsigneeName},
            </if>
            
            <if test="orderType != null">
                #{orderType},
            </if>
            
            <if test="returnZipcode != null">
                #{returnZipcode},
            </if>
            
            <if test="orderMarkId != null">
                #{orderMarkId},
            </if>
            
            <if test="returnAddress != null">
                #{returnAddress},
            </if>
            
            <if test="returnConstactPhone != null">
                #{returnConstactPhone},
            </if>
            
            <if test="needGrab != null">
                #{needGrab},
            </if>
            
            <if test="onlinePayTime != null">
                #{onlinePayTime},
            </if>
            
            <if test="problemTypeId != null">
                #{problemTypeId},
            </if>
            
            <if test="problemTypeName != null">
                #{problemTypeName},
            </if>
            
            <if test="userStore != null">
                #{userStore},
            </if>
            
            <if test="saleCompanyNo != null">
                #{saleCompanyNo},
            </if>
            
            <if test="orderCreateTime != null">
                #{orderCreateTime},
            </if>
            
            <if test="sourceType != null">
                #{sourceType},
            </if>
            
            <if test="orderStyle != null">
                #{orderStyle},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
            <if test="refBillNo != null">
                #{refBillNo},
            </if>
            
            <if test="originalOrderNo != null">
                #{originalOrderNo},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="outOrderId != null">
                #{outOrderId},
            </if>
            
            <if test="orderStatus != null">
                #{orderStatus},
            </if>
            
            <if test="payStatus != null">
                #{payStatus},
            </if>
            
            <if test="payName != null">
                #{payName},
            </if>
            
            <if test="paymentCode != null">
                #{paymentCode},
            </if>
            
            <if test="deliverStatus != null">
                #{deliverStatus},
            </if>
            
            <if test="deliverStatusName != null">
                #{deliverStatusName},
            </if>
            
            <if test="interfacePlatform != null">
                #{interfacePlatform},
            </if>
            
            <if test="originPlatform != null">
                #{originPlatform},
            </if>
            
            <if test="originPlatformName != null">
                #{originPlatformName},
            </if>
            
            <if test="shopNo != null">
                #{shopNo},
            </if>
            
            <if test="validateBatch != null">
                #{validateBatch},
            </if>
            
            <if test="shopName != null">
                #{shopName},
            </if>
            
            <if test="channelNo != null">
                #{channelNo},
            </if>
            
            <if test="storeNo != null">
                #{storeNo},
            </if>
            
        </trim>
    </insert>


    <insert id="insertForUpdate" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrder"  >
        <!-- 未实现 -->
    </insert>



    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.RetailOrder">
        UPDATE retail_order
        <set>
            
            <if test="businessType != null">
                `business_type` = #{businessType},
            </if> 
            <if test="orderMarkName != null">
                `order_mark_name` = #{orderMarkName},
            </if> 
            <if test="syncTimeStamp != null">
                `sync_time_stamp` = #{syncTimeStamp},
            </if> 
            <if test="createTime != null">
                `create_time` = #{createTime},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="isVip != null">
                `is_vip` = #{isVip},
            </if> 
            <if test="orderStatusName != null">
                `order_status_name` = #{orderStatusName},
            </if> 
            <if test="returnBillNo != null">
                `return_bill_no` = #{returnBillNo},
            </if> 
            <if test="shipTime != null">
                `ship_time` = #{shipTime},
            </if> 
            <if test="followResultType != null">
                `follow_result_type` = #{followResultType},
            </if> 
            <if test="balanceDueDate != null">
                `balance_due_date` = #{balanceDueDate},
            </if> 
            <if test="poNo != null">
                `po_no` = #{poNo},
            </if> 
            <if test="closeCode != null">
                `close_code` = #{closeCode},
            </if> 
            <if test="orderPayTotalAmont != null">
                `order_pay_total_amont` = #{orderPayTotalAmont},
            </if> 
            <if test="orderAmount != null">
                `order_amount` = #{orderAmount},
            </if> 
            <if test="sendDetailTotal != null">
                `send_detail_total` = #{sendDetailTotal},
            </if> 
            <if test="actualPostage != null">
                `actual_postage` = #{actualPostage},
            </if> 
            <if test="logisticsName != null">
                `logistics_name` = #{logisticsName},
            </if> 
            <if test="logisticsCode != null">
                `logistics_code` = #{logisticsCode},
            </if> 
            <if test="returnConsigneeName != null">
                `return_consignee_name` = #{returnConsigneeName},
            </if> 
            <if test="orderType != null">
                `order_type` = #{orderType},
            </if> 
            <if test="returnZipcode != null">
                `return_zipcode` = #{returnZipcode},
            </if> 
            <if test="orderMarkId != null">
                `order_mark_id` = #{orderMarkId},
            </if> 
            <if test="returnAddress != null">
                `return_address` = #{returnAddress},
            </if> 
            <if test="returnConstactPhone != null">
                `return_constact_phone` = #{returnConstactPhone},
            </if> 
            <if test="needGrab != null">
                `need_grab` = #{needGrab},
            </if> 
            <if test="onlinePayTime != null">
                `online_pay_time` = #{onlinePayTime},
            </if> 
            <if test="problemTypeId != null">
                `problem_type_id` = #{problemTypeId},
            </if> 
            <if test="problemTypeName != null">
                `problem_type_name` = #{problemTypeName},
            </if> 
            <if test="userStore != null">
                `user_store` = #{userStore},
            </if> 
            <if test="saleCompanyNo != null">
                `sale_company_no` = #{saleCompanyNo},
            </if> 
            <if test="orderCreateTime != null">
                `order_create_time` = #{orderCreateTime},
            </if> 
            <if test="sourceType != null">
                `source_type` = #{sourceType},
            </if> 
            <if test="orderStyle != null">
                `order_style` = #{orderStyle},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            <if test="refBillNo != null">
                `ref_bill_no` = #{refBillNo},
            </if> 
            <if test="originalOrderNo != null">
                `original_order_no` = #{originalOrderNo},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="outOrderId != null">
                `out_order_id` = #{outOrderId},
            </if> 
            <if test="orderStatus != null">
                `order_status` = #{orderStatus},
            </if> 
            <if test="payStatus != null">
                `pay_status` = #{payStatus},
            </if> 
            <if test="payName != null">
                `pay_name` = #{payName},
            </if> 
            <if test="paymentCode != null">
                `payment_code` = #{paymentCode},
            </if> 
            <if test="deliverStatus != null">
                `deliver_status` = #{deliverStatus},
            </if> 
            <if test="deliverStatusName != null">
                `deliver_status_name` = #{deliverStatusName},
            </if> 
            <if test="interfacePlatform != null">
                `interface_platform` = #{interfacePlatform},
            </if> 
            <if test="originPlatform != null">
                `origin_platform` = #{originPlatform},
            </if> 
            <if test="originPlatformName != null">
                `origin_platform_name` = #{originPlatformName},
            </if> 
            <if test="shopNo != null">
                `shop_no` = #{shopNo},
            </if> 
            <if test="validateBatch != null">
                `validate_batch` = #{validateBatch},
            </if> 
            <if test="shopName != null">
                `shop_name` = #{shopName},
            </if> 
            <if test="channelNo != null">
                `channel_no` = #{channelNo},
            </if> 
            <if test="storeNo != null">
                `store_no` = #{storeNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id} OR bill_no = #{billNo}
    </update>
        <!-- auto generate end-->


</mapper>