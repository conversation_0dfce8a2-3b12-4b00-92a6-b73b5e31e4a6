<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.wonhigh.baize.repository.ios.InternetOrderCancelApplyRepository">
    <!-- auto generate  -->
    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.ios.InternetOrderCancelApply">
                
        <id column="id" property="id" jdbcType="CHAR" />

        <result column="reason" property="reason" jdbcType="VARCHAR" />
        
        <result column="sharding_flag" property="shardingFlag" jdbcType="CHAR" />
        
        <result column="order_sub_no" property="orderSubNo" jdbcType="VARCHAR" />
        
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        
        <result column="refund_time" property="refundTime" jdbcType="TIMESTAMP" />
        
        <result column="order_type" property="orderType" jdbcType="TINYINT" />
        
        <result column="refund_no" property="refundNo" jdbcType="VARCHAR" />
        
        <result column="status" property="status" jdbcType="TINYINT" />
        
        <result column="bill_no" property="billNo" jdbcType="VARCHAR" />
        
    </resultMap>

    <sql id="column_list">
        `reason`,`sharding_flag`,`order_sub_no`,`update_time`,`sku_no`,`refund_time`,`order_type`,`refund_no`,`id`,`status`,`bill_no`
    </sql>

    <sql id="condition">
        <if test="null!=params">
            <if test="null!=params.queryCondition and ''!=params.queryCondition">
                AND ${params.queryCondition}
            </if>
            
            <if test="null!=params.reason  and ''!=params.reason ">
                
                AND `reason`=#{params.reason}
                
            </if>
            
            <if test="null!=params.shardingFlag  and ''!=params.shardingFlag ">
                
                AND `sharding_flag`=#{params.shardingFlag}
                
            </if>
            
            <if test="null!=params.orderSubNo  and ''!=params.orderSubNo ">
                
                AND `order_sub_no`=#{params.orderSubNo}
                
            </if>
            
            <if test="null!=params.updateTime ">
                
                AND `update_time`=#{params.updateTime}
                
            </if>
            
            <if test="null!=params.skuNo  and ''!=params.skuNo ">
                
                AND `sku_no`=#{params.skuNo}
                
            </if>
            
            <if test="null!=params.refundTime ">
                
                AND `refund_time`=#{params.refundTime}
                
            </if>
            
            <if test="null!=params.orderType ">
                
                AND `order_type`=#{params.orderType}
                
            </if>
            
            <if test="null!=params.refundNo  and ''!=params.refundNo ">
                
                AND `refund_no`=#{params.refundNo}
                
            </if>
            
            <if test="null!=params.id  and ''!=params.id ">
                
                AND `id`=#{params.id}
                
            </if>
            
            <if test="null!=params.status ">
                
                AND `status`=#{params.status}
                
            </if>
            
            <if test="null!=params.billNo  and ''!=params.billNo ">
                
                AND `bill_no`=#{params.billNo}
                
            </if>
            
        </if>
    </sql>

    <sql id="uniqe_condition">
        
    </sql>

    <select id="findByPrimaryKey" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_cancel_apply
        WHERE id = #{id}
    </select>

    <select id="findByUnique" resultMap="baseResultMap" >
        SELECT
        <include refid="column_list" />
        FROM internet_order_cancel_apply
        <where>
            <include refid="uniqe_condition" />
        </where>
    </select>

    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_cancel_apply
        <where>
            <include refid="condition" />
        </where>
        
        LIMIT 1
        
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT COUNT(1) as s FROM internet_order_cancel_apply
        <where>
            <include refid="condition" />
        </where>
    </select>

    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
        
        SELECT
        <include refid="column_list" />
        FROM internet_order_cancel_apply
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
        LIMIT ${page.startRowNum},${page.pageSize}
        
    </select>

    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
        SELECT
        <include refid="column_list" />
        FROM internet_order_cancel_apply
        <where>
            <include refid="condition" />
        </where>
        <if test="orderby != null and ''!=orderby">
            ORDER BY ${orderby}
        </if>
    </select>

    <delete id="deleteByPrimaryKey">
        DELETE FROM internet_order_cancel_apply
        WHERE id = #{id}
    </delete>

    <delete id="deleteByUnique">
        DELETE FROM internet_order_cancel_apply
        <where>
            <include refid="condition" />
        </where>
    </delete>

    <delete id="deleteByParams" parameterType="map">
        DELETE
        FROM internet_order_cancel_apply
        <where>
            <include refid="condition" />
            <if test="params.ids!=null and ''!=params.ids ">
                AND id in ( ${params.ids} )
            </if>
        </where>
    </delete>

    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderCancelApply"  >
        INSERT INTO internet_order_cancel_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            
            <if test="reason != null">
                `reason`,
            </if>
            
            <if test="shardingFlag != null">
                `sharding_flag`,
            </if>
            
            <if test="orderSubNo != null">
                `order_sub_no`,
            </if>
            
            <if test="updateTime != null">
                `update_time`,
            </if>
            
            <if test="skuNo != null">
                `sku_no`,
            </if>
            
            <if test="refundTime != null">
                `refund_time`,
            </if>
            
            <if test="orderType != null">
                `order_type`,
            </if>
            
            <if test="refundNo != null">
                `refund_no`,
            </if>
            
            <if test="id != null">
                `id`,
            </if>
            
            <if test="status != null">
                `status`,
            </if>
            
            <if test="billNo != null">
                `bill_no`,
            </if>
            
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            
            <if test="reason != null">
                #{reason},
            </if>
            
            <if test="shardingFlag != null">
                #{shardingFlag},
            </if>
            
            <if test="orderSubNo != null">
                #{orderSubNo},
            </if>
            
            <if test="updateTime != null">
                #{updateTime},
            </if>
            
            <if test="skuNo != null">
                #{skuNo},
            </if>
            
            <if test="refundTime != null">
                #{refundTime},
            </if>
            
            <if test="orderType != null">
                #{orderType},
            </if>
            
            <if test="refundNo != null">
                #{refundNo},
            </if>
            
            <if test="id != null">
                #{id},
            </if>
            
            <if test="status != null">
                #{status},
            </if>
            
            <if test="billNo != null">
                #{billNo},
            </if>
            
        </trim>
    </insert>


    <update id="update" parameterType="cn.wonhigh.baize.model.entity.ios.InternetOrderCancelApply">
        UPDATE internet_order_cancel_apply
        <set>
            
            <if test="reason != null">
                `reason` = #{reason},
            </if> 
            <if test="shardingFlag != null">
                `sharding_flag` = #{shardingFlag},
            </if> 
            <if test="orderSubNo != null">
                `order_sub_no` = #{orderSubNo},
            </if> 
            <if test="skuNo != null">
                `sku_no` = #{skuNo},
            </if> 
            <if test="refundTime != null">
                `refund_time` = #{refundTime},
            </if> 
            <if test="orderType != null">
                `order_type` = #{orderType},
            </if> 
            <if test="refundNo != null">
                `refund_no` = #{refundNo},
            </if> 
            <if test="status != null">
                `status` = #{status},
            </if> 
            <if test="billNo != null">
                `bill_no` = #{billNo},
            </if> 
            update_time =  now() 
        </set>
        WHERE id = #{id}
    </update>
        <!-- auto generate end-->


</mapper>