<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="mapUnderscoreToCamelCase" value="true" />
    </settings>

    <typeAliases>
        <package name="cn.wonhigh.baize.model.entity.ios" />
    </typeAliases>

    <plugins>
        <plugin interceptor="topmall.framework.repository.inspector.DataAccessInspector"/>
        <plugin interceptor="cn.wonhigh.baize.domain.configuration.datasource.ApOperationInterceptor"/>
    </plugins>

</configuration>
