package cn.wonhigh.baize.events.systemlog;

import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.model.enums.InventoryActiveLockOpscodeEnum;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.util.function.Tuple2;
import topmall.framework.security.Authorization;

import java.util.Date;
import java.util.Optional;

@Component
public class InventoryActiveLockLogHelper {

    /**
     * 活动锁库日志
     *
     * @param activeLockInfo
     * @param lockOpscodeEnum
     * @param message
     */
    // 记录活动锁库日志
    public static void log(Tuple2<String, String> activeLockInfo,
                           InventoryActiveLockOpscodeEnum lockOpscodeEnum,
                           String message) {
        InternetSystemLogsMessage logsMessage = new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                .setOpscode(lockOpscodeEnum.getCode())
                .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown"))
                .setCreateTime(new Date())
                .setSyscode(String.valueOf(MenuTypeEnums.ACTIVELOCK.getType()))
                .setSysname(MenuTypeEnums.ACTIVELOCK.getDesc())
                .setKeyword1(activeLockInfo.getT1())
                .setKeyword1info("活动编码:" + activeLockInfo.getT1() + "(" + activeLockInfo.getT2() + ")")
                .setKeyword2(activeLockInfo.getT1())
                .setRemark(
                        Optional.ofNullable(message).orElse("").length() > 1800 ? StringUtils.substring(message, 0, 1800) + "..." : message
                )
                .build();

        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(logsMessage));
    }

}
