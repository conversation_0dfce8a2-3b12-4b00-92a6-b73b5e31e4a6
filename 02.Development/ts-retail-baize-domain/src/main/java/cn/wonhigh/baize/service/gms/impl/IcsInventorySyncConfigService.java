/** q **/
package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.repository.gms.IcsInventorySyncConfigRepository;
import cn.wonhigh.baize.service.gms.IIcsInventorySyncConfigService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import java.util.List;

@Service
public class IcsInventorySyncConfigService extends BaseService<IcsInventorySyncConfig,String> implements IIcsInventorySyncConfigService {
    @Autowired
    private IcsInventorySyncConfigRepository repository;

    protected IRepository<IcsInventorySyncConfig,String> getRepository(){
        return repository;
    }
      
	public List<IcsInventorySyncConfig> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<IcsInventorySyncConfig> list) {
    	return repository.batchInsert(list);
    }


    public List<IcsInventorySyncConfig> selectShopPageByParams(Query query, Pagenation pagenation) {
    	return repository.selectShopPageByParams(query.asMap(), pagenation);
    }


    public Integer selectShopCountByParams(Query var1) {
    	return repository.selectShopCountByParams(var1.asMap());
    }


    @Override
    public List<InternetVirtualWarehouseInfo> selectVstoreListByParams(Query query) {
        return repository.selectVstoreListByParams(query.asMap());
    }
    public List<IcsInventorySyncConfig> selectByPageWithDistinct(Query query,Pagenation page){
        return repository.selectByPageWithDistinct(query.asMap(),page);
    }

    @Override
    public List<IcsInventorySyncConfig> selectWithDistinct(Query query) {
        return repository.selectWithDistinct(query.asMap());
    }

    public Integer selectByPageWithDistinctCount(Query query){
        return repository.selectByPageWithDistinctCount(query.asMap());
    }

    @Override
    public Integer countByChannel(Query query) {
        return repository.countByChannel(query.asMap());
    }

    @Override
    public List<IcsInventorySyncConfig> pageByChannel(Query query, Pagenation page) {
        return repository.pageByChannel(query.asMap(), page, query.getSort());
    }

    @Override
    public List<IcsInventorySyncConfig> selectChannelVstoreInfo(Query query) {
        return repository.selectChannelVstoreInfo(query.asMap());
    }
}