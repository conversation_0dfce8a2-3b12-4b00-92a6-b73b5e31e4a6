/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.LookupEntry;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
@Mapper
public interface LookupEntryRepository extends IRepository<LookupEntry,String> {
    
    Integer batchInsert(@Param("list") List<LookupEntry> list);
    
    List<LookupEntry> selectByIds(@Param("list") List<Integer> ids);
}