/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
import cn.wonhigh.baize.service.gms.ISellOutConfigDtlService;
import cn.wonhigh.baize.manager.gms.ISellOutConfigDtlManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class SellOutConfigDtlManager extends BaseManager<SellOutConfigDtl,String> implements ISellOutConfigDtlManager{
    @Autowired
    private ISellOutConfigDtlService service;

    protected IService<SellOutConfigDtl,String> getService(){
        return service;
    }
    
    @Override
	public List<SellOutConfigDtl> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	@Override
	public Integer batchInsert(List<SellOutConfigDtl> list) {
        if (list == null) {
            return 0;
        }
		list.forEach(this::initEntry);
		return service.batchInsert(list);
	}

    @Override
    public void deleteByBillNo(List<String> collect) {
        if (collect == null || collect.isEmpty()) {
            return;
        }
        service.deleteByBillNo(collect);
    }
}