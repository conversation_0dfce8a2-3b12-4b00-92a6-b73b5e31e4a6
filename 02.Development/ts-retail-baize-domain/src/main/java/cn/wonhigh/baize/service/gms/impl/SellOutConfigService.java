/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
import cn.wonhigh.baize.model.entity.gms.ItemAttr;
import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
import cn.wonhigh.baize.repository.gms.SellOutConfigRepository;
import cn.wonhigh.baize.service.gms.ISellOutConfigService;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class SellOutConfigService extends BaseService<SellOutConfig,String> implements  ISellOutConfigService{
    @Autowired
    private SellOutConfigRepository repository;

    protected IRepository<SellOutConfig,String> getRepository(){
        return repository;
    }
      
	public List<SellOutConfig> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}

    @Override
    public List<ItemAttr> getShareClassifyList(Query query) {
        return repository.selectShareClassifyList(query.asMap());
    }

    @Override
    public int selectSellOutCount(Query query) {
        return repository.selectSellOutCount(query.asMap());
    }

    @Override
    public List<SellOutConfigDto> selectSellOutList(Query query, Pagenation page) {
        return repository.selectSellOutList(query.asMap(),  page);
    }

    public Integer batchInsert(List<SellOutConfig> list) {
    	return repository.batchInsert(list);
    }

	@Override
	public int updateStatusByParams(Query q) {
		return repository.updateStatusByParams(q.asMap());
	}

    @Override
    public List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list) {
        return repository.selectByUniqueList(type, list);
    }

    @Override
    public void selectByParamsForHandler(Map<String, Object> map, ResultHandler<SellOutConfig> o) {
        repository.selectByParamsForHandler(map, o);
    }
}