/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
@Mapper
public interface SellOutConfigDtlRepository extends IRepository<SellOutConfigDtl,String> {
    
    Integer batchInsert(@Param("list") List<SellOutConfigDtl> list);
    
    List<SellOutConfigDtl> selectByIds(@Param("list") List<Integer> ids);

    int deleteByBillNo(List<String> collect);
}