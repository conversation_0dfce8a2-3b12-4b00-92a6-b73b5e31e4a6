/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.CompanyPartConfig;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface CompanyPartConfigRepository extends IRepository<CompanyPartConfig, String> {

    Integer batchInsert(@Param("list") List<CompanyPartConfig> list);

    List<CompanyPartConfig> selectByIds(@Param("list") List<Integer> ids);

    List<CompanyPartConfig> getShardingFlagByStoreNo(@Param("storeNo") String storeNo);

    String getShardingFlagByOrderUnitNo(@Param("orderUnitNo")String orderUnitNo);
}