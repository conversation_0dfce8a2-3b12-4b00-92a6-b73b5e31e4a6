/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderDtl;
import cn.wonhigh.baize.repository.ios.RetailOrderDtlRepository;

import cn.wonhigh.baize.service.ios.IRetailOrderDtlService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class RetailOrderDtlService extends BaseService<RetailOrderDtl,String> implements  IRetailOrderDtlService{
    @Autowired
    private RetailOrderDtlRepository repository;

    protected IRepository<RetailOrderDtl,String> getRepository(){
        return repository;
    }

    
}