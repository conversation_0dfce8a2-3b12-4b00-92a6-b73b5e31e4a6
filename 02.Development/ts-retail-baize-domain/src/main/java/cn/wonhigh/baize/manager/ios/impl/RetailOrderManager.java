/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrder;
import cn.wonhigh.baize.service.ios.IRetailOrderService;
import cn.wonhigh.baize.manager.ios.IRetailOrderManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class RetailOrderManager extends BaseManager<RetailOrder,String> implements IRetailOrderManager{
    @Autowired
    private IRetailOrderService service;

    protected IService<RetailOrder,String> getService(){
        return service;
    }

    
    public RetailOrder findByUnique(String billNo) {
        try {
			return service.findByUnique(billNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String billNo) {
        try {
			return service.deleteByUnique(billNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(RetailOrder entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}