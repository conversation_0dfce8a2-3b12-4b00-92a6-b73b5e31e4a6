/** q **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.manager.gms.ICommodityCorpMatchProductManager;
import cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct;
import cn.wonhigh.baize.service.gms.ICommodityCorpMatchProductService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class CommodityCorpMatchProductManager extends BaseManager<CommodityCorpMatchProduct,String> implements ICommodityCorpMatchProductManager {
    @Autowired
    private ICommodityCorpMatchProductService service;

    protected IService<CommodityCorpMatchProduct,String> getService(){
        return service;
    }
    	
	public List<CommodityCorpMatchProduct> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<CommodityCorpMatchProduct> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}

	@Override
	public List<CommodityCorpMatchProduct> selectItemSkuByParams(Map<String, Object> params) {
		return service.selectItemSkuByParams(params);
	}
}