/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.repository.gms.InternetVirtualWarehouseInfoRepository;
import cn.wonhigh.baize.service.gms.IInternetVirtualWarehouseInfoService;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

@Service
public class InternetVirtualWarehouseInfoService extends BaseService<InternetVirtualWarehouseInfo,String> implements IInternetVirtualWarehouseInfoService {
    @Autowired
    private InternetVirtualWarehouseInfoRepository repository;

    protected IRepository<InternetVirtualWarehouseInfo,String> getRepository(){
        return repository;
    }

    
    public InternetVirtualWarehouseInfo findByUnique(String vstoreCode){
        return repository.findByUnique(vstoreCode);
    }

    public Integer deleteByUnique(String vstoreCode){
        return repository.deleteByUnique(vstoreCode);
    }

    public Integer insertForUpdate(InternetVirtualWarehouseInfo entry){
       return repository.insertForUpdate(entry);
    }
    
}