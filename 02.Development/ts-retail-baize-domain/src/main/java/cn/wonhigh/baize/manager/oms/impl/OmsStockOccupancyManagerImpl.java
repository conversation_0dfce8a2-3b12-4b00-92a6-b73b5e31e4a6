package cn.wonhigh.baize.manager.oms.impl;

import cn.wonhigh.baize.manager.oms.IOmsStockOccupancyManager;
import cn.wonhigh.baize.model.dto.OmsStockOccupancyDTO;
import cn.wonhigh.baize.model.entity.oms.OmsStockOccupancy;
import cn.wonhigh.baize.service.oms.IOmsStockOccupancyService;
import org.springframework.beans.factory.annotation.Autowired;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.List;

/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.manager.oms.impl
 * @Project：ts-retail-baize
 * @name：OmsStockOccupancyManagerImpl
 * @Date：2024/12/28 9:38
 * @Filename：OmsStockOccupancyManagerImpl
 * @description：
 */
public class OmsStockOccupancyManagerImpl  extends BaseManager<OmsStockOccupancy, Long> implements IOmsStockOccupancyManager {
    @Autowired
    private IOmsStockOccupancyService omsStockOccupancyService;

    @Override
    public OmsStockOccupancyDTO queryLockStockQty(String warehouseId, String skuId) {
        return omsStockOccupancyService.queryLockStockQty(warehouseId, skuId);
    }

    @Override
    protected IService<OmsStockOccupancy, Long> getService() {
        return omsStockOccupancyService;
    }
}
