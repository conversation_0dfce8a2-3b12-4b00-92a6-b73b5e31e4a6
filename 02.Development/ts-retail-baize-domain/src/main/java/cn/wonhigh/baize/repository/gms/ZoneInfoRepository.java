/** zkh **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.ZoneInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;
@Mapper
public interface ZoneInfoRepository extends IRepository<ZoneInfo,Integer> {
    
    ZoneInfo findByUnique(@Param("zoneNo") String zoneNo);

    Integer deleteByUnique(@Param("zoneNo") String zoneNo);

    List<ZoneInfo> selectByUniques(@Param("list") List<String> zoneNos);
    
    Integer batchInsert(@Param("list") List<ZoneInfo> list);
    
    List<ZoneInfo> selectByIds(@Param("list") List<Integer> ids);
}