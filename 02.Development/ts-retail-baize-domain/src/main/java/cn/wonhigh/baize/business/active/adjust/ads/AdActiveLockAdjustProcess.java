package cn.wonhigh.baize.business.active.adjust.ads;

import cn.mercury.security.IUser;
import cn.wonhigh.baize.business.active.adjust.SingleActiveLockAdjustProcess;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.model.enums.BrandMerchantCodeEnums;
import cn.wonhigh.baize.model.enums.OcsOrderSourceConfigChannelTypeEnum;

import org.apache.commons.lang3.tuple.Pair;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 阿迪活动锁库调整服务
 * @date 2025/6/11 11:41
 */
public class AdActiveLockAdjustProcess extends SingleActiveLockAdjustProcess {

    public AdActiveLockAdjustProcess(String billNo, IUser user) {
        super(billNo, user);
    }

    @Override
    public List<IcsActiveLockAdjustDtl> checkProcess() {
        return Collections.emptyList();
    }

    @Override
    protected void adjustSyncInventory(Pair<InventoryActiveLock, List<InventoryActiveLockDtl>> tuple) {
    }

    @Override
    protected void auditProcess() {
    }

    @Override
    protected void saveDtlProcess(IcsActiveLockAdjustDtl entry) {
    }

    @Override
    protected void importDtlProcess(List<Object> objectList) {
    }

	@Override
	protected BrandMerchantCodeEnums merchantCodeEnum() {
		return BrandMerchantCodeEnums.ADSFS;
	}

	@Override
	protected OcsOrderSourceConfigChannelTypeEnum channelTypeEnum() {
		return OcsOrderSourceConfigChannelTypeEnum.PPF;
	}
}
