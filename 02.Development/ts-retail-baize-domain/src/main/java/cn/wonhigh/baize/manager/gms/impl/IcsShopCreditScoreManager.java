/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.manager.IEntryResultHandler;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore;
import cn.wonhigh.baize.service.gms.IIcsShopCreditScoreService;
import  cn.wonhigh.baize.manager.gms.IIcsShopCreditScoreManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class IcsShopCreditScoreManager extends BaseManager<IcsShopCreditScore,String> implements IIcsShopCreditScoreManager{
    @Autowired
    private IIcsShopCreditScoreService service;

    protected IService<IcsShopCreditScore,String> getService(){
        return service;
    }
    	
	public List<IcsShopCreditScore> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}

	@Override
	public int selectShopCreditScoreByCount(Map<String, Object> params) {
		return service.selectShopCreditScoreByCount(params);
	}

	@Override
	public List<IcsShopCreditScore> selectShopCreditScoreByPage(Map<String, Object> params, Pagenation page) {
		return service.selectShopCreditScoreByPage(params, page);
	}

	@Override
	public void selectShopCreditScoreByParams(Map<String, Object> params, IEntryResultHandler<IcsShopCreditScore> handler) {
		service.selectShopCreditScoreByParams(params, handler);
	}

	public Integer batchInsert(List<IcsShopCreditScore> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}
}