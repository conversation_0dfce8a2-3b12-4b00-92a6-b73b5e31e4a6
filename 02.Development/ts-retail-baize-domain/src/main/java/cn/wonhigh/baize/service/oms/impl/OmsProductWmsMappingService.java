package cn.wonhigh.baize.service.oms.impl;

import cn.wonhigh.baize.model.entity.oms.OmsProductWmsMapping;
import cn.wonhigh.baize.repository.oms.OmsProductWmsMappingRepository;
import cn.wonhigh.baize.service.oms.IOmsProductWmsMappingService;

import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;

/**
 * (OmsProductWmsMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-28 11:21:23
 */
@Service("omsProductWmsMappingService")
public class OmsProductWmsMappingService extends BaseService<OmsProductWmsMapping, Long> implements IOmsProductWmsMappingService {
    @Resource
    private OmsProductWmsMappingRepository repository;

    protected IRepository<OmsProductWmsMapping, Long> getRepository() {
        return repository;
    }

}