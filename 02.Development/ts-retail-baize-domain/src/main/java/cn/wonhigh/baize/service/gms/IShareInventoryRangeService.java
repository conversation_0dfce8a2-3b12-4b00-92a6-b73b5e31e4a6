/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.ShareInventoryRange;
import topmall.framework.service.IService;
import java.util.List;
import java.util.Map;

public interface IShareInventoryRangeService extends IService<ShareInventoryRange,String>{
    int insertSelective(ShareInventoryRange shareInventoryRange);

    int updateShare(Map<String, Object> map);

    Integer batchInsert(List<ShareInventoryRange> list);

    Integer batchUpdate(List<ShareInventoryRange> list);

    Integer findExistInBrandInventoryRange(Map<String, Object> map);
    
}