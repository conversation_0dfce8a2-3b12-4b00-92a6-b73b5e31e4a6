/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.GmsStore;
import cn.wonhigh.baize.repository.gms.GmsStoreRepository;
import cn.wonhigh.baize.service.gms.IGmsStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

@Service
public class GmsStoreService extends BaseService<GmsStore,Integer> implements IGmsStoreService {
    @Autowired
    private GmsStoreRepository repository;

    protected IRepository<GmsStore,Integer> getRepository(){
        return repository;
    }

    
    public GmsStore findByUnique(String storeNo){
        return repository.findByUnique(storeNo);
    }

    public Integer deleteByUnique(String storeNo){
        return repository.deleteByUnique(storeNo);
    }

    public Integer insertForUpdate(GmsStore entry){
       return repository.insertForUpdate(entry);
    }
    
}