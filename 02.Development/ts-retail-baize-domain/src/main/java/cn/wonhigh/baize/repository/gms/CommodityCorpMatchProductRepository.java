/** q **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface CommodityCorpMatchProductRepository extends IRepository<CommodityCorpMatchProduct,String> {
    
    Integer batchInsert(@Param("list") List<CommodityCorpMatchProduct> list);
    
    List<CommodityCorpMatchProduct> selectByIds(@Param("list") List<Integer> ids);

    List<CommodityCorpMatchProduct> selectItemSkuByParams(@Param("param") Map<String, Object> params);
}