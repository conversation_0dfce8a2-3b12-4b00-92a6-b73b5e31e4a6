package cn.wonhigh.baize.business.virtualwarehousescope;

public class Validate<PERSON>esult {
    private boolean success;

    private String msg;

    private ValidateResult(boolean success, String msg) {
        this.success = success;
        this.msg = msg;
    }

    public static ValidateResult success() {
        return new ValidateResult(true, "");
    }
    public static ValidateResult error(String msg) {
        return new ValidateResult(false, msg);
    }


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
