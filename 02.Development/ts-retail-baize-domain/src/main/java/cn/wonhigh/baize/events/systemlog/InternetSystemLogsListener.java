package cn.wonhigh.baize.events.systemlog;

import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.manager.gms.impl.InternetSystemLogsManager;
import cn.wonhigh.baize.model.entity.gms.InternetSystemLogs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class InternetSystemLogsListener implements ApplicationListener<InternetSystemLogsEvent> {

    @Autowired
    private InternetSystemLogsManager internetSystemLogsManager;

    public static final Logger LOGGER = LoggerFactory.getLogger(InternetSystemLogs.class);

    @Override
    @Async
    public void onApplicationEvent(InternetSystemLogsEvent event) {
        if (!(event.getSource() instanceof InternetSystemLogsMessage)) {
            LOGGER.warn("未知的系统操作");
            return;
        }

        LOGGER.info("系统操作日志:{}", JsonUtils.toJson(event.getSource()));
        InternetSystemLogsMessage internetSystemLogsMessage = (InternetSystemLogsMessage) event.getSource();
        InternetSystemLogs saveInternetSystemLogs = new InternetSystemLogs();
        try {
            saveInternetSystemLogs.setSyscode(internetSystemLogsMessage.getSyscode());
            saveInternetSystemLogs.setSysname(internetSystemLogsMessage.getSysname());
            saveInternetSystemLogs.setOpscode(internetSystemLogsMessage.getOpscode());
            saveInternetSystemLogs.setKeyword1(internetSystemLogsMessage.getKeyword1());
            saveInternetSystemLogs.setKeyword1info(internetSystemLogsMessage.getKeyword1info());
            saveInternetSystemLogs.setKeyword2(internetSystemLogsMessage.getKeyword2());
            saveInternetSystemLogs.setKeyword2info(internetSystemLogsMessage.getKeyword2info());
            saveInternetSystemLogs.setRemark(internetSystemLogsMessage.getRemark());
            saveInternetSystemLogs.setCreateUser(internetSystemLogsMessage.getCreateUser());
            saveInternetSystemLogs.setCreateTime(internetSystemLogsMessage.getCreateTime());
            internetSystemLogsManager.insert(saveInternetSystemLogs);
        } catch (Exception e) {
            LOGGER.error("保存系统操作日志失败:"+e.getMessage(), e);

        }
    }
}
