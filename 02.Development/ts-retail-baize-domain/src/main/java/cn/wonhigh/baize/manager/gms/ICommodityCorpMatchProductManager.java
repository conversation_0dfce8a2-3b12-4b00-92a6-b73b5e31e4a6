/** q **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct;
import java.util.List;

public interface ICommodityCorpMatchProductManager extends IManager<CommodityCorpMatchProduct,String>{
    
    Integer batchInsert(List<CommodityCorpMatchProduct> list);
    
    List<CommodityCorpMatchProduct> selectByIds(List<Integer> ids);

    List<CommodityCorpMatchProduct> selectItemSkuByParams(java.util.Map<String, Object> params);
}