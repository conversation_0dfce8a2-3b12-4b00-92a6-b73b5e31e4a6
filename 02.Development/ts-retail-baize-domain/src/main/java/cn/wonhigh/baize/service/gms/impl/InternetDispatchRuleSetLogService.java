/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetLog;
import cn.wonhigh.baize.repository.gms.InternetDispatchRuleSetLogRepository;

import cn.wonhigh.baize.service.gms.IInternetDispatchRuleSetLogService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetDispatchRuleSetLogService extends BaseService<InternetDispatchRuleSetLog,String> implements  IInternetDispatchRuleSetLogService{
    @Autowired
    private InternetDispatchRuleSetLogRepository repository;

    protected IRepository<InternetDispatchRuleSetLog,String> getRepository(){
        return repository;
    }

    
}