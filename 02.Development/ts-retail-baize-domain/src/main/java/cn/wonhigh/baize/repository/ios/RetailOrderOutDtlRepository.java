/**
 *
 **/
package cn.wonhigh.baize.repository.ios;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;
import java.util.Map;

@Mapper
public interface RetailOrderOutDtlRepository extends IRepository<RetailOrderOutDtl, String> {


	List<RetailOrderOutDtl> selectPageForReport(@Param("params") Map<String, Object> var1, @Param("page") Pagenation var2);

	int selectPageForReportCount(@Param("params") Map<String, Object> var1);
}