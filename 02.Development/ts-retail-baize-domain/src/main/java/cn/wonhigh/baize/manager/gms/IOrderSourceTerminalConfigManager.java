/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;

import cn.mercury.manager.IManager;

import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

public interface IOrderSourceTerminalConfigManager extends IManager<OrderSourceTerminalConfig,String>{


    List<OrderSourceTerminalConfig> selectShopPageByParams(Query query, Pagenation pagenation);

    Integer selectShopCountByParams(Query query);

    Supplier<Optional<OrderSourceTerminalConfig>> getByLazyMerchantCode(String merchantCode);
}