/** zkh **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.InternetAreaRelation;
import cn.wonhigh.baize.repository.gms.InternetAreaRelationRepository;
import cn.wonhigh.baize.service.gms.IInternetAreaRelationService;
import org.springframework.stereotype.Service;

import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;

@Service
public class InternetAreaRelationService extends BaseService<InternetAreaRelation,String> implements IInternetAreaRelationService {
    @Autowired
    private InternetAreaRelationRepository repository;

    protected IRepository<InternetAreaRelation,String> getRepository(){
        return repository;
    }
      
	public List<InternetAreaRelation> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<InternetAreaRelation> list) {
    	return repository.batchInsert(list);
    }

    @Override
    public List<InternetAreaRelation> selectByRetailCodeName(Map<String, Object> params) {
        return repository.selectByRetailCodeName(params);
    }
}