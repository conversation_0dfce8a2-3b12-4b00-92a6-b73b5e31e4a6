package cn.wonhigh.baize.utils.common.baozun;

import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2023/10/25 14:26
 */
@XmlRootElement(name = "response")
public class BaoZunResponse implements Serializable {
    private static final long serialVersionUID = -1271513662842774313L;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误原因
     */
    private String errorMsg;
    
    /**
     * 数据接收状态，0失败，1成功
     */
    private Integer response;

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getResponse() {
        return response;
    }

    public void setResponse(Integer response) {
        this.response = response;
    }
}
