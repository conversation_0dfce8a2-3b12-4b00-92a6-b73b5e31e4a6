/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.Company;
import cn.wonhigh.baize.repository.gms.CompanyRepository;
import cn.wonhigh.baize.service.gms.ICompanyService;
import org.springframework.stereotype.Service;

import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class CompanyService extends BaseService<Company,Integer> implements ICompanyService {
    @Autowired
    private CompanyRepository repository;

    protected IRepository<Company,Integer> getRepository(){
        return repository;
    }
    
    public Company findByUnique(String companyNo){
        return repository.findByUnique(companyNo);
    }

    public Integer deleteByUnique(String companyNo){
        return repository.deleteByUnique(companyNo);
    }

    public List<Company> selectByUniques(List<String> companyNos) {
        return repository.selectByUniques(companyNos);
    }
      
	public List<Company> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<Company> list) {
    	return repository.batchInsert(list);
    }
}