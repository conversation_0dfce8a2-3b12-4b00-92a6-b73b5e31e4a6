/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
import topmall.framework.service.IService;
import java.util.List;

public interface ISellOutConfigDtlService extends IService<SellOutConfigDtl,String>{
      
    Integer batchInsert(List<SellOutConfigDtl> list);
    
    List<SellOutConfigDtl> selectByIds(List<Integer> ids);

    int deleteByBillNo(List<String> collect);
}