/** zkh **/
package cn.wonhigh.baize.manager.gms;


import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.InternetAreaRelation;

import java.util.List;
import java.util.Map;

public interface IInternetAreaRelationManager extends IManager<InternetAreaRelation,String>{
    
    Integer batchInsert(List<InternetAreaRelation> list);
    
    List<InternetAreaRelation> selectByIds(List<Integer> ids);

    List<InternetAreaRelation> selectByRetailCodeName(Map<String, Object> params);
}