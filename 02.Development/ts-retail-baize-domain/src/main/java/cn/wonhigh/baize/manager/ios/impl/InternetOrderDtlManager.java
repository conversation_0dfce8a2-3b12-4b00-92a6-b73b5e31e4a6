/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderDtl;
import cn.wonhigh.baize.service.ios.IInternetOrderDtlService;
import cn.wonhigh.baize.manager.ios.IInternetOrderDtlManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class InternetOrderDtlManager extends BaseManager<InternetOrderDtl,String> implements IInternetOrderDtlManager{
    @Autowired
    private IInternetOrderDtlService service;

    protected IService<InternetOrderDtl,String> getService(){
        return service;
    }

    
}