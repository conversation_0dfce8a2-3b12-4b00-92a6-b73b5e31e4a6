/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;
import java.util.Map;

@Mapper
public interface InternetVirtualWarehouseScopeRepository extends IRepository<InternetVirtualWarehouseScope,String> {



    List<InternetVirtualWarehouseScope> selectStoreByPage(@Param("params") Map<String, Object> var1, @Param("page") Pagenation var2);
    int selectStoreCount(@Param("params") Map<String, Object> var1);


    /**
     * 查询机构是否存在
     * @param params
     * @return
     */
    public List<InternetVirtualWarehouseScope> selectExistStore(@Param("params") Map<String, Object> params);

    /**
     * 查询虚仓下是否有所有店或者单店的范围
     * @param params
     * @return
     */
    public List<InternetVirtualWarehouseScope> selectExistShop(@Param("params") Map<String, Object> params);



    /**
     * 总仓删除所有店时，子仓删除
     * @param paramMap
     * @return
     */
    public int deleteAllShopScope(@Param("params") Map<String, Object> paramMap);

    /**
     * 修改所有子仓存类型
     * @param paramMap
     * @return
     */
    public int updateAllScope(@Param("params") Map<String, Object> paramMap);


    /**
     * 总仓删除指定店或者仓时，子仓删除
     * @param paramMap
     * @return
     */
    public int deleteStoreScope(@Param("params") Map<String, Object> paramMap);

    /**
     * 查询机构货管信息
     * @param
     * @return
     */
    List<InternetVirtualWarehouseScope> selectScopeInfo(@Param("params") Map<String, Object> var1);

    List<InternetVirtualWarehouseScope> selectScopeListByPage(@Param("params") Map<String, Object> var1, @Param("page") Pagenation var2);
    int selectScopeListCount(@Param("params") Map<String, Object> var1);

}