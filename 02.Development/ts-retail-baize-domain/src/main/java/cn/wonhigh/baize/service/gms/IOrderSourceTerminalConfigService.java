/**  **/
package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import topmall.framework.service.IService;

import java.util.List;

public interface IOrderSourceTerminalConfigService extends IService<OrderSourceTerminalConfig,String>{


    List<OrderSourceTerminalConfig> selectShopPageByParams(Query query, Pagenation pagenation);

    Integer selectShopCountByParams(Query query);
}