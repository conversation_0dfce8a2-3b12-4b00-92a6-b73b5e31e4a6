/**  **/
package cn.wonhigh.baize.service.ios.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl;
import cn.wonhigh.baize.repository.ios.RetailOrderOutDtlRepository;

import cn.wonhigh.baize.service.ios.IRetailOrderOutDtlService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Service
public class RetailOrderOutDtlService extends BaseService<RetailOrderOutDtl,String> implements  IRetailOrderOutDtlService{
    @Autowired
    private RetailOrderOutDtlRepository repository;

    protected IRepository<RetailOrderOutDtl,String> getRepository(){
        return repository;
    }


    @Override
    public List<RetailOrderOutDtl> selectPageForReport(Query query, Pagenation pagenation) {
        return this.repository.selectPageForReport(query.asMap(), pagenation);
    }

    @Override
    public int selectPageForReportCount(Query query) {
        return this.repository.selectPageForReportCount(query.asMap());
    }
}