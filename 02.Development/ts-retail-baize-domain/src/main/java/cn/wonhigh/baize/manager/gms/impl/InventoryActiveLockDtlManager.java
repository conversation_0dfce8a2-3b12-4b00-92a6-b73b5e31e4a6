/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.hutool.core.map.MapBuilder;
import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.manager.gms.IInventoryActiveLockDtlManager;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import cn.wonhigh.baize.model.enums.BrandMerchantCodeEnums;
import cn.wonhigh.baize.service.gms.IInventoryActiveLockDtlService;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import org.springframework.util.Assert;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class InventoryActiveLockDtlManager extends BaseManager<InventoryActiveLockDtl,String> implements IInventoryActiveLockDtlManager {
    @Autowired
    private IInventoryActiveLockDtlService service;

    protected IService<InventoryActiveLockDtl,String> getService(){
        return service;
    }
    	
	public List<InventoryActiveLockDtl> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<InventoryActiveLockDtl> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}

	@Override
	public int batchSaveOrUpdateDtl(List<InventoryActiveLockDtl> list) {
		Assert.notEmpty(list);
		Integer i = service.batchSaveOrUpdateDtl(list);
		return i;
	}

	@Override
	public Integer insert(InventoryActiveLockDtl entry) throws ManagerException {
		Integer i = super.insert(entry);
		return i;
	}

	@Override
	public Integer update(InventoryActiveLockDtl entry) throws ManagerException {
		Integer i = super.update(entry);
		return i;
	}

	@Override
	public List<InventoryActiveLockDtl> selectByVstoreInfo(Map<String, Object> var1){
		return service.selectByVstoreInfo(var1);
	}

	@Override
	public List<InventoryActiveLockDtl> findBalanceLockQtyInOnStore(Map<String, Object> params) {
		return service.findBalanceLockQtyInOnStore(params);
	}

    @Override
    public List<InventoryActiveLockDtlOccupied> selectByInventoryActiveLockDtlQuery(InventoryActiveLockDtlQuery query) {
        return this.service.selectByInventoryActiveLockDtlQuery(query);
    }

    @Override
	public List<InventoryActiveLockDtl> selectByAdjustBillNo(String billNo, Integer syncStatus) {
		Map<String, Object> params = MapBuilder.<String, Object>create().put("billNo", billNo)
				.put("syncStatus", syncStatus).put("merchantsCode", BrandMerchantCodeEnums.PUSFS.getCode())
				.put("channelNo", BrandMerchantCodeEnums.PUSFS.getCode()).build();
		return service.selectByAdjustBillNo(params);
	}

	@Override
	public int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList) {
		return service.batchUpdateDtlForAudit(updateLockDtlList);
	}

}