package cn.wonhigh.baize.repository.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;
import java.util.Map;

public interface StockDistirbutionConfigMapper extends IRepository<StockDistirbutionConfig, String> {

    List<StockDistirbutionConfig> pageByShop(@Param("params") Map<String, Object> var1, @Param("page") Pagenation var2, @Param("orderby") String var3);

    Integer countByShop(@Param("params") Map<String, Object> var1);
    
}