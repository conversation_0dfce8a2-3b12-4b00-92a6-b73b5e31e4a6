package cn.wonhigh.baize.service.ios.impl;

import cn.wonhigh.baize.model.entity.ios.RetailOrderOutNt;
import cn.wonhigh.baize.repository.ios.RetailOrderOutNtRepository;
import cn.wonhigh.baize.service.ios.IRetailOrderOutNtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wudong
 * @create: 2025-07-14 10:51
 **/
@Service
public class RetailOrderOutNtService implements IRetailOrderOutNtService {
    @Autowired
    private RetailOrderOutNtRepository retailOrderOutNtRepository;


    @Override
    public RetailOrderOutNt findByBillNo(String billNO) {
        return retailOrderOutNtRepository.findByBillNo(billNO);
    }


    @Override
    public List<RetailOrderOutNt> selectByOrderSubNo(String orderSubNo, List<Integer> status) {
        Map param = new HashMap<>();
        param.put("orderSubNo", orderSubNo);

        List<RetailOrderOutNt> list = retailOrderOutNtRepository.selectByParams(param, null);
        return list.stream()
                .filter(retailOrderOutNt -> status.contains(retailOrderOutNt.getStatus()))
                .collect(Collectors.toList());
    }
}
