package cn.wonhigh.baize.business.virtualwarehousescope;

import cn.hutool.core.lang.Assert;
import cn.mercury.basic.UUID;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseScopeManager;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import org.apache.commons.lang3.ObjectUtils;
import topmall.framework.security.Authorization;

import java.util.*;
import java.util.stream.Collectors;


public abstract class AbstractVirtualWarehouseScopeSaveExecutor implements VirtualWarehouseScopeSaveExecutor{


    protected InternetVirtualWarehouseScopeSave saveData;

    protected InternetVirtualWarehouseInfo internetVirtualWarehouseInfo;

    protected ValidateDataService validateDataService;


    protected final IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;
    protected final IInternetVirtualWarehouseScopeManager internetVirtualWarehouseScopeManager;

    public AbstractVirtualWarehouseScopeSaveExecutor(InternetVirtualWarehouseScopeSave saveData) {
        Assert.notNull(saveData);
        this.saveData = saveData;
        this.internetVirtualWarehouseInfoManager = SpringContext.getBean(IInternetVirtualWarehouseInfoManager.class);
        this.internetVirtualWarehouseScopeManager = SpringContext.getBean(IInternetVirtualWarehouseScopeManager.class);
    }


    @Override
    public void execute() {
        if (this.internetVirtualWarehouseInfo == null ) {
            throw new ManagerException("聚合仓信息不能为空");
        }
        try {
            ValidateResult validateResult = validateDataService.validate(saveData);
            if (!validateResult.isSuccess()) {
                throw new ManagerException(validateResult.getMsg());
            }
        } catch (Exception e) {
            throw new ManagerException(e);
        }

        this.addData();

    }

    public abstract void addData();

    protected void saveLogs(List<InternetVirtualWarehouseScope> scopes){
        for (InternetVirtualWarehouseScope scope : scopes) {
            String inventoryTypeStr = "";
            //转换数字含义
            if(saveData.getInventoryType()==1)
            {
                inventoryTypeStr = "共享";
            }else if(saveData.getInventoryType()==2){
                inventoryTypeStr = "独享";
            }else if(saveData.getInventoryType()==3){
                inventoryTypeStr = "电商";
            }
            SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                    new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                            .setOpscode(OpscodeEnum.ADD.getCode()).setSyscode(String.valueOf(MenuTypeEnums.VWS.getType())).
                            setSysname(MenuTypeEnums.VWS.getDesc()).
                            setKeyword1(scope.getVstoreCode()).
                            setKeyword1info("聚合仓编码:" + scope.getVstoreCode() + "(" + scope.getVstoreName() + ")").
                            setKeyword2(scope.getStoreNo()).
                            setKeyword2info("机构:" + scope.getStoreNo() + "(" + scope.getStoreName() + ")").
                            setRemark("新增：聚合仓编码:" + scope.getVstoreCode() + "(" + scope.getVstoreName() + ");机构:"+
                                    scope.getStoreNo()+"("+(saveData.getMoreStoreFlag().equals("1")?"所有店":scope.getStoreName())+");货管:"+
                                    scope.getOrderUnitNo()+"("+scope.getOrderUnitName()+");库存类型:"+inventoryTypeStr)
                            .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                            .setCreateTime(new Date())
                            .build()));
        }
    }

    protected void updateLogs(InternetVirtualWarehouseScope scope){
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                        .setOpscode(OpscodeEnum.UP.getCode())
                        .setSyscode(String.valueOf(MenuTypeEnums.VWS.getType()))
                        .setSysname(MenuTypeEnums.VWS.getDesc()).
                        setKeyword1(scope.getVstoreCode()).
                        setKeyword1info("聚合仓编码:" + scope.getVstoreCode()).
                        setKeyword2(scope.getOrderUnitNo()).
                        setKeyword2info("货管:" + scope.getOrderUnitNo()).
                        setRemark(String.format("%s所有店：聚合仓编码:%s;货管:%s;机构:%s",
                                (scope.getStatus() == 1 ? "启用": "禁用"),
                                scope.getVstoreCode(),
                                scope.getOrderUnitNo(), scope.getStoreNo()))
                        .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                        .setCreateTime(new Date())
                        .build()));
    }

    protected void extracted(InternetVirtualWarehouseScope virtualWarehouseScope) {
        Query params = Query.empty();
        params.and("vstoreCode", this.internetVirtualWarehouseInfo.getVstoreCode());
        params.and("vstoreType", this.internetVirtualWarehouseInfo.getVstoreType());//子仓总仓类型
        params.and("storeType", this.saveData.getStoreType());
        params.and("orderUnitNo",  this.saveData.getOrderUnitNo());
        params.and("storeNo", virtualWarehouseScope.getStoreNo());
        params.and("moreStoreFlag", virtualWarehouseScope.getMoreStoreFlag());
        List<InternetVirtualWarehouseScope> listExit = this.internetVirtualWarehouseScopeManager.selectByParams(params);
        if (ObjectUtils.isNotEmpty(listExit)) {
            for (InternetVirtualWarehouseScope virtual : listExit) {
                virtualWarehouseScope.setId(virtual.getId());
                this.internetVirtualWarehouseScopeManager.update(virtualWarehouseScope);
                if (this.internetVirtualWarehouseInfo.getVstoreType() == 1) {
                    this.parentStore(virtual);
                } else {
                    this.childrenStore(virtual);
                }
            }
        } else  {
            if (this.internetVirtualWarehouseInfo.getVstoreType() == 2) {
                this.childrenStore(virtualWarehouseScope);
            }
            this.internetVirtualWarehouseScopeManager.insert(virtualWarehouseScope);
        }
    }

    protected void parentStore (InternetVirtualWarehouseScope virtual) {
        Query query = Query.empty();
        query.and("parentVstoreCode", this.internetVirtualWarehouseInfo.getVstoreCode());
        query.and("vstoreType", 2);
        List<InternetVirtualWarehouseInfo> listChildVirtual = internetVirtualWarehouseInfoManager.selectByParams(query);
        List<String> listVstoreCode = Optional.ofNullable(
                listChildVirtual
        ).orElse(new ArrayList<>()).stream().map(InternetVirtualWarehouseInfo::getVstoreCode).collect(Collectors.toList());

        if (ObjectUtils.isNotEmpty(listChildVirtual)) {
            Query paramsMap = Query.empty();
            paramsMap.and("listVstoreCode", listVstoreCode);
            paramsMap.and("orderUnitNo", virtual.getOrderUnitNo());
            paramsMap.and("status", virtual.getStatus());
            paramsMap.and("storeNo", virtual.getStoreNo());
            paramsMap.and("updateUser", Authorization.getUser().getName());
            this.internetVirtualWarehouseScopeManager.updateAllScope(paramsMap.asMap());
        }
    }

    protected void childrenStore(InternetVirtualWarehouseScope virtual) {
        Query query = Query.empty();
        query.and("parentVstoreCode", this.internetVirtualWarehouseInfo.getParentVstoreCode());
        query.and("vstoreType", 2);
        List<InternetVirtualWarehouseInfo> listChildVirtual = internetVirtualWarehouseInfoManager
                .selectByParams(query);

        List<String> listVstoreCode = new ArrayList<>();
        listVstoreCode.add(this.internetVirtualWarehouseInfo.getParentVstoreCode());

        if (ObjectUtils.isNotEmpty(listChildVirtual)) {
            for (InternetVirtualWarehouseInfo info : listChildVirtual) {
                listVstoreCode.add(info.getVstoreCode());
            }
        }

        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("listVstoreCode", listVstoreCode);
        paramsMap.put("orderUnitNo", virtual.getOrderUnitNo());
        paramsMap.put("storeNo", virtual.getStoreNo());
        paramsMap.put("status", virtual.getStatus());
        paramsMap.put("updateUser", Authorization.getUser().getName());
        this.internetVirtualWarehouseScopeManager.updateAllScope(paramsMap);

    }

    //构造新增货管
    protected InternetVirtualWarehouseScope buildScope(InternetVirtualWarehouseScopeSave.SaveDtl addDtl, Date date){
        String uuid = UUID.gernerate();
        InternetVirtualWarehouseScope scope = InternetVirtualWarehouseScope.build().id(uuid)
                .vstoreCode(saveData.getVstoreCode())
                .vstoreType(this.internetVirtualWarehouseInfo.getVstoreType())
                .vstoreName(this.internetVirtualWarehouseInfo.getVstoreName())
                .originalVstoreCode(addDtl.getOriginalVstoreCode())
                .originalVstoreName(addDtl.getOriginalVstoreName())
                .storeType(addDtl.getStoreType()).storeNo(addDtl.getStoreNo()).storeName(addDtl.getStoreName())
                .orderUnitNo(addDtl.getOrderUnitNo()).orderUnitName(addDtl.getOrderUnitName())
                .inventoryType(saveData.getInventoryType()).moreStoreFlag(Integer.valueOf(saveData.getMoreStoreFlag()))
                .status(addDtl.getStatus()).createUser(Authorization.getUser().getName()).createTime(date)
                .updateUser(Authorization.getUser().getName()).updateTime(date).object();
        return scope;
    }


    public void setInternetVirtualWarehouseInfo(InternetVirtualWarehouseInfo internetVirtualWarehouseInfo) {
        this.internetVirtualWarehouseInfo = internetVirtualWarehouseInfo;
    }


    public ValidateDataService getValidateDataService() {
        return validateDataService;
    }

    public void setValidateDataService(ValidateDataService validateDataService) {
        this.validateDataService = validateDataService;
    }
}
