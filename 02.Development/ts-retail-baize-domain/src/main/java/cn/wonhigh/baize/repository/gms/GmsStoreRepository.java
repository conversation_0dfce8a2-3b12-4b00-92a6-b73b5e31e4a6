/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.GmsStore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

@Mapper
public interface GmsStoreRepository extends IRepository<GmsStore,Integer> {
    
    public GmsStore findByUnique(@Param("storeNo") String storeNo);

    public Integer deleteByUnique(@Param("storeNo") String storeNo);

    public Integer insertForUpdate(GmsStore entry);

    
}