package cn.wonhigh.baize.manager.oms;

import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.dto.WarehouseRatioDetailDto;
import cn.wonhigh.baize.model.dto.WarehouseRatioDto;
import cn.wonhigh.baize.model.dto.WarehouseRatioExportDto;
import cn.wonhigh.baize.model.entity.oms.OmsWarehouse;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface IInventoryBoardManager extends IManager<OmsWarehouse,Long> {
    PageResult<WarehouseRatioDto> selectPageForWarehouseRatio(Query query, Pagenation page);

    PageResult<WarehouseRatioDetailDto> loadWarehouseRatioByWarehouseCode(Query query, Pagenation page);

    List<WarehouseRatioExportDto> selectWarehouseRatioByWarehouseCode(List<String> warehouseCode,List<String> brandCode);
}
