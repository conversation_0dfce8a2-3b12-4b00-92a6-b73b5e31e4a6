package cn.wonhigh.baize.business.virtualwarehousescope;

import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2024/9/19 10:47
 */
public class NormalVirtualSaveExecutor extends AbstractVirtualWarehouseScopeSaveExecutor{

    public NormalVirtualSaveExecutor(InternetVirtualWarehouseScopeSave saveData) {
        super(saveData);
    }

    @Override
    public void addData() {
        if (this.saveData.getAddDtls() != null) {
            //批量添加的货管
            List<InternetVirtualWarehouseScope> addScopes = new ArrayList<>();
            for (InternetVirtualWarehouseScopeSave.SaveDtl addDtl : this.saveData.getAddDtls()) {
                Date date = new Date();
                if(StringUtils.isBlank(addDtl.getId())){
                    InternetVirtualWarehouseScope scope = buildScope(addDtl, date);
                    addScopes.add(scope);
                    if (this.internetVirtualWarehouseInfo.getVstoreType() == 2) {
                        this.childrenStore(scope);
                    }
                }
                /*if(StringUtils.isNotEmpty(addDtl.getId())){
                    //修改货管
                    InternetVirtualWarehouseScope updateScope = InternetVirtualWarehouseScope.build()
                            .id(addDtl.getId()).status(addDtl.getStatus()).object();
                    this.internetVirtualWarehouseScopeManager.update(updateScope);
                    updateScope.setStoreNo(addDtl.getStoreNo());
                    updateScope.setOrderUnitNo(addDtl.getOrderUnitNo());
                    updateScope.setVstoreCode(this.internetVirtualWarehouseInfo.getVstoreCode());
                    updateLogs(updateScope);
                    if (this.internetVirtualWarehouseInfo.getVstoreType() == 1) {
                        //修改所属子仓得
                        this.parentStore(updateScope);
                    } else if(addDtl.getStatus() == 1){
                        this.childrenStore(updateScope);
                    }
                } else {
                    InternetVirtualWarehouseScope scope = buildScope(addDtl, date);
                    addScopes.add(scope);
                    if (this.internetVirtualWarehouseInfo.getVstoreType() == 2) {
                        this.childrenStore(scope);
                    }
                }*/
            }
            if(addScopes.size() > 0){
                internetVirtualWarehouseScopeManager.insertBatch(addScopes);
                saveLogs(addScopes);
            }
        }
    }

}
