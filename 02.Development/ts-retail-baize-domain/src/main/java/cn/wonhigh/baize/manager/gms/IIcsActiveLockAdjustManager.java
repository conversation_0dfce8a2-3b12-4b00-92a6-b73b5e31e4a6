/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.manager.IManager;
import cn.wonhigh.baize.business.active.adjust.ActiveLockAdjustProcess;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.function.Supplier;

public interface IIcsActiveLockAdjustManager extends IManager<IcsActiveLockAdjust,String>{
    
    IcsActiveLockAdjust findByUnique(String billNo) ;

    Integer deleteByUnique(String billNo);

    List<IcsActiveLockAdjust> selectByUniques(List<String> billNos);
    
    Integer batchInsert(List<IcsActiveLockAdjust> list);
    
    List<IcsActiveLockAdjust> selectByIds(List<Integer> ids);

    ActiveLockAdjustProcess getProcess(String billNo);

    Pair<Boolean, List<InventoryActiveLockDtl>> auditActiveLockAdjust(List<IcsActiveLockAdjustDtl> adjustDtlList,
                                  Supplier<List<InventoryActiveLockDtl>> updateActiveLockSupplier,
                                  Runnable inventoryOperationRunnable);

    void updateAdjustStatus(String billNo, Integer status);

	void updateAdjustAndDtl(IcsActiveLockAdjust adjust, List<IcsActiveLockAdjustDtl> adjustDtlList,
			List<InventoryActiveLockDtl> right);
}