/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.BrandInventoryRange;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface BrandInventoryRangeRepository extends IRepository<BrandInventoryRange,String> {
    
    Integer batchInsert(@Param("list") List<BrandInventoryRange> list);
    
    List<BrandInventoryRange> selectByIds(@Param("list") List<String> ids);

    int selectShareNoExist(@Param("params") Map<String, Object> paramMaps);

    void updateShare(Map<String, Object> paramMap);

}