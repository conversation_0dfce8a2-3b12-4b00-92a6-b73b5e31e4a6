/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.OrderSourceVstoreConfig;
import cn.wonhigh.baize.repository.gms.OrderSourceVstoreConfigRepository;
import cn.wonhigh.baize.service.gms.IOrderSourceVstoreConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

@Service
public class OrderSourceVstoreConfigService extends BaseService<OrderSourceVstoreConfig,String> implements IOrderSourceVstoreConfigService {
    @Autowired
    private OrderSourceVstoreConfigRepository repository;
    protected IRepository<OrderSourceVstoreConfig,String> getRepository(){
        return repository;
    }
}