/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig;
import cn.wonhigh.baize.repository.ios.OcsOrderSourceConfigRepository;

import cn.wonhigh.baize.service.ios.IOcsOrderSourceConfigService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class OcsOrderSourceConfigService extends BaseService<OcsOrderSourceConfig,String> implements  IOcsOrderSourceConfigService{
    @Autowired
    private OcsOrderSourceConfigRepository repository;

    protected IRepository<OcsOrderSourceConfig,String> getRepository(){
        return repository;
    }

    
    public OcsOrderSourceConfig findByUnique(String shopNo){
        return repository.findByUnique(shopNo);
    }

    public Integer deleteByUnique(String shopNo){
        return repository.deleteByUnique(shopNo);
    }

    public Integer insertForUpdate(OcsOrderSourceConfig entry){
       return repository.insertForUpdate(entry);
    }
    
}