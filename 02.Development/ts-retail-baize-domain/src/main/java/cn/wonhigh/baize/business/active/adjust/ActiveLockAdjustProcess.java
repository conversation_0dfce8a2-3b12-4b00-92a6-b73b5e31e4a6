package cn.wonhigh.baize.business.active.adjust;

import cn.mercury.basic.query.Query;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.business.active.ActiveMethodListener;
import cn.wonhigh.baize.manager.gms.IIcsActiveLockAdjustDtlManager;
import cn.wonhigh.baize.manager.gms.IIcsActiveLockAdjustManager;
import cn.wonhigh.baize.manager.gms.IInventoryActiveLockDtlManager;
import cn.wonhigh.baize.manager.gms.IInventoryActiveLockManager;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.model.enums.BrandMerchantCodeEnums;
import cn.wonhigh.baize.model.enums.OcsOrderSourceConfigChannelTypeEnum;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import cn.wonhigh.baize.service.gms.IInventorySearchService;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动锁库调整业务逻辑处理类
 * @date 2025/6/11 10:57
 */
public abstract class ActiveLockAdjustProcess implements ActiveLockAdjustService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActiveLockAdjustProcess.class);

    protected IIcsActiveLockAdjustManager  icsActiveLockAdjustManager;
    protected IIcsActiveLockAdjustDtlManager  icsActiveLockAdjustDtlManager;
    protected IInventoryActiveLockManager inventoryActiveLockManager;
    protected IInventoryActiveLockDtlManager inventoryActiveLockDtlManager;
    protected IInventorySearchService inventorySearchService;

    //调整单表头
    protected Supplier<IcsActiveLockAdjust> adjustSupplier;
    //调整单明细
    protected Supplier<List<IcsActiveLockAdjustDtl>>  adjustDtlSupplier;

    protected IcsActiveLockAdjust adjust;
    protected List<IcsActiveLockAdjustDtl>  adjustDtlList;

    private final List<ActiveMethodListener> listeners = new ArrayList<>();

    //调整单号
    protected final String billNo;

    //当前用户
    protected final IUser user;

    public ActiveLockAdjustProcess(String billNo, IUser user) {
        init();
        this.billNo = billNo;
        this.user = user;
    }

    public void addListener(ActiveMethodListener listener) {
        listeners.add(listener);
    }

    private void notifyListeners(String methodName, boolean success, Throwable throwable, Tuple3<IcsActiveLockAdjust, IUser, String> tuple) {
        if (throwable != null) {
            LOGGER.error("methodName:{} error:{}", methodName, throwable.getMessage());
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                listeners.forEach(listener -> listener.onEvent(methodName, success, throwable, tuple));
            }
        }).start();
    }

    private void init() {
        this.icsActiveLockAdjustManager = SpringContext.getBean(IIcsActiveLockAdjustManager.class);
        this.icsActiveLockAdjustDtlManager = SpringContext.getBean(IIcsActiveLockAdjustDtlManager.class);
        this.inventoryActiveLockManager = SpringContext.getBean(IInventoryActiveLockManager.class);
        this.inventoryActiveLockDtlManager = SpringContext.getBean(IInventoryActiveLockDtlManager.class);
        this.inventorySearchService = SpringContext.getBean(IInventorySearchService.class);
        this.adjustSupplier = () -> icsActiveLockAdjustManager.findByUnique(billNo);
        this.adjustDtlSupplier = () -> icsActiveLockAdjustDtlManager.selectByParams(new Query().where("billNo", billNo));
    }

    public void initAdjustDtlList() {
        if (adjustDtlList == null) {
            adjustDtlList = adjustDtlSupplier.get();
        }
    }

    public void initAdjust() {
        if (adjust == null) {
            adjust = adjustSupplier.get();
        }
    }

    @Override
    public List<IcsActiveLockAdjustDtl> check() {
        return checkProcess();
    }

    @Override
    public void importDtl(List<Object> objectList) {
        importDtlProcess(objectList);
    }

    @Override
    public void saveDtl(IcsActiveLockAdjustDtl adjustDtl) {
        saveDtlProcess(adjustDtl);
    }

    @Override
    public void audit() {
        try {
            auditProcess();
            notifyListeners(OpscodeEnum.AUDIT.getCode(), true, null, Tuples.of(adjust, user, "锁库调整单审核成功, 库存预占成功"));
        } catch (Exception e) {
            notifyListeners(OpscodeEnum.AUDIT.getCode(), false, e, Tuples.of(adjust, user, "锁库调整单审核失败, 库存预占失败"));
            throw e;
        }
    }

    @Override
    public void cancel() {
    	cancelProcess();
    }

    @Override
    public void failureSync() {
    	try {
    		failureSyncProcess();
    		notifyListeners(OpscodeEnum.FAIL_SYNC.getCode(), true, null, Tuples.of(adjust, user, "锁库调整单失败重传成功, 失败库存同步成功"));
		} catch (Exception e) {
			notifyListeners(OpscodeEnum.FAIL_SYNC.getCode(), true, e, Tuples.of(adjust, user, "锁库调整单失败重传失败， 失败库存同步失败"));
			throw e;
		}
    }

    @Override
    public void create(IcsActiveLockAdjust adjust) {
        try {
            createProcess(adjust);
            notifyListeners(OpscodeEnum.ADD.getCode(), true, null, Tuples.of(adjust, user, "锁库调整单创建成功"));
        } catch (Exception e) {
            notifyListeners(OpscodeEnum.ADD.getCode(), false, e, Tuples.of(adjust, user, "锁库调整单创建失败"));
            throw e;
        }
    }
    
    public void update(IcsActiveLockAdjust adjust) {
    	try {
			boolean bol = updateProcess(adjust);
			if (bol) notifyListeners(OpscodeEnum.UP.getCode(), true, null, Tuples.of(adjust, user, String.format("锁库调整单修改成功, 修改备注: %s", adjust.getRemark())));
		} catch (Exception e) {
			notifyListeners(OpscodeEnum.UP.getCode(), true, e, Tuples.of(adjust, user, "锁库调整单修改失败"));
			throw e;
		}
	}

    @Override
    public void effect() {
        try {
            effectProcess();
            notifyListeners(OpscodeEnum.SYNC.getCode(), true, null, Tuples.of(adjust, user, "锁库调整单同步库存成功"));
        } catch (Exception e) {
            notifyListeners(OpscodeEnum.SYNC.getCode(), false, e, Tuples.of(adjust, user, "锁库调整单同步库存失败"));
            throw e;
        }
    }

    /**
     * 操作生效同步库存处理
     */
    protected  abstract void effectProcess();

    /**
     * 活动锁库调整单创建处理
     * @param adjust
     */
    protected abstract void createProcess(IcsActiveLockAdjust adjust);
    
    /**
     * 活动锁库调整单更新处理
     * @param adjust
     * @return
     */
    protected abstract boolean updateProcess(IcsActiveLockAdjust adjust);

    /**
     * 活动锁库调整单失败重传处理
     */
    protected abstract void failureSyncProcess();

    /**
     * 活动锁库调整单取消处理
     */
    protected abstract void cancelProcess();

    /**
     * 活动锁库调整单审核处理
     */
    protected abstract void auditProcess();

    /**
     * 活动锁库调整单保存处理
     * @param adjustDtl
     */
    protected abstract void saveDtlProcess(IcsActiveLockAdjustDtl adjustDtl);

    /**
     * 活动锁库调整单导入处理
     * @param objectList
     */
    protected abstract void importDtlProcess(List<Object> objectList);

    /**
     * 活动锁库调整单明细校验处理
     * @return
     */
    protected abstract List<IcsActiveLockAdjustDtl> checkProcess();
    
    /**
     * 品牌商品编码
     * @return
     */
    protected abstract BrandMerchantCodeEnums merchantCodeEnum();
    
    /**
     * 渠道类型
     * @return
     */
    protected abstract OcsOrderSourceConfigChannelTypeEnum channelTypeEnum();

    /**
     * 活动锁库调整单同步库存处理
     * @return
     */
    protected abstract void adjustSyncInventory(Pair<InventoryActiveLock, List<InventoryActiveLockDtl>> tuple);

    public <T> void batchProcess(List<T> dataList, int batchSize, Consumer<List<T>> consumer) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        IntStream.range(0, (dataList.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> dataList.stream()
                        .skip((long) i * batchSize)
                        .limit(batchSize)
                        .collect(Collectors.toList()))
                .forEach(consumer);
    }

    /**
     * 活动锁库调整单调整类型分支处理
     * @param adjustType
     * @return
     */
    public AdjustTypeBranchHandle addOrSubtract(int adjustType) {
        return (addHandle, subtractHandle) -> {
            if (adjustType == 1) {
                addHandle.run();
            } else {
                subtractHandle.run();
            }
        };
    }

}
