/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.Item;
import cn.wonhigh.baize.model.entity.gms.ItemBaseInfo;
import cn.wonhigh.baize.repository.gms.ItemRepository;
import cn.wonhigh.baize.service.gms.IItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import java.util.List;
import java.util.Map;

@Service
public class ItemService extends BaseService<Item,Integer> implements IItemService {
    @Autowired
    private ItemRepository repository;

    protected IRepository<Item,Integer> getRepository(){
        return repository;
    }

    @Override
    public List<ItemBaseInfo> queryItemByParams(Map<String, Object> map) {
        return repository.queryItemByParams(map);
    }
}