/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.Company;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
@Mapper
public interface CompanyRepository extends IRepository<Company,Integer> {
    
    Company findByUnique(@Param("companyNo") String companyNo);

    Integer deleteByUnique(@Param("companyNo") String companyNo);

    List<Company> selectByUniques(@Param("list") List<String> companyNos);
    
    Integer batchInsert(@Param("list") List<Company> list);
    
    List<Company> selectByIds(@Param("list") List<Integer> ids);
}