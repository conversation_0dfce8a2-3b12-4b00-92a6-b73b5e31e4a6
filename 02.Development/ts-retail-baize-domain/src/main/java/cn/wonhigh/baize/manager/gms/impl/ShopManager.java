/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.model.dto.ShopCompanyDto;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.Shop;
import cn.wonhigh.baize.service.gms.IShopService;
import cn.wonhigh.baize.manager.gms.IShopManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;

import java.util.Map;


@Service
public class ShopManager extends BaseManager<Shop,Integer> implements IShopManager{
    @Autowired
    private IShopService service;

    protected IService<Shop,Integer> getService(){
        return service;
    }

    
    public Shop findByUnique(String shopNo) {
        try {
			return service.findByUnique(shopNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String shopNo) {
        try {
			return service.deleteByUnique(shopNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(Shop entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    @Override
    public ShopCompanyDto selectShopAndCompany(Map<String, Object> map) {
        return this.service.selectShopAndCompany(map);
    }
}