/**
 *
 **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;
import org.apache.ibatis.annotations.Param;
import topmall.framework.service.IService;

import java.util.Map;

public interface IInternetDispatchRuleSetService extends IService<InternetDispatchRuleSet, Long> {


    /**
     * 根据查询条件判断数是否存在
     * @param query
     * @return
     */
    int exitsQueryData(Map<String, Object> query);

    long nextId();
}