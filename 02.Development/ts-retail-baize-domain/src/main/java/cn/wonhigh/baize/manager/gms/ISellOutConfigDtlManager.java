/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;

import cn.mercury.manager.IManager;
import java.util.List;

public interface ISellOutConfigDtlManager extends IManager<SellOutConfigDtl,String>{
    
    Integer batchInsert(List<SellOutConfigDtl> list);
    
    List<SellOutConfigDtl> selectByIds(List<Integer> ids);

    void deleteByBillNo(List<String> collect);

}