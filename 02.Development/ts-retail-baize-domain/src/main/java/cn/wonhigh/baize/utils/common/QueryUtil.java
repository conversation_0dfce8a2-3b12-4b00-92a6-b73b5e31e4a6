package cn.wonhigh.baize.utils.common;

import cn.mercury.basic.query.Query;

import java.util.Map;

public class QueryUtil {


    public static Query mapToQuery(Map<String, Object> map) {
        if (map == null) {
            return Query.empty();
        }
        Query query = Query.empty();
        for (Map.Entry<String, Object> stringObjectEntry : map.entrySet()) {
            query.and(stringObjectEntry.getKey(), stringObjectEntry.getValue());
        }
        return query;
    }
}
