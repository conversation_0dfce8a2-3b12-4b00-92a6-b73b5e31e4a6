/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderCancelApply;
import cn.wonhigh.baize.service.ios.IInternetOrderCancelApplyService;
import cn.wonhigh.baize.manager.ios.IInternetOrderCancelApplyManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class InternetOrderCancelApplyManager extends BaseManager<InternetOrderCancelApply,String> implements IInternetOrderCancelApplyManager{
    @Autowired
    private IInternetOrderCancelApplyService service;

    protected IService<InternetOrderCancelApply,String> getService(){
        return service;
    }

    
}