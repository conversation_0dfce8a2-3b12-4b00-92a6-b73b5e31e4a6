/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
import cn.wonhigh.baize.repository.gms.SellOutConfigDtlRepository;

import cn.wonhigh.baize.service.gms.ISellOutConfigDtlService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class SellOutConfigDtlService extends BaseService<SellOutConfigDtl,String> implements  ISellOutConfigDtlService{
    @Autowired
    private SellOutConfigDtlRepository repository;

    protected IRepository<SellOutConfigDtl,String> getRepository(){
        return repository;
    }
    
    @Override
	public List<SellOutConfigDtl> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	@Override
    public Integer batchInsert(List<SellOutConfigDtl> list) {
    	return repository.batchInsert(list);
    }

    @Override
    public int deleteByBillNo(List<String> collect) {
        return repository.deleteByBillNo(collect);
    }
}