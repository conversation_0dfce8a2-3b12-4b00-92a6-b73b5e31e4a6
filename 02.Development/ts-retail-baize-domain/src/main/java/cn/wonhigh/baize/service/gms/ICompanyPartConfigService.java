/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.CompanyPartConfig;
import org.apache.ibatis.annotations.Param;
import topmall.framework.service.IService;
import java.util.List;

public interface ICompanyPartConfigService extends IService<CompanyPartConfig,String>{
      
    Integer batchInsert(List<CompanyPartConfig> list);
    
    List<CompanyPartConfig> selectByIds(List<Integer> ids);

    List<CompanyPartConfig> getShardingFlagByStoreNo(String storeNo);

    String getShardingFlagByOrderUnitNo(String orderUnitNo);
}