package cn.wonhigh.baize.utils.common;

import cn.wonhigh.baize.business.virtualwarehousescope.ValidateResult;
import cn.wonhigh.baize.model.dto.commons.ValidResult;
import org.hibernate.validator.HibernateValidator;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

public class ValidationUtil {
    /**
     * 开启快速结束模式 failFast (true)
     */
    private static final Validator failFastValidator = Validation.byProvider(HibernateValidator.class)
            .configure()
            .failFast(true)
            .buildValidatorFactory().getValidator();

    /**
     * 全部校验
     */
    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    private ValidationUtil() {
    }

    /**
     * 注解验证参数(快速失败模式)
     *
     * @param obj
     */
    public static <T> ValidResult fastFailValidate(T obj) {
        Set<ConstraintViolation<T>> constraintViolations = failFastValidator.validate(obj);
        //返回异常result
        if (!constraintViolations.isEmpty()) {
            return ValidResult.fail(constraintViolations.iterator().next().getPropertyPath().toString(), constraintViolations.iterator().next().getMessage());
        }
        return ValidResult.success();
    }


    public static ValidateResult validateByGroup(Object obj, Class<?>... groups) {
        Set<ConstraintViolation<Object>> constraintViolations = failFastValidator.validate(obj, groups);
        //返回异常result
        if (!constraintViolations.isEmpty()) {
            Iterator<ConstraintViolation<Object>> iterator = constraintViolations.iterator();
            return ValidateResult.error(iterator.next().getMessage());
        }
        return ValidateResult.success();
    }

    /**
     * 注解验证参数(全部校验)
     *
     * @param obj
     */
    public static <T> ValidResult allCheckValidate(T obj) {
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(obj);
        //返回异常result
        if (!constraintViolations.isEmpty()) {
            List<String> errorMessages = new LinkedList<String>();
            for (ConstraintViolation<T> violation : constraintViolations) {
                errorMessages.add(String.format("%s:%s", violation.getPropertyPath().toString(), violation.getMessage()));
            }
            return ValidResult.fail(errorMessages);
        }
        return ValidResult.success();
    }
}
