/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetDtl;
import cn.wonhigh.baize.repository.gms.InternetDispatchRuleSetDtlRepository;

import cn.wonhigh.baize.service.gms.IInternetDispatchRuleSetDtlService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetDispatchRuleSetDtlService extends BaseService<InternetDispatchRuleSetDtl,Long> implements  IInternetDispatchRuleSetDtlService{
    @Autowired
    private InternetDispatchRuleSetDtlRepository repository;

    protected IRepository<InternetDispatchRuleSetDtl,Long> getRepository(){
        return repository;
    }

    
}