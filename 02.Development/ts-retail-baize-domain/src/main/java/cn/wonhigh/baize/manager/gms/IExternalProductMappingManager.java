/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.entity.gms.ExternalProductMapping;

import cn.mercury.manager.IManager;
import java.util.List;
import java.util.Map;

public interface IExternalProductMappingManager extends IManager<ExternalProductMapping,String>{
    
    Integer batchInsert(List<ExternalProductMapping> list);
    
    List<ExternalProductMapping> selectByIds(List<Integer> ids);

    List<ExternalProductMapping> selectItemSkuByParams(Map<String, Object> params);
}