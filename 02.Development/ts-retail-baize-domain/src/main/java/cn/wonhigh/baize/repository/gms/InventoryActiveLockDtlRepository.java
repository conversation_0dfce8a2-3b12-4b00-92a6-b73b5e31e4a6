/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface InventoryActiveLockDtlRepository extends IRepository<InventoryActiveLockDtl, String> {

    Integer batchInsert(@Param("list") List<InventoryActiveLockDtl> list);

    List<InventoryActiveLockDtl> selectByIds(@Param("list") List<Integer> ids);

    List<Map<String, Object>> selectByBillNoQty(@Param("billNos") List<String> billNos);

    int batchSaveOrUpdateDtl(@Param("list") List<InventoryActiveLockDtl> list);

    List<InventoryActiveLockDtl> selectByVstoreInfo(@Param("params") Map<String, Object> var1);

    List<InventoryActiveLockDtl> findBalanceLockQtyInOnStore(@Param("params") Map<String, Object> params);

    void batchUpdateSyncStatus(@Param("list") List<InventoryActiveLockDtl> splitList, @Param("syncStatus") Integer syncStatus);

    List<InventoryActiveLockDtl> selectByAdjustBillNo(@Param("params") Map<String, Object> params);

    void batchUpdateByBillNo(@Param("billNo") String billNo, @Param("status") Integer syncStatus);

    List<Map<String, Object>> selectStatusByBillNo(@Param("billNo") String billNo);

	int batchUpdateDtlForAudit(@Param("list") List<InventoryActiveLockDtl> updateLockDtlList);

    List<InventoryActiveLockDtlOccupied> selectByInventoryActiveLockDtlQuery(@Param("query") InventoryActiveLockDtlQuery query);
}