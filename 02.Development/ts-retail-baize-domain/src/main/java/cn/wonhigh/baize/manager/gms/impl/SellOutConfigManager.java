/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.manager.gms.ISellOutConfigDtlManager;
import cn.wonhigh.baize.manager.gms.ISellOutConfigManager;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
import cn.wonhigh.baize.model.entity.gms.ItemAttr;
import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
import cn.wonhigh.baize.service.gms.ISellOutConfigDtlService;
import cn.wonhigh.baize.service.gms.ISellOutConfigService;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SellOutConfigManager extends BaseManager<SellOutConfig,String> implements ISellOutConfigManager{
    @Autowired
    private ISellOutConfigService service;

    @Autowired
    private ISellOutConfigDtlManager sellOutConfigDtlManager;

    protected IService<SellOutConfig,String> getService(){
        return service;
    }
    	
	public List<SellOutConfig> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}

	@Override
	public List<ItemAttr> getShareClassifyList(Query query) {
		return service.getShareClassifyList(query);
	}

	@Override
	public int selectSellOutCount(Query query) {
		return service.selectSellOutCount(query);
	}

	@Override
	public List<SellOutConfigDto> selectSellOutList(Query query, Pagenation page) {
		return service.selectSellOutList(query, page);
	}


    @Override
    @Transactional
    public Integer batchSave(List<SellOutConfig> inserted, List<SellOutConfig> updated, List<SellOutConfig> deleted) throws ManagerException {

        List<SellOutConfigDtl> configDtls = new ArrayList<>();
        if (inserted != null && !inserted.isEmpty()) {
            inserted.forEach(this::initEntry);
            configDtls.addAll(inserted.stream().map(SellOutConfig::getSellOutConfigDtlList).flatMap(List::stream)
                    .collect(Collectors.toList()));
        }

        if (updated != null && !updated.isEmpty()) {
            updated.forEach(this::initEntry);
            this.sellOutConfigDtlManager.deleteByBillNo(updated.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList()));
            configDtls.addAll(updated.stream().map(SellOutConfig::getSellOutConfigDtlList).flatMap(List::stream).collect(Collectors.toList()));
        }


        Integer integer = super.batchSave(inserted, updated, deleted);

        if (!configDtls.isEmpty()) {
            this.sellOutConfigDtlManager.batchInsert(configDtls);
        }
        return integer;
    }



    @Override
	public int updateStatusByParams(Query q) {
		return service.updateStatusByParams(q);
	}

    @Override
    public List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list) {
        return service.selectByUniqueList(type, list);
    }

    @Override
    public int deleteByUnique(Integer type, Tuple3<String, String, String> tuple3) {
        Assert.notNull(type, "type is null");
        List<SellOutConfig> sellOutConfigs = this.selectByUniqueList(type, Collections.singletonList(tuple3));
        if (sellOutConfigs != null && !sellOutConfigs.isEmpty()) {
            this.sellOutConfigDtlManager.deleteByBillNo(sellOutConfigs.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList()));
            return this.deleteByParams(Query.Where("id", sellOutConfigs.get(0).getId()));
        }

        return 0;
    }

    @Override
    public void selectByParamsForHandler(Query query, ResultHandler<SellOutConfig> o) {
        this.service.selectByParamsForHandler(query.asMap(), o);
    }
}