/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.dto.ShopCompanyDto;
import cn.wonhigh.baize.model.entity.gms.Shop;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface ShopRepository extends IRepository<Shop,Integer> {
    
    public Shop findByUnique(@Param("shopNo") String shopNo);

    public Integer deleteByUnique(@Param("shopNo") String shopNo);

    public Integer insertForUpdate(Shop entry);

    ShopCompanyDto selectShopAndCompany(@Param("params") Map<String, Object> map);
}