/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.service.gms.IInternetVirtualWarehouseInfoService;
import org.springframework.stereotype.Service;

import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class InternetVirtualWarehouseInfoManager extends BaseManager<InternetVirtualWarehouseInfo,String> implements IInternetVirtualWarehouseInfoManager {
    @Autowired
    private IInternetVirtualWarehouseInfoService service;

    protected IService<InternetVirtualWarehouseInfo,String> getService(){
        return service;
    }

    
    public InternetVirtualWarehouseInfo findByUnique(String vstoreCode) {
        try {
			return service.findByUnique(vstoreCode);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String vstoreCode) {
        try {
			return service.deleteByUnique(vstoreCode);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(InternetVirtualWarehouseInfo entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}