/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import topmall.framework.service.IService;

import java.util.List;
import java.util.Map;

public interface IInventoryActiveLockDtlService extends IService<InventoryActiveLockDtl,String>{
      
    Integer batchInsert(List<InventoryActiveLockDtl> list);
    
    List<InventoryActiveLockDtl> selectByIds(List<Integer> ids);

    /**
     * 根据单据号查询, 每个批次号下面的 总的锁库数量 和 总剩余锁库数量
     * @param billNos 批次号集合
     * @return key[0] = billNo, key[1] = totalLockQty, key[2] = totalBalanceLockQty
     */
    List<Map<String, Object>> selectByBillNoQty(List<String> billNos);


    public int batchSaveOrUpdateDtl(List<InventoryActiveLockDtl> list);

    /**
     * @description 通过虚仓+机构+货管查询有效的锁库活动
     * @param
     */
    List<InventoryActiveLockDtl> selectByVstoreInfo(Map<String, Object> var1);

    /**
     * @description 按机构批量查询商品库存锁库
     * @param params
     * @return
     */
    List<InventoryActiveLockDtl> findBalanceLockQtyInOnStore(Map<String, Object> params);

    /**
     * @description 批量更新同步状态
     * @param syncStatus
     * @return
     */
    void batchUpdateSyncStatus(List<InventoryActiveLockDtl> splitList, Integer syncStatus);

    void batchUpdateByBillNo(String billNo, Integer syncStatus);

    /**
     * @description 按单据号批量查询商品库存锁库
     * @param params
     * @return
     */
    List<InventoryActiveLockDtl> selectByAdjustBillNo(Map<String, Object> params);

    List<Map<String, Object>> selectStatusByBillNo(String billNo);

    /**
     * 审核成功更新剩余锁库数量
     * @param updateLockDtlList
     * @return
     */
	int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList);

    List<InventoryActiveLockDtlOccupied> selectByInventoryActiveLockDtlQuery(InventoryActiveLockDtlQuery query);
}