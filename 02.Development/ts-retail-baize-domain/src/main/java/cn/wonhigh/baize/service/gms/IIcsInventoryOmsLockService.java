package cn.wonhigh.baize.service.gms;



import cn.wonhigh.baize.model.dto.IcsInventoryOmsLockDto;
import cn.wonhigh.baize.model.entity.gms.IcsInventoryOmsLock;
import topmall.framework.service.IService;

import java.util.List;
import java.util.Map;

/**
 * oms锁库(IcsInventoryOmsLock)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-28 10:04:34
 */
public interface IIcsInventoryOmsLockService extends IService<IcsInventoryOmsLock, String> {
    Integer updateStock(List<IcsInventoryOmsLockDto> params);
}