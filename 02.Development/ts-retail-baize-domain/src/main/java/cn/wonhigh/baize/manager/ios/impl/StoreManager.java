/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.Store;
import cn.wonhigh.baize.service.ios.IStoreService;
import cn.wonhigh.baize.manager.ios.IStoreManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class StoreManager extends BaseManager<Store,Integer> implements IStoreManager{
    @Autowired
    private IStoreService service;

    protected IService<Store,Integer> getService(){
        return service;
    }

    
    public Store findByUnique(String storeNo) {
        try {
			return service.findByUnique(storeNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String storeNo) {
        try {
			return service.deleteByUnique(storeNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(Store entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}