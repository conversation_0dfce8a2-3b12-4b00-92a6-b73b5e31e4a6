package cn.wonhigh.baize.utils.common;

import cn.mercury.basic.query.PageResult;

import java.util.List;
import java.util.Objects;

/**
 * 自定义List分页工具
 *
 * <AUTHOR>
 */
public class PageUtil {

    /**
     * 开始分页
     *
     * @param originalData 数据
     * @param pageNum     页码
     * @param pageSize    每页多少条数据
     * @return PageResult
     */
    public static <T> PageResult<T> startPage(List<T> originalData, Integer pageNum, Integer pageSize) {
        if (originalData == null || originalData.size() == 0) {
            return new PageResult<>(originalData, 0);
        }
        Integer count = originalData.size(); // 记录总数
        int pageCount = 0; // 页数
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }

        int fromIndex = 0; // 开始索引
        int toIndex = 0; // 结束索引

        if (!Objects.equals(pageNum, pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }

        List<T> pageList = originalData.subList(fromIndex, toIndex);

        return new PageResult<T>(pageList, count);
    }
}