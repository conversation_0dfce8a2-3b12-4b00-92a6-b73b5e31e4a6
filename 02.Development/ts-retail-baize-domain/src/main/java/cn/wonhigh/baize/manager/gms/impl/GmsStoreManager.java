/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.manager.gms.IGmsStoreManager;
import cn.wonhigh.baize.model.entity.gms.GmsStore;
import cn.wonhigh.baize.service.gms.IGmsStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;


@Service
public class GmsStoreManager extends BaseManager<GmsStore,Integer> implements IGmsStoreManager{
    @Autowired
    private IGmsStoreService service;

    protected IService<GmsStore,Integer> getService(){
        return service;
    }

    
    public GmsStore findByUnique(String storeNo) {
        try {
			return service.findByUnique(storeNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String storeNo) {
        try {
			return service.deleteByUnique(storeNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(GmsStore entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}