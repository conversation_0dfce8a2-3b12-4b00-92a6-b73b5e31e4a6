package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.InternetAvailableInventory;
import cn.wonhigh.baize.model.entity.gms.VstoreStoreAvailableInventory;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

public interface IInventorySearchService {


    public List<InternetAvailableInventory> findAvailableInOneStore(Map<String, Object> params);

    Stream<List<VstoreStoreAvailableInventory>> streamVstoreQty(List<String> skuNos,
                                                                List<String> vstoreCodeList);

    /**
     * @param skuNo
     * @param vstoreCodeList
     * @param pageNum        第几页  从1开始
     * @param pageSize
     * @return
     */
    List<VstoreStoreAvailableInventory> pageVstoreQty(List<String> skuNos,
                                                      List<String> vstoreCodeList,
                                                      int pageNum,
                                                      int pageSize);

    int countVstoreQty(List<String> skuNos,
                       List<String> vstoreCodeList);
}
