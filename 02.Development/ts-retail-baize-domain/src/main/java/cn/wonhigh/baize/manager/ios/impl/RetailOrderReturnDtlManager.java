/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderReturnDtl;
import cn.wonhigh.baize.service.ios.IRetailOrderReturnDtlService;
import cn.wonhigh.baize.manager.ios.IRetailOrderReturnDtlManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class RetailOrderReturnDtlManager extends BaseManager<RetailOrderReturnDtl,String> implements IRetailOrderReturnDtlManager{
    @Autowired
    private IRetailOrderReturnDtlService service;

    protected IService<RetailOrderReturnDtl,String> getService(){
        return service;
    }

    
}