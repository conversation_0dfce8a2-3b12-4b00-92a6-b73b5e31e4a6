/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface IcsActiveLockAdjustDtlRepository extends IRepository<IcsActiveLockAdjustDtl,String> {
    
    Integer batchInsert(@Param("list") List<IcsActiveLockAdjustDtl> list);
    
    List<IcsActiveLockAdjustDtl> selectByIds(@Param("list") List<Integer> ids);

    List<IcsActiveLockAdjustDtl> selectActiveDtlList(@Param("params") Map<String, Object> map, @Param("page") Pagenation page);

    void batchSaveOrUpdateDtl(@Param("list") List<IcsActiveLockAdjustDtl> dtlList);

    void batchUpdateSyncStatus(@Param("list") List<IcsActiveLockAdjustDtl> dtlList, @Param("syncStatus") int syncStatus);
}