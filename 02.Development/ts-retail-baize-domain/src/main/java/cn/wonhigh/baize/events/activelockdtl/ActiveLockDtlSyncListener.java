package cn.wonhigh.baize.events.activelockdtl;

import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import cn.wonhigh.baize.model.enums.AdjustDtlSyncStatusEnums;
import cn.wonhigh.baize.model.enums.AdjustSyncStatusEnums;
import cn.wonhigh.baize.service.gms.impl.InventoryActiveLockDtlService;
import cn.wonhigh.baize.service.gms.impl.InventoryActiveLockService;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


import java.util.List;
import java.util.Map;

@Component
public class ActiveLockDtlSyncListener implements ApplicationListener<ActiveLockDtlSyncEvent> {

    public static final Logger log = LoggerFactory.getLogger(ActiveLockDtlSyncListener.class.getName());

    @Autowired
    private InventoryActiveLockDtlService activeLockDtlService;

    @Autowired
    private InventoryActiveLockService activeLockService;

    public static final String COUNT_KEY = "count";
    public static final String SYNC_STATUS_KEY = "syncStatus";


    @Override
    @Async
    public void onApplicationEvent(ActiveLockDtlSyncEvent event) {
        if (event.getSource() == null) {
            return;
        }

        List<Map<String, Object>> statusMap = activeLockDtlService.selectStatusByBillNo(event.getSource().toString());
        if (statusMap == null || statusMap.isEmpty()) {
            return;
        }

        long notSync = statusMap.stream().filter(map -> map.get(SYNC_STATUS_KEY).equals(AdjustDtlSyncStatusEnums.NOT_SYNC.getValue()))
                .findFirst().map(map -> NumberUtils.toLong(map.getOrDefault(COUNT_KEY, 0).toString())).orElse(0L);

        long syncFail = statusMap.stream().filter(map -> map.get(SYNC_STATUS_KEY).equals(AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue()))
                .findFirst().map(map -> NumberUtils.toLong(map.getOrDefault(COUNT_KEY, 0).toString())).orElse(0L);

        long syncSuccess = statusMap.stream().filter(map -> map.get(SYNC_STATUS_KEY).equals(AdjustDtlSyncStatusEnums.SYNC_SUCCESS.getValue()))
                .findFirst().map(map -> NumberUtils.toLong(map.getOrDefault(COUNT_KEY, 0).toString())).orElse(0L);

        if (notSync == 0 && syncFail == 0 && syncSuccess == 0) {
            log.info("锁库活动明细状态异常, 锁库编码:{}", event.getSource());
            return;
        }

        InventoryActiveLock inventoryActiveLock = new InventoryActiveLock();
        inventoryActiveLock.setBillNo(event.getSource().toString());
        if (notSync > 0 && syncFail == 0 && syncSuccess == 0) {
            inventoryActiveLock.setSyncStatus(AdjustSyncStatusEnums.NOT_SYNC.getValue());
        } else  if (syncFail >0 && syncSuccess > 0) {
            inventoryActiveLock.setSyncStatus(AdjustSyncStatusEnums.PART_SYNC.getValue());
        } else if (notSync == 0 && syncFail == 0 && syncSuccess > 0) {
            inventoryActiveLock.setSyncStatus(AdjustSyncStatusEnums.ALL_SYNC.getValue());
        } else if (notSync == 0 && syncFail > 0 && syncSuccess == 0) {
            inventoryActiveLock.setSyncStatus(AdjustSyncStatusEnums.ALL_FAIL_SYNC.getValue());
        }
        activeLockService.update(inventoryActiveLock);
    }
}
