/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import cn.mercury.security.IUser;
import cn.wonhigh.baize.business.active.adjust.ActiveLockAdjustProcess;
import cn.wonhigh.baize.business.active.adjust.ads.AdActiveLockAdjustProcess;
import cn.wonhigh.baize.business.active.adjust.puma.PumaActiveLockAdjustProcess;
import cn.wonhigh.baize.manager.gms.IIcsActiveLockAdjustManager;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.model.enums.AdjustDtlSyncStatusEnums;
import cn.wonhigh.baize.model.enums.AdjustStatusEnums;
import cn.wonhigh.baize.model.enums.AdjustSyncStatusEnums;
import cn.wonhigh.baize.model.enums.BrandMerchantCodeEnums;
import cn.wonhigh.baize.service.gms.IIcsActiveLockAdjustDtlService;
import cn.wonhigh.baize.service.gms.IIcsActiveLockAdjustService;
import cn.wonhigh.baize.service.gms.IInventoryActiveLockService;
import topmall.framework.manager.BaseManager;
import topmall.framework.security.Authorization;
import topmall.framework.service.IService;

@Service
public class IcsActiveLockAdjustManager extends BaseManager<IcsActiveLockAdjust,String> implements IIcsActiveLockAdjustManager{
    private static final Logger LOGGER = LoggerFactory.getLogger(IcsActiveLockAdjustManager.class);

    @Autowired
    private IIcsActiveLockAdjustService service;
    @Autowired
    private IInventoryActiveLockService inventoryActiveLockService;
    @Autowired
    private IIcsActiveLockAdjustDtlService icsActiveLockAdjustDtlService;

    protected IService<IcsActiveLockAdjust,String> getService(){
        return service;
    }
    
    @Override
    public IcsActiveLockAdjust findByUnique(String billNo) {
			  return service.findByUnique(billNo);
    }

    @Override
    public Integer deleteByUnique(String billNo) {
			  return service.deleteByUnique(billNo);
    }

    @Override
    public List<IcsActiveLockAdjust> selectByUniques(List<String> billNos) {
		return service.selectByUniques(billNos);
	}
    
    @Override
	public List<IcsActiveLockAdjust> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}

    @Override
    public ActiveLockAdjustProcess getProcess(String billNo) {
        IcsActiveLockAdjust adjust = service.findByUnique(billNo);
        String refBillNo = adjust != null ? adjust.getRefBillNo() : billNo;
        InventoryActiveLock active = inventoryActiveLockService.findByUnique(refBillNo);
        Assert.notNull(active, "未找到活动单据");
        String sourcePlatform = active.getSourcePlatform();
        BrandMerchantCodeEnums enums = BrandMerchantCodeEnums.getNameByCode(sourcePlatform);
        Assert.notNull(enums, "未定义的来源平台");
        IUser user = Authorization.getUser();
        ActiveLockAdjustProcess process = null;
        switch (enums) {
            case PUSFS:
                process = new PumaActiveLockAdjustProcess(billNo, user);
                break;
            case ADSFS:
                process = new AdActiveLockAdjustProcess(billNo, user);
                break;
            default:
                logger.warn("未定义的来源平台：{}", sourcePlatform);
                break;
        }
        Assert.notNull(process, "未定义处理器");
        return process;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Pair<Boolean, List<InventoryActiveLockDtl>> auditActiveLockAdjust(List<IcsActiveLockAdjustDtl> adjustDtlList,
                                      Supplier<List<InventoryActiveLockDtl>> updateActiveLockSupplier,
                                      Runnable inventoryOperationRunnable) {
        if (updateActiveLockSupplier == null || inventoryOperationRunnable == null) {
            return Pair.of(false, null);
        }
        LOGGER.info("开始更新锁库活动调整单...");
        List<InventoryActiveLockDtl> updateActiveLockDtlList = updateActiveLockSupplier.get();
        Assert.isTrue(adjustDtlList.size() == updateActiveLockDtlList.size(), "调整单和更新活动单明细数量不一致");
        LOGGER.info("开始更新锁库活动单状态...");
        // 更新活动锁库调整单状态
        service.updateAdjustStatus(adjustDtlList.get(0).getBillNo(), AdjustStatusEnums.AUDIT_SUCCESS.getValue());
        LOGGER.info("开始预占锁库调整单明细...");
        inventoryOperationRunnable.run();
        return Pair.of(true, updateActiveLockDtlList);
    }

    @Override
    public void updateAdjustStatus(String billNo, Integer status) {
        service.updateAdjustStatus(billNo, status);
    }

    @Override
	public Integer batchInsert(List<IcsActiveLockAdjust> list) {
		list.forEach(this::initEntry);
		return service.batchInsert(list);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateAdjustAndDtl(IcsActiveLockAdjust adjust, List<IcsActiveLockAdjustDtl> adjustDtlList,
			List<InventoryActiveLockDtl> activeLockDtl) {
		List<IcsActiveLockAdjustDtl> updateAdjustDtlList = adjustDtlList.stream()
		.filter(d1 -> activeLockDtl.stream().anyMatch(d2 -> d2.getUniqueKey().equals(d1.getUniqueKey())))
		.collect(Collectors.toList());
		Map<Integer, List<IcsActiveLockAdjustDtl>> statusGroupMap = updateAdjustDtlList.stream().collect(Collectors.groupingBy(IcsActiveLockAdjustDtl::getSyncStatus));
		statusGroupMap.forEach((status, groupAdjustDtlList) -> {
			icsActiveLockAdjustDtlService.batchUpdateSyncStatus(groupAdjustDtlList, status);
		});
		int updateStatus = AdjustSyncStatusEnums.PART_SYNC.getValue();
		List<Integer> statusList = adjustDtlList.stream().map(IcsActiveLockAdjustDtl::getSyncStatus).distinct().collect(Collectors.toList());
		if (CollectionUtils.isEmpty(statusList)) return;
		if (statusList.size() == 1) {
			AdjustDtlSyncStatusEnums e = AdjustDtlSyncStatusEnums.getAdjustDtlSyncStatusEnums(statusList.get(0));
			switch (e) {
			case NOT_SYNC:
				updateStatus = AdjustSyncStatusEnums.NOT_SYNC.getValue();
				break;
			case SYNC_SUCCESS:
				updateStatus = AdjustSyncStatusEnums.ALL_SYNC.getValue();
				break;
			case SYNC_FAIL:
				updateStatus = AdjustSyncStatusEnums.ALL_FAIL_SYNC.getValue();
				break;
			default:
				updateStatus = AdjustSyncStatusEnums.PART_SYNC.getValue();
				break;
			}
		}
		adjust.setSyncStatus(updateStatus);
		service.update(adjust);
	}
}