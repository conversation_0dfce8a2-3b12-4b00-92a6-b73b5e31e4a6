/**  **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.Store;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

import org.apache.ibatis.annotations.Param;

@Mapper
public interface StoreRepository extends IRepository<Store,Integer> {
    
    public Store findByUnique(@Param("storeNo") String storeNo);

    public Integer deleteByUnique(@Param("storeNo") String storeNo);

    public Integer insertForUpdate(Store entry);

    
}