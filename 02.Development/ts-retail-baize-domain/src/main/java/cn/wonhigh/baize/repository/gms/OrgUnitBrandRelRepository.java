/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface OrgUnitBrandRelRepository extends IRepository<OrgUnitBrandRel, Integer> {


    List<OrgUnitBrandRel> selectStoreByUnitNo(@Param("orderUnitNo") String orderUnitNo);

    Set<String> selectValidStoreNos(@Param("storeNos") List<String> storeNos, @Param("zoneNo") String zoneNo);

    List<OrgUnitBrandRel> selectValidStoreNoZones(@Param("storeNos") List<String> storeNos);

    List<OrgUnitBrandRel> pageValidShop(@Param("zoneNos") Collection<String> zoneNos,
                                        @Param("brandNos") Collection<String> brandNos,
                                        @Param("storeNoOrName") String storeNoOrNameMatch,
                                        @Param("startRowNum") Integer startRowNum,
                                        @Param("pageSize") Integer pageSize);

    int countValidShop(@Param("zoneNos") Collection<String> zoneNos,
                       @Param("brandNos") Collection<String> brandNos,
                       @Param("storeNoOrName") String storeNoOrNameMatch);
}