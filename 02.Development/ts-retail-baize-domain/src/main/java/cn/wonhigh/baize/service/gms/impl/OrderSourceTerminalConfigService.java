/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import cn.wonhigh.baize.repository.gms.OrderSourceTerminalConfigRepository;

import cn.wonhigh.baize.service.gms.IOrderSourceTerminalConfigService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@Service
public class OrderSourceTerminalConfigService extends BaseService<OrderSourceTerminalConfig,String> implements  IOrderSourceTerminalConfigService{
    @Autowired
    private OrderSourceTerminalConfigRepository repository;

    protected IRepository<OrderSourceTerminalConfig,String> getRepository(){
        return repository;
    }


    @Override
    public List<OrderSourceTerminalConfig> selectShopPageByParams(Query query, Pagenation pagenation) {
        return repository.selectShopPageByParams(query.asMap(), pagenation);
    }

    @Override
    public Integer selectShopCountByParams(Query query) {
        return repository.selectShopCountByParams(query.asMap());
    }
}