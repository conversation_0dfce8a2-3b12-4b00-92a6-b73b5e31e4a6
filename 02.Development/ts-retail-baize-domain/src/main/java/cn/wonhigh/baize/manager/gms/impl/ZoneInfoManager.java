/** zkh **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.manager.gms.IZoneInfoManager;
import cn.wonhigh.baize.model.entity.gms.ZoneInfo;
import cn.wonhigh.baize.service.gms.IZoneInfoService;
import org.springframework.stereotype.Service;

import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class ZoneInfoManager extends BaseManager<ZoneInfo,Integer> implements IZoneInfoManager {
    @Autowired
    private IZoneInfoService service;

    protected IService<ZoneInfo,Integer> getService(){
        return service;
    }
    
    public ZoneInfo findByUnique(String zoneNo) {
			  return service.findByUnique(zoneNo);
    }

    public Integer deleteByUnique(String zoneNo) {
			  return service.deleteByUnique(zoneNo);
    }

    public List<ZoneInfo> selectByUniques(List<String> zoneNos) {
		return service.selectByUniques(zoneNos);
	}
    	
	public List<ZoneInfo> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<ZoneInfo> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}
}