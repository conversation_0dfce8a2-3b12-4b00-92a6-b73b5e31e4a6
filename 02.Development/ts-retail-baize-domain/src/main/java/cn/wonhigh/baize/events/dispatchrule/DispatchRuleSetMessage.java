package cn.wonhigh.baize.events.dispatchrule;

import cn.hutool.core.util.ObjectUtil;

import java.io.Serializable;
import java.util.LinkedList;

/**
 * <AUTHOR>
 */
public class DispatchRuleSetMessage implements Serializable {

    private Long id;

    private String optUser;

    /**
     * 1 启用
     * <p>
     * 2 禁用
     * <p>
     * 3 修改规则
     * <p>
     * 4 新增规则
     * <p>
     * 5 删除规则
     */
    private int optType;

    private String optTime;

    /**
     * 修改之前规则数据
     */
    private LinkedList<String> beforeRuleName;

    /**
     * 修改之后规则数据
     */
    private LinkedList<String> afterRuleName;


    @Override
    public String toString() {
        return "DispatchRuleSetMessage{" +
                "id=" + id +
                ", optUser='" + optUser + '\'' +
                ", optType=" + optType +
                ", optTime='" + optTime + '\'' +
                ", beforeRuleName=" + ObjectUtil.toString(beforeRuleName) +
                ", afterRuleName=" + ObjectUtil.toString(afterRuleName) +
                '}';
    }

    public DispatchRuleSetMessage(Builder builder) {
        this.afterRuleName = builder.afterRuleName;
        this.beforeRuleName = builder.beforeRuleName;
        this.id = builder.id;
        this.optTime = builder.optTime;
        this.optUser = builder.optUser;
        this.optType = builder.optType;
    }

    public static final class Builder {
        private Long id;

        private String optUser;
        private int optType;

        private String optTime;

        private LinkedList<String> beforeRuleName;

        private LinkedList<String> afterRuleName;

        public Builder setId(Long id) {
            this.id = id;
            return this;
        }

        public Builder setOptUser(String optUser) {
            this.optUser = optUser;
            return this;
        }

        public Builder setOptType(OptTypeEnum optType) {
            this.optType = optType.optType;
            return this;
        }

        public Builder setOptTime(String optTime) {
            this.optTime = optTime;
            return this;
        }

        public Builder setBeforeRuleName(LinkedList<String> beforeRuleName) {
            this.beforeRuleName = beforeRuleName;
            return this;
        }

        public Builder setAfterRuleName(LinkedList<String> afterRuleName) {
            this.afterRuleName = afterRuleName;
            return this;
        }

        public DispatchRuleSetMessage build() {
            return new DispatchRuleSetMessage(this);
        }
    }


    public enum OptTypeEnum {
        enable(1),
        disable(2),
        update(3),
        add(3),
        delete(4)
        ;
        private int optType;

        OptTypeEnum(int optType) {
            this.optType = optType;
        }
    }


    public Long getId() {
        return id;
    }

    public String getOptUser() {
        return optUser;
    }

    public int getOptType() {
        return optType;
    }

    public String getOptTime() {
        return optTime;
    }

    public LinkedList<String> getBeforeRuleName() {
        return beforeRuleName;
    }

    public LinkedList<String> getAfterRuleName() {
        return afterRuleName;
    }
}
