/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.OrderUnit;
import cn.wonhigh.baize.service.gms.IOrderUnitService;
import cn.wonhigh.baize.manager.gms.IOrderUnitManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class OrderUnitManager extends BaseManager<OrderUnit,Integer> implements IOrderUnitManager{
    @Autowired
    private IOrderUnitService service;

    protected IService<OrderUnit,Integer> getService(){
        return service;
    }

    
    public OrderUnit findByUnique(String orderUnitNo) {
        try {
			return service.findByUnique(orderUnitNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String orderUnitNo) {
        try {
			return service.deleteByUnique(orderUnitNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(OrderUnit entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}