package cn.wonhigh.baize.service.oms;

import cn.wonhigh.baize.model.dto.OmsStockOccupancyDTO;
import cn.wonhigh.baize.model.entity.oms.OmsStockOccupancy;
import topmall.framework.service.IService;

import java.util.List;

/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.service.oms
 * @Project：ts-retail-baize
 * @name：IOmsStockOccupancyService
 * @Date：2024/12/27 18:37
 * @Filename：IOmsStockOccupancyService
 * @description：
 */
public interface IOmsStockOccupancyService extends IService<OmsStockOccupancy,Long> {

    OmsStockOccupancyDTO queryLockStockQty( String warehouseId,  String skuId);
}
