/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
import cn.wonhigh.baize.model.entity.gms.ItemAttr;
import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
import org.apache.ibatis.session.ResultHandler;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;

import java.util.List;

public interface ISellOutConfigManager extends IManager<SellOutConfig,String>{

    
    List<SellOutConfig> selectByIds(List<Integer> ids);

    List<ItemAttr> getShareClassifyList(Query query);

    int selectSellOutCount(Query query);

    List<SellOutConfigDto> selectSellOutList(Query query, Pagenation page);

	int updateStatusByParams(Query q);

    List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list);

    int deleteByUnique(Integer type, Tuple3<String, String, String> tuple3);

    void selectByParamsForHandler(Query query, ResultHandler<SellOutConfig> o);
}