/**  **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.RetailOrderReturn;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

@Mapper
public interface RetailOrderReturnRepository extends IRepository<RetailOrderReturn,String> {
    
    public RetailOrderReturn findByUnique(String billNo);

    public Integer deleteByUnique(String billNo);

    public Integer insertForUpdate(RetailOrderReturn entry);

    
}