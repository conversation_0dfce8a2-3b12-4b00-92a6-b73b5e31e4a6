/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface InventoryActiveLockRepository extends IRepository<InventoryActiveLock,String> {
    
    InventoryActiveLock findByUnique(@Param("billNo") String billNo);

    Integer deleteByUnique(@Param("billNo") String billNo);

    List<InventoryActiveLock> selectByUniques(@Param("list") List<String> billNos);
    
    Integer batchInsert(@Param("list") List<InventoryActiveLock> list);
    
    List<InventoryActiveLock> selectByIds(@Param("list") List<Integer> ids);

}