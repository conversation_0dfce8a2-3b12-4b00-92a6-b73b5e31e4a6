package cn.wonhigh.baize.events.systemlog;

import java.util.Date;

public class InternetSystemLogsMessage {

    private String syscode;


    private String sysname;


    private String opscode;

    private String keyword1;


    private String keyword1info;


    private String keyword2;


    private String keyword2info;

    private String createUser;


    private Date createTime;


    private String remark;

    public InternetSystemLogsMessage() {
    }

    public InternetSystemLogsMessage(InternetSystemLogsMessageBuilder builder) {
        this.syscode = builder.syscode;
        this.sysname = builder.sysname;
        this.opscode = builder.opscode;
        this.keyword1 = builder.keyword1;
        this.keyword1info = builder.keyword1info;
        this.keyword2 = builder.keyword2;
        this.keyword2info = builder.keyword2info;
        this.createUser = builder.createUser;
        this.createTime = builder.createTime;
        this.remark = builder.remark;
    }

    public final static class InternetSystemLogsMessageBuilder {
        private String syscode;
        private String sysname;
        private String opscode;
        private String keyword1;
        private String keyword1info;

        private String keyword2;

        private String keyword2info;

        private String createUser;

        private Date createTime;


        private String remark;


        public InternetSystemLogsMessageBuilder() {
        }

        public InternetSystemLogsMessageBuilder setSyscode(String syscode) {
            this.syscode = syscode;
            return this;
        }

        public InternetSystemLogsMessageBuilder setSysname(String sysname) {
            this.sysname = sysname;
            return this;
        }

        public InternetSystemLogsMessageBuilder setOpscode(String opscode) {
            this.opscode = opscode;
            return this;
        }

        public InternetSystemLogsMessageBuilder setKeyword1(String keyword1) {
            this.keyword1 = keyword1;
            return this;
        }

        public InternetSystemLogsMessageBuilder setKeyword1info(String keyword1info) {
            this.keyword1info = keyword1info;
            return this;
        }

        public InternetSystemLogsMessageBuilder setKeyword2(String keyword2) {
            this.keyword2 = keyword2;
            return this;
        }

        public InternetSystemLogsMessageBuilder setKeyword2info(String keyword2info) {
            this.keyword2info = keyword2info;
            return this;
        }

        public InternetSystemLogsMessageBuilder setCreateUser(String createUser) {
            this.createUser = createUser;
            return this;
        }

        public InternetSystemLogsMessageBuilder setCreateTime(Date createTime) {
            this.createTime = createTime;
            return this;
        }

        public InternetSystemLogsMessageBuilder setRemark(String remark) {
            this.remark = remark;
            return this;
        }

        public InternetSystemLogsMessage build() {
            return new InternetSystemLogsMessage(this);
        }
    }

    public String getSyscode() {
        return syscode;
    }

    public void setSyscode(String syscode) {
        this.syscode = syscode;
    }

    public String getSysname() {
        return sysname;
    }

    public void setSysname(String sysname) {
        this.sysname = sysname;
    }

    public String getOpscode() {
        return opscode;
    }

    public void setOpscode(String opscode) {
        this.opscode = opscode;
    }

    public String getKeyword1() {
        return keyword1;
    }

    public void setKeyword1(String keyword1) {
        this.keyword1 = keyword1;
    }

    public String getKeyword1info() {
        return keyword1info;
    }

    public void setKeyword1info(String keyword1info) {
        this.keyword1info = keyword1info;
    }

    public String getKeyword2() {
        return keyword2;
    }

    public void setKeyword2(String keyword2) {
        this.keyword2 = keyword2;
    }

    public String getKeyword2info() {
        return keyword2info;
    }

    public void setKeyword2info(String keyword2info) {
        this.keyword2info = keyword2info;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
