package cn.wonhigh.baize.domain.configuration.datasource;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
//@ConditionalOnProperty(prefix = "retail.ios.datasource", name = { "url", "username", "password" })
@MapperScan(basePackages = {
		IOSDataSourceConfiguration.BASE_PACKAGE }, sqlSessionFactoryRef = "iosSqlSessionFactory")
@Order(2)
public class IOSDataSourceConfiguration {

	public static final String BASE_PACKAGE = "cn.wonhigh.baize.repository.ios";

	public IOSDataSourceConfiguration() {
		System.err.println("init ios datasource ...");
	}
	
	@Bean("masterIosDatasource")
	@ConfigurationProperties("retail.ios.datasource")
	public DataSource dataSourceMysql() {
		return DataSourceBuilder.create().type(DruidDataSource.class).build();
	}

	@Bean("slaveIosDataSource")
	@ConfigurationProperties("retail.ios.datasource.ap")
	public DataSource dataSourceApGms() {
		return DataSourceBuilder.create().type(DruidDataSource.class).build();
	}


	@Bean("iosDatasource")
	public DataSource dynamicDataSource(@Qualifier("masterIosDatasource") DataSource masterDataSource,
										@Qualifier("slaveIosDataSource") DataSource slaveDataSource) {
		String apKey = "ap";
		Map<Object, Object> targetDataSources = new HashMap<>();
		targetDataSources.put("tp", masterDataSource);
		targetDataSources.put(apKey, slaveDataSource);

		AbstractRoutingDataSource dataSource = new AbstractRoutingDataSource() {
			@Override
			protected Object determineCurrentLookupKey() {
				if (ApDataSourceSwitch.openedAp()) {
					return apKey;
				}
				return null;
			}
		};
		dataSource.setDefaultTargetDataSource(masterDataSource);
		dataSource.setTargetDataSources(targetDataSources);
		// 指定的数据源不存在时直接报错
		dataSource.setLenientFallback(false);
		return dataSource;
	}

	@Bean("iosSqlSessionFactory")	
	public SqlSessionFactory sqlSessionFactory(@Qualifier("iosDatasource") DataSource ds) throws Exception {

		SqlSessionFactoryBean bean = new SqlSessionFactoryBean();

		bean.setDataSource(ds);

		Resource rs = new PathMatchingResourcePatternResolver().getResources("mybatis-ios-config.xml")[0];
		bean.setConfigLocation(rs);
		bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("mapper/ios/*.xml"));

		return bean.getObject();
	}

	@Bean("iosSqlSessionTemplate")	
	public SqlSessionTemplate sqlSessionTemplate(@Qualifier("iosSqlSessionFactory") SqlSessionFactory factory) {
		return new SqlSessionTemplate(factory);
	}

	@Bean("iosTransactionManager")
	public DataSourceTransactionManager transactionManager(@Qualifier("iosDatasource") DataSource ds) {
		return new DataSourceTransactionManager(ds);
	}

}
