/**
 *
 **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface InternetDispatchRuleSetRepository extends IRepository<InternetDispatchRuleSet, Long> {


    /**
     * 判断已经存在某些数据
     *
     * @param query 查询条件
     * @return 记录
     */
    int exitsQueryData(@Param("query") Map<String, Object> query);

    long nextId();
}