/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.BrandInventoryRange;
import cn.wonhigh.baize.service.gms.IBrandInventoryRangeService;
import cn.wonhigh.baize.manager.gms.IBrandInventoryRangeManager;
import topmall.framework.security.Authorization;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class BrandInventoryRangeManager extends BaseManager<BrandInventoryRange,String> implements IBrandInventoryRangeManager{
    @Autowired
    private IBrandInventoryRangeService service;

    protected IService<BrandInventoryRange,String> getService(){
        return service;
    }
    	
	public List<BrandInventoryRange> selectByIds(List<String> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<BrandInventoryRange> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}

	@Override
	public int selectShareNoExist(Map<String, Object> paramMaps) {
		return service.selectShareNoExist(paramMaps);
	}

	@Override
	public void updateShare(Map<String, Object> paramMap) {
		service.updateShare(paramMap);
	}

}