/**  **/
package cn.wonhigh.baize.service.ios;

import cn.wonhigh.baize.model.entity.ios.InternetOrder;
import topmall.framework.service.IService;

public interface IInternetOrderService extends IService<InternetOrder,String>{

    
    public InternetOrder findByUnique(String orderSubNo);

    public Integer deleteByUnique(String orderSubNo);

    public Integer insertForUpdate(InternetOrder entry);
    
}