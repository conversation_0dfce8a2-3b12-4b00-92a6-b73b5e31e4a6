/**  **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.RetailOrder;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

@Mapper
public interface RetailOrderRepository extends IRepository<RetailOrder,String> {
    
    public RetailOrder findByUnique(String billNo);

    public Integer deleteByUnique(String billNo);

    public Integer insertForUpdate(RetailOrder entry);

    
}