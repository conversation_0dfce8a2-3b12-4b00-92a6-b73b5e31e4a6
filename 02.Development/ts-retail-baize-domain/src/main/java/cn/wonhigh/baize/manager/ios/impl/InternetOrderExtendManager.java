/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderExtend;
import cn.wonhigh.baize.service.ios.IInternetOrderExtendService;
import cn.wonhigh.baize.manager.ios.IInternetOrderExtendManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class InternetOrderExtendManager extends BaseManager<InternetOrderExtend,String> implements IInternetOrderExtendManager{
    @Autowired
    private IInternetOrderExtendService service;

    protected IService<InternetOrderExtend,String> getService(){
        return service;
    }

    
    public InternetOrderExtend findByUnique(String orderSubNo) {
        try {
			return service.findByUnique(orderSubNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String orderSubNo) {
        try {
			return service.deleteByUnique(orderSubNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(InternetOrderExtend entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}