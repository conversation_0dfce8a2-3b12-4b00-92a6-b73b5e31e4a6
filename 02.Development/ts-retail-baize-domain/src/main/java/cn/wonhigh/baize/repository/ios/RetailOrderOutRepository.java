/**  **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.RetailOrderOut;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

@Mapper
public interface RetailOrderOutRepository extends IRepository<RetailOrderOut,String> {
    
    public RetailOrderOut findByUnique(String billNo);

    public Integer deleteByUnique(String billNo);

    public Integer insertForUpdate(RetailOrderOut entry);

    
}