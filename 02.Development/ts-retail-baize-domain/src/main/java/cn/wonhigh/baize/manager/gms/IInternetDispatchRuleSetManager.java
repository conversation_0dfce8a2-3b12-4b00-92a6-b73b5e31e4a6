/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.model.dto.dispatchruleset.DispatchRuleSetSaveDto;
import cn.wonhigh.baize.model.dto.dispatchruleset.ExitsQueryDataDto;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;

import cn.mercury.manager.IManager;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
public interface IInternetDispatchRuleSetManager extends IManager<InternetDispatchRuleSet,Long>{


    /**
     * 保存派单规则
     * @param saveDto 数据
     */
    @Transactional
    void save(DispatchRuleSetSaveDto saveDto) throws ManagerException;


    int exitsData(ExitsQueryDataDto queryDataDto);

}