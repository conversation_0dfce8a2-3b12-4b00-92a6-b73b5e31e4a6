package cn.wonhigh.baize.domain.configuration.datasource;

import java.util.Date;

/**
 * @author: wudong
 * @create: 2025-07-08 09:56
 **/
public class ApDataSourceSwitch implements AutoCloseable {
    public static final ThreadLocal<ApDataSourceSwitch> SWITCH = new ThreadLocal<>();
    private boolean useAp;
    private Date snapshotTime;

    private ApDataSourceSwitch(boolean useAp, Date snapshotTime) {
        this.useAp = useAp;
        this.snapshotTime = snapshotTime;
        if (snapshotTime != null && !useAp) {
            throw new IllegalStateException("snapshotTime should only be set when useAp");
        }
        SWITCH.set(this);
    }


    public boolean isUseAp() {
        return useAp;
    }

    public Date getSnapshotTime() {
        return snapshotTime;
    }

    public static ApDataSourceSwitch apSwitch() {
        return SWITCH.get();
    }

    public static boolean openedAp(){
        ApDataSourceSwitch apDataSourceSwitch = apSwitch();
        if (apDataSourceSwitch != null && apDataSourceSwitch.isUseAp()) {
            return true;
        }
        return false;
    }

    public static ApDataSourceSwitch openAp(){
        return new ApDataSourceSwitch(true, null);
    }

    public static ApDataSourceSwitch openApOnQueryDate(Date snapshotTime){
        return new ApDataSourceSwitch(true, snapshotTime);
    }

    @Override
    public void close() {
        SWITCH.remove();
    }



}
