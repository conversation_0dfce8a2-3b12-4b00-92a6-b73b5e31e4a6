package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.InternetAvailableInventory;
import cn.wonhigh.baize.model.entity.gms.VstoreStoreAvailableInventory;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface InternetSearchInventoryRepository {

    public List<InternetAvailableInventory> findAvailableInOneStore(Map<String, Object> params);

    List<VstoreStoreAvailableInventory> queryVstoreQtyDetail(@Param("skuNoList") List<String> skuNo,
                                                             @Param("vstoreCodeList") List<String> vstoreCodeList,
                                                             @Param("startRowNum") int startRowNum,
                                                             @Param("pageSize") int pageSize);

    Integer countVstoreQty(@Param("skuNoList") List<String> skuNo,
                           @Param("vstoreCodeList") List<String> vstoreCodeList);
}
