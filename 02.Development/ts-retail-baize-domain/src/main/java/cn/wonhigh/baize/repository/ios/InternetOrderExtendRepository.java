/**  **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.InternetOrderExtend;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

@Mapper
public interface InternetOrderExtendRepository extends IRepository<InternetOrderExtend,String> {
    
    public InternetOrderExtend findByUnique(String orderSubNo);

    public Integer deleteByUnique(String orderSubNo);

    public Integer insertForUpdate(InternetOrderExtend entry);

    
}