package cn.wonhigh.baize.converter;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.wonhigh.baize.model.dto.dispatchruleset.*;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetDtl;
import cn.wonhigh.baize.model.enums.InternetDispatchRuleSetEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class InternetDispatchRuleSetConverter {


    public DispatchRuleSetDto entityToDto(InternetDispatchRuleSet entity) {
        if (entity == null) {
            return null;
        }

        DispatchRuleSetDto dto= new DispatchRuleSetDto();
        dto.setId(entity.getId());
        dto.setState(entity.getState());
        dto.setBrandName(entity.getBrandName());
        dto.setBrandNo(entity.getBrandNo());
        dto.setRuleNo(entity.getRuleNo());
        dto.setRuleName(entity.getRuleName());
        dto.setType(entity.getType());
        dto.setCreateTime(entity.getCreateTime());
        dto.setCreateUser(entity.getCreateUser());
        dto.setUpdateUser(entity.getUpdateUser());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setRuleList(getInternetDispatchRulePriorityDtos(entity));


        if (entity.getDtlList() != null) {
            List<InternetDispatchRuleSetDtlDto> dtlList= new ArrayList<>(entity.getDtlList().size());
            for (InternetDispatchRuleSetDtl internetDispatchRuleSetDtl : entity.getDtlList()) {
                InternetDispatchRuleSetDtlDto dtl = entityToDtlDto(internetDispatchRuleSetDtl);
                dtlList.add(dtl);
            }

            dto.setDtlList(dtlList);
        }
        return dto;
    }


    public InternetDispatchRuleSet saveDtoConvertEntity(DispatchRuleSetSaveDto saveDto) {
        if (saveDto == null) {
            return null;
        }
        InternetDispatchRuleSet internetDispatchRuleSet = new InternetDispatchRuleSet();
        internetDispatchRuleSet.setId(saveDto.getId());
        internetDispatchRuleSet.setBrandNo(saveDto.getBrandNo());
        internetDispatchRuleSet.setBrandName(saveDto.getBrandName());
        internetDispatchRuleSet.setType(saveDto.getType());
        internetDispatchRuleSet.setRuleNo(saveDto.getRuleNo());
        internetDispatchRuleSet.setRuleName(saveDto.getRuleName());
        internetDispatchRuleSet.setPlatformName(saveDto.getPlatformName());
        internetDispatchRuleSet.setPlatformNo(saveDto.getPlatformNo());

        if (saveDto.getRuleList() != null) {
            for (InternetDispatchRuleSetEnum value : InternetDispatchRuleSetEnum.values()) {
                InternetDispatchRulePriorityDto priorityDto = saveDto.getRuleList().stream().filter(item -> StrUtil.equals(item.getPriorityCode(), value.getPriorityCode()))
                        .findFirst().orElse(null);
                String stateFieldName = StrUtil.toCamelCase(value.getStateCode());
                String priorityFieldName = StrUtil.toCamelCase(value.getPriorityCode());
                if (priorityDto != null) {
                    BeanUtil.setFieldValue(internetDispatchRuleSet, priorityFieldName, priorityDto.getSort()+10);
                    BeanUtil.setFieldValue(internetDispatchRuleSet, stateFieldName, 1);
                } else {
                    BeanUtil.setFieldValue(internetDispatchRuleSet, stateFieldName, 0);
                    BeanUtil.setFieldValue(internetDispatchRuleSet, priorityFieldName, 0);
                }
            }
        }

        if (saveDto.getDtlList() != null) {
            List<InternetDispatchRuleSetDtl> dtlList= new ArrayList<>(saveDto.getDtlList().size());
            for (InternetDispatchRuleSetDtlSaveDto internetDispatchRuleSetDtlSaveDto : saveDto.getDtlList()) {
                InternetDispatchRuleSetDtl dtl = saveDtoToEntity(internetDispatchRuleSetDtlSaveDto);
                dtlList.add(dtl);
            }

            internetDispatchRuleSet.setDtlList(dtlList);
        }

        return internetDispatchRuleSet;
    }


    public DispatchRuleSetSaveDto entityToSaveDto(InternetDispatchRuleSet entity) {
        if (entity == null) {
            return null;
        }
        DispatchRuleSetSaveDto saveDto= new DispatchRuleSetSaveDto();
        saveDto.setBrandName(entity.getBrandName());
        saveDto.setId(entity.getId());
        saveDto.setBrandNo(entity.getBrandNo());
        saveDto.setRuleNo(entity.getRuleNo());
        saveDto.setRuleName(entity.getRuleName());
        saveDto.setType(entity.getType());
        saveDto.setPlatformName(entity.getPlatformName());
        saveDto.setPlatformNo(entity.getPlatformNo());


        saveDto.setRuleList(getInternetDispatchRulePriorityDtos(entity));

        List<InternetDispatchRuleSetDtlSaveDto> setDtlSaveDtos = new ArrayList<>();
        if (entity.getDtlList() != null) {
            for (InternetDispatchRuleSetDtl dtl : entity.getDtlList()) {
                setDtlSaveDtos.add(entityToSaveDto(dtl));
            }
        }
        saveDto.setDtlList(setDtlSaveDtos);

        return saveDto;
    }

    private List<InternetDispatchRulePriorityDto> getInternetDispatchRulePriorityDtos(InternetDispatchRuleSet entity) {
        List<InternetDispatchRulePriorityDto> priorityDtos = new ArrayList<>();
        for (InternetDispatchRuleSetEnum value : InternetDispatchRuleSetEnum.values()) {
            String stateFieldName = StrUtil.toCamelCase(value.getStateCode());
            String priorityFieldName = StrUtil.toCamelCase(value.getPriorityCode());

            Integer state =  BeanUtil.getProperty(entity, stateFieldName);
            Integer priority =  BeanUtil.getProperty(entity, priorityFieldName);

            if (state !=null && state == 1) {
                InternetDispatchRulePriorityDto internetDispatchRulePriorityDto = new InternetDispatchRulePriorityDto();
                internetDispatchRulePriorityDto.setId(value.getId());
                internetDispatchRulePriorityDto.setStateCode(value.getStateCode());
                internetDispatchRulePriorityDto.setPriorityCode(value.getPriorityCode());
                internetDispatchRulePriorityDto.setName(value.getName());
                internetDispatchRulePriorityDto.setSort(priority-10);
                priorityDtos.add(internetDispatchRulePriorityDto);
            }

        }
        return priorityDtos.stream().sorted(Comparator.comparing(InternetDispatchRulePriorityDto::getSort).reversed())
                .collect(Collectors.toList());
    }


    private InternetDispatchRuleSetDtl saveDtoToEntity(InternetDispatchRuleSetDtlSaveDto saveDto) {
        if (saveDto == null) {
            return null;
        }
        InternetDispatchRuleSetDtl dtl = new InternetDispatchRuleSetDtl();
        dtl.setRuleNo(saveDto.getRuleNo());
        dtl.setChannelName(saveDto.getChannelName());
        dtl.setChannelNo(saveDto.getChannelNo());
        dtl.setOrderSourceNo(saveDto.getOrderSourceNo());
        dtl.setOrderSourceName(saveDto.getOrderSourceName());
        dtl.setId(saveDto.getId());
        dtl.setMoreShopFlag(saveDto.getMoreShopFlag());
        return dtl;
    }


    private InternetDispatchRuleSetDtlSaveDto entityToSaveDto(InternetDispatchRuleSetDtl entity) {
        if (entity == null) {
            return null;
        }
        InternetDispatchRuleSetDtlSaveDto dtl = new InternetDispatchRuleSetDtlSaveDto();
        dtl.setRuleNo(entity.getRuleNo());
        dtl.setChannelName(entity.getChannelName());
        dtl.setChannelNo(entity.getChannelNo());
        dtl.setOrderSourceNo(entity.getOrderSourceNo());
        dtl.setOrderSourceName(entity.getOrderSourceName());
        dtl.setId(entity.getId());
        dtl.setMoreShopFlag(entity.getMoreShopFlag());
        return dtl;
    }

    private InternetDispatchRuleSetDtlDto entityToDtlDto(InternetDispatchRuleSetDtl entity) {
        if (entity == null) {
            return null;
        }
        InternetDispatchRuleSetDtlDto dtl = new InternetDispatchRuleSetDtlDto();
        dtl.setRuleNo(entity.getRuleNo());
        dtl.setChannelName(entity.getChannelName());
        dtl.setChannelNo(entity.getChannelNo());
        dtl.setOrderSourceNo(entity.getOrderSourceNo());
        dtl.setOrderSourceName(entity.getOrderSourceName());
        dtl.setId(entity.getId());
        dtl.setMoreShopFlag(entity.getMoreShopFlag());
        return dtl;
    }
}
