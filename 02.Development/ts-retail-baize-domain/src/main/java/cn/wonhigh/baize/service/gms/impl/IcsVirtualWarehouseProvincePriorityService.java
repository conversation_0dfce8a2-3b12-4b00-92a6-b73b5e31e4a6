/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority;
import cn.wonhigh.baize.repository.gms.IcsVirtualWarehouseProvincePriorityRepository;

import cn.wonhigh.baize.service.gms.IIcsVirtualWarehouseProvincePriorityService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class IcsVirtualWarehouseProvincePriorityService extends BaseService<IcsVirtualWarehouseProvincePriority,String> implements  IIcsVirtualWarehouseProvincePriorityService{
    @Autowired
    private IcsVirtualWarehouseProvincePriorityRepository repository;

    protected IRepository<IcsVirtualWarehouseProvincePriority,String> getRepository(){
        return repository;
    }
      
	public List<IcsVirtualWarehouseProvincePriority> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<IcsVirtualWarehouseProvincePriority> list) {
    	return repository.batchInsert(list);
    }
}