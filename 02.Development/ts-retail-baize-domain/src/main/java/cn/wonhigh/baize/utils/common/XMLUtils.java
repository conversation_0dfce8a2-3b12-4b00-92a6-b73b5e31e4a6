package cn.wonhigh.baize.utils.common;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2023/10/23 16:32
 */
public class XMLUtils {

    /**
     * xmlString转换成对象
     */
    public static Object convertXmlStrToObject(Class clazz, String xmlStr) throws Exception {
        JAXBContext context = JAXBContext.newInstance(clazz);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        StringReader sr = new StringReader(xmlStr);
        return unmarshaller.unmarshal(sr);
    }

    /**
     * 对象转换成xmlString
     * @return
     */
    public static String convertToXmlStr(Object obj) throws Exception {
        //创建输出流
        StringWriter sw = new StringWriter();
        sw.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>" + "\n");
        //利用jdk中自带的转换类实现
        JAXBContext context = JAXBContext.newInstance(obj.getClass());
        Marshaller marshaller = context.createMarshaller();
        //格式化xml输出的格式
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
        //去掉生成xml的默认报文头
        marshaller.setProperty(Marshaller.JAXB_FRAGMENT, Boolean.TRUE);
        //将对象转换成输出流形式的xml
        marshaller.marshal(obj, sw);

        return sw.toString();
    }

}
