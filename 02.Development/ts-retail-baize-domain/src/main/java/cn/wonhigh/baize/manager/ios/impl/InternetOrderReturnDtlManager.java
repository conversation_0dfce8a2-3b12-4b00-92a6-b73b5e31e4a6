/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import cn.wonhigh.baize.manager.ios.IInternetOrderReturnDtlManager;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderReturnDtl;
import cn.wonhigh.baize.service.ios.IInternetOrderReturnDtlService;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class InternetOrderReturnDtlManager extends BaseManager<InternetOrderReturnDtl,String> implements IInternetOrderReturnDtlManager {
    @Autowired
    private IInternetOrderReturnDtlService service;

    protected IService<InternetOrderReturnDtl,String> getService(){
        return service;
    }

    
}