/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderDtl;
import cn.wonhigh.baize.repository.ios.InternetOrderDtlRepository;

import cn.wonhigh.baize.service.ios.IInternetOrderDtlService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetOrderDtlService extends BaseService<InternetOrderDtl,String> implements  IInternetOrderDtlService{
    @Autowired
    private InternetOrderDtlRepository repository;

    protected IRepository<InternetOrderDtl,String> getRepository(){
        return repository;
    }

    
}