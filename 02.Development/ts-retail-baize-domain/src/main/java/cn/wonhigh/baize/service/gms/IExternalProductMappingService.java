/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.ExternalProductMapping;
import reactor.util.function.Tuple3;
import topmall.framework.service.IService;
import java.util.List;
import java.util.Map;

public interface IExternalProductMappingService extends IService<ExternalProductMapping,String>{
      
    Integer batchInsert(List<ExternalProductMapping> list);
    
    List<ExternalProductMapping> selectByIds(List<Integer> ids);

    public List<ExternalProductMapping> selectItemSkuByParams(Map<String, Object> params);

    /**
     * 根据商品编码、品牌编码、尺码编码查询
     *
     * @param merchantsCode 商家编码
     * @param itemBrandSizeList T1:商品编码 T2:品牌编码 T3:尺码编码
     * @return 商品映射信息
     */
    List<ExternalProductMapping> selectItemsByTuple3(String merchantsCode, List<Tuple3<String, String, String>> itemBrandSizeList);

}