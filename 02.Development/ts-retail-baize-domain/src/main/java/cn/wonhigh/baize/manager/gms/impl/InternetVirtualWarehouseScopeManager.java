/**
 *
 **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.hutool.core.lang.Assert;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.business.virtualwarehousescope.*;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseScopeManager;
import cn.wonhigh.baize.manager.gms.IInventoryActiveLockDtlManager;
import cn.wonhigh.baize.manager.gms.IInventoryActiveLockManager;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeStatusChangeDto;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeValidateDto;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import cn.wonhigh.baize.service.gms.IInternetVirtualWarehouseInfoService;
import cn.wonhigh.baize.service.gms.IInternetVirtualWarehouseScopeService;
import cn.wonhigh.baize.utils.common.QueryUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import topmall.framework.manager.BaseManager;
import topmall.framework.security.Authorization;
import topmall.framework.service.IService;

import javax.validation.ValidationException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class InternetVirtualWarehouseScopeManager extends BaseManager<InternetVirtualWarehouseScope, String> implements IInternetVirtualWarehouseScopeManager {

    @Autowired
    private IInternetVirtualWarehouseScopeService service;

    @Autowired
    private IInternetVirtualWarehouseInfoService virtualWarehouseInfoService;

    @Autowired
    private IInventoryActiveLockDtlManager inventoryActiveLockDtlManager;

    @Autowired
    private IInventoryActiveLockManager inventoryActiveLockManager;

    private static final Integer SAVE_SIZE = 100;

    protected IService<InternetVirtualWarehouseScope, String> getService() {
        return service;
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectScopeListByPage(Map<String, Object> var1, Pagenation var2) {
        return service.selectScopeListByPage(var1, var2);
    }

    @Override
    public int selectScopeListCount(Map<String, Object> var1) {
        return service.selectScopeListCount(var1);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectStoreByPage(Map<String, Object> var1, Pagenation var2) {
        return service.selectStoreByPage(var1, var2);
    }

    @Override
    public int selectStoreCount(Map<String, Object> var1) {
        return service.selectStoreCount(var1);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectExistStore(Map<String, Object> params) {
        return this.service.selectExistStore(params);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectExistShop(Map<String, Object> params) {
        return this.service.selectExistShop(params);
    }

    @Override
    public void validateData(InternetVirtualWarehouseScopeValidateDto validateDto, InternetVirtualWarehouseInfo virtualWarehouseInfo) throws ValidationException {

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveInternetVirtualWarehouseScope(InternetVirtualWarehouseScopeSave save, InternetVirtualWarehouseInfo info) throws ManagerException {
        Assert.notNull(save, () -> new ManagerException("参数不能为空"));
        VirtualWarehouseScopeSaveExecutor virtualWarehouseScopeSaveExecutor = getInstance(save, info);
        if (virtualWarehouseScopeSaveExecutor instanceof AbstractVirtualWarehouseScopeSaveExecutor) {
            ((AbstractVirtualWarehouseScopeSaveExecutor) virtualWarehouseScopeSaveExecutor).setInternetVirtualWarehouseInfo(info);
            ((AbstractVirtualWarehouseScopeSaveExecutor) virtualWarehouseScopeSaveExecutor).setValidateDataService(new SaveScopeValidateService(info));
        }
        virtualWarehouseScopeSaveExecutor.execute();
    }



    public VirtualWarehouseScopeSaveExecutor getInstance(InternetVirtualWarehouseScopeSave save, InternetVirtualWarehouseInfo info) {
//        if (Integer.parseInt(save.getMoreStoreFlag()) == 1) {
//            return new MultiAgencyExecutor(save);
//        } else
//        return new SingleAgencyExecutor(save);
        if(info.getVstoreMold() == 1){//标准仓
            return new StandardVirtualSaveExecutor(save);
        } else {
            return new NormalVirtualSaveExecutor(save);
        }
    }

    @Override
    public int updateAllScope(Map<String, Object> params) {
        return this.service.updateAllScope(params);
    }

    @Override
    public int deleteAllShopScope(Map<String, Object> params) {
        return this.service.deleteAllShopScope(params);
    }

    @Override
    public int deleteStoreScope(Map<String, Object> params) {
        return this.service.deleteStoreScope(params);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectScopeInfo(Map<String, Object> params){
        return service.selectScopeInfo(params);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = { Exception.class })
    public Integer insertBatch(List<InternetVirtualWarehouseScope> list){
        if(!CollectionUtils.isEmpty(list)){
            AtomicInteger count = new AtomicInteger(0);
            int limit = (list.size() + SAVE_SIZE - 1) / SAVE_SIZE;
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(page -> {
                List<InternetVirtualWarehouseScope> list1 = list.stream().skip(SAVE_SIZE * page).limit(SAVE_SIZE)
                        .collect(Collectors.toList());
                Integer succs = service.insertBatch(list1);
                if (succs != null && succs > 0) {
                    count.addAndGet(succs);
                }
            });
            return count.get();
        }
        return 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = { Exception.class })
    public void deleteData(List<String> dataIds) {
        InternetVirtualWarehouseInfo info = null;
        Set<String> delScope = new HashSet<>();
        for (String dataId : dataIds) {
            InternetVirtualWarehouseScope scope = service.findByPrimaryKey(dataId);
            if (scope == null) {
                throw new ManagerException("删除聚合仓范围不存在");
            }
            if (scope.getStatus() == 1) {
                throw new ManagerException("删除聚合仓范围状态不是禁用状态,storeNo:" + scope.getStoreNo() + ",orderUnitNo:" + scope.getOrderUnitNo());
            }
            if (info == null) {
                List<InternetVirtualWarehouseInfo> infos = virtualWarehouseInfoService.selectByParams(
                        Query.Where("vstoreCode", scope.getVstoreCode()));
                if(CollectionUtils.isEmpty(infos)) {
                    throw new ManagerException("找不到聚合仓信息,vstoreCode:" + scope.getVstoreCode());
                }
                info = infos.get(0);
            }
            delScope.add(scope.getOrderUnitNo() + scope.getStoreNo());
            service.deleteByPrimaryKey(scope.getId());
            saveLog(buildLog(OpscodeEnum.DEL.getCode(), scope.getVstoreCode(), scope.getOrderUnitNo(), scope.getStoreNo(), OpscodeEnum.DEL.getName()));
        }
        //总仓删除同时删除子仓
        if(info.getVstoreType() == 1 && delScope.size() > 0){
            List<InternetVirtualWarehouseScope> listChildScope = service.selectScopeInfo(
                    Query.Where("parentVstoreCode", info.getVstoreCode()).and("vstoreType", 2).asMap());
            if (!CollectionUtils.isEmpty(listChildScope)) {
                for (InternetVirtualWarehouseScope scope : listChildScope) {
                    if(delScope.contains(scope.getOrderUnitNo() + scope.getStoreNo())){
                        service.deleteByPrimaryKey(scope.getId());
                        saveLog(buildLog(OpscodeEnum.DEL.getCode(), scope.getVstoreCode(), scope.getOrderUnitNo(), scope.getStoreNo(), OpscodeEnum.DEL.getName()));
                    }
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = { Exception.class })
    public void statusChange(InternetVirtualWarehouseScopeStatusChangeDto statusChangeDto, InternetVirtualWarehouseInfo info){
        String remark = statusChangeDto.getStatus() == 1 ? "启用": "禁用";
        Date date = new Date();
        String updateUser = Authorization.getUser().getName();

        List<InventoryActiveLockDtl> lockDtls = new ArrayList<>();
        List<InternetSystemLogsMessage> logs = new ArrayList<>();
        for (InternetVirtualWarehouseScopeStatusChangeDto.Dtl itemDtl : statusChangeDto.getDtlData()) {
            //修改传入明细状态
            updateStatus(info.getVstoreCode(), statusChangeDto.getStatus(), date, updateUser, remark, lockDtls, logs, itemDtl);

            //修改关联数据明细状态
            String vstoreCode = "";
            //标准逻辑:总仓联动子仓启用禁用,子仓没有启用禁用
            if(info.getVstoreMold() == 1){
                vstoreCode = statusChangeDto.getVstoreCode().concat("-RY");
            } else if(info.getVstoreType() == 1){
                //非标准逻辑:子仓单独禁用，其余互相联动
                vstoreCode = statusChangeDto.getVstoreCode().concat("-101");
            } else if(statusChangeDto.getStatus() == 1) {
                vstoreCode = statusChangeDto.getVstoreCode().substring(0,  statusChangeDto.getVstoreCode().length()-4);
            }
            if(StringUtils.hasText(vstoreCode)){
                Map<String,Object> params = new HashMap<String,Object>();
                params.put("vstoreCode", vstoreCode);
                params.put("storeNo", itemDtl.getStoreNo());
                params.put("orderUnitNo", itemDtl.getOrderUnitNo());
                List<InternetVirtualWarehouseScope> vList = service.selectByParams(QueryUtil.mapToQuery(params));
                if(vList!= null && !vList.isEmpty()) {
                    if(vList.get(0).getStatus() != statusChangeDto.getStatus()){
                        itemDtl.setId(vList.get(0).getId());
                        updateStatus(vstoreCode, statusChangeDto.getStatus(), date, updateUser, remark, lockDtls, logs, itemDtl);
                    }
                }
            }
        }
        //释放锁库明细
        if(!CollectionUtils.isEmpty(lockDtls)){
            Map<String, List<InventoryActiveLockDtl>> map = lockDtls.stream().collect(Collectors.groupingBy(x->x.getBillNo()));
            for (String billNo : map.keySet()) {
                InventoryActiveLock lock = new InventoryActiveLock();
                lock.setBillNo(billNo);
                try {
                    logger.info("聚合仓明细禁用同步释放活动锁库, billNo:{}, activeLockDtls:{}", billNo, map.get(billNo));
                    inventoryActiveLockManager.disableLockOccupiedInventory(lock, map.get(billNo));
                } catch (Exception e) {
                    logger.error("释放活动预占异常, billNo:{},  disableLockOccupiedInventory error, msg:{}", billNo, e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            }
            //释放成功修改锁库明细数量
            for (InventoryActiveLockDtl lockDtl : lockDtls) {
                InventoryActiveLockDtl updateDtl = new InventoryActiveLockDtl();
                updateDtl.setId(lockDtl.getId());
                updateDtl.setBalanceLockQty(0);
                inventoryActiveLockDtlManager.update(updateDtl);
            }
        }
        //最后再保存修改日志
        if(!CollectionUtils.isEmpty(logs)){
            logs.stream().forEach(x->saveLog(x));
        }
    }

    //修改状态
    private void updateStatus(String vstoreCode, Integer status, Date date, String updateUser, String remark,
                      List<InventoryActiveLockDtl> lockDtls,List<InternetSystemLogsMessage> logs,
                      InternetVirtualWarehouseScopeStatusChangeDto.Dtl itemDtl){
        InternetVirtualWarehouseScope scope = new InternetVirtualWarehouseScope();
        scope.setId(itemDtl.getId());
        scope.setStatus(status);
        scope.setUpdateUser(updateUser);
        scope.setUpdateTime(date);
        service.update(scope);
        logs.add(buildLog(OpscodeEnum.UP.getCode(), vstoreCode, itemDtl.getOrderUnitNo(), itemDtl.getStoreNo(), remark));
        //禁用查询活动锁库
        if(status == 0){
            List<InventoryActiveLockDtl> activeLocks = inventoryActiveLockDtlManager.selectByVstoreInfo(
                    Query.Where("vstoreCode", vstoreCode).and("storeNo", itemDtl.getStoreNo())
                            .and("orderUnitNo", itemDtl.getOrderUnitNo()).and("qtime", date).asMap());
            if(!CollectionUtils.isEmpty(activeLocks)){
                lockDtls.addAll(activeLocks);
            }
        }
    }

    //保存日志
    private void saveLog(InternetSystemLogsMessage logsMessage){
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(logsMessage));
    }

    //构造日志实体
    private InternetSystemLogsMessage buildLog(String opscode, String vstoreCode, String orderUnitNo, String storeNo, String remark){
        InternetSystemLogsMessage logsMessage =  new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                .setOpscode(opscode)
                .setSyscode(String.valueOf(MenuTypeEnums.VWS.getType()))
                .setSysname(MenuTypeEnums.VWS.getDesc())
                .setKeyword1(vstoreCode)
                .setKeyword1info("聚合仓编码:" + vstoreCode)
                .setKeyword2(orderUnitNo)
                .setKeyword2info("货管:" + orderUnitNo)
                .setRemark(String.format("%s：聚合仓编码:%s;货管:%s;机构:%s", remark, vstoreCode, orderUnitNo, storeNo))
                .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                .setCreateTime(new Date())
                .build();
        return logsMessage;
    }

}