/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.CompanyPartConfig;
import cn.wonhigh.baize.service.gms.ICompanyPartConfigService;
import  cn.wonhigh.baize.manager.gms.ICompanyPartConfigManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class CompanyPartConfigManager extends BaseManager<CompanyPartConfig,String> implements ICompanyPartConfigManager{
    @Autowired
    private ICompanyPartConfigService service;

    protected IService<CompanyPartConfig,String> getService(){
        return service;
    }
    	
	public List<CompanyPartConfig> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<CompanyPartConfig> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}
}