package cn.wonhigh.baize.events.dispatchrule;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.mercury.basic.UUID;
import cn.mercury.utils.DateUtil;
import cn.wonhigh.baize.manager.gms.IInternetDispatchRuleSetLogManager;
import cn.wonhigh.baize.manager.gms.impl.InternetDispatchRuleSetManager;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.LinkedList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DispatchRuleSetContextListener implements ApplicationListener<DispatchRuleSetEvent> {

    public static final Logger LOGGER = LoggerFactory.getLogger(DispatchRuleSetContextListener.class);

    @Autowired
    private IInternetDispatchRuleSetLogManager dispatchRuleSetLogManager;

    @Autowired
    private InternetDispatchRuleSetManager internetDispatchRuleSetManager;


    @Override
    public void onApplicationEvent(DispatchRuleSetEvent event) {
        Object o = event.getSource();
        if (!(o instanceof DispatchRuleSetMessage)) {
            return;
        }
        DispatchRuleSetMessage message = (DispatchRuleSetMessage) o;
        LOGGER.info("{}, 操作派单规则事件, 事件信息:{}",Thread.currentThread().getName(),  message.toString());

        InternetDispatchRuleSet ruleSet= internetDispatchRuleSetManager.findByPrimaryKey(message.getId());
        if (ruleSet == null) {
            return;
        }

        try {
            InternetDispatchRuleSetLog internetDispatchRuleSetLog = new InternetDispatchRuleSetLog();
            internetDispatchRuleSetLog.setId(UUID.gernerate());
            internetDispatchRuleSetLog.setOperationUser(message.getOptUser());
            internetDispatchRuleSetLog.setCreateTime(new Date());
            internetDispatchRuleSetLog.setOperationTime(DateUtil.parseDate(message.getOptTime()));

            // 这里需要和internet_dispatch_rule_set.platform_no保持一致
            internetDispatchRuleSetLog.setPlatformNo(ruleSet.getPlatformNo());
            internetDispatchRuleSetLog.setPlatformName(ruleSet.getPlatformName());

            internetDispatchRuleSetLog.setParentId(Math.toIntExact(message.getId()));
            internetDispatchRuleSetLog.setBrandName(ruleSet.getBrandName());
            internetDispatchRuleSetLog.setBrandNo(ruleSet.getBrandNo());
            internetDispatchRuleSetLog.setOrganTypeNo(ruleSet.getOrganTypeNo());
            internetDispatchRuleSetLog.setOrganTypeName(ruleSet.getOrganTypeName());

            internetDispatchRuleSetLog.setOpenRuleStr(String.join("-->", ObjectUtil.defaultIfNull(message.getBeforeRuleName(), new LinkedList<String>())));
            internetDispatchRuleSetLog.setCloseRuleStr(String.join("-->", ObjectUtil.defaultIfNull(message.getAfterRuleName(), new LinkedList<String>())));

            dispatchRuleSetLogManager.insert(internetDispatchRuleSetLog);
        } catch (Exception e) {
            LOGGER.error("记录派单操作日志错误,{}", e.getMessage(), e);
        }
    }
}

