/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;

import cn.mercury.manager.IManager;
import java.util.List;
import java.util.Map;

public interface IInventoryActiveLockDtlManager extends IManager<InventoryActiveLockDtl,String>{
    
    Integer batchInsert(List<InventoryActiveLockDtl> list);
    
    List<InventoryActiveLockDtl> selectByIds(List<Integer> ids);


    public int batchSaveOrUpdateDtl(List<InventoryActiveLockDtl> list);

    /**
     * @description 通过虚仓+机构+货管查询有效的锁库活动
     * @param
     */
    List<InventoryActiveLockDtl> selectByVstoreInfo(Map<String, Object> var1);

    /**
     * @description 按机构批量查询商品库存锁库
     * @param params
     * @return
     */
    List<InventoryActiveLockDtl> findBalanceLockQtyInOnStore(Map<String, Object> params);


    List<InventoryActiveLockDtlOccupied> selectByInventoryActiveLockDtlQuery(InventoryActiveLockDtlQuery query);

    /**
     * @description 按锁库调整单查询锁库活动
     * @param billNo
     * @param syncStatus
     * @return
     */
    List<InventoryActiveLockDtl> selectByAdjustBillNo(String billNo, Integer syncStatus);

    /**
     * 审核成功更新剩余锁库
     * @param updateLockDtlList
     */
	int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList);
}