/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
@Mapper
public interface OrderUnitPriorityConfigRepository extends IRepository<OrderUnitPriorityConfig,String> {
    
    Integer batchInsert(@Param("list") List<OrderUnitPriorityConfig> list);
    
    List<OrderUnitPriorityConfig> selectByIds(@Param("list") List<Integer> ids);
}