package cn.wonhigh.baize.manager.gms;


import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.dto.IcsInventoryOmsLockDto;
import cn.wonhigh.baize.model.entity.gms.IcsInventoryOmsLock;

import java.util.List;

/**
 * oms锁库(IcsInventoryOmsLock)
 *
 * <AUTHOR>
 * @since 2024-12-28 10:04:34
 */
public interface IIcsInventoryOmsLockManager extends IManager<IcsInventoryOmsLock, String> {
    Integer updateStock(List<IcsInventoryOmsLockDto> params);
}