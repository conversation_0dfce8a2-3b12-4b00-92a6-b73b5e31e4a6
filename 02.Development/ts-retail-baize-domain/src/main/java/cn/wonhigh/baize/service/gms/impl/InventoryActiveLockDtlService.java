/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.repository.gms.InventoryActiveLockDtlRepository;
import cn.wonhigh.baize.service.gms.IInventoryActiveLockDtlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class InventoryActiveLockDtlService extends BaseService<InventoryActiveLockDtl,String> implements IInventoryActiveLockDtlService {
    @Autowired
    private InventoryActiveLockDtlRepository repository;


    protected IRepository<InventoryActiveLockDtl,String> getRepository(){
        return repository;
    }
      
	public List<InventoryActiveLockDtl> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<InventoryActiveLockDtl> list) {
    	return repository.batchInsert(list);
    }

    @Override
    public List<Map<String, Object>> selectByBillNoQty(List<String> billNos) {
        return repository.selectByBillNoQty(billNos);
    }

    @Override
    public int batchSaveOrUpdateDtl(List<InventoryActiveLockDtl> list) {
        return repository.batchSaveOrUpdateDtl(list);
    }

    @Override
    public List<InventoryActiveLockDtl> selectByVstoreInfo(Map<String, Object> var1){
        return repository.selectByVstoreInfo(var1);
    }

    @Override
    public List<InventoryActiveLockDtl> findBalanceLockQtyInOnStore(Map<String, Object> params) {
        return repository.findBalanceLockQtyInOnStore(params);
    }

    @Override
    public void batchUpdateSyncStatus(List<InventoryActiveLockDtl> splitList, Integer syncStatus) {
        repository.batchUpdateSyncStatus(splitList, syncStatus);
    }

    @Override
    public void batchUpdateByBillNo(String billNo, Integer syncStatus) {
        repository.batchUpdateByBillNo(billNo, syncStatus);
    }

    @Override
    public List<InventoryActiveLockDtl> selectByAdjustBillNo(Map<String, Object> params) {
        return repository.selectByAdjustBillNo(params);
    }

    @Override
    public  List<Map<String, Object>> selectStatusByBillNo(String billNo) {
        return repository.selectStatusByBillNo(billNo);
    }

	@Override
	public int batchUpdateDtlForAudit(List<InventoryActiveLockDtl> updateLockDtlList) {
		return repository.batchUpdateDtlForAudit(updateLockDtlList);
	}

    @Override
    public List<InventoryActiveLockDtlOccupied> selectByInventoryActiveLockDtlQuery(InventoryActiveLockDtlQuery query) {
        return this.repository.selectByInventoryActiveLockDtlQuery(query);
    }
}