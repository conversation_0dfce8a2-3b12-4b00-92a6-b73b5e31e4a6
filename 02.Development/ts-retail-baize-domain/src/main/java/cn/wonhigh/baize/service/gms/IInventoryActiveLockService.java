/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import topmall.framework.service.IService;
import java.util.List;

public interface IInventoryActiveLockService extends IService<InventoryActiveLock,String>{
    
    InventoryActiveLock findByUnique(String billNo);

    Integer deleteByUnique(String billNo);

    List<InventoryActiveLock> selectByUniques(List<String> billNos);
      
    Integer batchInsert(List<InventoryActiveLock> list);
    
    List<InventoryActiveLock> selectByIds(List<Integer> ids);

}