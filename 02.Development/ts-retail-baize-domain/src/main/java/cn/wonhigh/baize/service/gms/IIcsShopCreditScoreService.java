/**  **/
package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.manager.IEntryResultHandler;
import cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore;
import topmall.framework.service.IService;
import java.util.List;
import java.util.Map;

public interface IIcsShopCreditScoreService extends IService<IcsShopCreditScore,String>{
      
    Integer batchInsert(List<IcsShopCreditScore> list);
    
    List<IcsShopCreditScore> selectByIds(List<Integer> ids);

    int selectShopCreditScoreByCount(Map<String, Object> params);

    List<IcsShopCreditScore> selectShopCreditScoreByPage(Map<String, Object> params, Pagenation page);

    void selectShopCreditScoreByParams(Map<String, Object> params, IEntryResultHandler<IcsShopCreditScore> handler);
}