/**  **/
package cn.wonhigh.baize.manager.gms;


import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.Company;

import java.util.List;

public interface ICompanyManager extends IManager<Company,Integer>{
    
    Company findByUnique(String companyNo) ;

    Integer deleteByUnique(String companyNo);

    List<Company> selectByUniques(List<String> companyNos);
    
    Integer batchInsert(List<Company> list);
    
    List<Company> selectByIds(List<Integer> ids);
}