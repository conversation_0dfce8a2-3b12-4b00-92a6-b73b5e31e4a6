package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.dto.IcsInventoryOmsLockDto;
import cn.wonhigh.baize.model.entity.gms.IcsInventoryOmsLock;
import cn.wonhigh.baize.repository.gms.IcsInventoryOmsLockRepository;
import cn.wonhigh.baize.service.gms.IIcsInventoryOmsLockService;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;
import java.util.List;

/**
 * oms锁库(IcsInventoryOmsLock)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-28 10:04:34
 */
@Service("icsInventoryOmsLockService")
public class IcsInventoryOmsLockService extends BaseService<IcsInventoryOmsLock, String> implements IIcsInventoryOmsLockService {
    @Resource
    private IcsInventoryOmsLockRepository repository;

    protected IRepository<IcsInventoryOmsLock, String> getRepository() {
        return repository;
    }


    @Override
    public Integer updateStock(List<IcsInventoryOmsLockDto> params) {
        return repository.updateStock(params);
    }
}