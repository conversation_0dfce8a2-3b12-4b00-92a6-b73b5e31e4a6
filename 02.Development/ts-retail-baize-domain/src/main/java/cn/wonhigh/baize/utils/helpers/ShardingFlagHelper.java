package cn.wonhigh.baize.utils.helpers;

import cn.wonhigh.baize.model.entity.gms.CompanyPartConfig;
import cn.wonhigh.baize.service.gms.ICompanyPartConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ShardingFlagHelper {

    @Resource
    private ICompanyPartConfigService companyPartConfigService;


    public String getShardingFlagByOrderUnitNo(String orderUnitNo) {
        return companyPartConfigService.getShardingFlagByOrderUnitNo(orderUnitNo);
    }


    public String getShardingFlagByStoreNo(String storeNo) {
        if (storeNo == null || storeNo.isEmpty()) {
            return null;
        }
        List<CompanyPartConfig> config = companyPartConfigService.getShardingFlagByStoreNo(storeNo);
        if (config == null || config.isEmpty()) {
            return null;
        }
        return  config.get(0).getPartitionNo();
    }
}
