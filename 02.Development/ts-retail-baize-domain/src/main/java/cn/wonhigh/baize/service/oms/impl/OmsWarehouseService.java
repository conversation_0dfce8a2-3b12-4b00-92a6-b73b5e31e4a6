package cn.wonhigh.baize.service.oms.impl;

import cn.wonhigh.baize.model.entity.oms.OmsWarehouse;
import cn.wonhigh.baize.repository.oms.OmsWarehouseRepository;
import cn.wonhigh.baize.service.oms.IOmsWarehouseService;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;

/**
 * 仓库(OmsWarehouse)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-30 11:10:03
 */
@Service("omsWarehouseService")
public class OmsWarehouseService extends BaseService<OmsWarehouse, Long> implements IOmsWarehouseService {
    @Resource
    private OmsWarehouseRepository repository;

    protected IRepository<OmsWarehouse, Long> getRepository() {
        return repository;
    }

}