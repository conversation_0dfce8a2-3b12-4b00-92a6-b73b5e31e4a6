/**  **/
package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import topmall.framework.service.IService;

import java.util.List;
import java.util.Map;

public interface IInternetVirtualWarehouseScopeService extends IService<InternetVirtualWarehouseScope,String>{


    List<InternetVirtualWarehouseScope> selectStoreByPage(Map<String, Object> var1, Pagenation var2);
    int selectStoreCount( Map<String, Object> var1);

    /**
     * 查询机构是否存在
     * @param params
     * @return
     */
    public List<InternetVirtualWarehouseScope> selectExistStore(Map<String, Object> params);

    /**
     * 查询虚仓下是否有所有店或者单店的范围
     * @param params
     * @return
     */
    public List<InternetVirtualWarehouseScope> selectExistShop(Map<String, Object> params);




    /**
     * 修改所有子仓库存类型
     * @param params
     * @return
     */
    public int updateAllScope(Map<String, Object> params);

    /**
     * 总仓删除所有店时，子仓删除
     * @param params
     * @return
     */
    public int deleteAllShopScope(Map<String, Object> params);

    /**
     * 总仓删除指定店或者仓时，子仓删除
     * @param params
     * @return
     */
    public int deleteStoreScope(Map<String, Object> params);

    /**
     * 查询机构货管信息
     * @param params
     * @return
     */
    List<InternetVirtualWarehouseScope> selectScopeInfo(Map<String, Object> params);


    List<InternetVirtualWarehouseScope> selectScopeListByPage(Map<String, Object> var1, Pagenation var2);
    int selectScopeListCount(Map<String, Object> var1);
}