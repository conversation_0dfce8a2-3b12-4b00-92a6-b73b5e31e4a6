/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.manager.IEntryResultHandler;
import cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.ResultHandler;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface IcsShopCreditScoreRepository extends IRepository<IcsShopCreditScore,String> {
    
    Integer batchInsert(@Param("list") List<IcsShopCreditScore> list);
    
    List<IcsShopCreditScore> selectByIds(@Param("list") List<Integer> ids);

    int selectShopCreditScoreByCount(@Param("params") Map<String, Object> params);

    List<IcsShopCreditScore> selectShopCreditScoreByPage(@Param("params") Map<String, Object> params, @Param("page") Pagenation page);

    void selectShopCreditScoreByParams(@Param("params") Map<String, Object> params, ResultHandler<IcsShopCreditScore> handler);
}