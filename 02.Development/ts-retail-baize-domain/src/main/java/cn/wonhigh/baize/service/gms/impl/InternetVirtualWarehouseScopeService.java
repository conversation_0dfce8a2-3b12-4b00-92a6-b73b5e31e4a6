/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import cn.wonhigh.baize.repository.gms.InternetVirtualWarehouseScopeRepository;
import cn.wonhigh.baize.service.gms.IInternetVirtualWarehouseScopeService;
import org.springframework.stereotype.Service;

import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class InternetVirtualWarehouseScopeService extends BaseService<InternetVirtualWarehouseScope,String> implements IInternetVirtualWarehouseScopeService {
    @Autowired
    private InternetVirtualWarehouseScopeRepository repository;

    protected IRepository<InternetVirtualWarehouseScope,String> getRepository(){
        return repository;
    }


    @Override
    public List<InternetVirtualWarehouseScope> selectStoreByPage(Map<String, Object> var1, Pagenation var2) {
        return repository.selectStoreByPage(var1,var2);
    }

    @Override
    public int selectStoreCount(Map<String, Object> var1) {
        return repository.selectStoreCount(var1);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectExistStore(Map<String, Object> params) {
        return repository.selectExistStore(params);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectExistShop(Map<String, Object> params) {
        return repository.selectExistShop(params);
    }


    @Override
    public int updateAllScope(Map<String, Object> params) {
        return repository.updateAllScope(params);
    }

    @Override
    public int deleteAllShopScope(Map<String, Object> params) {
        return repository.deleteAllShopScope(params);
    }

    @Override
    public int deleteStoreScope(Map<String, Object> params) {
        return repository.deleteStoreScope(params);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectScopeInfo(Map<String, Object> params){
        return repository.selectScopeInfo(params);
    }

    @Override
    public Integer insertBatch(List<InternetVirtualWarehouseScope> dataList) {
        return repository.insertBatch(dataList);
    }

    @Override
    public List<InternetVirtualWarehouseScope> selectScopeListByPage(Map<String, Object> var1, Pagenation var2) {
        return repository.selectScopeListByPage(var1, var2);
    }

    @Override
    public int selectScopeListCount(Map<String, Object> var1) {
        return repository.selectScopeListCount(var1);
    }
}