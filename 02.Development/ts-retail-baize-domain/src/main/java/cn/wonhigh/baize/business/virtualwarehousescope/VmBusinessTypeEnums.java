package cn.wonhigh.baize.business.virtualwarehousescope;

/**
 * 虚拟仓业务类型 1-网销 0-特殊
 */
public enum VmBusinessTypeEnums {
	INTERNET(1, "网销"),
	SPECIAL(0, "特殊")	;

	private int type;
	private String desc;

	public int getType() {
		return type;
	}

	public String getDesc() {
		return desc;
	}

	private VmBusinessTypeEnums(int type, String desc) {
		this.type = type;
		this.desc = desc;
	}

	public static String getTypeDesc(int type) {
		VmBusinessTypeEnums[] typeArr = VmBusinessTypeEnums.values();
		for (VmBusinessTypeEnums bte : typeArr) {
			if (bte.getType() == type) {
				return bte.getDesc();
			}
		}
		return String.valueOf(type);
	}
}
