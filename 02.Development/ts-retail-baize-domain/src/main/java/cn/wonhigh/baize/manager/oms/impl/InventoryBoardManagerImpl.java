package cn.wonhigh.baize.manager.oms.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.mercury.basic.query.*;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.manager.oms.IInventoryBoardManager;
import cn.wonhigh.baize.model.dto.WarehouseRatioDetailDto;
import cn.wonhigh.baize.model.dto.WarehouseRatioDto;
import cn.wonhigh.baize.model.dto.WarehouseRatioExportDto;
import cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import cn.wonhigh.baize.model.entity.oms.OmsStockUploadStrategy;
import cn.wonhigh.baize.model.entity.oms.OmsStore;
import cn.wonhigh.baize.model.entity.oms.OmsVirtualWarehouse;
import cn.wonhigh.baize.model.entity.oms.OmsWarehouse;
import cn.wonhigh.baize.service.gms.IIcsInventorySyncConfigService;
import cn.wonhigh.baize.service.gms.IOrderSourceTerminalConfigService;
import cn.wonhigh.baize.service.oms.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class InventoryBoardManagerImpl extends BaseManager<OmsWarehouse, Long> implements IInventoryBoardManager {

    public static final Logger logger = LoggerFactory.getLogger(InventoryBoardManagerImpl.class);

    @Autowired
    private IInventoryBoardService iInventoryBoardService;
    @Autowired
    private IIcsInventorySyncConfigService icsInventorySyncConfigService;
    @Autowired
    private IOrderSourceTerminalConfigService orderSourceTerminalConfigService;
    @Autowired
    private IOmsStoreService omsStoreService;
    @Autowired
    private IOmsStockUploadStrategyService omsStockUploadStrategyService;
    @Autowired
    private IOmsWarehouseService omsWarehouseService;
    @Autowired
    private IOmsVirtualWarehouseService omsVirtualWarehouseService;

    @Override
    public PageResult<WarehouseRatioDto> selectPageForWarehouseRatio(Query query, Pagenation page) {
        List<WarehouseRatioDto> list = Lists.newArrayList();
        Integer count = icsInventorySyncConfigService.selectByPageWithDistinctCount(query);
        if (count == 0) {

            return new PageResult<>(list, 0);
        }
        Map<String, Object> params = query.asMap();
        String brandCode = (String) params.get("brandCode");
        String warehouseCode = (String) params.get("warehouseCode");

        List<IcsInventorySyncConfig> icsInventorySyncConfigs = icsInventorySyncConfigService.selectByParams(Q.where("vstoreCode",warehouseCode));
       /* //渠道编码和虚仓编码一致的记录，不用再去OMS查询放大比例
        List<IcsInventorySyncConfig> matchList = icsInventorySyncConfigs.stream().filter(f -> Objects.equals(f.getChannelNo(), f.getVstoreCode())).collect(Collectors.toList());
        logger.info("[InventoryBoardManagerImpl]  渠道编码和虚仓编码一致的记录条数:{}", CollectionUtils.isEmpty(matchList) ? 0 : matchList.size());
        toMapWarehouseRatioDto(matchList, list,true);

        //渠道编码和虚仓编码不一致的记录，去OMS查询放大比例
        List<IcsInventorySyncConfig> noMatchList = icsInventorySyncConfigs
                .stream()
                .filter(f -> !Objects.equals(f.getChannelNo(), f.getVstoreCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(noMatchList)) {
            if(StringUtils.isNotBlank(brandCode)){
                return new PageResult<>(Lists.newArrayList(), 0);
            }
            return new PageResult<>(list, count);
        }*/

        //数据处理校验
        Result result = dataCollectAndCheck(icsInventorySyncConfigs);
        /*if(!result.checkResult){
            if(StringUtils.isNotBlank(brandCode)){
                return new PageResult<>(Lists.newArrayList(), 0);
            }
            toMapWarehouseRatioDto(icsInventorySyncConfigs, list, false);
            return new PageResult<>(list, count);
        }*/

        //step6 汇总所有上传策略
        List<Triple<String, IcsInventorySyncConfig,  List<Map<String, Object>>>> settingTripleList = Lists.newArrayList();
        List<OmsStockUploadStrategy> uploadStrategyList1 = omsStockUploadStrategyService.selectByParams(Q.where(Q.NotIn("is_auto_upload", 0)));

        icsInventorySyncConfigs.forEach(f -> {
            List<Map<String, Object>> settingList1 = Lists.newArrayList();
            //OMS类型查询是否有配置OMS上传策略
            if(1==f.getChannelType()){
                OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(f.getVstoreCode());
                if(Objects.nonNull(omsWarehouse)) {
                    List<OmsVirtualWarehouse> omsVirtualWarehouseList = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                    uploadStrategyList1.forEach(u->{
                        for (OmsVirtualWarehouse omsVirtualWarehouse : omsVirtualWarehouseList){
                            if(u.getSettingJson().contains(omsVirtualWarehouse.getVirtualWarehouseId().toString())){
                                settingList1.add(JsonUtils.fromJson(u.getSettingJson(), Map.class));
                            }
                        }
                    });
                }
            }
            settingTripleList.add(Triple.of(f.getVstoreCode(), f, settingList1));

  /*          String code = f.getVstoreCode();
            List<IcsInventorySyncConfig> sublist = result.vstoreCodeToMap.get(code);
            List<Map<String, Object>> settingList = Lists.newArrayList();
            sublist.forEach(fo -> {
                Optional.ofNullable(result.thirdPlatformToGroup.get(fo.getChannelNo()))
                        .ifPresent(orderSourceTerminalConfigList -> {
                            Optional<OrderSourceTerminalConfig> optional = orderSourceTerminalConfigList.stream().filter(o -> Objects.nonNull(result.storeCodeTOMap.get(o.getThirdChannelNo()))).findFirst();
                            if(optional.isPresent()){
                                OrderSourceTerminalConfig orderSourceTerminalConfig = optional.get();
                                OmsStore omsStore = result.storeCodeTOMap.get(orderSourceTerminalConfig.getThirdChannelNo());
                                Optional.ofNullable(omsStore).flatMap(os -> Optional.ofNullable(result.uploadStrategyGroup.get(os.getStoreId())))
                                        .ifPresent(l -> {
                                                    for (OmsStockUploadStrategy omsStockUploadStrategy : l) {
                                                        settingList.add(JsonUtils.fromJson(omsStockUploadStrategy.getSettingJson(), Map.class));
                                                    }
                                                }

                                        );

                            }
                        });
                settingTripleList.add(Triple.of(code, fo, settingList));

            });*/
        });
        //step7 仓库编码+渠道编码遍历统计
        Map<String,WarehouseRatioDto> resultMap = Maps.newHashMap();
        for (Triple<String, IcsInventorySyncConfig, List<Map<String, Object>>> node : settingTripleList) {
            List<Map<String, Object>> settingList = node.getRight();
            String vStoreCode = node.getLeft();
            IcsInventorySyncConfig syncConfig = node.getMiddle();
            //如果渠道类型是OMS，且没有配置OMS上传策略，则不参与放大比例计算
            if (1 == syncConfig.getChannelType() && CollectionUtils.isEmpty(settingList)) {
                continue;
            }

            if (StringUtils.isNotBlank(brandCode)) {
                countAmplifyRatioByBrandCode(settingList, brandCode, result, vStoreCode, resultMap, syncConfig);
            } else {
                countAmplifyRatioAllBrandCode(settingList, result, vStoreCode, resultMap, syncConfig);
            }
        }

        for (Map.Entry<String, WarehouseRatioDto> entry : resultMap.entrySet()){
            list.add(entry.getValue());
        }
        if(StringUtils.isNotBlank(brandCode)){
            list = list.stream().filter(f -> Objects.equals(f.getBrandCode(),brandCode)).collect(Collectors.toList());
        }
        list = list.stream().sorted(Comparator.comparing(WarehouseRatioDto::getWarehouseCode)).collect(Collectors.toList());
        long start = page.getStartRowNum();
        long end = page.getStartRowNum()+page.getPageSize();
        List<WarehouseRatioDto> finalResult = list.subList(Long.valueOf(start).intValue(), end > list.size() ? Long.valueOf(list.size()).intValue() : Long.valueOf(end).intValue());
        return new PageResult<>(finalResult, list.size());
    }

    private void countAmplifyRatioAllBrandCode(List<Map<String, Object>> settingList, Result result, String vStoreCode, Map<String, WarehouseRatioDto> resultMap, IcsInventorySyncConfig syncConfig) {
        if(CollectionUtils.isEmpty(settingList)){
            WarehouseRatioDto warehouseRatioDto = Objects.nonNull(resultMap.get(vStoreCode +"_"+" ")) ? resultMap.get(vStoreCode +"_"+" ") : new WarehouseRatioDto();
            warehouseRatioDto.setWarehouseCode(syncConfig.getVstoreCode());
            warehouseRatioDto.setWarehouseName(syncConfig.getVstoreName());
            warehouseRatioDto.setAmplifyRatioNum(Objects.isNull(warehouseRatioDto.getAmplifyRatioNum()) ? syncConfig.getSharingRatio() :
                    warehouseRatioDto.getAmplifyRatioNum() + syncConfig.getSharingRatio());
            resultMap.put(vStoreCode +"_"+" ", warehouseRatioDto);
            return;
        }
        for (Map<String, Object> setting : settingList) {
            String branCodesKey =  StringUtils.isBlank((String)setting.get("brandCodes"))?" ":(String)setting.get("brandCodes");
            OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(vStoreCode);

            if (Objects.nonNull(omsWarehouse)) {
                List<OmsVirtualWarehouse> omsVirtualWarehouse = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                List<Long> virtualWarehouseIds = omsVirtualWarehouse.stream().map(OmsVirtualWarehouse::getVirtualWarehouseId).collect(Collectors.toList());
                WarehouseRatioDto warehouseRatioDto = Objects.nonNull(resultMap.get(vStoreCode +"_"+branCodesKey)) ? resultMap.get(vStoreCode +"_"+branCodesKey) : new WarehouseRatioDto();
                warehouseRatioDto.setWarehouseCode(syncConfig.getVstoreCode());
                warehouseRatioDto.setWarehouseName(syncConfig.getVstoreName());
                warehouseRatioDto.setBrandCode((String) setting.get("brandCodes"));
                warehouseRatioDto.setBrandName((String) setting.get("brandNames"));
                List<Map<String, Object>> warehouses = (List) setting.get("warehouses");
                Optional<Map<String, Object>> warehouseOptional = warehouses.stream().filter(v -> virtualWarehouseIds.contains(Long.valueOf((String)v.get("virtualWarehouseId")))).findFirst();
                warehouseOptional.ifPresent(o -> {
                    Integer ratio = (syncConfig.getSharingRatio() * (Integer) o.get("ratio")) / 100;
                    warehouseRatioDto.setAmplifyRatioNum(
                            Objects.isNull(warehouseRatioDto.getAmplifyRatioNum()) ? ratio :
                                    warehouseRatioDto.getAmplifyRatioNum() + ratio

                    );
                });
                if(Objects.isNull(warehouseRatioDto.getAmplifyRatioNum())){
                    warehouseRatioDto.setAmplifyRatioNum(syncConfig.getSharingRatio());
                }
                resultMap.put(vStoreCode +"_"+branCodesKey, warehouseRatioDto);
            }
        }
    }

    private void countDetailAmplifyRatioAllBrandCode(List<Map<String, Object>> settingList, Result result, String vStoreCode, Map<String, WarehouseRatioDetailDto> resultMap, IcsInventorySyncConfig syncConfig) {
        if(CollectionUtils.isEmpty(settingList)){
            WarehouseRatioDetailDto warehouseRatioDetailDto = new WarehouseRatioDetailDto();
            warehouseRatioDetailDto.setShopCode(syncConfig.getChannelNo());
            warehouseRatioDetailDto.setShopName(syncConfig.getChannelName());
            warehouseRatioDetailDto.setPlatformAmplifyRatioNum(syncConfig.getSharingRatio());
            warehouseRatioDetailDto.setShopAmplifyRatioNum(syncConfig.getSharingRatio());
            resultMap.put(vStoreCode + "_" + syncConfig.getChannelNo(), warehouseRatioDetailDto);
            return;
        }
        OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(vStoreCode);
        if(Objects.nonNull(omsWarehouse)) {
            for (Map<String, Object> setting : settingList) {
                if (Objects.equals("", setting.get("brandNames"))) {
                    List<OmsVirtualWarehouse> omsVirtualWarehouse = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                    List<Long> virtualWarehouseIds = omsVirtualWarehouse.stream().map(OmsVirtualWarehouse::getVirtualWarehouseId).collect(Collectors.toList());
                    WarehouseRatioDetailDto warehouseRatioDetailDto = new WarehouseRatioDetailDto();
                    List<OmsStore> omsStoreList = omsStoreService.selectByParams(Q.where("storeId", setting.get("storeId")));
                    warehouseRatioDetailDto.setShopCode(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreCode():"");
                    warehouseRatioDetailDto.setShopName(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreName():"");
                    warehouseRatioDetailDto.setPlatformAmplifyRatioNum(syncConfig.getSharingRatio());
                    warehouseRatioDetailDto.setBrandName((String) setting.get("brandNames"));

                    List<Map<String, Object>> warehouses = (List) setting.get("warehouses");
                    Optional<Map<String, Object>> warehouseOptional = warehouses.stream().filter(v -> virtualWarehouseIds.contains(Long.valueOf((String)v.get("virtualWarehouseId")))).findFirst();
                    warehouseOptional.ifPresent(o -> {
                        warehouseRatioDetailDto.setOmsAmplifyRatioNum((Integer) o.get("ratio"));
                        Integer ratio = Objects.isNull(o.get("ratio")) ? syncConfig.getSharingRatio() : (syncConfig.getSharingRatio() * (Integer) o.get("ratio")) / 100;
                        warehouseRatioDetailDto.setShopAmplifyRatioNum(ratio);
                    });
                    if(Objects.isNull(warehouseRatioDetailDto.getShopAmplifyRatioNum())){
                        warehouseRatioDetailDto.setShopAmplifyRatioNum(syncConfig.getSharingRatio());
                    }
                    resultMap.put(vStoreCode + "_" + warehouseRatioDetailDto.getShopCode(), warehouseRatioDetailDto);
                }
            }
        }
    }

    private void countAmplifyRatioByBrandCode(List<Map<String, Object>> settingList, String brandCode, Result result, String vStoreCode, Map<String, WarehouseRatioDto> resultMap, IcsInventorySyncConfig syncConfig) {
        List<Map<String, Object>> brandCodeToSettingList = settingList.stream().filter(f -> Objects.equals(f.get("brandCodes"), brandCode)).collect(Collectors.toList());
        for (Map<String, Object> setting : brandCodeToSettingList){
            OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(vStoreCode);
            if (Objects.nonNull(omsWarehouse)) {
                List<OmsVirtualWarehouse> omsVirtualWarehouse = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                List<Long> virtualWarehouseIds = omsVirtualWarehouse.stream().map(OmsVirtualWarehouse::getVirtualWarehouseId).collect(Collectors.toList());
                WarehouseRatioDto warehouseRatioDto = Objects.nonNull(resultMap.get(vStoreCode+"_"+setting.get("brandCodes")))? resultMap.get(vStoreCode+"_"+setting.get("brandCodes")):new WarehouseRatioDto();
                warehouseRatioDto.setWarehouseCode(syncConfig.getVstoreCode());
                warehouseRatioDto.setWarehouseName(syncConfig.getVstoreName());
                warehouseRatioDto.setBrandCode((String) setting.get("brandCodes"));
                warehouseRatioDto.setBrandName((String) setting.get("brandNames"));

                    List<Map<String, Object>> warehouses = (List) setting.get("warehouses");
                    Optional<Map<String, Object>> warehouseOptional = warehouses.stream().filter(v -> virtualWarehouseIds.contains(Long.valueOf((String)v.get("virtualWarehouseId")))).findFirst();
                    warehouseOptional.ifPresent(o -> {
                        Integer ratio = (syncConfig.getSharingRatio() * (Integer) o.get("ratio")) / 100;
                        warehouseRatioDto.setAmplifyRatioNum(
                                Objects.isNull(warehouseRatioDto.getAmplifyRatioNum()) ? ratio :
                                        warehouseRatioDto.getAmplifyRatioNum() + ratio

                        );
                    });
                if(Objects.isNull(warehouseRatioDto.getAmplifyRatioNum())){
                    warehouseRatioDto.setAmplifyRatioNum(syncConfig.getSharingRatio());
                }
                resultMap.put(vStoreCode+"_"+setting.get("brandCodes"), warehouseRatioDto);
            }
        }
    }

    private void countDetailAmplifyRatioByBrandCode(List<Map<String, Object>> settingList, String brandCode, Result result, String vStoreCode, Map<String, WarehouseRatioDetailDto> resultMap, IcsInventorySyncConfig syncConfig) {
        List<Map<String, Object>> brandCodeToSettingList = settingList.stream().filter(f -> Objects.equals(f.get("brandCodes"), brandCode)).collect(Collectors.toList());
        for (Map<String, Object> setting : brandCodeToSettingList){
            OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(vStoreCode);
            if (Objects.nonNull(omsWarehouse)) {
                List<OmsVirtualWarehouse> omsVirtualWarehouse = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                List<Long> virtualWarehouseIds = omsVirtualWarehouse.stream().map(OmsVirtualWarehouse::getVirtualWarehouseId).collect(Collectors.toList());
                WarehouseRatioDetailDto warehouseRatioDetailDto = new WarehouseRatioDetailDto();
                List<OmsStore> omsStoreList = omsStoreService.selectByParams(Q.where("storeId", setting.get("storeId")));
                warehouseRatioDetailDto.setShopCode(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreCode():"");
                warehouseRatioDetailDto.setShopName(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreName():"");
                warehouseRatioDetailDto.setPlatformAmplifyRatioNum(syncConfig.getSharingRatio());
                warehouseRatioDetailDto.setBrandName((String) setting.get("brandNames"));

                List<Map<String, Object>> warehouses = (List) setting.get("warehouses");
                Optional<Map<String, Object>> warehouseOptional = warehouses.stream().filter(v ->  virtualWarehouseIds.contains(Long.valueOf((String)v.get("virtualWarehouseId")))).findFirst();
                warehouseOptional.ifPresent(o -> {
                    warehouseRatioDetailDto.setOmsAmplifyRatioNum((Integer) o.get("ratio"));
                    Integer ratio = Objects.isNull(o.get("ratio"))?syncConfig.getSharingRatio():(syncConfig.getSharingRatio() * (Integer) o.get("ratio")) / 100;
                    warehouseRatioDetailDto.setShopAmplifyRatioNum(ratio);
                });
                if(Objects.isNull(warehouseRatioDetailDto.getShopAmplifyRatioNum())){
                    warehouseRatioDetailDto.setShopAmplifyRatioNum(syncConfig.getSharingRatio());
                }
                resultMap.put(vStoreCode +"_"+warehouseRatioDetailDto.getShopCode(), warehouseRatioDetailDto);
            }
        }

    }

    private void countExportAmplifyRatioByBrandCode(List<Map<String, Object>> settingList, String brandCode, Result result, String vStoreCode, Map<String, WarehouseRatioExportDto> resultMap, IcsInventorySyncConfig syncConfig) {
        List<Map<String, Object>> brandCodeToSettingList = settingList.stream().filter(f -> Objects.equals(f.get("brandCodes"), brandCode)).collect(Collectors.toList());
        for (Map<String, Object> setting : brandCodeToSettingList){
            OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(vStoreCode);
            if (Objects.nonNull(omsWarehouse)) {
                List<OmsVirtualWarehouse> omsVirtualWarehouse = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                List<Long> virtualWarehouseIds = omsVirtualWarehouse.stream().map(v -> v.getVirtualWarehouseId()).collect(Collectors.toList());
                WarehouseRatioExportDto warehouseRatioExportDto = new WarehouseRatioExportDto();
                List<OmsStore> omsStoreList = omsStoreService.selectByParams(Q.where("storeId", setting.get("storeId")));
                warehouseRatioExportDto.setWarehouseCode(syncConfig.getVstoreCode());
                warehouseRatioExportDto.setWarehouseName(syncConfig.getVstoreName());
                warehouseRatioExportDto.setShopCode(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreCode():"");
                warehouseRatioExportDto.setShopName(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreName():"");
                warehouseRatioExportDto.setPlatformAmplifyRatio(Objects.nonNull(syncConfig.getSharingRatio())?syncConfig.getSharingRatio().toString()+"%":"");
                warehouseRatioExportDto.setBrandName((String) setting.get("brandNames"));
                warehouseRatioExportDto.setBrandCode((String) setting.get("brandCodes"));

                List<Map<String, Object>> warehouses = (List) setting.get("warehouses");
                Optional<Map<String, Object>> warehouseOptional = warehouses.stream().filter(v ->virtualWarehouseIds.contains(Long.valueOf((String)v.get("virtualWarehouseId")))).findFirst();
                warehouseOptional.ifPresent(o -> {
                    warehouseRatioExportDto.setOmsAmplifyRatio(Objects.nonNull(o.get("ratio"))?o.get("ratio")+"%":"");
                    Integer ratio = Objects.isNull(o.get("ratio"))?syncConfig.getSharingRatio():(syncConfig.getSharingRatio() * (Integer) o.get("ratio")) / 100;
                    warehouseRatioExportDto.setShopAmplifyRatio(ratio+"%");
                });
                if(Objects.isNull(warehouseRatioExportDto.getShopAmplifyRatio())){
                    warehouseRatioExportDto.setShopAmplifyRatio(warehouseRatioExportDto.getPlatformAmplifyRatio());
                }
                resultMap.put(vStoreCode +"_"+warehouseRatioExportDto.getShopCode(), warehouseRatioExportDto);
            }
        }
    }

    private void countExportAmplifyRatioAllBrandCode(List<Map<String, Object>> settingList, Result result, String vStoreCode, Map<String, WarehouseRatioExportDto> resultMap, IcsInventorySyncConfig syncConfig) {
        if(CollectionUtils.isEmpty(settingList)){
            WarehouseRatioExportDto warehouseRatioExportDto = Objects.nonNull(resultMap.get(vStoreCode +syncConfig.getChannelNo()+"_"+" ")) ? resultMap.get(vStoreCode +syncConfig.getChannelNo()+"_"+" ") : new WarehouseRatioExportDto();
            warehouseRatioExportDto.setWarehouseCode(syncConfig.getVstoreCode());
            warehouseRatioExportDto.setWarehouseName(syncConfig.getVstoreName());
            warehouseRatioExportDto.setShopCode(syncConfig.getChannelNo());
            warehouseRatioExportDto.setShopName(syncConfig.getChannelName());
            warehouseRatioExportDto.setPlatformAmplifyRatio(Objects.nonNull(syncConfig.getSharingRatio())?syncConfig.getSharingRatio().toString()+"%":" ");
            warehouseRatioExportDto.setShopAmplifyRatio(Objects.nonNull(syncConfig.getSharingRatio())?syncConfig.getSharingRatio().toString()+"%":" ");
            resultMap.put(vStoreCode + syncConfig.getChannelNo()+"_"+" ", warehouseRatioExportDto);
            return;
        }
        for (Map<String, Object> setting : settingList) {
            String branCodesKey =  StringUtils.isBlank((String)setting.get("brandCodes"))?" ":(String)setting.get("brandCodes");
            OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(vStoreCode);

            if (Objects.nonNull(omsWarehouse)) {
                List<OmsVirtualWarehouse> omsVirtualWarehouse = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                List<Long> virtualWarehouseIds = omsVirtualWarehouse.stream().map(v -> v.getVirtualWarehouseId()).collect(Collectors.toList());
                WarehouseRatioExportDto warehouseRatioExportDto = Objects.nonNull(resultMap.get(vStoreCode +syncConfig.getChannelNo()+"_"+branCodesKey)) ? resultMap.get(vStoreCode +syncConfig.getChannelNo()+"_"+branCodesKey) : new WarehouseRatioExportDto();
                warehouseRatioExportDto.setWarehouseCode(syncConfig.getVstoreCode());
                warehouseRatioExportDto.setWarehouseName(syncConfig.getVstoreName());
                List<OmsStore> omsStoreList = omsStoreService.selectByParams(Q.where("storeId", setting.get("storeId")));
                warehouseRatioExportDto.setShopCode(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreCode():"");
                warehouseRatioExportDto.setShopName(CollectionUtils.isNotEmpty(omsStoreList)?omsStoreList.get(0).getStoreName():"");
                warehouseRatioExportDto.setPlatformAmplifyRatio(Objects.nonNull(syncConfig.getSharingRatio())?syncConfig.getSharingRatio().toString()+"%":" ");
                warehouseRatioExportDto.setShopAmplifyRatio(Objects.nonNull(syncConfig.getSharingRatio())?syncConfig.getSharingRatio().toString()+"%":" ");
                warehouseRatioExportDto.setBrandCode((String) setting.get("brandCodes"));
                warehouseRatioExportDto.setBrandName((String) setting.get("brandNames"));
                List<Map<String, Object>> warehouses = (List) setting.get("warehouses");
                Optional<Map<String, Object>> warehouseOptional = warehouses.stream().filter(v -> virtualWarehouseIds.contains(Long.valueOf((String)v.get("virtualWarehouseId")))).findFirst();
                warehouseOptional.ifPresent(o -> {
                    warehouseRatioExportDto.setOmsAmplifyRatio(Objects.nonNull(o.get("ratio"))?o.get("ratio")+"%":" ");
                    Integer ratio = (syncConfig.getSharingRatio() * (Integer) o.get("ratio")) / 100;
                    warehouseRatioExportDto.setShopAmplifyRatio(ratio+"%");

                });
                resultMap.put(vStoreCode + warehouseRatioExportDto.getShopCode()+"_"+branCodesKey, warehouseRatioExportDto);
            }
        }
    }

    private static class Result {
        final Map<String, List<IcsInventorySyncConfig>> vstoreCodeToMap;
        final Map<String, List<OrderSourceTerminalConfig>> thirdPlatformToGroup;
        final Map<String, OmsStore> storeCodeTOMap;
        final Map<Long, List<OmsStockUploadStrategy>> uploadStrategyGroup;
        final Map<String, OmsWarehouse> warehouseCodeToMap;
        final Map<Long, List<OmsVirtualWarehouse>> omsVirtualWarehouseMap;
        final Boolean checkResult;

        public Result(Map<String, List<IcsInventorySyncConfig>> vstoreCodeToMap, Map<String, List<OrderSourceTerminalConfig>> thirdPlatformToMap, Map<String, OmsStore> storeCodeTOMap, Map<Long, List<OmsStockUploadStrategy>> uploadStrategyGroup, Map<String, OmsWarehouse> warehouseCodeToMap, Map<Long, List<OmsVirtualWarehouse>> omsVirtualWarehouseMap, Boolean checkResult) {
            this.vstoreCodeToMap = vstoreCodeToMap;
            this.thirdPlatformToGroup = thirdPlatformToMap;
            this.storeCodeTOMap = storeCodeTOMap;
            this.uploadStrategyGroup = uploadStrategyGroup;
            this.warehouseCodeToMap = warehouseCodeToMap;
            this.omsVirtualWarehouseMap = omsVirtualWarehouseMap;
            this.checkResult = checkResult;
        }

        public Result(Map<String, OmsWarehouse> warehouseCodeToMap, Map<Long, List<OmsVirtualWarehouse>> omsVirtualWarehouseMap, Boolean checkResult) {
            this.vstoreCodeToMap = null;
            this.thirdPlatformToGroup = null;
            this.storeCodeTOMap = null;
            this.uploadStrategyGroup = null;
            this.warehouseCodeToMap = warehouseCodeToMap;
            this.omsVirtualWarehouseMap = omsVirtualWarehouseMap;
            this.checkResult = checkResult;
        }

        public Result(Boolean checkResult) {
            this.omsVirtualWarehouseMap = null;
            this.vstoreCodeToMap = null;
            this.thirdPlatformToGroup = null;
            this.storeCodeTOMap = null;
            this.uploadStrategyGroup = null;
            this.warehouseCodeToMap = null;
            this.checkResult = checkResult;
        }
    }


    private Result dataCollectAndCheck(List<IcsInventorySyncConfig> icsInventorySyncConfigs){
        List<String> vstoreCode = icsInventorySyncConfigs.stream()
                .map(IcsInventorySyncConfig::getVstoreCode)
                .collect(Collectors.toList());

/*        List<IcsInventorySyncConfig> inventorySyncConfigs = icsInventorySyncConfigService.selectByParams(Q.where(Q.In("vstoreCode", vstoreCode)));
        logger.info("[InventoryBoardManagerImpl]  虚仓编码查询所有与其不一致的的渠道编码条数:{}", CollectionUtils.isEmpty(inventorySyncConfigs) ? 0 : inventorySyncConfigs.size());

        Map<String, List<IcsInventorySyncConfig>> vstoreCodeToMap = inventorySyncConfigs.stream().collect(Collectors.groupingBy(IcsInventorySyncConfig::getVstoreCode));

        List<String> channelNoList = inventorySyncConfigs.stream()
                .filter(f -> !Objects.equals(f.getChannelNo(), f.getVstoreCode()))
                .map(IcsInventorySyncConfig::getChannelNo)
                .distinct()
                .collect(Collectors.toList());

        //step2 根据渠道编码查询出对应的三级渠道
        List<OrderSourceTerminalConfig> thirdPlatform = orderSourceTerminalConfigService.selectByParams(Q.where(Q.In("thirdPlatform", channelNoList)));
        logger.info("[InventoryBoardManagerImpl]  渠道编码查询出对应的三级渠道条数:{}", CollectionUtils.isEmpty(thirdPlatform) ? 0 : thirdPlatform.size());

        if (CollectionUtils.isEmpty(thirdPlatform)) {
           return new Result(false);
        }

        Map<String, List<OrderSourceTerminalConfig>> thirdPlatformToGroup = thirdPlatform.stream().collect(Collectors.groupingBy(OrderSourceTerminalConfig::getThirdPlatform));

        List<String> thirdChannelNoList = thirdPlatform.stream().map(OrderSourceTerminalConfig::getThirdChannelNo).collect(Collectors.toList());

        //step3 根据三级渠道编码去oms查询对应的店铺
        List<OmsStore> omsStoreList = omsStoreService.selectByParams(Q.where(Q.In("storeCode", thirdChannelNoList)));
        logger.info("[InventoryBoardManagerImpl]  根据三级渠道编码去oms查询对应的店铺条数:{}", CollectionUtils.isEmpty(omsStoreList) ? 0 : omsStoreList.size());

        if (CollectionUtils.isEmpty(omsStoreList)) {
            return new Result(false);
        }

        Map<String, OmsStore> storeCodeTOMap = omsStoreList.stream().collect(Collectors.toMap(OmsStore::getStoreCode, v -> v));

        List<Long> storeIdList = omsStoreList.stream().map(OmsStore::getStoreId).collect(Collectors.toList());

        //step4 根据店铺去oms查询对应的上传策略
        List<OmsStockUploadStrategy> uploadStrategyList = omsStockUploadStrategyService.selectByParams(Q.where(Q.In("storeId", storeIdList)));
        logger.info("[InventoryBoardManagerImpl]  根据店铺去oms查询对应的上传策略条数:{}", CollectionUtils.isEmpty(uploadStrategyList) ? 0 : uploadStrategyList.size());

        if (CollectionUtils.isEmpty(uploadStrategyList)) {
            return new Result(false);
        }

        Map<Long, List<OmsStockUploadStrategy>> uploadStrategyGroup = uploadStrategyList.stream().collect(Collectors.groupingBy(OmsStockUploadStrategy::getStoreId));*/

        //step5 根据虚仓编码查询对应的仓库
        List<OmsWarehouse> omsWarehouseList = omsWarehouseService.selectByParams(Q.where(Q.In("warehouseCode", vstoreCode)));
        logger.info("[InventoryBoardManagerImpl]  根据虚仓编码查询对应的仓库条数:{}", CollectionUtils.isEmpty(omsWarehouseList) ? 0 : omsWarehouseList.size());

        if (CollectionUtils.isEmpty(omsWarehouseList)) {
            return new Result(Maps.newHashMap(),Maps.newHashMap(),false);
        }

        List<Long> warehouseIdList = omsWarehouseList.stream().map(OmsWarehouse::getWarehouseId).collect(Collectors.toList());
        List<OmsVirtualWarehouse> omsVirtualWarehouses = omsVirtualWarehouseService.selectByParams(Q.where(Q.In("warehouseId", warehouseIdList)));
        logger.info("[InventoryBoardManagerImpl]  根据物理仓id查询对应的虚仓条数:{}", CollectionUtils.isEmpty(omsVirtualWarehouses) ? 0 : omsVirtualWarehouses.size());

        if (CollectionUtils.isEmpty(omsVirtualWarehouses)) {
            return new Result(Maps.newHashMap(),Maps.newHashMap(),false);
        }

        Map<String, OmsWarehouse> warehouseCodeToMap = omsWarehouseList.stream().collect(Collectors.toMap(OmsWarehouse::getWarehouseCode, v -> v));
        Map<Long, List<OmsVirtualWarehouse>> omsVirtualWarehouseToGroup = omsVirtualWarehouses.stream().collect(Collectors.groupingBy(OmsVirtualWarehouse::getWarehouseId));

        return new Result(warehouseCodeToMap,omsVirtualWarehouseToGroup , true);
    }

    @Override
    public PageResult<WarehouseRatioDetailDto> loadWarehouseRatioByWarehouseCode(Query query, Pagenation page) {
        if (Objects.isNull(query) || query.asMap().isEmpty()) {
            return new PageResult<>(Collections.EMPTY_LIST, 0);
        }
        List<WarehouseRatioDetailDto> list = Lists.newArrayList();
        Integer count = icsInventorySyncConfigService.selectCount(query);
        if (count == 0) {
            return new PageResult<>(list, 0);
        }

        List<IcsInventorySyncConfig> icsInventorySyncConfigs = icsInventorySyncConfigService.selectByParams(query);
        logger.info("[InventoryBoardManagerImpl] [loadWarehouseRatioByWarehouseCode] 虚仓编码查询所有的渠道编码条数:{}", CollectionUtils.isEmpty(icsInventorySyncConfigs) ? 0 : icsInventorySyncConfigs.size());

        //过滤渠道编码和虚仓编码不一致的记录
        //icsInventorySyncConfigs = icsInventorySyncConfigs.stream().filter(f -> !Objects.equals(f.getChannelNo(), f.getVstoreCode())).collect(Collectors.toList());
        /*if(CollectionUtils.isNotEmpty(matchList)){
            //toMapWarehouseRatioDetailDto(icsInventorySyncConfigs, list);
            return new PageResult<>(list, 0);
        }*/

        Map<String, Object> queryMap = query.asMap();
        String brandCode = (String)queryMap.get("brandCode");

        Result result = dataCollectAndCheck(icsInventorySyncConfigs);
/*        if(!result.checkResult){
            toMapWarehouseRatioDetailDto(icsInventorySyncConfigs, list);
            return new PageResult<>(list, count);
        }*/

        //step6 汇总所有上传策略
        List<Triple<String, IcsInventorySyncConfig,  List<Map<String, Object>>>> settingTripleList = Lists.newArrayList();
        List<OmsStockUploadStrategy> uploadStrategyList1 = omsStockUploadStrategyService.selectByParams(Q.where(Q.NotIn("is_auto_upload", 0)));
        icsInventorySyncConfigs.forEach(f -> {
            List<Map<String, Object>> settingList1 = Lists.newArrayList();
            //OMS类型查询是否有配置OMS上传策略
            if(1==f.getChannelType()) {
                OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(f.getVstoreCode());
                if (Objects.nonNull(omsWarehouse)) {
                    List<OmsVirtualWarehouse> omsVirtualWarehouseList = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                    uploadStrategyList1.forEach(u -> {
                        for (OmsVirtualWarehouse omsVirtualWarehouse : omsVirtualWarehouseList) {
                            if (u.getSettingJson().contains(omsVirtualWarehouse.getVirtualWarehouseId().toString())) {
                                Map map = JsonUtils.fromJson(u.getSettingJson(), Map.class);
                                map.put("storeId",u.getStoreId());
                                map.put("storeName",u.getStoreName());
                                settingList1.add(map);
                            }
                        }
                    });
                }
            }
            settingTripleList.add(Triple.of(f.getVstoreCode(), f, settingList1));

           /* String code = f.getVstoreCode();
            List<IcsInventorySyncConfig> sublist = result.vstoreCodeToMap.get(code);
            List<Map<String, Object>> settingList = Lists.newArrayList();
            sublist.forEach(fo -> {
                Optional.ofNullable(result.thirdPlatformToGroup.get(fo.getChannelNo()))
                        .ifPresent(orderSourceTerminalConfigList -> {
                            Optional<OrderSourceTerminalConfig> optional = orderSourceTerminalConfigList.stream().filter(o -> Objects.nonNull(result.storeCodeTOMap.get(o.getThirdChannelNo()))).findFirst();
                            if(optional.isPresent()){
                                OrderSourceTerminalConfig orderSourceTerminalConfig = optional.get();
                                OmsStore omsStore = result.storeCodeTOMap.get(orderSourceTerminalConfig.getThirdChannelNo());
                                Optional.ofNullable(omsStore).flatMap(os -> Optional.ofNullable(result.uploadStrategyGroup.get(os.getStoreId())))
                                        .ifPresent(l -> {
                                                    for (OmsStockUploadStrategy omsStockUploadStrategy : l) {
                                                        settingList.add(JsonUtils.fromJson(omsStockUploadStrategy.getSettingJson(), Map.class));
                                                    }
                                                }

                                        );

                            }
                        });
                settingTripleList.add(Triple.of(code, fo, settingList));

            });*/
        });
        //step7 仓库编码+渠道编码遍历统计
        Map<String, WarehouseRatioDetailDto> resultMap = Maps.newHashMap();
        for (Triple<String, IcsInventorySyncConfig, List<Map<String, Object>>> node : settingTripleList) {
            List<Map<String, Object>> settingList = node.getRight();
            String vStoreCode = node.getLeft();
            IcsInventorySyncConfig syncConfig = node.getMiddle();

            if (1 == syncConfig.getChannelType() && CollectionUtils.isEmpty(settingList)) {
                continue;
            }

            if (StringUtils.isNotBlank(brandCode)) {
                countDetailAmplifyRatioByBrandCode(settingList, brandCode, result, vStoreCode, resultMap, syncConfig);
            } else {
                countDetailAmplifyRatioAllBrandCode(settingList, result, vStoreCode, resultMap, syncConfig);
            }
        }

        for (Map.Entry<String, WarehouseRatioDetailDto> entry : resultMap.entrySet()){
            list.add(entry.getValue());
        }
        list = list.stream().sorted(Comparator.comparing(WarehouseRatioDetailDto::getShopCode)).collect(Collectors.toList());
        long start = page.getStartRowNum();
        long end = page.getStartRowNum()+page.getPageSize();
        List<WarehouseRatioDetailDto> finalResult = list.subList(Long.valueOf(start).intValue(), end > list.size() ? Long.valueOf(list.size()).intValue() : Long.valueOf(end).intValue());
        return new PageResult<>(finalResult, list.size());
    }

    @Override
    public List<WarehouseRatioExportDto> selectWarehouseRatioByWarehouseCode(List<String> warehouseCodes,List<String> brandCode) {
        List<WarehouseRatioExportDto> exportDtos = Lists.newArrayList();
        Map<String, String> warehouseToBarandCodeMap = Maps.newHashMap();
        for (int i = 0; i <warehouseCodes.size(); i++) {
            warehouseToBarandCodeMap.put(warehouseCodes.get(i),brandCode.get(i));
        }
        Query query = new Query();
        if (CollectionUtil.isNotEmpty(warehouseCodes))
            query.and(Q.In("vstoreCode", warehouseCodes));

        List<IcsInventorySyncConfig> icsInventorySyncConfigs = icsInventorySyncConfigService.selectByParams(query);
        logger.info("[InventoryBoardManagerImpl] [selectWarehouseRatioByWarehouseCode] 虚仓编码查询所有的渠道编码条数:{}", CollectionUtils.isEmpty(icsInventorySyncConfigs) ? 0 : icsInventorySyncConfigs.size());

        //渠道编码和虚仓编码一致的记录，不用再去OMS查询放大比例
        /*List<IcsInventorySyncConfig> matchList = icsInventorySyncConfigs.stream().filter(f -> Objects.equals(f.getChannelNo(), f.getVstoreCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(matchList)) {
            exportDtos.addAll(buildWarehouseRatioExportDto(matchList,true));
        }*/

        //渠道编码和虚仓编码不一致的记录，去OMS查询放大比例
       /* List<IcsInventorySyncConfig> noMatchList = icsInventorySyncConfigs
                .stream()
                .filter(f -> !Objects.equals(f.getChannelNo(), f.getVstoreCode()))
                .collect(Collectors.toList());*/


        /*if(CollectionUtils.isEmpty(noMatchList)){
            return exportDtos;
        }*/
        Result result = dataCollectAndCheck(icsInventorySyncConfigs);
        /*if(!result.checkResult){
            exportDtos.addAll(buildWarehouseRatioExportDto(noMatchList, false));
            return exportDtos;
        }*/

        //step6 汇总所有上传策略
        List<Triple<String, IcsInventorySyncConfig,  List<Map<String, Object>>>> settingTripleList = Lists.newArrayList();
        List<OmsStockUploadStrategy> uploadStrategyList1 = omsStockUploadStrategyService.selectByParams(Q.where(Q.NotIn("is_auto_upload", 0)));
        icsInventorySyncConfigs.forEach(f -> {
                List<Map<String, Object>> settingList1 = Lists.newArrayList();
                //OMS类型查询是否有配置OMS上传策略
                if(1==f.getChannelType()) {
                    OmsWarehouse omsWarehouse = result.warehouseCodeToMap.get(f.getVstoreCode());
                    if (Objects.nonNull(omsWarehouse)) {
                        List<OmsVirtualWarehouse> omsVirtualWarehouseList = result.omsVirtualWarehouseMap.get(omsWarehouse.getWarehouseId());
                        uploadStrategyList1.forEach(u -> {
                            for (OmsVirtualWarehouse omsVirtualWarehouse : omsVirtualWarehouseList) {
                                if (u.getSettingJson().contains(omsVirtualWarehouse.getVirtualWarehouseId().toString())) {
                                    Map map = JsonUtils.fromJson(u.getSettingJson(), Map.class);
                                    map.put("storeId",u.getStoreId());
                                    map.put("storeName",u.getStoreName());
                                    settingList1.add(map);
                                }
                            }
                        });
                    }
                }
                settingTripleList.add(Triple.of(f.getVstoreCode(), f, settingList1));
           /* String code = f.getVstoreCode();
            List<IcsInventorySyncConfig> sublist = result.vstoreCodeToMap.get(code);
            List<Map<String, Object>> settingList = Lists.newArrayList();
            sublist.forEach(fo -> {
                Optional.ofNullable(result.thirdPlatformToGroup.get(fo.getChannelNo()))
                        .ifPresent(orderSourceTerminalConfigList -> {
                            Optional<OrderSourceTerminalConfig> optional = orderSourceTerminalConfigList.stream().filter(o -> Objects.nonNull(result.storeCodeTOMap.get(o.getThirdChannelNo()))).findFirst();
                            if(optional.isPresent()){
                                OrderSourceTerminalConfig orderSourceTerminalConfig = optional.get();
                                OmsStore omsStore = result.storeCodeTOMap.get(orderSourceTerminalConfig.getThirdChannelNo());
                                Optional.ofNullable(omsStore).flatMap(os -> Optional.ofNullable(result.uploadStrategyGroup.get(os.getStoreId())))
                                        .ifPresent(l -> {
                                                    for (OmsStockUploadStrategy omsStockUploadStrategy : l) {
                                                        settingList.add(JsonUtils.fromJson(omsStockUploadStrategy.getSettingJson(), Map.class));
                                                    }
                                                }

                                        );

                            }
                        });
                settingTripleList.add(Triple.of(code, fo, settingList));
                settingMap.put(code.concat("_").concat(fo.getChannelNo()), settingList);

            });*/
        });
        //step7 仓库编码+渠道编码遍历统计
        Map<String,WarehouseRatioExportDto> resultMap = Maps.newHashMap();
        for (Triple<String, IcsInventorySyncConfig,  List<Map<String, Object>>> node : settingTripleList) {
            List<Map<String, Object>> settingList = node.getRight();
            String vStoreCode = node.getLeft();
            IcsInventorySyncConfig syncConfig = node.getMiddle();
            String vStoreCodeToBrandCode = warehouseToBarandCodeMap.get(vStoreCode);

            //如果渠道类型是OMS，且没有配置OMS上传策略，则不参与放大比例计算
            if (1 == syncConfig.getChannelType() && CollectionUtils.isEmpty(settingList)) {
                continue;
            }

            if (StringUtils.isNotBlank(vStoreCodeToBrandCode)) {
                countExportAmplifyRatioByBrandCode(settingList, vStoreCodeToBrandCode, result, vStoreCode, resultMap, syncConfig);
            } else {
                countExportAmplifyRatioAllBrandCode(settingList, result, vStoreCode, resultMap, syncConfig);
            }
        }
        for (Map.Entry<String, WarehouseRatioExportDto> entry : resultMap.entrySet()){
            exportDtos.add(entry.getValue());
        }
        if(CollectionUtils.isNotEmpty(brandCode)){
            exportDtos = exportDtos.stream().filter(f -> brandCode.contains(f.getBrandCode())).collect(Collectors.toList());
        }

        return exportDtos;
    }

    private  List<WarehouseRatioExportDto> buildWarehouseRatioExportDto(List<IcsInventorySyncConfig> list,Boolean flag) {
        return list.stream().map(f->{
            WarehouseRatioExportDto exportDto = new WarehouseRatioExportDto();
            exportDto.setWarehouseCode(f.getVstoreCode());
            exportDto.setWarehouseName(f.getVstoreName());
            exportDto.setShopCode(f.getChannelNo());
            exportDto.setShopName(f.getChannelName());
            if(!flag) {
                exportDto.setPlatformAmplifyRatio(Objects.nonNull(f.getSharingRatio()) ? f.getSharingRatio().toString().concat("%") : "");
                exportDto.setShopAmplifyRatio(Objects.nonNull(f.getSharingRatio()) ? f.getSharingRatio().toString().concat("%") : "");
            }
            return exportDto;
        }).collect(Collectors.toList());
    }

    private void toMapWarehouseRatioDto(List<IcsInventorySyncConfig> icsInventorySyncConfigs, List<WarehouseRatioDto> list,Boolean flag ) {
        icsInventorySyncConfigs
                .forEach(m -> {
                    WarehouseRatioDto warehouseRatioDto = new WarehouseRatioDto();
                    warehouseRatioDto.setWarehouseCode(m.getVstoreCode());
                    warehouseRatioDto.setWarehouseName(m.getVstoreName());
                    if(flag){
                        warehouseRatioDto.setAmplifyRatioNum(0);
                    }else {
                        warehouseRatioDto.setAmplifyRatioNum(Objects.nonNull(m.getSharingRatio()) ? m.getSharingRatio() : null);
                    }
                    list.add(warehouseRatioDto);
                });
    }

    private void toMapWarehouseRatioDetailDto(List<IcsInventorySyncConfig> icsInventorySyncConfigs, List<WarehouseRatioDetailDto> list) {
        icsInventorySyncConfigs
                .forEach(m -> {
                    WarehouseRatioDetailDto warehouseRatioDetailDto = new WarehouseRatioDetailDto();
                    warehouseRatioDetailDto.setPlatformAmplifyRatioNum(m.getSharingRatio());
                    warehouseRatioDetailDto.setShopCode(m.getChannelNo());
                    warehouseRatioDetailDto.setShopName(m.getChannelName());
                    warehouseRatioDetailDto.setShopAmplifyRatioNum(m.getSharingRatio());
                    list.add(warehouseRatioDetailDto);
                });
    }

    @Override
    protected IService<OmsWarehouse, Long> getService() {
        return iInventoryBoardService;
    }
}
