/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.dto.ShopCompanyDto;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.Shop;
import cn.wonhigh.baize.repository.gms.ShopRepository;

import cn.wonhigh.baize.service.gms.IShopService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Service
public class ShopService extends BaseService<Shop,Integer> implements  IShopService{
    @Autowired
    private ShopRepository repository;

    protected IRepository<Shop,Integer> getRepository(){
        return repository;
    }

    
    public Shop findByUnique(String shopNo){
        return repository.findByUnique(shopNo);
    }

    public Integer deleteByUnique(String shopNo){
        return repository.deleteByUnique(shopNo);
    }

    public Integer insertForUpdate(Shop entry){
       return repository.insertForUpdate(entry);
    }

    @Override
    public ShopCompanyDto selectShopAndCompany(Map<String, Object> map) {
        return repository.selectShopAndCompany(map);
    }
}