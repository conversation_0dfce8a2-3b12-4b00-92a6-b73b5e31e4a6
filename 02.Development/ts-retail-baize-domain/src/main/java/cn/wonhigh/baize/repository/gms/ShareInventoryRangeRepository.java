/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.ShareInventoryRange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;
import java.util.List;
import java.util.Map;

@Mapper
public interface ShareInventoryRangeRepository extends IRepository<ShareInventoryRange,String> {
    int insertSelective(ShareInventoryRange shareInventoryRange);

    int updateShare(@Param("params") Map<String, Object> map);

    Integer batchInsert(@Param("datas") List<ShareInventoryRange> list);

    Integer batchUpdate(@Param("datas")List<ShareInventoryRange> list);

    Integer findExistInBrandInventoryRange(@Param("params") Map<String, Object> map);
}