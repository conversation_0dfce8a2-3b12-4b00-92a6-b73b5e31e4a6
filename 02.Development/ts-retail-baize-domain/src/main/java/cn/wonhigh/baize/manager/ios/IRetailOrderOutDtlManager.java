/**  **/
package cn.wonhigh.baize.manager.ios;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl;

import cn.mercury.manager.IManager;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IRetailOrderOutDtlManager extends IManager<RetailOrderOutDtl,String>{


	List<RetailOrderOutDtl> selectPageForReport(Query query, Pagenation pagenation);
	int selectPageForReportCount(Query query);

}