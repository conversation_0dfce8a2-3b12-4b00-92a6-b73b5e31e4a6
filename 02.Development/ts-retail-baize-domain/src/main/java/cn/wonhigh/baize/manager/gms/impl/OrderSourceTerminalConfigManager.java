/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.UUID;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.ICompanyManager;
import cn.wonhigh.baize.manager.gms.IOrderSourceTerminalConfigManager;
import cn.wonhigh.baize.manager.gms.IZoneInfoManager;
import cn.wonhigh.baize.model.entity.gms.Company;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import cn.wonhigh.baize.model.entity.gms.ZoneInfo;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OcsOrderSourceConfigChannelTypeEnum;
import cn.wonhigh.baize.model.enums.PlatformEnum;
import cn.wonhigh.baize.model.enums.SecondPlatformEnum;
import cn.wonhigh.baize.service.gms.IOrderSourceTerminalConfigService;
import com.google.common.base.Suppliers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import topmall.framework.manager.BaseManager;
import topmall.framework.security.Authorization;
import topmall.framework.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;


@Service
public class OrderSourceTerminalConfigManager extends BaseManager<OrderSourceTerminalConfig,String> implements IOrderSourceTerminalConfigManager{
    @Autowired
    private IOrderSourceTerminalConfigService service;

    @Autowired
    private IZoneInfoManager zoneInfoManager;

    @Autowired
    private ICompanyManager companyManager;

    protected IService<OrderSourceTerminalConfig,String> getService(){
        return service;
    }


    @Override
    public Integer insert(OrderSourceTerminalConfig config) {
        boolean isAdd = StringUtils.isEmpty(config.getId());
        OrderSourceTerminalConfig newConfig = buildData(config, isAdd);
        Integer i = 0;
        if(isAdd){
            i = service.insert(newConfig);
        } else {
            i = service.update(newConfig);
        }
        try {
            saveLogs(newConfig, isAdd);
        } catch (Exception e) {
            logger.error("保存日志失败", e);
        }
        return i;
    }

    OrderSourceTerminalConfig buildData(OrderSourceTerminalConfig data, boolean isAdd){
        Date now = new Date();


        OrderSourceTerminalConfig newData;

        String userName = Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown");
        if(!isAdd && StringUtils.isNotEmpty(data.getId())){
            newData = service.findByPrimaryKey(data.getId());
            if (newData == null) {
                throw new ManagerException("找不到数据");
            }
            newData.setUpdateTime(now);
            newData.setUpdateUser(userName);
            newData.setCompanyName(data.getCompanyName());
            newData.setCompanyNo(data.getCompanyNo());
            newData.setManagerZoneName(data.getManagerZoneName());
            newData.setManagerZoneNo(data.getManagerZoneNo());
            newData.setProvinceName(data.getProvinceName());
            newData.setProvinceNo(data.getProvinceNo());
            newData.setCityName(data.getCityName());
            newData.setCityNo(data.getCityNo());
            newData.setZoneName(data.getZoneName());
            newData.setZoneNo(data.getZoneNo());
            newData.setRemark(data.getRemark());
        } else {
            newData = data;
            String thirdChannelNo = data.getThirdChannelNo();
            newData.setThirdChannelNo(thirdChannelNo);
            newData.setThirdChannelName(data.getThirdChannelName());
            newData.setId(UUID.gernerate());
            newData.setThirdPlatform(data.getThirdPlatform());
            newData.setThirdPlatformName(data.getThirdPlatformName());
            newData.setSecondPlatform(data.getSecondPlatform());
            newData.setSecondPlatformName(data.getSecondPlatformName());
            newData.setMerchantCode(Optional.ofNullable(data.getMerchantCode()).orElse(""));

            newData.setCreateTime(now);
            newData.setUpdateTime(now);
            newData.setCreateUser(userName);
            newData.setTerminalName(OcsOrderSourceConfigChannelTypeEnum.getName(data.getTerminal()));
            newData.setPlatformName(PlatformEnum.getName(data.getPlatform()));
            newData.setSecondPlatformName(SecondPlatformEnum.getName(data.getSecondPlatform()));

            List<OrderSourceTerminalConfig> configs = service.selectByParams(Query.Where("thirdChannelNo", thirdChannelNo));
            if(!CollectionUtils.isEmpty(configs)){
                throw new ManagerException(String.format("一级来源+二级来源+店铺编码重复：[%s]", thirdChannelNo));
            }
        }



        if(!StringUtils.isEmpty(data.getZoneNo())){
            //根据小区查出大区
            List<ZoneInfo> zoneInfos = zoneInfoManager.selectByParams(Query.Where("zoneNo", data.getZoneNo()));
            if(CollectionUtils.isEmpty(zoneInfos)){
                throw new ManagerException(String.format("查询不到小区[%s]", data.getZoneNo()));
            }
            ZoneInfo zoneInfo = zoneInfos.get(0);
            newData.setZoneName(zoneInfo.getName());
            newData.setManagerZoneNo(zoneInfo.getManageZoneNo());
        }
        if(!StringUtils.isEmpty(data.getCompanyNo())){
            //查询结算公司
            List<Company> companies = companyManager.selectByParams(Query.Where("companyNo", data.getCompanyNo()));
            if (CollectionUtils.isEmpty(companies)) {
                throw new ManagerException(String.format("查询不到结算公司[%s]", data.getCompanyNo()));
            }
            newData.setCompanyName(companies.get(0).getName());
        }

        return newData;
    }

    void saveLogs(OrderSourceTerminalConfig config, boolean isAdd){
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                        .setOpscode(isAdd ? "insert" : "update").
                        setSyscode(String.valueOf(MenuTypeEnums.OSTC.getType())).
                        setSysname(MenuTypeEnums.OSTC.getDesc()).
                        setKeyword1(config.getThirdPlatform()).
                        setKeyword1info("渠道店铺编码:" + config.getThirdPlatform() + "(" + config.getThirdPlatformName() + ")").
                        setKeyword2(config.getThirdChannelNo()).
                        setKeyword2info("三级来源编码:" + config.getThirdChannelNo() + "(" + config.getThirdChannelName() + ")").
                        setRemark(String.format("%s：终端编码:%s(%s);平台编码:%s(%s);二级平台编码:%s(%s);省:%s;市:%s;区:%s(%s);大区编码:%s(%s);结算公司编码:%s(%s)",
                                isAdd ? "新增" : "修改", config.getTerminal(),  config.getTerminalName(), config.getPlatform(),
                                config.getPlatformName(), config.getSecondPlatform(), config.getSecondPlatformName(),
                                config.getProvinceNo(), config.getCityNo(), config.getZoneNo(), config.getZoneName(),
                                config.getManagerZoneNo(), config.getManagerZoneName(),
                                config.getCompanyNo(), config.getCompanyName()))
                        .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                        .setCreateTime(new Date())

                        .build()));
    }

    @Override
    public List<OrderSourceTerminalConfig> selectShopPageByParams(Query query, Pagenation pagenation) {
        return service.selectShopPageByParams(query, pagenation);
    }

    @Override
    public Integer selectShopCountByParams(Query query) {
        return service.selectShopCountByParams(query);
    }


    @Override
    public Supplier<Optional<OrderSourceTerminalConfig>> getByLazyMerchantCode(String merchantCode) {
        if (StringUtils.isEmpty(merchantCode)) {
            return Optional::empty;
        }
        return Suppliers.memoizeWithExpiration(() -> {
            List<OrderSourceTerminalConfig> configs = service.selectByParams(Query.Where("merchantCode", merchantCode));
            if(CollectionUtils.isEmpty(configs)){
                return Optional.empty();
            }
            return Optional.of(configs.get(0));
        }, 3, TimeUnit.MINUTES);
    }
}