package cn.wonhigh.baize.utils.common.baozun;

import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.domain.configuration.baozun.BaoZunInterfaceConfig;
import cn.wonhigh.baize.utils.common.HttpUtil;
import cn.wonhigh.baize.utils.common.XMLUtils;
import com.alibaba.fastjson.JSONObject;
import com.yougou.logistics.base.common.exception.ManagerException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.MessageDigest;
import java.util.*;

/**
 * @className: BaoZunHttpUtils
 * @Time: 2023/5/17 16:27
 * <AUTHOR> zhangshun
 * @Description : 请求宝尊HUB4接口工具类
 */
public class BaoZunHttpUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaoZunHttpUtils.class);
    //字符集
    public static final String CHARSET_UTF8 = "UTF-8";
    // 请求body加密字符规则使用
    private static final String AES_CBC_PCK_ALG = "AES/CBC/PKCS5Padding";
    // 请求body加密字符规则使用
    private static final byte[] AES_IV = new byte[]{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    // 摘要格式
    private static final String AES_ALG = "AES";

    public static final String DEFAULT_CONTENT_TYPE = "text/plain";

    /**
     * 请求宝尊HUB4接口method
     *
     * @param body       请求体内容
     * @param methodName 业务methodName
     * @return
     * @throws Exception
     */
    public static <T extends BaoZunResponse> T requestBaoZun(Object body, String methodName, Class<T> tClass) throws Exception {
        BaoZunInterfaceConfig config = SpringContext.getBean(BaoZunInterfaceConfig.class);
        //请求报文转为xml格式
        String requestBody = XMLUtils.convertToXmlStr(body);
        LOGGER.info("bz requestBody:" + requestBody);
        boolean isNeedEncrypt = true;
        //如果业务需要加密,则执行下面报文加密操作
        if (isNeedEncrypt) {
            requestBody = encrypt(requestBody, encryptMD5(config.getPuma().getAppSecret()), CHARSET_UTF8);
        }
        LOGGER.info("bz requestBody encrypt:" + requestBody);
        //构造请求url参数
        String requestWithParamUrl = buildUrl(methodName, requestBody, config);
        LOGGER.info("bz url:" + requestWithParamUrl);
        String result = HttpUtil.post(requestWithParamUrl, requestBody, CHARSET_UTF8, DEFAULT_CONTENT_TYPE);
        LOGGER.info("bz response:" + result);
        if (StringUtils.isEmpty(result)) {
            throw new ManagerException("BZ接口请求响应数据为空");
        }
        T response = null;
        try {
            response = (T) XMLUtils.convertXmlStrToObject(tClass, result);
            LOGGER.info("bz response:{}", JSONObject.toJSON(response));
        } catch (Exception e) {
            LOGGER.error("BZ接口响应数据转换异常,", e);
            throw new ManagerException("BZ接口响应数据转换异常:" + e);
        }
        return response;
    }

    //构造请求路径
    public static String buildUrl(String methodName, String requestBody, BaoZunInterfaceConfig config) throws Exception {
        //生成签名
        Map<String, String> params = new HashMap<>();
        params.put("version", "1.0");
        // sourceApp-appKey 为 宝尊提供的请求方唯一标识
        params.put("sourceApp", config.getPuma().getAppKey());
        params.put("interfaceType", "1");
        // methodName 为 具体的业务名称,宝尊提供
        params.put("methodName", methodName);
        params.put("requestTime", String.valueOf(new Date().getTime()));
        params.put("sign", makeSign(params, requestBody, config.getPuma().getAppSecret()));

        // 请求url拼接
        StringBuilder stringBuilder = new StringBuilder();
        Set<Map.Entry<String, String>> entries = params.entrySet();
        stringBuilder.append(config.getUrl()).append("?");
        for (Map.Entry<String, String> map : entries) {
            stringBuilder.append(map.getKey()).append("=").append(map.getValue()).append("&");
        }
        String requestWithParamUrl = stringBuilder.toString();
        requestWithParamUrl = requestWithParamUrl.substring(0, requestWithParamUrl.length() - 1);

        return requestWithParamUrl;
    }

    /**
     * requestBody 加密
     */
    public static String encrypt(String content, byte[] aesKey, String charset) throws Exception {
        Cipher cipher = Cipher.getInstance(AES_CBC_PCK_ALG);
        IvParameterSpec iv = new IvParameterSpec(AES_IV);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(Base64.encodeBase64(aesKey), AES_ALG), iv);
        byte[] encryptBytes = cipher.doFinal(content.getBytes(charset));
        return new String(Base64.encodeBase64(encryptBytes));
    }


    /**
     * 签名生成算法
     * params 请求参数对象
     */
    private static String makeSign(Map<String, String> params, String body, String secret) throws IOException {
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();
        query.append(secret);
        for (String key : keys) {
            String value = params.get(key);
            if (areNotEmpty(key, value)) {
                query.append(key).append(value);
            }
        }
        // 第三步：把请求主体拼接在参数后面
        if (body != null) {
            query.append(body);
        }
        query.append(secret);
        // 第四步：使用MD5加密
        byte[] bytes = encryptMD5(query.toString());
        // 第五步：把二进制转化为大写的十六进制
        return byte2hex(bytes);
    }


    public static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }

    /**
     * 检查指定的字符串列表是否不为空。
     */
    public static boolean areNotEmpty(String... values) {
        boolean result = true;
        if (values == null || values.length == 0) {
            result = false;
        } else {
            for (String value : values) {
                result &= !isEmpty(value);
            }
        }
        return result;
    }


    public static boolean isEmpty(String value) {
        int strLen;
        if (value == null || (strLen = value.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((!Character.isWhitespace(value.charAt(i)))) {
                return false;
            }
        }
        return true;
    }


    /**
     * MD5值获取
     */
    public static byte[] encryptMD5(String data) throws IOException {
        byte[] bytes = null;

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            bytes = md.digest(data.getBytes(CHARSET_UTF8));
        } catch (GeneralSecurityException gse) {
            throw new IOException(gse.toString());
        }
        return bytes;
    }

}
