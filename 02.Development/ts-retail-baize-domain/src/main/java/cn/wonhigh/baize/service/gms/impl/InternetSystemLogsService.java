/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InternetSystemLogs;
import cn.wonhigh.baize.repository.gms.InternetSystemLogsRepository;

import cn.wonhigh.baize.service.gms.IInternetSystemLogsService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetSystemLogsService extends BaseService<InternetSystemLogs,Long> implements  IInternetSystemLogsService{
    @Autowired
    private InternetSystemLogsRepository repository;

    protected IRepository<InternetSystemLogs,Long> getRepository(){
        return repository;
    }

    
}