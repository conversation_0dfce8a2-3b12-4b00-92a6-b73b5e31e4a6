package cn.wonhigh.baize.domain.configuration.datasource;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
//@ConditionalOnProperty(prefix = "retail.gms.datasource", name = { "url", "username", "password" })
@MapperScan(basePackages = {
        GmsDataSourceConfiguration.BASE_PACKAGE}, sqlSessionFactoryRef = "sqlSessionFactory")
@Order(-10)
public class GmsDataSourceConfiguration {

    public static final String BASE_PACKAGE = "cn.wonhigh.baize.repository.gms";

    public GmsDataSourceConfiguration() {
        System.err.println("init gms default datasource ...");
    }

    @Bean("masterDataSource")
    @ConfigurationProperties("retail.gms.datasource")
    public DataSource dataSourceGms() {
        return DataSourceBuilder.create().type(DruidDataSource.class).build();
    }

    @Bean("slaveDataSource")
    @ConfigurationProperties("retail.gms.datasource.ap")
    public DataSource dataSourceApGms() {
        return DataSourceBuilder.create().type(DruidDataSource.class).build();
    }


    @Bean("datasource")
    @Primary
    public DataSource dynamicDataSource(@Qualifier("masterDataSource") DataSource masterDataSource,
                                        @Qualifier("slaveDataSource") DataSource slaveDataSource) {
        String apKey = "ap";
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put("tp", masterDataSource);
        targetDataSources.put(apKey, slaveDataSource);

        AbstractRoutingDataSource dataSource = new AbstractRoutingDataSource() {
            @Override
            protected Object determineCurrentLookupKey() {
                if (ApDataSourceSwitch.openedAp()) {
                    return apKey;
                }
                return null;
            }
        };
        dataSource.setDefaultTargetDataSource(masterDataSource);
        dataSource.setTargetDataSources(targetDataSources);
        // 指定的数据源不存在时直接报错
        dataSource.setLenientFallback(false);
        return dataSource;
    }

    @Bean("sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier("datasource") DataSource ds) throws Exception {

        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();

        bean.setDataSource(ds);

        Resource rs = new PathMatchingResourcePatternResolver()
                .getResources("mybatis-gms-config.xml")[0];
        bean.setConfigLocation(rs);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("mapper/gms/*.xml"));

        return bean.getObject();
    }


    @Bean("sqlSessionTemplate")
    @Primary
    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("sqlSessionFactory") SqlSessionFactory factory) {
        return new SqlSessionTemplate(factory);
    }

    @Bean("transactionManager")
    @Primary
    public DataSourceTransactionManager transactionManager(@Qualifier("datasource") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

}
