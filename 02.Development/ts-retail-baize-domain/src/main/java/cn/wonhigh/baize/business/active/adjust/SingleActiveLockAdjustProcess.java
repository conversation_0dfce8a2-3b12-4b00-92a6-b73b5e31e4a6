package cn.wonhigh.baize.business.active.adjust;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.UUID;
import cn.mercury.basic.query.Query;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.mercury.utils.CollectionsUtil;
import cn.wonhigh.baize.business.virtualwarehousescope.ValidateResult;
import cn.wonhigh.baize.manager.gms.IExternalProductMappingManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseScopeManager;
import cn.wonhigh.baize.manager.gms.IOrgUnitBrandRelManager;
import cn.wonhigh.baize.manager.gms.impl.InternetVirtualWarehouseScopeManager;
import cn.wonhigh.baize.model.dto.commons.ValidResult;
import cn.wonhigh.baize.model.entity.gms.*;
import cn.wonhigh.baize.model.enums.*;
import cn.wonhigh.baize.service.gms.IItemService;
import cn.wonhigh.baize.utils.common.ValidationUtil;
import cn.wonhigh.baize.utils.helpers.ShardingFlagHelper;
import cn.wonhigh.retail.gms.api.service.InventoryOccupiedApi;
import cn.wonhigh.retail.gms.api.vo.ShopInventoryOccupiedDtlDto;
import cn.wonhigh.retail.gms.api.vo.ShopInventoryOccupiedDto;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import topmall.framework.security.Authorization;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 单机构活动锁库调整处理
 * @date 2025/6/11 10:54
 */
public abstract class SingleActiveLockAdjustProcess extends ActiveLockAdjustProcess {
    private static final Logger LOGGER = LoggerFactory.getLogger(SingleActiveLockAdjustProcess.class);

    public SingleActiveLockAdjustProcess(String billNo, IUser user) {
        super(billNo, user);
    }

    @Override
    protected void createProcess(IcsActiveLockAdjust adjust) {
        adjust.setId(UUID.gernerate());
        adjust.setAdjustStatus(AdjustStatusEnums.NEW_STATUS.getValue());
        adjust.setSyncStatus(AdjustSyncStatusEnums.NOT_SYNC.getValue());
        icsActiveLockAdjustManager.insert(adjust);
    }
    
    @Override
	protected boolean updateProcess(IcsActiveLockAdjust adjust) {
        initAdjust();
    	LOGGER.info("活动锁库调整单更新处理, billNo={}", billNo);
        Assert.isTrue(StringUtils.isNotBlank(adjust.getBillNo()), "参数校验不通过, 更新失败");
        boolean uptFlag = this.adjust.getRemark().equals(adjust.getRemark());
        if (uptFlag) return false;
        int count = icsActiveLockAdjustManager.update(adjust);
    	return count > 0;
	}

	@Override
    protected void failureSyncProcess() {
        LOGGER.info("活动锁库调整单失败重传, billNo={}", billNo);
        initAdjust();
        Assert.notNull(adjust, "未找到调整单据");
        int status = adjust.getAdjustStatus();
        Assert.isTrue(status == AdjustStatusEnums.AUDIT_SUCCESS.getValue(), String.format("单据状态为%s, 不允许操作失败重传", Objects.requireNonNull(AdjustStatusEnums.getAdjustStatusEnums(status)).getDesc()));
        adjustDtlList = icsActiveLockAdjustDtlManager.selectByParams(new Query().where("billNo", billNo).and("syncStatus", AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue()));
        Assert.notEmpty(adjustDtlList, "未找到同步失败锁库活动明细");
        List<InventoryActiveLockDtl> syncDtlList = inventoryActiveLockDtlManager.selectByAdjustBillNo(billNo, AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue());
        Assert.notEmpty(syncDtlList, String.format("未找到状态为%s锁库活动明细", AdjustDtlSyncStatusEnums.SYNC_FAIL.getDesc()));
        InventoryActiveLock activeLock = inventoryActiveLockManager.findByUnique(adjust.getRefBillNo());
        Assert.isTrue(activeLock.getStatus().intValue() == ActiveLockStatusEnums.EFFECTIVE_STATUS.getValue().intValue(), String.format("锁库单状态为%s, 不允许操作失败重传", Objects.requireNonNull(ActiveLockStatusEnums.getActiveLockEnums(activeLock.getStatus())).getDesc()));
        adjustSyncInventory(Pair.of(activeLock, syncDtlList));
    }

    @Override
    public void cancelProcess() {
        LOGGER.info("活动锁库调整单作废, billNo={}", billNo);
        initAdjust();
        Assert.notNull(adjust, "未找到调整单据");
        int status = adjust.getAdjustStatus();
        // 锁库活动调整单状态是新建或审核失败状态才允许作废
        Assert.isTrue(status == AdjustStatusEnums.NEW_STATUS.getValue() || status == AdjustStatusEnums.AUDIT_FAIL.getValue(), String.format("单据状态为%s, 不允许作废", Objects.requireNonNull(AdjustStatusEnums.getAdjustStatusEnums(status)).getDesc()));
        icsActiveLockAdjustManager.updateAdjustStatus(adjust.getBillNo(), AdjustStatusEnums.CANCEL_STATUS.getValue());
    }

    @Override
    protected void auditProcess() {
        LOGGER.info("活动锁库调整单审核, billNo={}", billNo);
        initAdjust();
        Assert.notNull(adjust, "未找到调整单据");
        initAdjustDtlList();
        Assert.notNull(adjustDtlList, "未找到锁库活动明细");
        int status = adjust.getAdjustStatus();
        // 锁库活动调整单状态是新建或审核失败状态才允许审核
        Assert.isTrue(status == AdjustStatusEnums.NEW_STATUS.getValue() || status == AdjustStatusEnums.AUDIT_FAIL.getValue(), String.format("单据状态为%s, 不允许审核", Objects.requireNonNull(AdjustStatusEnums.getAdjustStatusEnums(status)).getDesc()));
        List<IcsActiveLockAdjustDtl> checkFailAdjectDtlList = checkProcess();
        if (!CollectionUtils.isEmpty(checkFailAdjectDtlList)) {
            //获取列表中错误信息
            String errorMsg = checkFailAdjectDtlList.stream().map(IcsActiveLockAdjustDtl::getErrorMessage).filter(Objects::nonNull).collect(Collectors.joining("|"));
            Assert.isTrue(StrUtil.isBlank(errorMsg), errorMsg);
        }
        // 库存处理 -> 更新锁库单 -> 同步库存
        addOrSubtract(adjust.getAdjustType()).addOrSubtractHandle(this::addAuditAdjust, this::subtractAuditAdjust);
    }

    @Override
    protected void effectProcess() {
        initAdjust();
        Assert.notNull(adjust, "未找到调整单据");
        int status = adjust.getAdjustStatus();
        int syncStatus = adjust.getSyncStatus();
        Assert.isTrue((status == AdjustStatusEnums.AUDIT_SUCCESS.getValue()
                && syncStatus == AdjustSyncStatusEnums.NOT_SYNC.getValue()),
                String.format("单据状态为%s, 同步状态为%s, 不允许操作库存同步", Objects.requireNonNull(AdjustStatusEnums.getAdjustStatusEnums(status)).getDesc(),
                Objects.requireNonNull(AdjustSyncStatusEnums.getAdjustSyncStatusEnums(status)).getDesc()));
        List<InventoryActiveLockDtl> activeLockDtlList = inventoryActiveLockDtlManager.selectByAdjustBillNo(adjust.getBillNo(), AdjustDtlSyncStatusEnums.NOT_SYNC.getValue());
        Assert.notEmpty(activeLockDtlList, String.format("未找到状态为%s锁库活动明细", AdjustDtlSyncStatusEnums.NOT_SYNC.getDesc()));
        InventoryActiveLock activeLock = inventoryActiveLockManager.findByUnique(adjust.getRefBillNo());
        Assert.isTrue(activeLock.getStatus().intValue() == ActiveLockStatusEnums.EFFECTIVE_STATUS.getValue().intValue(), String.format("锁库活动单状态为%s, 不允许操作同步库存", Objects.requireNonNull(ActiveLockStatusEnums.getActiveLockEnums(activeLock.getStatus())).getDesc()));
        adjustSyncInventory(Pair.of(activeLock, activeLockDtlList));
    }

    /**
     * 添加库存调整
     */
    private void addAuditAdjust() {
        Pair<Boolean, List<InventoryActiveLockDtl>> pair = icsActiveLockAdjustManager.auditActiveLockAdjust(
                adjustDtlList, this::updateActiveLockDtl, this::occupiedAdjustInventory);
        LOGGER.info("更新活动锁库单和预占库存, result={}", pair.getLeft());
        Assert.isTrue(pair.getLeft(), "活动锁库调整单审核失败");
    }

    /**
     * 扣减库存调整
     */
    private void subtractAuditAdjust() {
        Pair<Boolean, List<InventoryActiveLockDtl>> pair = icsActiveLockAdjustManager.auditActiveLockAdjust(
                adjustDtlList, this::updateActiveLockDtl, this::releaseAdjustInventory);
        LOGGER.info("更新活动锁库单和释放库存, result={}", pair.getLeft());
        Assert.isTrue(pair.getLeft(), "活动锁库调整单审核失败");
    }

    /**
     * 释放库存
     */
    private void releaseAdjustInventory() {
        ShopInventoryOccupiedDto occupiedDto = buildShopInventoryOccupiedDto();
        InventoryOccupiedApi inventoryOccupiedApi = SpringContext.getBean(InventoryOccupiedApi.class);
        inventoryOccupiedApi.shopInventoryRelease(occupiedDto);
    }

    /**
     * 预占库存
     */
    private void occupiedAdjustInventory() {
        ShopInventoryOccupiedDto occupiedDto = buildShopInventoryOccupiedDto();
        InventoryOccupiedApi inventoryOccupiedApi = SpringContext.getBean(InventoryOccupiedApi.class);
        inventoryOccupiedApi.shopInventoryOccupied(occupiedDto);
    }

    /**
     * 构建预占/释放DTO
     * @return
     */
    private ShopInventoryOccupiedDto buildShopInventoryOccupiedDto() {
        ShopInventoryOccupiedDto occupiedDto = buildBaseOccupiedDto();
        List<ShopInventoryOccupiedDtlDto> occupiedDtlDtoList = buildOccupiedDtlDtoList(occupiedDto.getShardingFlag());
        occupiedDto.setDtlList(occupiedDtlDtoList);
        return occupiedDto;
    }

    /**
     * 更新活动锁库单明细
     * @return
     */
    private List<InventoryActiveLockDtl> updateActiveLockDtl() {
        List<InventoryActiveLockDtl> activeLockDtlList = inventoryActiveLockDtlManager.selectByParams(Query.Where("billNo", adjust.getRefBillNo()));
        Map<String, InventoryActiveLockDtl> groupMap = activeLockDtlList.stream().collect(Collectors.toMap(InventoryActiveLockDtl::getUniqueKey, Function.identity(), (d1, d2) ->  d2));
        List<InventoryActiveLockDtl> updateLockDtlList = adjustDtlList.stream()
                .map(dtl -> {
                    InventoryActiveLockDtl activeLockDtl = groupMap.get(dtl.getUniqueKey());
                    // 新增锁库活动明细
                    if (activeLockDtl == null) {
                        activeLockDtl = new InventoryActiveLockDtl();
                        activeLockDtl.setBillNo(adjust.getRefBillNo());
                        activeLockDtl.setStoreNo(dtl.getStoreNo());
                        activeLockDtl.setStoreName(dtl.getStoreName());
                        activeLockDtl.setOrderUnitNo(dtl.getOrderUnitNo());
                        activeLockDtl.setOrderUnitName(dtl.getOrderUnitName());
                        activeLockDtl.setBrandNo(dtl.getBrandNo());
                        activeLockDtl.setItemCode(dtl.getItemCode());
                        activeLockDtl.setSizeNo(dtl.getSizeNo());
                        activeLockDtl.setBarcode(dtl.getBarcode());
                        activeLockDtl.setSkuNo(dtl.getSkuNo());
                        activeLockDtl.setLockQty(dtl.getAdjustQty());
                        activeLockDtl.setBalanceLockQty(dtl.getAdjustQty());
                        activeLockDtl.setSyncStatus(AdjustSyncStatusEnums.NOT_SYNC.getValue());
                        activeLockDtl.setCreateTime(new Date());
                        activeLockDtl.setCreateUser(adjust.getCreateUser());
                        activeLockDtl.setUpdateTime(new Date());
                        activeLockDtl.setUpdateUser(adjust.getCreateUser());
                        activeLockDtl.setId(UUID.gernerate());
                        return activeLockDtl;
                    }
                    activeLockDtl.setBalanceLockQty(dtl.getAdjustQty() * adjust.getAdjustType());
                    return activeLockDtl;
                })
                .filter(it -> {
                    // 扣减数量, 需要校验锁库明细是否存在, 不存在则过滤掉
                    if (Objects.equals(adjust.getAdjustType(), AdjustTypeEnums.AUDIT_SUCCESS.getValue())) {
                        return groupMap.containsKey(it.getUniqueKey());
                    }
                    return true;
                })
                .collect(Collectors.toList());
        inventoryActiveLockDtlManager.batchUpdateDtlForAudit(updateLockDtlList);
        return updateLockDtlList;
    }

    /**
     * 构建库存占用单基础信息
     * @return
     */
    private ShopInventoryOccupiedDto buildBaseOccupiedDto() {
        IcsActiveLockAdjustDtl adjustDtl = adjustDtlList.get(0);
        ShardingFlagHelper shardingFlagHelper = SpringContext.getBean(ShardingFlagHelper.class);
        String shardingFlag = shardingFlagHelper.getShardingFlagByOrderUnitNo(adjustDtl.getOrderUnitNo());
        ShopInventoryOccupiedDto occupiedDto = new ShopInventoryOccupiedDto();
        occupiedDto.setOrderNo(getBillNo());
        occupiedDto.setRefBillNo(getRefBillNo());
        occupiedDto.setShopNo(adjustDtl.getStoreNo());
        occupiedDto.setBillType(BillTypeEnumsISP.salesReturn.getRequestId().toString());
        occupiedDto.setChannelNo(adjust.getRefBillNo());
        occupiedDto.setBusinessType("2");
        occupiedDto.setShardingFlag(shardingFlag);
        return occupiedDto;
    }
    
    /**
     * 预占/释放单号
     * @return
     */
    private String getBillNo() {
    	IcsActiveLockAdjustDtl adjustDtl = adjustDtlList.get(0);
    	return adjust.getAdjustType() == -1 ? StrUtil.join("-", adjust.getRefBillNo(), adjustDtl.getStoreNo(), adjustDtl.getOrderUnitNo()) : adjust.getBillNo();
    }
    
    /**
     * 预占/释放相关单号
     * @return
     */
    private String getRefBillNo() {
    	return adjust.getAdjustType() == -1 ? adjust.getBillNo() : adjust.getRefBillNo();
    }

    /**
     * 构建库存占用单明细信息
     * @param shardingFlag
     * @return
     */
    private List<ShopInventoryOccupiedDtlDto> buildOccupiedDtlDtoList(String shardingFlag) {
        return adjustDtlList.stream()
                .map(dtl -> {
                    ShopInventoryOccupiedDtlDto dto = new ShopInventoryOccupiedDtlDto();
                    dto.setOrderUnitNo(dtl.getOrderUnitNo());
                    dto.setShopNo(dtl.getStoreNo());
                    dto.setQty(dtl.getAdjustQty());
                    dto.setSkuNo(dtl.getSkuNo());
                    dto.setShardingFlag(shardingFlag);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected void saveDtlProcess(IcsActiveLockAdjustDtl entry) {
        LOGGER.info("保存活动锁库单明细, billNo={}", billNo);
        String storeNo = entry.getStoreNo();
        String orderUnitNo = entry.getOrderUnitNo();
        Assert.isTrue(StrUtil.isNotBlank(storeNo) && StrUtil.isNotBlank(orderUnitNo), "机构编码、货管编码不能为空");
        String itemCode = entry.getItemCode();
        String sizeNo = entry.getSizeNo();
        String brandNo = entry.getBrandNo();
        Assert.isTrue(StrUtil.isNotBlank(itemCode) && StrUtil.isNotBlank(sizeNo) && StrUtil.isNotBlank(brandNo), "商品编码、品牌,尺码不能为空");
        int adjustQty = entry.getAdjustQty();
        Assert.isTrue(adjustQty > 0, "调整数量不能小于等于为0");
        initAdjust();
        Assert.notNull(adjust, "未找到调整单据");


        InventoryActiveLockDtl lockDtl = inventoryActiveLockDtlManager.findByParam(Query.Where("billNo", adjust.getRefBillNo())
                .and("storeNo", storeNo)
                .and("orderUnitNo", orderUnitNo)
                .and("itemCode", itemCode)
                .and("sizeNo", sizeNo)
                .and("brandNo", brandNo));

        AdjustTypeEnums adjustTypeEnums = AdjustTypeEnums.getAdjustStatusEnums(adjust.getAdjustType());
        IcsActiveLockAdjustDtl adjustDtl = null;
        if (adjustTypeEnums == AdjustTypeEnums.ADD && lockDtl == null) {
            InventoryActiveLockDtl newLockDtl = convertImportDtl(entry);
            adjustDtl = convertImportDtl(adjustQty, newLockDtl);
        } else if (adjustTypeEnums == AdjustTypeEnums.AUDIT_SUCCESS) {
            Assert.notNull(lockDtl, "锁库活动明细不存在");
            adjustDtl = convertImportDtl(adjustQty, lockDtl);
        }
        icsActiveLockAdjustDtlManager.insert(adjustDtl);
    }



    @Override
    protected void importDtlProcess(List<Object> objectList) {
        LOGGER.info("导入活动锁库单明细, billNo={}", billNo);
        initAdjust();
        Assert.notNull(adjust, "未找到调整单据");
        int status = adjust.getAdjustStatus();
        Assert.isTrue(status == AdjustStatusEnums.NEW_STATUS.getValue(), "锁库活动调整单状态不是新建状态");
        List<IcsActiveLockAdjustDtl> dtlList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        int index = 1;
        for (Object object : objectList) {
            index ++ ;
            if (!(object instanceof LinkedHashMap)) {
                continue;
            }
            LinkedHashMap<Integer, Object> map = (LinkedHashMap<Integer, Object>) object;
            Pair<Boolean, InventoryActiveLockDtl> pair = validateImportDtl(map, errorList, index);
            if (!pair.getLeft()) {
                continue;
            }
            int adjustQty = MapUtils.getInteger(map, 5, null);
            IcsActiveLockAdjustDtl dtl = convertImportDtl(adjustQty, pair.getRight());
            dtlList.add(dtl);
        }
        Assert.isTrue(CollectionUtils.isEmpty(errorList), String.join("|", errorList));
        Set<String> ukSet = dtlList.stream().map(IcsActiveLockAdjustDtl::getUniqueKey).collect(Collectors.toSet());
        Assert.isTrue(ukSet.size() == dtlList.size(), "商品行存在重复数据，请核查后再操作！");
        icsActiveLockAdjustDtlManager.batchInsertOrUpdate(dtlList);
    }

    /**
     * 转换导入的库存锁库活动明细
     * @param adjustDtl
     * @return
     */
    private InventoryActiveLockDtl convertImportDtl(IcsActiveLockAdjustDtl adjustDtl) {
        InventoryActiveLockDtl.InventoryActiveLockDtlBuilder builder = new InventoryActiveLockDtl().build();

        // 验证映射表数据
        List<ExternalProductMapping> externalProductMappings = SpringContext.getBean(IExternalProductMappingManager.class)
                .selectItemSkuByParams(MapBuilder.<String, Object>create()
                        .put("merchantsCode", this.merchantCodeEnum().getCode())
                        .put("brandCode", adjustDtl.getBrandNo())
                        .put("productCode", adjustDtl.getItemCode())
                        .put("sizeCode", adjustDtl.getSizeNo())
                        .build());

        if (CollectionUtils.isEmpty(externalProductMappings)) {
            throw new RuntimeException("映射表中数据不存在");
        }

        // 验证机构货管品牌关系
        List<OrgUnitBrandRel> orgUnitBrandRels = SpringContext.getBean(IOrgUnitBrandRelManager.class).selectByParams(
                OrgUnitBrandRel.build()
                        .storeNo(adjustDtl.getStoreNo())
                        .brandNo(adjustDtl.getBrandNo())
                        .orderUnitNo(adjustDtl.getOrderUnitNo())
                        .status(1)
                        .asQuery()
        );

        if (CollectionUtils.isEmpty(orgUnitBrandRels)) {
            throw new RuntimeException("机构货管品牌关系不存在");
        }

        if (orgUnitBrandRels.get(0).getStoreType() == 21) {
            throw new RuntimeException("机构类型错误");
        }

        // 校验虚仓范围
        List<InternetVirtualWarehouseScope> scopes = SpringContext.getBean(IInternetVirtualWarehouseScopeManager.class).selectByParams(
                InternetVirtualWarehouseScope.build()
                        .storeNo(adjustDtl.getStoreNo())
                        .orderUnitNo(adjustDtl.getOrderUnitNo())
                        .vstoreCode(this.adjust.getWarehouseCode())
                        .status(1)
                        .asQuery()
        );

        if (CollectionUtils.isEmpty(scopes)) {
            throw new RuntimeException(String.format("虚仓:%s机构货管关系不存在", this.adjust.getWarehouseCode()));
        }

        //校验商品品牌尺码
        List<ItemBaseInfo> itemBaseInfos = SpringContext.getBean(IItemService.class).queryItemByParams(
                MapBuilder.<String, Object>create()
                        .put("code", adjustDtl.getItemCode())
                        .put("sizeNo", adjustDtl.getSizeNo())
                        .put("brandNo", adjustDtl.getBrandNo())
                        .put("status", 1)
                        .build()
        );

        if (CollectionUtils.isEmpty(itemBaseInfos)) {
            throw new RuntimeException("商品品牌尺码不存在");
        }



        String username = Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown");

        builder.storeName(orgUnitBrandRels.get(0).getStoreName());
        builder.storeType(orgUnitBrandRels.get(0).getStoreType());
        builder.orderUnitName(orgUnitBrandRels.get(0).getOrderUnitName());
        builder.itemCode(itemBaseInfos.get(0).getItemCode());
        builder.brandNo(itemBaseInfos.get(0).getBrandNo());
        builder.sizeNo(itemBaseInfos.get(0).getSizeNo());
        builder.barcode(externalProductMappings.get(0).getBarcode());
        builder.lockQty(adjustDtl.getAdjustQty());
        builder.balanceLockQty(adjustDtl.getAdjustQty());
        builder.storeNo(adjustDtl.getStoreNo());
        builder.orderUnitNo(adjustDtl.getOrderUnitNo());
        builder.createUser(username);
        builder.createTime(new Date());
        builder.updateUser(username);
        builder.updateTime(new Date());
        builder.syncStatus(AdjustSyncStatusEnums.NOT_SYNC.getValue());
        builder.id(UUID.gernerate());
        builder.skuNo(itemBaseInfos.get(0).getSkuNo());
        builder.billNo(adjust.getBillNo());


        return builder.object();
    }


    /**
     * 转换导入的库存锁库活动明细
     * @param adjustQty
     * @param lockDtl
     * @return
     */
    private IcsActiveLockAdjustDtl convertImportDtl(int adjustQty, InventoryActiveLockDtl lockDtl) {
        IcsActiveLockAdjustDtl dtl = new IcsActiveLockAdjustDtl();
        dtl.setId(UUID.gernerate());
        dtl.setBillNo(billNo);
        dtl.setStoreNo(lockDtl.getStoreNo());
        dtl.setStoreName(lockDtl.getStoreName());
        dtl.setOrderUnitNo(lockDtl.getOrderUnitNo());
        dtl.setOrderUnitName(lockDtl.getOrderUnitName());
        dtl.setItemCode(lockDtl.getItemCode());
        dtl.setSizeNo(lockDtl.getSizeNo());
        dtl.setBrandNo(lockDtl.getBrandNo());
        dtl.setBarcode(lockDtl.getBarcode());
        dtl.setSkuNo(lockDtl.getSkuNo());
        dtl.setAdjustQty(adjustQty);
        dtl.setSyncStatus(AdjustSyncStatusEnums.NOT_SYNC.getValue());
        dtl.setCreateTime(new Date());
        String username = Authorization.getUser().getName();
        dtl.setCreateUser(username);
        dtl.setUpdateTime(new Date());
        dtl.setUpdateUser(username);
        return dtl;
    }

    /**
     * 验证导入的库存锁库活动明细
     * @param map
     * @param errorList
     * @param index
     * @return
     */
    private Pair<Boolean, InventoryActiveLockDtl> validateImportDtl(LinkedHashMap<Integer, Object> map, List<String> errorList, int index) {
        String storeNo = MapUtils.getString(map, 0, "");
        String orderUnitNo = MapUtils.getString(map, 1, "");
        if (StrUtil.isBlank(storeNo) || StrUtil.isBlank(orderUnitNo)) {
            LOGGER.error("第{}行数据错误, 机构编码、货管编码不能为空", index);
            errorList.add("第" + index + "行数据错误, 机构编码、货管编码不能为空");
            return Pair.of(false, null);
        }
        String brandNo = MapUtils.getString(map, 2, "");
        String itemCode = MapUtils.getString(map, 3, "");
        String sizeNo = MapUtils.getString(map, 4, "");
        if (StrUtil.isBlank(brandNo) || StrUtil.isBlank(itemCode) ||  StrUtil.isBlank(sizeNo)) {
            LOGGER.error("第{}行数据错误, 品牌编码、商品编码、货号不能为空", index);
            errorList.add("第" + index + "行数据错误, 品牌编码、商品编码、货号不能为空");
            return Pair.of(false, null);
        }
        Integer adjustQty = MapUtils.getInteger(map, 5, null);
        if (adjustQty == null) {
            LOGGER.error("第{}行数据错误, 调整数量不能为空", index);
            errorList.add("第" + index + "行数据错误, 调整数量不能为空");
            return Pair.of(false, null);
        }



        InventoryActiveLockDtl dtl = inventoryActiveLockDtlManager.findByParam(Query.Where("billNo", adjust.getRefBillNo())
                .and("storeNo", storeNo)
                .and("orderUnitNo", orderUnitNo)
                .and("itemCode", itemCode)
                .and("sizeNo", sizeNo)
                .and("brandNo", brandNo));
        // 校验
        if (Objects.equals(this.adjust.getAdjustType(), AdjustTypeEnums.AUDIT_SUCCESS.getValue())) {
            if (dtl == null) {
                LOGGER.error("第{}行数据错误, 锁库活动明细不存在", index);
                errorList.add("第" + index + "行数据错误, 锁库活动明细不存在");
                return Pair.of(false, null);
            }
        } else if (Objects.equals(this.adjust.getAdjustType(), AdjustTypeEnums.ADD.getValue()) && dtl == null) {
            IcsActiveLockAdjustDtl.IcsActiveLockAdjustDtlBuilder adjustDtlBuilder = new IcsActiveLockAdjustDtl().build();
            adjustDtlBuilder.storeNo(storeNo).orderUnitNo(orderUnitNo).itemCode(itemCode).sizeNo(sizeNo).brandNo(brandNo);
            try {
                dtl = convertImportDtl(adjustDtlBuilder.object());
            } catch (Exception e) {
                LOGGER.error("第{}行数据错误, {}", index, e.getMessage());
                errorList.add("第" + index + "行数据错误, " + e.getMessage());
                return Pair.of(false, null);
            }
        }
        Assert.notNull(dtl, "锁库活动明细不存在");
        return Pair.of(true, dtl);
    }

    @Override
    public List<IcsActiveLockAdjustDtl> checkProcess() {
        LOGGER.info("锁库活动调整单数据校验, billNo={}", billNo);
        initAdjust();
        Assert.notNull(adjust, "未找到调整单据");
        initAdjustDtlList();
        Assert.notEmpty(adjustDtlList, "未找到调整单明细数据");
        addOrSubtract(adjust.getAdjustType()).addOrSubtractHandle(() -> {
            batchProcess(adjustDtlList, 200, this::addInventoryQty);
        }, () -> {
            batchProcess(adjustDtlList, 200, this::subtractInventoryQty);
        });
        return adjustDtlList.stream().filter(dtl -> StrUtil.isNotBlank(dtl.getVerifyResult())).collect(Collectors.toList());
    }

    private void subtractInventoryQty(List<IcsActiveLockAdjustDtl> icsActiveLockAdjustDtls) {
        IcsActiveLockAdjustDtl dtl = icsActiveLockAdjustDtls.get(0);
        List<String> skuNoList = icsActiveLockAdjustDtls.stream().map(IcsActiveLockAdjustDtl::getSkuNo).distinct().collect(Collectors.toList());
        List<InventoryActiveLockDtl> activeLockDtlList = inventoryActiveLockDtlManager.findBalanceLockQtyInOnStore(MapBuilder.<String, Object>create().put(
                        "storeNo", dtl.getStoreNo()
                ).put("orderUnitNo", dtl.getOrderUnitNo())
                .put("skuNoList", skuNoList)
                .put("billNo", adjust.getRefBillNo())
                .build());
        if (CollectionUtils.isEmpty(activeLockDtlList)) {
            icsActiveLockAdjustDtls.forEach(adjustDtl -> {
                adjustDtl.setVerifyResult("异常");
                adjustDtl.setErrorMessage(String.format("机构【%s】,货管【%s】, 商品【%s】, 尺码【%s】, 剩余锁库库存%s<扣减数量%s", adjustDtl.getStoreNo(), adjustDtl.getOrderUnitNo(), adjustDtl.getItemCode(), adjustDtl.getSizeNo(), 0, adjustDtl.getAdjustQty()));
            });
            return;
        }
        Map<String, InventoryActiveLockDtl> groupMap = activeLockDtlList.stream().collect(Collectors.toMap(InventoryActiveLockDtl::getUniqueKey, Function.identity(), (d1, d2) ->  d2));
        icsActiveLockAdjustDtls.forEach(adjustDtl -> compareInventoryQty(adjustDtl, Optional.of(groupMap.get(adjustDtl.getUniqueKey())).map(InventoryActiveLockDtl::getBalanceLockQty).orElse(0)));
    }

    private void addInventoryQty(List<IcsActiveLockAdjustDtl> icsActiveLockAdjustDtls) {
        //puma活动锁库都是单机构货管
        IcsActiveLockAdjustDtl dtl = icsActiveLockAdjustDtls.get(0);
        List<String> skuNoList = icsActiveLockAdjustDtls.stream().map(IcsActiveLockAdjustDtl::getSkuNo).distinct().collect(Collectors.toList());
        List<InternetAvailableInventory> availableInventoryList = inventorySearchService.findAvailableInOneStore(MapBuilder.<String, Object>create().put(
                        "storeNo", dtl.getStoreNo()
                ).put("orderUnitNo", dtl.getOrderUnitNo())
                .put("skuNoList", skuNoList)
                .put("merchantsCode", merchantCodeEnum().getCode())
                .put("terminal", channelTypeEnum().getType())
                .build());
        if (CollectionUtils.isEmpty(availableInventoryList)) {
            icsActiveLockAdjustDtls.forEach(adjustDtl -> {
                adjustDtl.setVerifyResult("异常");
                adjustDtl.setErrorMessage(String.format("机构【%s】,货管【%s】, 商品【%s】, 尺码【%s】, 可用库存%s<补货数量%s", adjustDtl.getStoreNo(), adjustDtl.getOrderUnitNo(), adjustDtl.getItemCode(), adjustDtl.getSizeNo(), 0, adjustDtl.getAdjustQty()));
            });
            return;
        }
        Map<String, InternetAvailableInventory> groupMap = availableInventoryList.stream().collect(Collectors.toMap(InternetAvailableInventory::getUniqueKey, Function.identity(), (d1, d2) -> d2));
        icsActiveLockAdjustDtls.forEach(adjustDtl -> compareInventoryQty(adjustDtl, Optional.of(groupMap.get(adjustDtl.getUniqueKey())).map(InternetAvailableInventory::getAvailableQty).orElse(0)));
    }

    private void compareInventoryQty(IcsActiveLockAdjustDtl adjustDtl, int compareQty) {
        int compareResult = Integer.compare(compareQty, adjustDtl.getAdjustQty());
        if (compareResult < 0) {
            adjustDtl.setVerifyResult("异常");
            adjustDtl.setErrorMessage(String.format("机构【%s】,货管【%s】, 商品【%s】, 尺码【%s】, %s库存%s<补货数量%s", adjustDtl.getStoreNo(), adjustDtl.getOrderUnitNo(), adjustDtl.getItemCode(), adjustDtl.getSizeNo(), adjust.getAdjustType()  == 1 ? "可用" : "剩余锁库", compareQty, adjustDtl.getAdjustQty()));
        }
    }
}