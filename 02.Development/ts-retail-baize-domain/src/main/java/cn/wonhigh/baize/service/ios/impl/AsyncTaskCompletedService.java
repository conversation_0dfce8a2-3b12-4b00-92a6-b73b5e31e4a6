package cn.wonhigh.baize.service.ios.impl;

import cn.wonhigh.baize.model.entity.ios.AsyncTaskCompleted;
import cn.wonhigh.baize.repository.ios.AsyncTaskCompletedRepository;
import cn.wonhigh.baize.service.ios.IAsyncTaskCompletedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: wudong
 * @create: 2025-07-14 11:19
 **/
@Service
public class AsyncTaskCompletedService implements IAsyncTaskCompletedService {
    @Autowired
    private AsyncTaskCompletedRepository asyncTaskCompletedRepository;


    @Override
    public AsyncTaskCompleted getLastCreateByBillNos(List<String> billNos) {
        return asyncTaskCompletedRepository.getLastCreateByBillNos(1021, billNos);
    }
}
