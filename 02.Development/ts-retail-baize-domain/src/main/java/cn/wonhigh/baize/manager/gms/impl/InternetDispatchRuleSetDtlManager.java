/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetDtl;
import cn.wonhigh.baize.service.gms.IInternetDispatchRuleSetDtlService;
import cn.wonhigh.baize.manager.gms.IInternetDispatchRuleSetDtlManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class InternetDispatchRuleSetDtlManager extends BaseManager<InternetDispatchRuleSetDtl,Long> implements IInternetDispatchRuleSetDtlManager{
    @Autowired
    private IInternetDispatchRuleSetDtlService service;

    protected IService<InternetDispatchRuleSetDtl,Long> getService(){
        return service;
    }

    
}