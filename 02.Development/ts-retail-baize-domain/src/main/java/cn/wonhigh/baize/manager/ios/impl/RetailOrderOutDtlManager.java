/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl;
import cn.wonhigh.baize.service.ios.IRetailOrderOutDtlService;
import cn.wonhigh.baize.manager.ios.IRetailOrderOutDtlManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


@Service
public class RetailOrderOutDtlManager extends BaseManager<RetailOrderOutDtl,String> implements IRetailOrderOutDtlManager{
    @Autowired
    private IRetailOrderOutDtlService service;


    protected IService<RetailOrderOutDtl,String> getService(){
        return service;
    }


    @Override
    public List<RetailOrderOutDtl> selectPageForReport(Query query, Pagenation pagenation) {
        return this.service.selectPageForReport(query, pagenation);
    }

    @Override
    public int selectPageForReportCount(Query query) {
        return this.service.selectPageForReportCount(query);
    }
}