package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.model.dto.IcsInventoryOmsLockDto;
import cn.wonhigh.baize.model.entity.gms.IcsInventoryOmsLock;
import cn.wonhigh.baize.service.gms.IIcsInventoryOmsLockService;
import cn.wonhigh.baize.manager.gms.IIcsInventoryOmsLockManager;

import org.springframework.stereotype.Service;
import topmall.framework.service.IService;
import topmall.framework.manager.BaseManager;

import javax.annotation.Resource;
import java.util.List;


/**
 * oms锁库(IcsInventoryOmsLock)
 *
 * <AUTHOR>
 * @since 2024-12-28 10:04:34
 */
@Service("icsInventoryOmsLockManager")
public class IcsInventoryOmsLockManager extends BaseManager<IcsInventoryOmsLock, String> implements IIcsInventoryOmsLockManager {

    @Resource
    private IIcsInventoryOmsLockService service;

    protected IService<IcsInventoryOmsLock, String> getService() {
        return service;
    }

    @Override
    public Integer updateStock(List<IcsInventoryOmsLockDto> params) {
        return service.updateStock(params);
    }
}