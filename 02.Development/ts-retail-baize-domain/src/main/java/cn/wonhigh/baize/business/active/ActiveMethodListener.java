package cn.wonhigh.baize.business.active;

import cn.mercury.security.IUser;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import reactor.util.function.Tuple3;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 监听器
 * @date 2025/6/27 15:28
 */
@FunctionalInterface
public interface ActiveMethodListener {
    void onEvent(String methodName, boolean success, Throwable throwable, Tuple3<IcsActiveLockAdjust, IUser, String> tuple);
}
