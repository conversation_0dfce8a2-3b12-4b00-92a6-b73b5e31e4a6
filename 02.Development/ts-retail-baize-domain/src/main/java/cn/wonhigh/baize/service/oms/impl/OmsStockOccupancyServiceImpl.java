package cn.wonhigh.baize.service.oms.impl;

import cn.wonhigh.baize.model.dto.OmsStockOccupancyDTO;
import cn.wonhigh.baize.model.entity.oms.OmsStockOccupancy;
import cn.wonhigh.baize.repository.oms.IOmsStockOccupancyRepository;
import cn.wonhigh.baize.service.oms.IOmsStockOccupancyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import java.util.Collections;
import java.util.List;

/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.service.oms.impl
 * @Project：ts-retail-baize
 * @name：OmsStockOccupancyServiceImpl
 * @Date：2024/12/27 18:37
 * @Filename：OmsStockOccupancyServiceImpl
 * @description：
 */
@Service
public class OmsStockOccupancyServiceImpl extends BaseService<OmsStockOccupancy,Long> implements IOmsStockOccupancyService {

    @Autowired
    private IOmsStockOccupancyRepository omsStockOccupancyRepository;

    @Override
    protected IRepository<OmsStockOccupancy, Long> getRepository() {
        return omsStockOccupancyRepository;
    }

    @Override
    public OmsStockOccupancyDTO queryLockStockQty(String warehouseId, String skuId) {
        return omsStockOccupancyRepository.queryLockStockQty(warehouseId, skuId);
    }
}
