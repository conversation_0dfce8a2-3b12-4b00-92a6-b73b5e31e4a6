/** q **/
package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import topmall.framework.service.IService;
import java.util.List;

public interface IIcsInventorySyncConfigService extends IService<IcsInventorySyncConfig,String> {
      
    Integer batchInsert(List<IcsInventorySyncConfig> list);
    
    List<IcsInventorySyncConfig> selectByIds(List<Integer> ids);

    List<InternetVirtualWarehouseInfo> selectVstoreListByParams(Query query);

    Integer selectShopCountByParams(Query var1);

    List<IcsInventorySyncConfig> selectShopPageByParams(Query query, Pagenation pagenation);

    List<IcsInventorySyncConfig> selectByPageWithDistinct(Query query, Pagenation page);

    List<IcsInventorySyncConfig> selectWithDistinct(Query query);

    Integer selectByPageWithDistinctCount(Query query);

    Integer countByChannel(Query var1);

    List<IcsInventorySyncConfig> pageByChannel(Query var1, Pagenation var2);

    List<IcsInventorySyncConfig> selectChannelVstoreInfo(Query var1);

}