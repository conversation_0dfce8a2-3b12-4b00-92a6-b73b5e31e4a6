/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;
import java.util.Map;

@Mapper
public interface OrderSourceTerminalConfigRepository extends IRepository<OrderSourceTerminalConfig,String> {

    List<OrderSourceTerminalConfig> selectShopPageByParams(@Param("params") Map<String, Object> map, @Param("page") Pagenation pagenation);

    Integer selectShopCountByParams(@Param("params") Map<String, Object> map);
}