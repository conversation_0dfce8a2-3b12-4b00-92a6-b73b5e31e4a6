/** q **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct;
import cn.wonhigh.baize.repository.gms.CommodityCorpMatchProductRepository;
import cn.wonhigh.baize.service.gms.ICommodityCorpMatchProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class CommodityCorpMatchProductService extends BaseService<CommodityCorpMatchProduct,String> implements ICommodityCorpMatchProductService {
    @Autowired
    private CommodityCorpMatchProductRepository repository;

    protected IRepository<CommodityCorpMatchProduct,String> getRepository(){
        return repository;
    }
      
	public List<CommodityCorpMatchProduct> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<CommodityCorpMatchProduct> list) {
    	return repository.batchInsert(list);
    }


    @Override
    public List<CommodityCorpMatchProduct> selectItemSkuByParams(Map<String, Object> params) {
        return repository.selectItemSkuByParams(params);
    }
}