/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;
import java.util.List;
import java.util.Map;

@Mapper
public interface IcsInventorySyncConfigRepository extends IRepository<IcsInventorySyncConfig,String> {
    
    Integer batchInsert(@Param("list") List<IcsInventorySyncConfig> list);
    
    List<IcsInventorySyncConfig> selectByIds(@Param("list") List<Integer> ids);

    Integer selectShopCountByParams(@Param("params")  Map<String,Object> query);

    List<IcsInventorySyncConfig> selectShopPageByParams(@Param("params") Map<String,Object> query, @Param("page") Pagenation pagenation);

    List<InternetVirtualWarehouseInfo> selectVstoreListByParams(@Param("params")  Map<String,Object> query);

    List<IcsInventorySyncConfig> selectByPageWithDistinct(@Param("params") Map<String, Object> params,@Param("page") Pagenation page);

    List<IcsInventorySyncConfig> selectWithDistinct(@Param("params") Map<String, Object> params);

    Integer selectByPageWithDistinctCount(@Param("params") Map<String, Object> params);

    List<IcsInventorySyncConfig> pageByChannel(@Param("params") Map<String, Object> var1, @Param("page") Pagenation var2, @Param("orderby") String var3);

    Integer countByChannel(@Param("params") Map<String, Object> var1);

    List<IcsInventorySyncConfig> selectChannelVstoreInfo(@Param("params") Map<String, Object> var1);

}