/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
@Mapper
public interface IcsVirtualWarehouseProvincePriorityRepository extends IRepository<IcsVirtualWarehouseProvincePriority,String> {
    
    Integer batchInsert(@Param("list") List<IcsVirtualWarehouseProvincePriority> list);
    
    List<IcsVirtualWarehouseProvincePriority> selectByIds(@Param("list") List<Integer> ids);
}