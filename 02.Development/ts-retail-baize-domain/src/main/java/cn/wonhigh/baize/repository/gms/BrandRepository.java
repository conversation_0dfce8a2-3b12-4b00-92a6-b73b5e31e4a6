/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.Brand;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
@Mapper
public interface BrandRepository extends IRepository<Brand,Integer> {
    
    Brand findByUnique(@Param("brandNo") String brandNo);

    Integer deleteByUnique(@Param("brandNo") String brandNo);

    List<Brand> selectByUniques(@Param("list") List<String> brandNos);
    
    Integer batchInsert(@Param("list") List<Brand> list);
    
    List<Brand> selectByIds(@Param("list") List<Integer> ids);
}