/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrder;
import cn.wonhigh.baize.repository.ios.InternetOrderRepository;

import cn.wonhigh.baize.service.ios.IInternetOrderService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetOrderService extends BaseService<InternetOrder,String> implements  IInternetOrderService{
    @Autowired
    private InternetOrderRepository repository;

    protected IRepository<InternetOrder,String> getRepository(){
        return repository;
    }

    
    public InternetOrder findByUnique(String orderSubNo){
        return repository.findByUnique(orderSubNo);
    }

    public Integer deleteByUnique(String orderSubNo){
        return repository.deleteByUnique(orderSubNo);
    }

    public Integer insertForUpdate(InternetOrder entry){
       return repository.insertForUpdate(entry);
    }
    
}