package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig;
import topmall.framework.service.IService;

import java.util.List;

public interface IStockDistirbutionConfigService extends IService<StockDistirbutionConfig, String> {

    Integer countByShop(Query query);

    List<StockDistirbutionConfig> pageByShop(Query var1, Pagenation var2);
	
}