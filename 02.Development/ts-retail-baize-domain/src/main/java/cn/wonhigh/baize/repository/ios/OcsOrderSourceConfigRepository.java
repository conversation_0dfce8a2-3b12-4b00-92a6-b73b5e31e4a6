/**  **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

@Mapper
public interface OcsOrderSourceConfigRepository extends IRepository<OcsOrderSourceConfig,String> {
    
    public OcsOrderSourceConfig findByUnique(String shopNo);

    public Integer deleteByUnique(String shopNo);

    public Integer insertForUpdate(OcsOrderSourceConfig entry);

    
}