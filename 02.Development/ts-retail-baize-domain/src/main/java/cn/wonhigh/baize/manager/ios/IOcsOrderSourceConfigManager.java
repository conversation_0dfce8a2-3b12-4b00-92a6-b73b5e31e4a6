/**  **/
package cn.wonhigh.baize.manager.ios;

import cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig;

import cn.mercury.manager.IManager;

public interface IOcsOrderSourceConfigManager extends IManager<OcsOrderSourceConfig,String>{

    
    public OcsOrderSourceConfig findByUnique(String shopNo) ;

    public Integer deleteByUnique(String shopNo);

    public Integer insertForUpdate(OcsOrderSourceConfig entry);
    
}