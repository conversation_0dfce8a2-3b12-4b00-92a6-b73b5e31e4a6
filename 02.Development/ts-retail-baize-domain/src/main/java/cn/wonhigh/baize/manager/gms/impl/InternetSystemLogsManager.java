/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.Query;
import cn.mercury.basic.query.Statement;
import cn.mercury.utils.DateUtil;
import cn.wonhigh.baize.model.enums.InventoryActiveLockOpscodeEnum;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InternetSystemLogs;
import cn.wonhigh.baize.service.gms.IInternetSystemLogsService;
import cn.wonhigh.baize.manager.gms.IInternetSystemLogsManager;
import org.springframework.util.Assert;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class InternetSystemLogsManager extends BaseManager<InternetSystemLogs,Long> implements IInternetSystemLogsManager{
    @Autowired
    private IInternetSystemLogsService service;

    protected IService<InternetSystemLogs,Long> getService(){
        return service;
    }

    @Override
    public List<Map<String, Object>> selectSystemLogsByKey2(String billNo, List<Integer> sysCodes) {
        Assert.isTrue(StrUtil.isNotBlank(billNo), "单号不能为空");
        Assert.notEmpty(sysCodes, "系统编码不能为空");
        List<InternetSystemLogs> list = service.selectByParams(Query.Where("keyword2", billNo)
                        .and(new Statement("syscode", "In", sysCodes))
                .orderby("create_time", false)
        );
        return getMaps(list, InventoryActiveLockOpscodeEnum::getCustomizeDesc);
    }



    private List<Map<String, Object>> getMaps(List<InternetSystemLogs> list, Function<String, String> provider) {
        return Optional.ofNullable(list)
                .orElse(Collections.emptyList()).stream().map(item -> {
                    MapBuilder<String, Object> builder = MapBuilder.create();
                    builder.put("optUser", item.getCreateUser());
                    builder.put("optTime", DateUtil.format(item.getCreateTime(), DateUtil.LONG_DATE_FORMAT));
                    builder.put("optType", provider.apply(item.getOpscode()));
                    builder.put("optInfo", item.getRemark());
                    return builder.build();
                }).collect(Collectors.toList());
    }


	@Override
	public List<Map<String, Object>> selectSystemLogsByKey1(String billNo, int menuType, Function<String, String> optFun) {
		Assert.isTrue(StrUtil.isNotBlank(billNo), "单号不能为空");
        List<InternetSystemLogs> list = service.selectByParams(Query.Where("keyword1", billNo).and("syscode", menuType));
        return getMaps(list, optFun);
	}

}