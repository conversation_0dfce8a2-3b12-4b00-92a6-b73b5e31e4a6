/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderReturnDtl;
import cn.wonhigh.baize.repository.ios.InternetOrderReturnDtlRepository;

import cn.wonhigh.baize.service.ios.IInternetOrderReturnDtlService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetOrderReturnDtlService extends BaseService<InternetOrderReturnDtl,String> implements  IInternetOrderReturnDtlService{
    @Autowired
    private InternetOrderReturnDtlRepository repository;

    protected IRepository<InternetOrderReturnDtl,String> getRepository(){
        return repository;
    }

    
}