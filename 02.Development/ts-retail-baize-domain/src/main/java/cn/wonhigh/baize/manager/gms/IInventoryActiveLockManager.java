/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;

import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;

import java.util.List;

public interface IInventoryActiveLockManager extends IManager<InventoryActiveLock,String>{
    
    InventoryActiveLock findByUnique(String billNo) ;

    Integer deleteByUnique(String billNo);

    List<InventoryActiveLock> selectByUniques(List<String> billNos);
    
    Integer batchInsert(List<InventoryActiveLock> list);
    
    List<InventoryActiveLock> selectByIds(List<Integer> ids);


    /**
     * 审批活动活动
     * @param activeLock
     */
    void approve(InventoryActiveLock activeLock);

    /**
     * 终止锁库活动
     *
     * 只会终止生效中的锁库活动
     * @param activeLock
     * @throws ManagerException
     */
    void stop(InventoryActiveLock activeLock) throws ManagerException;

    /**
     * 激活锁库活动, 进行库存预占
     * @see cn.wonhigh.retail.isp.manager.gms.InventoryActiveLockManager#disposeEndActiveLockForOther 释放预占
     * @param activeLock
     * @param activeLockDtls
     * @throws Exception 系统错误
     *
     */
    void activeLockOccupiedInventory(InventoryActiveLock activeLock, List<InventoryActiveLockDtl> activeLockDtls) throws Exception;

    /**
     * 禁用锁库活动, 释放库存预占 (目前在ISP , 有个定时任务在 根据时间)
     * @see cn.wonhigh.retail.isp.manager.gms.InventoryActiveLockManager#disposeEndActiveLockForOther
     * @param activeLock
     * @param activeLockDtls
     * @throws Exception 系统错误
     */
    @Deprecated
    void disableLockOccupiedInventory(InventoryActiveLock activeLock, List<? extends InventoryActiveLockDtl> activeLockDtls) throws Exception;

    /**
     * 生效同步库存
     * @param inventoryActiveLock
     * @param activeLockDtls
     */
    void syncInventory(InventoryActiveLock inventoryActiveLock, List<? extends InventoryActiveLockDtl> activeLockDtls);
}