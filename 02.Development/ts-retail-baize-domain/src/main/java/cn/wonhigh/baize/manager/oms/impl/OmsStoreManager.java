package cn.wonhigh.baize.manager.oms.impl;

import cn.wonhigh.baize.manager.oms.IOmsStoreManager;
import cn.wonhigh.baize.model.entity.oms.OmsStore;
import cn.wonhigh.baize.service.oms.IOmsStoreService;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import javax.annotation.Resource;


/**
 * (OmsStore)
 *
 * <AUTHOR>
 * @since 2024-10-29 22:19:20
 */
@Service("omsStoreManager")
public class OmsStoreManager extends BaseManager<OmsStore, Long> implements IOmsStoreManager {

    @Resource
    private IOmsStoreService service;

    protected IService<OmsStore, Long> getService() {
        return service;
    }

}