/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.repository.gms.InventoryActiveLockRepository;
import cn.wonhigh.baize.service.gms.IInventoryActiveLockService;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class InventoryActiveLockService extends BaseService<InventoryActiveLock,String> implements IInventoryActiveLockService {
    @Autowired
    private InventoryActiveLockRepository repository;

    protected IRepository<InventoryActiveLock,String> getRepository(){
        return repository;
    }
    
    public InventoryActiveLock findByUnique(String billNo){
        return repository.findByUnique(billNo);
    }

    public Integer deleteByUnique(String billNo){
        return repository.deleteByUnique(billNo);
    }

    public List<InventoryActiveLock> selectByUniques(List<String> billNos) {
        return repository.selectByUniques(billNos);
    }
      
	public List<InventoryActiveLock> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<InventoryActiveLock> list) {
    	return repository.batchInsert(list);
    }

}