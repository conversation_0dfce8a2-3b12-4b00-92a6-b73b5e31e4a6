/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.Item;
import cn.wonhigh.baize.model.entity.gms.ItemBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;
import java.util.List;
import java.util.Map;

@Mapper
public interface ItemRepository extends IRepository<Item,Integer> {
    List<ItemBaseInfo> queryItemByParams(@Param("params") Map<String, Object> map);

}