/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderReturn;
import cn.wonhigh.baize.repository.ios.InternetOrderReturnRepository;

import cn.wonhigh.baize.service.ios.IInternetOrderReturnService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetOrderReturnService extends BaseService<InternetOrderReturn,String> implements  IInternetOrderReturnService{
    @Autowired
    private InternetOrderReturnRepository repository;

    protected IRepository<InternetOrderReturn,String> getRepository(){
        return repository;
    }

    
}