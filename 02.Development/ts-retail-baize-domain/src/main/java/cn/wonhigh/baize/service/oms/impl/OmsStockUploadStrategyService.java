package cn.wonhigh.baize.service.oms.impl;

import cn.wonhigh.baize.model.entity.oms.OmsStockUploadStrategy;
import cn.wonhigh.baize.repository.oms.OmsStockUploadStrategyRepository;
import cn.wonhigh.baize.service.oms.IOmsStockUploadStrategyService;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;
import java.util.List;

/**
 * (OmsStockUploadStrategy)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-29 23:06:12
 */
@Service("omsStockUploadStrategyService")
public class OmsStockUploadStrategyService extends BaseService<OmsStockUploadStrategy, Long> implements IOmsStockUploadStrategyService {
    @Resource
    private OmsStockUploadStrategyRepository repository;

    protected IRepository<OmsStockUploadStrategy, Long> getRepository() {
        return repository;
    }

    public List<OmsStockUploadStrategy> selectStrategyByVstoreId(List<Long> vstoreIds){
        return repository.selectStrategyByVstoreId(vstoreIds);
    }

}