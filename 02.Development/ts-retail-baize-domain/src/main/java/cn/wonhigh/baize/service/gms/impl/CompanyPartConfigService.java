/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.CompanyPartConfig;
import cn.wonhigh.baize.repository.gms.CompanyPartConfigRepository;

import cn.wonhigh.baize.service.gms.ICompanyPartConfigService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@Service
public class CompanyPartConfigService extends BaseService<CompanyPartConfig,String> implements  ICompanyPartConfigService{
    @Autowired
    private CompanyPartConfigRepository repository;

    protected IRepository<CompanyPartConfig,String> getRepository(){
        return repository;
    }
      
	public List<CompanyPartConfig> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<CompanyPartConfig> list) {
    	return repository.batchInsert(list);
    }

    @Override
    public List<CompanyPartConfig> getShardingFlagByStoreNo(String storeNo) {
        return repository.getShardingFlagByStoreNo(storeNo);
    }

    @Override
    public String getShardingFlagByOrderUnitNo(String orderUnitNo) {
        return repository.getShardingFlagByOrderUnitNo(orderUnitNo);
    }
}