/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.OrderUnit;
import cn.wonhigh.baize.repository.gms.OrderUnitRepository;

import cn.wonhigh.baize.service.gms.IOrderUnitService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class OrderUnitService extends BaseService<OrderUnit,Integer> implements  IOrderUnitService{
    @Autowired
    private OrderUnitRepository repository;

    protected IRepository<OrderUnit,Integer> getRepository(){
        return repository;
    }

    
    public OrderUnit findByUnique(String orderUnitNo){
        return repository.findByUnique(orderUnitNo);
    }

    public Integer deleteByUnique(String orderUnitNo){
        return repository.deleteByUnique(orderUnitNo);
    }

    public Integer insertForUpdate(OrderUnit entry){
       return repository.insertForUpdate(entry);
    }
    
}