package cn.wonhigh.baize.business.virtualwarehousescope;

import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.manager.gms.*;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeStatusChangeDto;
import cn.wonhigh.baize.model.entity.gms.*;
import cn.wonhigh.baize.model.enums.StoreTypeEnums;
import cn.wonhigh.baize.utils.common.QueryUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ValidationException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ChangeStatusValidateService extends AbstractValidateDataService{

    private IOrgUnitBrandRelManager orgUnitBrandRelManager;

    private IOrderUnitManager orderUnitManager;

    private IInternetVirtualWarehouseScopeManager internetVirtualWarehouseScopeManager;

    public static final Logger LOGGER = LoggerFactory.getLogger(ChangeStatusValidateService.class);

    public ChangeStatusValidateService(InternetVirtualWarehouseInfo internetinternetVirtualWarehouseInfo) {
        super(internetinternetVirtualWarehouseInfo);
        this.internetVirtualWarehouseScopeManager = SpringContext.getBean(IInternetVirtualWarehouseScopeManager.class);
        this.orderUnitManager = SpringContext.getBean(IOrderUnitManager.class);
        this.orgUnitBrandRelManager = SpringContext.getBean(IOrgUnitBrandRelManager.class);
    }

    @Override
    public <T> ValidateResult validate(T t) {
        if (!(t instanceof InternetVirtualWarehouseScopeStatusChangeDto)) {
            return ValidateResult.error("类型错误");
        }

        try {
            InternetVirtualWarehouseScopeStatusChangeDto statusChangeDto = (InternetVirtualWarehouseScopeStatusChangeDto) t;
            //禁用状态
            if(statusChangeDto.getStatus() == 0){
                disableValidate(statusChangeDto);
            } else {
                enableValidate(statusChangeDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ValidateResult.error(e.getMessage());
        }
        return ValidateResult.success();
    }

    //启用校验
    void enableValidate(InternetVirtualWarehouseScopeStatusChangeDto statusChangeDto){
        InternetVirtualWarehouseScopeSave scopeSaveDto = new InternetVirtualWarehouseScopeSave();
        scopeSaveDto.setVstoreCode(statusChangeDto.getVstoreCode());
        for (InternetVirtualWarehouseScopeStatusChangeDto.Dtl validateDto : statusChangeDto.getDtlData()) {
            InternetVirtualWarehouseScope scope = validateScope(validateDto);
            validateDto.setStoreNo(scope.getStoreNo());
            validateDto.setOrderUnitNo(scope.getOrderUnitNo());
            validateDto.setStoreType(scope.getStoreType());
            validateDto.setInventoryType(scope.getInventoryType());

            if (1 == scope.getMoreStoreFlag() && StringUtils.isNotEmpty(scope.getStoreNo())) {
                throw new ValidationException("多机构类型不能有机构编码");
            }
            if (1 == scope.getMoreStoreFlag() && scope.getStoreType() != 21) {
                throw new ValidationException("多机构，类型只能是21");
            }
            if (0 == scope.getMoreStoreFlag() && StringUtils.isEmpty(scope.getStoreNo())) {
                throw new ValidationException("单机构类型机构编码不能为空");
            }

            // 校验货管编号是否存在
            OrderUnit orderUnit = orderUnitManager.findByUnique(validateDto.getOrderUnitNo());
            if (orderUnit == null) {
                throw new ValidationException("货管编号" + validateDto.getOrderUnitNo() + ", 不存在");
            }

            // 批发货管，不能添加到虚拟仓库存范围
            if (orderUnit.getType() == 1) {
                throw new ValidationException("批发货管，不能添加到虚拟仓库存范围,orderUnitNo:" + validateDto.getOrderUnitNo());
            }

            //如填写“机构编码“，对应的“货管编码”必须正确
            Map<String, Object> params1 = new HashMap<>();
            params1.put("storeNo", validateDto.getStoreNo());
            params1.put("orderUnitNo", validateDto.getOrderUnitNo());
            params1.put("status", 1);
            List<OrgUnitBrandRel> orgList = orgUnitBrandRelManager.selectByParams(QueryUtil.mapToQuery(params1));
            if (orgList == null || orgList.isEmpty()) {
                throw new ValidationException("货管编码" + validateDto.getOrderUnitNo() + "和机构编码" + validateDto.getStoreNo() + "不一致，需填写正确货管编码");
            }

            // 网销业务
            if (VmBusinessTypeEnums.INTERNET.getType() == internetVirtualWarehouseInfo.getBusinessType()) {
                if (StoreTypeEnums.SHOP.getType().equals(validateDto.getStoreType() + "") && !"1".equals(validateDto.getInventoryType() + "")) {
                    throw new ValidationException("机构类型为店时，库存范围只能是共享");
                }
            } else {
                // 特殊业务
                if (!"1".equals(validateDto.getInventoryType() + "")) {
                    throw new ValidationException("特殊业务类型聚合仓，库存范围只能是共享");
                }
            }
            scopeSaveDto.getAddDtls().add(InternetVirtualWarehouseScopeSave.buildDtl(scope.getStoreNo(), scope.getStoreName(), scope.getStoreType(),
                    scope.getOrderUnitNo(), scope.getOrderUnitName(), 1));
        }

        SaveScopeValidateService validateService = new SaveScopeValidateService(internetVirtualWarehouseInfo);
        ValidateResult result = validateService.validate(scopeSaveDto);
        if (!result.isSuccess()) {
            throw new ValidationException(result.getMsg());
        }
    }

    //禁用校验
    void disableValidate(InternetVirtualWarehouseScopeStatusChangeDto statusChangeDto){
        for (InternetVirtualWarehouseScopeStatusChangeDto.Dtl dtl : statusChangeDto.getDtlData()) {
            InternetVirtualWarehouseScope scope = validateScope(dtl);
            dtl.setStoreNo(scope.getStoreNo());
            dtl.setOrderUnitNo(scope.getOrderUnitNo());
            dtl.setStoreType(scope.getStoreType());
            dtl.setInventoryType(scope.getInventoryType());
        }
    }

    InternetVirtualWarehouseScope validateScope(InternetVirtualWarehouseScopeStatusChangeDto.Dtl validateDto){
        InternetVirtualWarehouseScope scope = internetVirtualWarehouseScopeManager.findByPrimaryKey(validateDto.getId());
        if(scope == null){
            throw new ValidationException("聚合仓范围不存在，机构：" + validateDto.getStoreNo() + ",货管：" + validateDto.getOrderUnitNo());
        }
        if(!internetVirtualWarehouseInfo.getVstoreCode().equals(scope.getVstoreCode())){
            throw new ValidationException("聚合仓:" +internetVirtualWarehouseInfo.getVstoreCode()+",范围不存在，机构：" + validateDto.getStoreNo() + ",货管：" + validateDto.getOrderUnitNo());
        }
        return scope;
    }

}
