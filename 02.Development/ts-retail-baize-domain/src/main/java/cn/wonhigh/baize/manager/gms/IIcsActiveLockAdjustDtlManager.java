/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;

import java.util.List;
import java.util.Map;

public interface IIcsActiveLockAdjustDtlManager extends IManager<IcsActiveLockAdjustDtl,String>{
    
    Integer batchInsert(List<IcsActiveLockAdjustDtl> list);
    
    List<IcsActiveLockAdjustDtl> selectByIds(List<Integer> ids);

    List<IcsActiveLockAdjustDtl> selectActiveDtlList(Map<String, Object> map, Pagenation page);

    /**
     * 批量保存或更新
     * @param dtlList
     */
    void batchInsertOrUpdate(List<IcsActiveLockAdjustDtl> dtlList);

    /**
     * 批量更新同步状态
     * @param adjustDtlList
     */
    void batchUpdateSyncStatus(List<IcsActiveLockAdjustDtl> adjustDtlList, int syncStatus);
}