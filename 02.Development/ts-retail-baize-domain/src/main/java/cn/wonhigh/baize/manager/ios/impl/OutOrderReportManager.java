package cn.wonhigh.baize.manager.ios.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.basic.query.Statement;
import cn.mercury.utils.DateUtil;
import cn.wonhigh.baize.manager.ios.*;
import cn.wonhigh.baize.model.dto.OutReportDTO;
import cn.wonhigh.baize.model.entity.ios.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 2023/2/27 9:47
 */
@Service
public class OutOrderReportManager implements IOutOrderReportManager {

	@Resource
	private IRetailOrderOutManager retailOrderOutManager;

	@Resource
	private IRetailOrderOutDtlManager retailOrderOutDtlManager;


	@Resource
	private IInternetOrderManager internetOrderManager;

	@Resource
	private IRetailOrderReturnManager retailOrderReturnManager;

	@Resource
	private IRetailOrderReturnDtlManager retailOrderReturnDtlManager;

	@Resource
	private IInternetOrderCancelApplyManager internetOrderCancelApplyManager;


	@Override
	@SuppressWarnings("unchecked")
	public <T> List<T> selectPage(Query p, Pagenation pagenation) {
		Assert.notNull(p);
		Assert.notNull(pagenation);

		List<RetailOrderOutDtl> retailOrderOutDtls = page(p, pagenation);
		if (retailOrderOutDtls == null) {
			return null;
		}


		List<RetailOrderOut> retailOrderOuts = Opt.ofEmptyAble(retailOrderOutDtls).get().stream().map(RetailOrderOutDtl::getRetailOrderOut).collect(Collectors.toList());

		List<String> orderSubNos = Opt.ofEmptyAble(retailOrderOuts).orElse(new ArrayList<>()).stream().map(RetailOrderOut::getOrderSubNo).collect(Collectors.toList());
		List<RetailOrderReturn> retailOrderReturns = getRetailOrderReturns(orderSubNos);
		List<RetailOrderReturnDtl> retailOrderReturnDtls = getRetailOrderReturnDtls(Opt.ofEmptyAble(retailOrderReturns).orElse(new ArrayList<>()).stream().map(RetailOrderReturn::getBillNo).collect(Collectors.toList()));
		List<InternetOrder> internetOrders = getInternetOrdersByRetailOutOrderDtlList(retailOrderOutDtls);
		List<InternetOrderCancelApply> internetOrderCancelApplies = getInternetOrderCancelApplies(orderSubNos);
		List<OutReportDTO> outReportDTOs = convertDataToOutReportDTO(retailOrderOutDtls, retailOrderOuts, internetOrders,
				retailOrderReturns, retailOrderReturnDtls, internetOrderCancelApplies);

		return (List<T>) outReportDTOs;
	}

	@Override
	public int selectCount(Query p) {
		Assert.notNull(p);
		return this.retailOrderOutDtlManager.selectPageForReportCount(p);
	}

	private List<OutReportDTO> convertDataToOutReportDTO(List<RetailOrderOutDtl> retailOrderOutDtls,
	                                                     List<RetailOrderOut> retailOrderOuts,
	                                                     List<InternetOrder> internetOrders,
	                                                     List<RetailOrderReturn> retailOrderReturns,
	                                                     List<RetailOrderReturnDtl> retailOrderReturnDtls,
	                                                     List<InternetOrderCancelApply> internetOrderCancelApplies
	) {

		List<OutReportDTO> reportDTOS = new ArrayList<>();
		for (RetailOrderOutDtl retailOrderOutDtl : retailOrderOutDtls) {
			RetailOrderOut retailOrderOut = Opt.ofEmptyAble(retailOrderOuts)
					.orElse(new ArrayList<>()).stream().filter(item -> StrUtil.equals(retailOrderOutDtl.getBillNo(), item.getBillNo())).findFirst().orElse(null);

			InternetOrder internetOrder = Opt.ofEmptyAble(internetOrders)
					.orElse(new ArrayList<>()).stream().filter(item -> StrUtil.equals(Opt.ofNullable(retailOrderOut)
									.map(RetailOrderOut::getOrderSubNo).orElse(null)
							, item.getOrderSubNo())).findFirst().orElse(null);

			RetailOrderReturn retailOrderReturn = Opt.ofEmptyAble(retailOrderReturns).orElse(new ArrayList<>()).stream()
					.filter(item -> StrUtil.equals(Opt.ofNullable(internetOrder).map(InternetOrder::getOrderSubNo).orElse(null), item.getOrderSubNo())
					).findFirst().orElse(null);

			RetailOrderReturnDtl retailOrderReturnDtl = Opt.ofEmptyAble(retailOrderReturnDtls).orElse(new ArrayList<>()).stream()
					.filter(item -> StrUtil.equals(Opt.ofNullable(retailOrderReturn).map(RetailOrderReturn::getBillNo).orElse(null), item.getBillNo())
					).filter(itemDtl -> StrUtil.equals(itemDtl.getSkuNo(), retailOrderOutDtl.getSkuNo())).findFirst().orElse(null);

			List<InternetOrderCancelApply> internetOrderCancelApplyList = Opt.ofEmptyAble(internetOrderCancelApplies)
					.orElse(new ArrayList<>())
					.stream().filter(item -> StrUtil.equals(Opt.ofNullable(retailOrderOut).map(RetailOrderOut::getOrderSubNo).orElse(null), item.getOrderSubNo()))
					.collect(Collectors.toList());

			reportDTOS.add(buildDTO(retailOrderOutDtl, retailOrderOut, internetOrder, retailOrderReturn, retailOrderReturnDtl, internetOrderCancelApplyList));
		}


		return reportDTOS;
	}


	private OutReportDTO buildDTO(RetailOrderOutDtl retailOrderOutDtl,
	                              RetailOrderOut retailOrderOut,
	                              InternetOrder internetOrder,
								  RetailOrderReturn retailOrderReturn,
	                              RetailOrderReturnDtl retailOrderReturnDtl,
	                              List<InternetOrderCancelApply> internetOrderCancelApplyList
	) {

		Opt<RetailOrderOutDtl> retailOrderOutDtlStream = Opt.ofNullable(retailOrderOutDtl);
		Opt<RetailOrderOut> retailOrderOutStream = Opt.ofNullable(retailOrderOut);
		Opt<InternetOrder> internetOrderStream = Opt.ofNullable(internetOrder);
		//Opt<RetailOrderReturn> retailOrderReturnStream = Opt.ofNullable(retailOrderReturn);
		Opt<RetailOrderReturnDtl> retailOrderReturnDtlStream = Opt.ofNullable(retailOrderReturnDtl);
		Opt<List<InternetOrderCancelApply>> internetOrderCancelApplyListStream = Opt.ofEmptyAble(internetOrderCancelApplyList);

		OutReportDTO outReportDTO = new OutReportDTO();


		outReportDTO.setId(retailOrderOutDtlStream.map(RetailOrderOutDtl::getId).orElse(null));
		outReportDTO.setBillNo(retailOrderOutDtlStream.map(RetailOrderOutDtl::getBillNo).orElse(null));
		outReportDTO.setRefBillNo(retailOrderOutStream.map(RetailOrderOut::getRefBillNo).orElse(null));
		outReportDTO.setOrderSubNo(retailOrderOutStream.map(RetailOrderOut::getOrderSubNo).orElse(null));
		outReportDTO.setOrderSourceNo(internetOrderStream.map(InternetOrder::getOrderSourceNo).orElse(null));
		outReportDTO.setOutOrderId(internetOrderStream.map(InternetOrder::getOutOrderId).orElse(null));
		outReportDTO.setOriginPlatformName(internetOrderStream.map(InternetOrder::getOriginPlatformName).orElse(null));
		outReportDTO.setSendStoreName(retailOrderOutStream.map(RetailOrderOut::getSendStoreName).orElse(null));
		outReportDTO.setOrderUnitName(retailOrderOutStream.map(RetailOrderOut::getOrderUnitName).orElse(null));
		outReportDTO.setTotalPrice(StrUtil.toStringOrNull(retailOrderOutDtlStream.map(RetailOrderOutDtl::getQuotePrice).orElse(null)));

		outReportDTO.setSendBarcode(retailOrderOutDtlStream.map(RetailOrderOutDtl::getBarcode).orElse(null));
		outReportDTO.setSendBrandNo(retailOrderOutDtlStream.map(RetailOrderOutDtl::getBrandNo).orElse(null));
		outReportDTO.setSendBrandName(retailOrderOutDtlStream.map(RetailOrderOutDtl::getBrandName).orElse(null));
		outReportDTO.setSendItemCode(retailOrderOutDtlStream.map(RetailOrderOutDtl::getItemCode).orElse(null));
		outReportDTO.setSendSizeNo(retailOrderOutDtlStream.map(RetailOrderOutDtl::getSizeNo).orElse(null));
		outReportDTO.setExpressCode(retailOrderOutStream.map(RetailOrderOut::getExpressCodes).orElse(null));


		outReportDTO.setReturnBarcode(retailOrderReturnDtlStream.map(RetailOrderReturnDtl::getBarcode).orElse(null));
		outReportDTO.setReturnBrandNo(retailOrderReturnDtlStream.map(RetailOrderReturnDtl::getBrandNo).orElse(null));
		outReportDTO.setReturnBrandName(retailOrderReturnDtlStream.map(RetailOrderReturnDtl::getBrandName).orElse(null));
		outReportDTO.setReturnItemCode(retailOrderReturnDtlStream.map(RetailOrderReturnDtl::getItemCode).orElse(null));
		outReportDTO.setReturnSizeNo(retailOrderReturnDtlStream.map(RetailOrderReturnDtl::getSizeNo).orElse(null));



		// 取最早的时间
		Date dates = internetOrderCancelApplyListStream.orElse(new ArrayList<>())
				.stream().map(InternetOrderCancelApply::getRefundTime)
				.filter(ObjectUtil::isNotNull)
				.min(cn.hutool.core.date.DateUtil::compare).orElse(null);
		outReportDTO.setCancelTime(ObjectUtil.isEmpty(dates) ? null: DateUtil.formatDate(dates));

		//
		boolean isAnySuccess = internetOrderCancelApplyListStream.orElse(new ArrayList<>()).stream().anyMatch(item -> ObjectUtil.equal(item.getStatus(), 1));
		boolean isAllFail = internetOrderCancelApplyListStream.orElse(new ArrayList<>()).stream().allMatch(item -> ObjectUtil.equal(item.getStatus(), 2));

		if (ObjectUtil.isNotEmpty(internetOrderCancelApplyListStream.get())) {
			// 都是拦截失败
			if (isAllFail) {
				outReportDTO.setDamageOfCargoName("直接货损");
				// 有任何拦截成功
			} else if (isAnySuccess) {
				outReportDTO.setDamageOfCargoName("已发已退");
			}
		} else {
			outReportDTO.setDamageOfCargoName("正常出库");
		}


		retailOrderOutStream.map(RetailOrderOut::getSendOutDate).ifPresent(date -> {
			outReportDTO.setSendOutDate(DateUtil.formatDate(date));
		});
		outReportDTO.setSendDetailTotal(StrUtil.toStringOrNull(retailOrderOutDtlStream.map(RetailOrderOutDtl::getSendOutQty).orElse(null)));


		return outReportDTO;
	}


	private List<RetailOrderOutDtl> page(Query query, Pagenation pagenation) {
		return this.retailOrderOutDtlManager.selectPageForReport(query, pagenation);
	}


	private List<String> ourOrderBillNos(List<RetailOrderOutDtl> orderOutDtls) {
		Assert.notNull(orderOutDtls);
		return orderOutDtls.stream().map(RetailOrderOutDtl::getBillNo).collect(Collectors.toList());
	}


	private List<RetailOrderOut> getRetailOrderOuts(List<String> billNos) {
		if (ObjectUtil.isEmpty(billNos)) {
			return null;
		}
		Query query = Query.empty().and(new Statement("billNo", "In", billNos));
		return this.retailOrderOutManager.selectByParams(query);
	}


	private List<InternetOrder> getInternetOrdersByRetailOutOrderDtlList(List<RetailOrderOutDtl> retailOrderOutDtls) {
		if (retailOrderOutDtls == null) {
			throw new RuntimeException("出库明细数据不能为空");
		}
		return retailOrderOutDtls.stream().map(RetailOrderOutDtl::getInternetOrder)
				.collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(InternetOrder::getOrderSubNo))), ArrayList::new));
	}
	private List<InternetOrder> getInternetOrders(List<String> orderSubNos) {
		if (ObjectUtil.isEmpty(orderSubNos)) {
			return null;
		}
		Query query = Query.empty().and(new Statement("orderSubNo", "In", orderSubNos));
		return this.internetOrderManager.selectByParams(query);
	}


	private List<RetailOrderReturn> getRetailOrderReturns(List<String> orderSubNos) {
		if (ObjectUtil.isEmpty(orderSubNos)) {
			return null;
		}
		Query query = Query.empty().and(new Statement("orderSubNo", "In", orderSubNos));
		return this.retailOrderReturnManager.selectByParams(query);
	}

	private List<RetailOrderReturnDtl> getRetailOrderReturnDtls(List<String> orderReturnBillNos) {
		if (ObjectUtil.isEmpty(orderReturnBillNos)) {
			return null;
		}
		Query query = Query.empty().and(new Statement("billNo", "In", orderReturnBillNos));
		return this.retailOrderReturnDtlManager.selectByParams(query);
	}


	private List<InternetOrderCancelApply> getInternetOrderCancelApplies(List<String> orderSubNos) {
		if (ObjectUtil.isEmpty(orderSubNos)) {
			return null;
		}
		Query query = Query.empty().and(new Statement("orderSubNo", "In", orderSubNos))
				.orderby("updateTime", true)
				;
		return this.internetOrderCancelApplyManager.selectByParams(query);
	}

}
