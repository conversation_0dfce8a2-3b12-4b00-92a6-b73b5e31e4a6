/**  **/
package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import topmall.framework.service.IService;
import java.util.List;
import java.util.Map;

public interface IIcsActiveLockAdjustDtlService extends IService<IcsActiveLockAdjustDtl,String>{
      
    Integer batchInsert(List<IcsActiveLockAdjustDtl> list);
    
    List<IcsActiveLockAdjustDtl> selectByIds(List<Integer> ids);

    List<IcsActiveLockAdjustDtl> selectActiveDtlList(Map<String, Object> map, Pagenation page);

    void batchSaveOrUpdateDtl(List<IcsActiveLockAdjustDtl> dtlList);

    void batchUpdateSyncStatus(List<IcsActiveLockAdjustDtl> adjustDtlList, int syncStatus);
}