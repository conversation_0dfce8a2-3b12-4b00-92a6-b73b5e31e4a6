package cn.wonhigh.baize.business.virtualwarehousescope;

import cn.hutool.core.collection.CollectionUtil;
import cn.mercury.basic.query.Query;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.manager.gms.IGmsStoreManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseScopeManager;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.entity.gms.GmsStore;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import cn.wonhigh.baize.model.enums.StoreTypeEnums;
import cn.wonhigh.baize.utils.common.QueryUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class SaveScopeValidateService extends AbstractValidateDataService{

    private final IInternetVirtualWarehouseScopeManager internetVirtualWarehouseScopeManager;
    private final IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;
    private final IGmsStoreManager storeManager;

    public SaveScopeValidateService(InternetVirtualWarehouseInfo internetVirtualWarehouseInfo) {
        super(internetVirtualWarehouseInfo);
        this.internetVirtualWarehouseScopeManager = SpringContext.getBean(IInternetVirtualWarehouseScopeManager.class);
        this.storeManager = SpringContext.getBean(IGmsStoreManager.class);
        this.internetVirtualWarehouseInfoManager = SpringContext.getBean(IInternetVirtualWarehouseInfoManager.class);
    }

    @Override
    public <T> ValidateResult validate(T t) {
        if (!(t instanceof InternetVirtualWarehouseScopeSave)) {
            return ValidateResult.error("类型错误");
        }
        InternetVirtualWarehouseScopeSave saveData = (InternetVirtualWarehouseScopeSave) t;
//        if (Integer.parseInt(saveData.getMoreStoreFlag()) == 1) {
//            return multiValidate(saveData);
//        } else {
//            return singleValidate(saveData);
//        }

        if(internetVirtualWarehouseInfo.getVstoreMold() == 1){
            return standardValidate(saveData);
        } else {
            return normalValidate(saveData);
        }
    }


    private ValidateResult multiValidate (InternetVirtualWarehouseScopeSave saveData) {
        Query query = Query.empty()
                .and("storeType", saveData.getStoreType())
                .and("orderUnitNo", saveData.getOrderUnitNo())
                .and("vstoreCode", saveData.getVstoreCode())
                .and("moreStoreFlag", saveData.getMoreStoreFlag())
                .and("vstoreType", internetVirtualWarehouseInfo.getVstoreType());

        List<InternetVirtualWarehouseScope> listScopes = null;
        // 子仓类型
        if (internetVirtualWarehouseInfo.getVstoreType() == 2) {
            query.and("parentVstoreCode", internetVirtualWarehouseInfo.getParentVstoreCode());

            listScopes = internetVirtualWarehouseScopeManager.selectExistShop(query.asMap());
            if (ObjectUtils.isEmpty(listScopes)) {
                String stringBuilder = "本虚拟子仓货管：" + saveData.getOrderUnitNo() + "在其总仓：" + internetVirtualWarehouseInfo.getParentVstoreCode() + "下不存在单店的库存范围";
                return ValidateResult.error(stringBuilder);
            }
        }

        if (ObjectUtils.isEmpty(listScopes)) {
            listScopes = internetVirtualWarehouseScopeManager.selectExistStore(query.asMap());
        }

        if (ObjectUtils.isNotEmpty(listScopes)) {

            for (InternetVirtualWarehouseScope scope : listScopes) {
                if (scope.getVstoreCode().equals(internetVirtualWarehouseInfo.getVstoreCode())) {
                    // 聚合仓内库存范围重叠校验
                    String stringBuilder = "货管:" + saveData.getOrderUnitNo() + "在聚合仓：" + internetVirtualWarehouseInfo.getVstoreCode() + "下已存在启动的";
                    return ValidateResult.error(stringBuilder);
                } else {

                    if (VmBusinessTypeEnums.INTERNET.getType() == internetVirtualWarehouseInfo.getBusinessType()
                            && internetVirtualWarehouseInfo.getVstoreType() == 1) {
                        //网销业务虚拟总仓间库存范围重叠校验
                        InternetVirtualWarehouseInfo ivwi = internetVirtualWarehouseInfoManager.findByUnique(scope.getVstoreCode());
                        if (ivwi != null && ivwi.getBusinessType() == VmBusinessTypeEnums.INTERNET.getType()
                                && Objects.equals(ivwi.getVstoreType(), internetVirtualWarehouseInfo.getVstoreType())) {
                            StringBuilder stringBuilder = new StringBuilder().append("货管:").append(saveData.getOrderUnitNo()).append("所有店的库存范围在聚合仓：").append(scope.getVstoreCode()).append("下已存在有效的");
                            return ValidateResult.error(stringBuilder.toString());
                        }
                    }
                }
            }
        }
        return ValidateResult.success();
    }

    private ValidateResult singleValidate (InternetVirtualWarehouseScopeSave saveData) {
        if (saveData.getAddDtls() != null) {
            for (InternetVirtualWarehouseScopeSave.SaveDtl temp : saveData.getAddDtls()) {
                Object storeNo = temp.getStoreNo();
                String orderUnitNo = saveData.getOrderUnitNo();
                Integer inventoryType = saveData.getInventoryType();
                String storeType = saveData.getStoreType() + "";
                int moreStoreFlag = Integer.parseInt(saveData.getMoreStoreFlag());

                Map<String, Object> objectMap = new HashMap<>();
                objectMap.put("orderUnitNo", saveData.getOrderUnitNo());
                objectMap.put("storeNo", storeNo);
                objectMap.put("inventoryType", 2);
                objectMap.put("status", 1);
                List<InternetVirtualWarehouseScope> scopes = internetVirtualWarehouseScopeManager.selectByParams(QueryUtil.mapToQuery(objectMap));
                if (CollectionUtil.isNotEmpty(scopes)) {
                    return ValidateResult.error("独享库存的机构和货管已存在,storeNo:" + storeNo + ",orderUnitNo:" + orderUnitNo);
                }
                if (inventoryType == 2) {
                    objectMap.put("inventoryType", null);
                    List<InternetVirtualWarehouseScope> scopeList = internetVirtualWarehouseScopeManager.selectByParams(QueryUtil.mapToQuery(objectMap));
                    if (CollectionUtil.isNotEmpty(scopeList)) {
                        return ValidateResult.error("独享库存的机构和货管已存在,storeNo:" + storeNo + ",orderUnitNo:" + orderUnitNo);
                    }
                }
                Map<String, Object> param = new HashMap<>();
                param.put("storeNo", storeNo);
                List<GmsStore> storeList = storeManager.selectByParams(QueryUtil.mapToQuery(param));
                if (storeList == null || storeList.isEmpty()) {
                    return ValidateResult.error("机构不存在,storeNo:" + storeNo);
                }

                GmsStore store = storeList.get(0);
                if (storeType.equals("22") && store.getVirtPhyStorageType() == 1) {
                    return ValidateResult.error("更新失败，机构类只允许是实仓和外部聚合仓类型,storeNo:" + store.getStoreNo());
                }

                Map<String, Object> params = new HashMap<String, Object>();
                List<InternetVirtualWarehouseScope> listScope;
                // 添加非所有店
                params.put("vstoreType", internetVirtualWarehouseInfo.getVstoreType());
                params.put("storeType", storeType);
                params.put("orderUnitNo", orderUnitNo);
                params.put("storeNo", storeNo);
                params.put("vstoreCode", internetVirtualWarehouseInfo.getVstoreCode());
                params.put("moreStoreFlag", moreStoreFlag);
                if (internetVirtualWarehouseInfo.getVstoreType() == 2) {// 子仓类型
                    params.put("parentVstoreCode", internetVirtualWarehouseInfo.getParentVstoreCode());


                    if (StoreTypeEnums.SHOP.getType().equals(storeType)) {
                        // 添加单店 新增单店校验聚合仓下是否有所有店的范围，新增所有店时校验聚合仓下是否有单店的范围
                        listScope = internetVirtualWarehouseScopeManager.selectExistShop(params);
                        if (CollectionUtils.isEmpty(listScope)) {
                            return ValidateResult.error("本虚拟子仓货管：" + orderUnitNo + "在其总仓：" + internetVirtualWarehouseInfo.getParentVstoreCode()
                                    + "下不存在" + (moreStoreFlag == 1 ? "所有店" : "单店") + "的库存范围");
                        }
                    }

                    // 查询当前子仓对应父仓是否存在启用的数据

                    Map<String,Object> queryParentParams = new HashMap<>();
                    queryParentParams.put("storeType", storeType);
                    queryParentParams.put("orderUnitNo", orderUnitNo);
                    queryParentParams.put("storeNo", storeNo);
                    queryParentParams.put("moreStoreFlag", moreStoreFlag);
                    queryParentParams.put("vstoreCode", internetVirtualWarehouseInfo.getParentVstoreCode());
                    listScope = internetVirtualWarehouseScopeManager.selectByParams(QueryUtil.mapToQuery(queryParentParams));
                    if (listScope == null || listScope.isEmpty()) {
                        return ValidateResult.error("总仓未启用当前仓+货管的库存范围");
                    }
                }

                if (StoreTypeEnums.STORE.getType().equals(storeType)) {
                    params.put("moreStoreFlag", null);
                }
                listScope = internetVirtualWarehouseScopeManager.selectExistStore(params);
                if (!CollectionUtils.isEmpty(listScope)) {
                    for (InternetVirtualWarehouseScope scope : listScope) {
                        if (scope.getVstoreCode().equals(internetVirtualWarehouseInfo.getVstoreCode())
                        ) {
                            if (StoreTypeEnums.SHOP.getType().equals(storeType)) {
                                // 聚合仓内库存范围重叠校验
                                return ValidateResult.error("货管:" + orderUnitNo + "机构编码:" + storeNo + "在聚合仓：" + internetVirtualWarehouseInfo.getVstoreCode() + "下已存在有效的");

                            }
                        }
                    }
                }
            }
        }

        return ValidateResult.success();
    }

    private ValidateResult normalValidate(InternetVirtualWarehouseScopeSave saveData) {
        if (CollectionUtils.isEmpty(saveData.getAddDtls())) {
            return ValidateResult.error("导入数据为空");
        }
        for (InternetVirtualWarehouseScopeSave.SaveDtl temp : saveData.getAddDtls()) {
            Object storeNo = temp.getStoreNo();
            boolean exit = false;
            List<InternetVirtualWarehouseScope> scopes = internetVirtualWarehouseScopeManager.selectScopeInfo(
                    Query.Where("orderUnitNo", temp.getOrderUnitNo())
                            //.and("status",1)
                            .and("storeNo", storeNo).asMap());
            if (CollectionUtil.isNotEmpty(scopes)) {
                for (InternetVirtualWarehouseScope scope : scopes) {
                    //判断聚合仓是否存在该货管
                    if (internetVirtualWarehouseInfo.getVstoreCode().equals(scope.getVstoreCode())) {
                        if (scope.getMoreStoreFlag() == 1 && temp.getStoreType() == 21) {
                            return ValidateResult.error("虚拟仓：" + scope.getVstoreCode() + "货管：" + scope.getOrderUnitNo()
                                    + "不能同时设置机构类型为店的单店和所有店的库存范围！");
                        }
                        temp.setId(scope.getId());
                        /*if(temp.getStatus() == 0 && scope.getStatus() == 1){
                            //禁用校验是否有锁库
                            validateActiveLock(scope);
                            //总仓禁用联动子仓禁用，需要校验子仓活动锁库
                            if(scope.getVstoreType() == 1){
                                scopes.stream().filter(x->scope.getVstoreCode().equals(x.getParentVstoreCode())
                                        && x.getVstoreType() == 2 && x.getStatus() == 1)
                                        .forEach(x->validateActiveLock(x));
                            }
                        }*/
                        continue;
                    }
                    //判断是否有独享货管
                    if (scope.getInventoryType() == 2 && scope.getStatus() == 1 && ((scope.getMoreStoreFlag() == 1 && scope.getStoreNo() == null)
                            || temp.getStoreNo().equals(scope.getStoreNo()))) {
                        return ValidateResult.error("独享库存的机构和货管已存在,storeNo:" + storeNo + ",orderUnitNo:" + temp.getOrderUnitNo());
                    }
                    //子仓，添加的要在总仓范围内
                    if (temp.getStoreNo().equals(scope.getStoreNo()) && scope.getVstoreCode().equals(internetVirtualWarehouseInfo.getParentVstoreCode())) {
                        exit = true;
                        if (internetVirtualWarehouseInfo.getVstoreType() == 2 && scope.getStatus() != 1 && temp.getStatus() == 1) {
                            return ValidateResult.error("总仓未启用当前仓+货管的聚合仓范围,storeNo:" + storeNo + ",orderUnitNo:" + temp.getOrderUnitNo());
                        }
                    }
                }
            }
            if (internetVirtualWarehouseInfo.getVstoreType() == 2 && !exit) {
                return ValidateResult.error("货管:" + temp.getOrderUnitNo() + "机构编码:" + storeNo + "不在所属聚合仓：" + internetVirtualWarehouseInfo.getParentVstoreCode() + "范围内");
            }

            Map<String, Object> param = new HashMap<>();
            param.put("storeNo", storeNo);
            List<GmsStore> storeList = storeManager.selectByParams(QueryUtil.mapToQuery(param));
            if (storeList == null || storeList.isEmpty()) {
                return ValidateResult.error("机构不存在,storeNo:" + storeNo);
            }

            GmsStore store = storeList.get(0);
            if (temp.getStoreType() == 22 && store.getVirtPhyStorageType() == 1) {
                return ValidateResult.error("机构类只允许是实仓和外部聚合仓类型,storeNo:" + store.getStoreNo());
            }
        }
        return ValidateResult.success();
    }

    private ValidateResult standardValidate(InternetVirtualWarehouseScopeSave saveData) {
        if (saveData.getAddDtls() != null) {
            for (InternetVirtualWarehouseScopeSave.SaveDtl temp : saveData.getAddDtls()) {
                Object storeNo = temp.getStoreNo();
                String orderUnitNo = temp.getOrderUnitNo();

                List<InternetVirtualWarehouseScope> scopes = internetVirtualWarehouseScopeManager.selectScopeInfo(
                        Query.Where("orderUnitNo", orderUnitNo)
                                //.and("status",1)
                                .and("standardStoreNo", storeNo).asMap());
                if (CollectionUtil.isNotEmpty(scopes)) {
                    //过滤出子仓启用的范围
//                    Map<String, InternetVirtualWarehouseScope> childScopeMap = scopes.stream()
//                            .filter(x->internetVirtualWarehouseInfo.getVstoreCode().equals(x.getParentVstoreCode()) && x.getVstoreType() == 2 && x.getStatus() == 1)
//                            .collect(Collectors.toMap(x->x.getStoreNo() + x.getOrderUnitNo() , y->y, (s1,s2)->s1));

                    for (InternetVirtualWarehouseScope scope : scopes) {
                        if(internetVirtualWarehouseInfo.getVstoreCode().equals(scope.getVstoreCode())){
                            temp.setId(scope.getId());
                            /*if(!scope.getStatus().equals(temp.getStatus())){
                                temp.setIsEdit(true);
                                if(temp.getStatus() == 0){
                                    //禁用校验是否有锁库
                                    validateActiveLock(scope);
                                    //过滤出子仓货管也要校验是否有锁库
                                    InternetVirtualWarehouseScope childScope = childScopeMap.get(scope.getStoreNo() + scope.getOrderUnitNo());
                                    if(childScope != null){
                                        validateActiveLock(childScope);
                                    }
                                }
                            }*/
                            continue;
                        }
                        //判断是否有独享货管
                        if(scope.getInventoryType() == 2 && scope.getStatus() == 1){
                            return ValidateResult.error("独享库存的机构和货管已存在,storeNo:" + storeNo + ",orderUnitNo:" + orderUnitNo);
                        }
                        //判断机构货管是否在别的标准仓，只有总仓添加范围明细，只判断总仓的
                        if(scope.getVstoreType() == 1 && scope.getVstoreMold() == 1){
                            return ValidateResult.error("机构货管已存在其它标准聚合仓中,storeNo:" + storeNo + ",orderUnitNo:" + orderUnitNo);
                        }
                    }
                }

                Map<String, Object> param = new HashMap<>();
                param.put("storeNo", storeNo);
                List<GmsStore> storeList = storeManager.selectByParams(QueryUtil.mapToQuery(param));
                if (storeList == null || storeList.isEmpty()) {
                    return ValidateResult.error("机构不存在,storeNo:" + storeNo);
                }

                GmsStore store = storeList.get(0);
                if (temp.getStoreType() == 22 && store.getVirtPhyStorageType() == 1) {
                    return ValidateResult.error("机构类只允许是实仓和外部聚合仓类型,storeNo:" + store.getStoreNo());
                }
            }
        }
        return ValidateResult.success();
    }

}
