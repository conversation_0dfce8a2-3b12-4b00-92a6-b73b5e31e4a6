/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOut;
import cn.wonhigh.baize.service.ios.IRetailOrderOutService;
import cn.wonhigh.baize.manager.ios.IRetailOrderOutManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class RetailOrderOutManager extends BaseManager<RetailOrderOut,String> implements IRetailOrderOutManager{
    @Autowired
    private IRetailOrderOutService service;

    protected IService<RetailOrderOut,String> getService(){
        return service;
    }

    
    public RetailOrderOut findByUnique(String billNo) {
        try {
			return service.findByUnique(billNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String billNo) {
        try {
			return service.deleteByUnique(billNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(RetailOrderOut entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}