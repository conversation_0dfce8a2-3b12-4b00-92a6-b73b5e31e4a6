/**  **/
package cn.wonhigh.baize.repository.gms;


import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

import org.apache.ibatis.annotations.Param;

@Mapper
public interface InternetVirtualWarehouseInfoRepository extends IRepository<InternetVirtualWarehouseInfo,String> {
    
    public InternetVirtualWarehouseInfo findByUnique(@Param("vstoreCode") String vstoreCode);

    public Integer deleteByUnique(@Param("vstoreCode") String vstoreCode);

    public Integer insertForUpdate(InternetVirtualWarehouseInfo entry);

    
}