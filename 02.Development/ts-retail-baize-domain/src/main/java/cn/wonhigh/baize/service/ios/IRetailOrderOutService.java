/**  **/
package cn.wonhigh.baize.service.ios;

import cn.wonhigh.baize.model.entity.ios.RetailOrderOut;
import topmall.framework.service.IService;

public interface IRetailOrderOutService extends IService<RetailOrderOut,String>{

    
    public RetailOrderOut findByUnique(String billNo);

    public Integer deleteByUnique(String billNo);

    public Integer insertForUpdate(RetailOrderOut entry);
    
}