/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig;
import cn.wonhigh.baize.service.gms.IOrderUnitPriorityConfigService;
import cn.wonhigh.baize.manager.gms.IOrderUnitPriorityConfigManager;
import topmall.framework.security.Authorization;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class OrderUnitPriorityConfigManager extends BaseManager<OrderUnitPriorityConfig,String> implements IOrderUnitPriorityConfigManager{
    @Autowired
    private IOrderUnitPriorityConfigService service;

    protected IService<OrderUnitPriorityConfig,String> getService(){
        return service;
    }
    	
	public List<OrderUnitPriorityConfig> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<OrderUnitPriorityConfig> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}

	@Override
	public Integer insert(OrderUnitPriorityConfig entry) throws ManagerException {
		Integer i = super.insert(entry);
		if(i > 0) {
			saveLog(false, entry);
		}
		return i;
	}

	@Override
	public Integer update(OrderUnitPriorityConfig entry) throws ManagerException {
		Integer i = super.update(entry);
		if(i > 0){
			saveLog(true, entry);
		}
		return i;
	}

	void saveLog(boolean isEdit, OrderUnitPriorityConfig entry){
		SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
				new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
						.setOpscode(isEdit ? "update" : "insert")
						.setSyscode(String.valueOf(MenuTypeEnums.OUPC.getType()))
						.setSysname(MenuTypeEnums.OUPC.getDesc()).
						setKeyword1(entry.getId()).
						setKeyword1info("ID:"+entry.getId()).
						setKeyword2(entry.getVstoreCode()).
						setKeyword2info(String.format("聚合仓编码:%s(%s)", entry.getVstoreCode(), entry.getVstoreName())).
						setRemark(String.format("%s：聚合仓编码:%s(%s);货管:%s(%s);优先级:%s;状态:%s",
								(isEdit ? "修改" : "新增"), entry.getVstoreCode(), entry.getVstoreName(),
								entry.getOrderUnitNo(), entry.getOrderUnitName(), entry.getOrderUnitLevel(),
								(entry.getStatus() == 1 ? "开启": "关闭")))
						.setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
						.setCreateTime(new Date())
						.build()));
	}

}