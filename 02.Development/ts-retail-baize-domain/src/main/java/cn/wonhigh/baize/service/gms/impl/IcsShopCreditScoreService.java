/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.manager.IEntryResultHandler;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore;
import cn.wonhigh.baize.repository.gms.IcsShopCreditScoreRepository;

import cn.wonhigh.baize.service.gms.IIcsShopCreditScoreService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class IcsShopCreditScoreService extends BaseService<IcsShopCreditScore,String> implements  IIcsShopCreditScoreService{
    @Autowired
    private IcsShopCreditScoreRepository repository;

    protected IRepository<IcsShopCreditScore,String> getRepository(){
        return repository;
    }
      
	public List<IcsShopCreditScore> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}

    @Override
    public int selectShopCreditScoreByCount(Map<String, Object> params) {
        return repository.selectShopCreditScoreByCount(params);
    }

    @Override
    public List<IcsShopCreditScore> selectShopCreditScoreByPage(Map<String, Object> params, Pagenation page) {
        return repository.selectShopCreditScoreByPage(params, page);
    }

    @Override
    public void selectShopCreditScoreByParams(Map<String, Object> params, IEntryResultHandler<IcsShopCreditScore> handler) {
        ResultHandler<IcsShopCreditScore> resultHandler = new ResultHandler<IcsShopCreditScore>() {
            public void handleResult(ResultContext<? extends IcsShopCreditScore> resultContext) {
                boolean ok = handler.handleResult(resultContext.getResultObject());
                if (!ok) {
                    resultContext.stop();
                }
            }
        };
        repository.selectShopCreditScoreByParams(params, resultHandler);
    }

    public Integer batchInsert(List<IcsShopCreditScore> list) {
    	return repository.batchInsert(list);
    }
}