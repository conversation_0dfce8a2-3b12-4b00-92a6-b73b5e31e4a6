/**  **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.InternetOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

@Mapper
public interface InternetOrderRepository extends IRepository<InternetOrder,String> {
    
    public InternetOrder findByUnique(@Param("orderSubNo") String orderSubNo);

    public Integer deleteByUnique(String orderSubNo);

    public Integer insertForUpdate(InternetOrder entry);

    
}