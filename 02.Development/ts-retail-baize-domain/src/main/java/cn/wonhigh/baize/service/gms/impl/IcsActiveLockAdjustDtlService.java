/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.repository.gms.IcsActiveLockAdjustDtlRepository;
import cn.wonhigh.baize.service.gms.IIcsActiveLockAdjustDtlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import java.util.List;
import java.util.Map;

@Service
public class IcsActiveLockAdjustDtlService extends BaseService<IcsActiveLockAdjustDtl,String> implements  IIcsActiveLockAdjustDtlService{
    @Autowired
    private IcsActiveLockAdjustDtlRepository repository;

    protected IRepository<IcsActiveLockAdjustDtl,String> getRepository(){
        return repository;
    }
    
    @Override
	public List<IcsActiveLockAdjustDtl> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}

    @Override
    public List<IcsActiveLockAdjustDtl> selectActiveDtlList(Map<String, Object> map, Pagenation page) {
        return repository.selectActiveDtlList(map, page);
    }

    @Override
    public void batchSaveOrUpdateDtl(List<IcsActiveLockAdjustDtl> dtlList) {
        repository.batchSaveOrUpdateDtl(dtlList);
    }

    @Override
    public void batchUpdateSyncStatus(List<IcsActiveLockAdjustDtl> adjustDtlList, int syncStatus) {
        repository.batchUpdateSyncStatus(adjustDtlList, syncStatus);
    }

    @Override
    public Integer batchInsert(List<IcsActiveLockAdjustDtl> list) {
    	return repository.batchInsert(list);
    }
}