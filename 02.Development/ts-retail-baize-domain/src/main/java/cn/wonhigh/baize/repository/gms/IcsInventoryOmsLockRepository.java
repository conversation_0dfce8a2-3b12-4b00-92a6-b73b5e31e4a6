package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.dto.IcsInventoryOmsLockDto;
import cn.wonhigh.baize.model.entity.gms.IcsInventoryOmsLock;
import org.apache.ibatis.annotations.Mapper;

import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;
import java.util.Map;

/**
 * oms锁库(IcsInventoryOmsLock)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-28 10:04:34
 */
@Mapper
public interface IcsInventoryOmsLockRepository extends IRepository<IcsInventoryOmsLock, String> {
    Integer updateStock(@Param("params") List<IcsInventoryOmsLockDto> params);

}
