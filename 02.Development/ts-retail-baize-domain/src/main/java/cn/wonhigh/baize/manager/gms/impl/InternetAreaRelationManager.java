/** zkh **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.manager.gms.IInternetAreaRelationManager;
import cn.wonhigh.baize.model.entity.gms.InternetAreaRelation;
import cn.wonhigh.baize.service.gms.IInternetAreaRelationService;
import org.springframework.stereotype.Service;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;

@Service
public class InternetAreaRelationManager extends BaseManager<InternetAreaRelation,String> implements IInternetAreaRelationManager {
    @Autowired
    private IInternetAreaRelationService service;

    protected IService<InternetAreaRelation,String> getService(){
        return service;
    }
    	
	public List<InternetAreaRelation> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<InternetAreaRelation> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}

	@Override
	public List<InternetAreaRelation> selectByRetailCodeName(Map<String, Object> params) {
		return service.selectByRetailCodeName(params);
	}
}