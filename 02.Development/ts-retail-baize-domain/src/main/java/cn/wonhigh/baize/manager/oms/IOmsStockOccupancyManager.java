package cn.wonhigh.baize.manager.oms;

import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.dto.OmsStockOccupancyDTO;
import cn.wonhigh.baize.model.entity.oms.OmsStockOccupancy;

import java.util.List;

/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.manager.oms
 * @Project：ts-retail-baize
 * @name：IOmsStockOccupancyManager
 * @Date：2024/12/27 18:27
 * @Filename：IOmsStockOccupancyManager
 * @description：
 */
public interface IOmsStockOccupancyManager extends IManager<OmsStockOccupancy,Long> {

    OmsStockOccupancyDTO queryLockStockQty(String warehouseId, String skuId);
}
