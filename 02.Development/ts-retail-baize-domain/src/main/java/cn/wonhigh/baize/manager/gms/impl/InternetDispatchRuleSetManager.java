/**
 *
 **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.mercury.utils.DateUtil;
import cn.wonhigh.baize.converter.InternetDispatchRuleSetConverter;
import cn.wonhigh.baize.events.dispatchrule.DispatchRuleSetEvent;
import cn.wonhigh.baize.events.dispatchrule.DispatchRuleSetMessage;
import cn.wonhigh.baize.manager.gms.IInternetDispatchRuleSetManager;
import cn.wonhigh.baize.model.dto.dispatchruleset.DispatchRuleSetDto;
import cn.wonhigh.baize.model.dto.dispatchruleset.DispatchRuleSetSaveDto;
import cn.wonhigh.baize.model.dto.dispatchruleset.ExitsQueryDataDto;
import cn.wonhigh.baize.model.dto.dispatchruleset.InternetDispatchRulePriorityDto;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;
import cn.wonhigh.baize.service.gms.IInternetDispatchRuleSetDtlService;
import cn.wonhigh.baize.service.gms.IInternetDispatchRuleSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.security.Authorization;
import topmall.framework.service.IService;

import javax.annotation.Resource;
import java.util.*;


@Service
public class InternetDispatchRuleSetManager extends BaseManager<InternetDispatchRuleSet, Long> implements IInternetDispatchRuleSetManager {
    @Autowired
    private IInternetDispatchRuleSetService service;

    @Autowired
    private IInternetDispatchRuleSetDtlService serviceDtl;


    @Resource
    private InternetDispatchRuleSetConverter converter;

    @Override
    protected IService<InternetDispatchRuleSet, Long> getService() {
        return service;
    }


    @Override
    public void save(DispatchRuleSetSaveDto saveDto) throws ManagerException {
        InternetDispatchRuleSet internetDispatchRuleSet = converter.saveDtoConvertEntity(saveDto);
        if (internetDispatchRuleSet == null) {
            throw new ManagerException("保存数据为空");
        }

        // 校验数据
        validateDataUniqueness(saveDto.getBrandNo(), saveDto.getDtlList(), internetDispatchRuleSet.getId());

        String userName = Optional.ofNullable(Authorization.getUser()).map(IUser::getAccount).orElse("unknown");
        if (internetDispatchRuleSet.getId() == null) {

            internetDispatchRuleSet.setId(service.nextId());
            internetDispatchRuleSet.setOrganTypeName("体");
            internetDispatchRuleSet.setOrganTypeNo("U010102");
            internetDispatchRuleSet.setRuletype(0);
            internetDispatchRuleSet.setRuleNo(String.valueOf(IdUtil.getSnowflakeNextId()));
            internetDispatchRuleSet.setUpdateUser(userName);
            internetDispatchRuleSet.setUpdateTime(new Date());
            internetDispatchRuleSet.setCreateTime(new Date());
            internetDispatchRuleSet.setCreateUser(userName);
            service.insert(internetDispatchRuleSet);

            pushEvent(internetDispatchRuleSet.getId(), internetDispatchRuleSet.getCreateUser(),new ArrayList<>(), saveDto.getRuleList());
        } else {
            InternetDispatchRuleSet find = service.findByPrimaryKey(internetDispatchRuleSet.getId());
            if (find == null) {
                return;
            }

            internetDispatchRuleSet.setUpdateTime(new Date());
            internetDispatchRuleSet.setUpdateUser(userName);
            // 更新
            service.update(internetDispatchRuleSet);

            DispatchRuleSetDto dto =  converter.entityToDto(find);
            pushEvent(internetDispatchRuleSet.getId(), userName,dto.getRuleList(), saveDto.getRuleList());

            // 需要删除的明细数据
            long[] ids = Arrays.stream(Optional.ofNullable(saveDto.getDeleteIds())
                    .orElse("").split(",")).filter(StrUtil::isNotBlank)
                    .mapToLong(Long::parseLong).toArray();
            if (ids != null) {
                for (long id : ids) {
                    serviceDtl.deleteByPrimaryKey(id);
                }
            }
        }


        Optional.ofNullable(internetDispatchRuleSet.getDtlList())
                .ifPresent(it -> {
                    it.forEach(item -> {
                        if (item.getId() == null) {
                            item.setRuleNo(internetDispatchRuleSet.getRuleNo());
                            item.setCreateUser(internetDispatchRuleSet.getCreateUser());
                            item.setCreateTime(new Date());
                            item.setUpdateTime(new Date());
                            item.setUpdateUser(internetDispatchRuleSet.getUpdateUser());
                            serviceDtl.insert(item);
                        } else {
                            item.setUpdateTime(new Date());
                            item.setUpdateUser(internetDispatchRuleSet.getUpdateUser());
                            serviceDtl.update(item);
                        }
                    });
                });
    }

    private static void pushEvent(Long id,  String  user, List<InternetDispatchRulePriorityDto> before, List<InternetDispatchRulePriorityDto> after) {
        SpringContext.getContext().publishEvent(new DispatchRuleSetEvent(new DispatchRuleSetMessage.Builder()
                .setOptType(DispatchRuleSetMessage.OptTypeEnum.add)
                .setBeforeRuleName(ListUtil.toLinkedList())
                .setOptTime(DateUtil.format(new Date(), DateUtil.LONG_DATE_FORMAT))
                .setOptUser(user)
                .setId(id)
                .setBeforeRuleName(ListUtil.toLinkedList(before.stream().map(InternetDispatchRulePriorityDto::getName).toArray(String[]::new)))
                .setAfterRuleName(ListUtil.toLinkedList(after.stream().map(InternetDispatchRulePriorityDto::getName).toArray(String[]::new)))
                .build()));
    }


    /**
     * 校验数据是否唯一
     *
     * @param dtlList 明细数据
     * @param brandNo  品牌
     */
    private void validateDataUniqueness (String brandNo,  List<?> dtlList, Long id) {
        if (ObjectUtil.isEmpty(dtlList)) {
            throw new ManagerException("品牌或渠道不能为空");
        }

        if (brandNo == null || brandNo.trim().isEmpty()) {
            brandNo = "empty";
        }

        Map<String, Object> query = new HashMap<>();
        query.put("brandNo", brandNo);
        query.put("dtlList", dtlList);
        query.put("noEqId", id);
        int count = service.exitsQueryData(query);
        if (count > 0){
            throw new ManagerException("已经存在相同的数据");
        }
    }

    @Override
    public int exitsData(ExitsQueryDataDto queryDataDto) {
        if (queryDataDto == null) {
            throw new ManagerException("参数不能为空");
        }

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("noEqRuleNo", queryDataDto.getNoEqRuleNo());

        if (queryDataDto.getBrandNo()  == null || queryDataDto.getBrandNo().trim().isEmpty()) {
            queryDataDto.setBrandNo("empty");
        }

        queryMap.put("brandNo", queryDataDto.getBrandNo());
        queryMap.put("channelNo", queryDataDto.getChannelNo());
        queryMap.put("dtlList", queryDataDto.getDtlList());

        return this.service.exitsQueryData(queryMap);
    }
}