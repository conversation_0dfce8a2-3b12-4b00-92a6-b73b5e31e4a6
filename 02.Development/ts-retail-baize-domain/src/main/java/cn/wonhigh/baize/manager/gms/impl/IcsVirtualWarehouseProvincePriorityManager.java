/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority;
import cn.wonhigh.baize.service.gms.IIcsVirtualWarehouseProvincePriorityService;
import  cn.wonhigh.baize.manager.gms.IIcsVirtualWarehouseProvincePriorityManager;
import topmall.framework.security.Authorization;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class IcsVirtualWarehouseProvincePriorityManager extends BaseManager<IcsVirtualWarehouseProvincePriority,String> implements IIcsVirtualWarehouseProvincePriorityManager{
    @Autowired
    private IIcsVirtualWarehouseProvincePriorityService service;

    protected IService<IcsVirtualWarehouseProvincePriority,String> getService(){
        return service;
    }
    	
	public List<IcsVirtualWarehouseProvincePriority> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<IcsVirtualWarehouseProvincePriority> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}

	@Override
	public Integer insert(IcsVirtualWarehouseProvincePriority entry) throws ManagerException {
		Integer i = super.insert(entry);
		if(i > 0){
			saveLog(false, entry);
		}
		return i;
	}

	@Override
	public Integer update(IcsVirtualWarehouseProvincePriority entry) throws ManagerException {
		Integer i = super.update(entry);
		if(i > 0){
			saveLog(true, entry);
		}
		return i;
	}

	void saveLog(boolean isEdit, IcsVirtualWarehouseProvincePriority entry){
		SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
				new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
						.setOpscode(isEdit ? "update" : "insert")
						.setSyscode(String.valueOf(MenuTypeEnums.VWPP.getType()))
						.setSysname(MenuTypeEnums.VWPP.getDesc()).
						setKeyword1(entry.getId()).
						setKeyword1info(entry.getId()).
						setKeyword2(entry.getVstoreCode()).
						setKeyword2info(String.format("聚合仓编码:%s(%s)", entry.getVstoreCode(), entry.getVstoreName())).
						setRemark(String.format("%s：聚合仓编码:%s(%s);省编码:%s(%s);状态:%s",
								(isEdit ? "修改" : "新增"), entry.getVstoreCode(), entry.getVstoreName(),
								entry.getProvinceNo(),entry.getProvinceName(), (entry.getStatus() == 1 ? "开启": "关闭")))
						.setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
						.setCreateTime(new Date())
						.build()));
	}

}