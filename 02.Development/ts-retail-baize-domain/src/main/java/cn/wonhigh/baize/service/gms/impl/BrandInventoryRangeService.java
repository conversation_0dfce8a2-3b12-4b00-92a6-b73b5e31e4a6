/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.BrandInventoryRange;
import cn.wonhigh.baize.repository.gms.BrandInventoryRangeRepository;

import cn.wonhigh.baize.service.gms.IBrandInventoryRangeService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;

@Service
public class BrandInventoryRangeService extends BaseService<BrandInventoryRange,String> implements  IBrandInventoryRangeService{
    @Autowired
    private BrandInventoryRangeRepository repository;

    protected IRepository<BrandInventoryRange,String> getRepository(){
        return repository;
    }
      
	public List<BrandInventoryRange> selectByIds(List<String> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<BrandInventoryRange> list) {
    	return repository.batchInsert(list);
    }


    @Override
    public int selectShareNoExist(Map<String, Object> paramMaps) {
        return repository.selectShareNoExist(paramMaps);
    }

    @Override
    public void updateShare(Map<String, Object> paramMap) {
        repository.updateShare(paramMap);
    }
}