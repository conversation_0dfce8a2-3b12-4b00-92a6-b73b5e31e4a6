/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.ShareInventoryRange;
import cn.wonhigh.baize.repository.gms.ShareInventoryRangeRepository;
import cn.wonhigh.baize.service.gms.IShareInventoryRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import java.util.List;
import java.util.Map;

@Service
public class ShareInventoryRangeService extends BaseService<ShareInventoryRange,String> implements IShareInventoryRangeService {
    @Autowired
    private ShareInventoryRangeRepository repository;

    @Override
    protected IRepository<ShareInventoryRange, String> getRepository() {
        return repository;
    }

    @Override
    public int insertSelective(ShareInventoryRange shareInventoryRange) {
        return repository.insertSelective(shareInventoryRange);
    }

    @Override
    public int updateShare(Map<String, Object> map) {
        return repository.updateShare(map);
    }

    @Override
    public Integer batchInsert(List<ShareInventoryRange> list) {
        return repository.batchInsert(list);
    }

    @Override
    public Integer batchUpdate(List<ShareInventoryRange> list) {
        return repository.batchUpdate(list);
    }

    @Override
    public Integer findExistInBrandInventoryRange(Map<String, Object> map) {
        return repository.findExistInBrandInventoryRange(map);
    }
}