package cn.wonhigh.baize.business.active.adjust.puma;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.mercury.security.IUser;
import cn.wonhigh.baize.business.active.adjust.SingleActiveLockAdjustProcess;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.model.enums.AdjustDtlSyncStatusEnums;
import cn.wonhigh.baize.model.enums.BrandMerchantCodeEnums;
import cn.wonhigh.baize.model.enums.OcsOrderSourceConfigChannelTypeEnum;
import cn.wonhigh.baize.utils.common.baozun.BaoZunHttpUtils;
import cn.wonhigh.baize.utils.common.baozun.BaoZunResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 彪马锁库调整服务
 * @date 2025/6/11 11:03
 */
public class PumaActiveLockAdjustProcess extends SingleActiveLockAdjustProcess {
    private static final Logger LOGGER = LoggerFactory.getLogger(PumaActiveLockAdjustProcess.class);
    
    public PumaActiveLockAdjustProcess(String billNo, IUser user) {
        super(billNo, user);
    }

    @Override
    protected void adjustSyncInventory(Pair<InventoryActiveLock, List<InventoryActiveLockDtl>> tuple) {
        adjust.setWarehouseCode(tuple.getLeft().getVstoreCode());
        initAdjustDtlList();
        batchProcess(tuple.getRight(), 200, this::disposeSyncInventory);
        icsActiveLockAdjustManager.updateAdjustAndDtl(adjust, adjustDtlList, tuple.getRight());
    }

    private void disposeSyncInventory(List<InventoryActiveLockDtl> activeLockDtls) {
    	Map<String, InventoryActiveLockDtl> activeLockDtlMap = activeLockDtls.stream().collect(Collectors.toMap(InventoryActiveLockDtl::getUniqueKey, Function.identity(), (d1, d2) ->  d2));
        try {
            FullSkuInventory fullSkuInventory = buildFullSkuInventory(activeLockDtls);
            BaoZunResponse response = BaoZunHttpUtils.requestBaoZun(fullSkuInventory, "sku_full_inventory", BaoZunResponse.class);
            adjustDtlList.forEach(dtl -> {
            	String uniqueKey = dtl.getUniqueKey();
            	if (activeLockDtlMap.containsKey(uniqueKey)) {
            		InventoryActiveLockDtl activeLockDtl = activeLockDtlMap.get(uniqueKey);
            		dtl.setSyncStatus(response.getResponse() == 1 ? AdjustDtlSyncStatusEnums.SYNC_SUCCESS.getValue() : AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue());
            		if (StringUtils.isBlank(activeLockDtl.getBarcode())) {
						dtl.setSyncStatus(AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue());
					}
				}
            });
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            adjustDtlList.forEach(dtl -> {
            	if (activeLockDtlMap.containsKey(dtl.getUniqueKey())) {
            		dtl.setSyncStatus(AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue());
				}
            });
        }
    }

    private FullSkuInventory buildFullSkuInventory(List<InventoryActiveLockDtl> activeLockDtls) {
        return new FullSkuInventory.Builder()
                .sourceMarkCode("routeiosp2_om")
                .msgId(System.currentTimeMillis() + "")
                .items(buildInventoryItemList(activeLockDtls))
                .build();
    }

    private List<InventoryItem> buildInventoryItemList(List<InventoryActiveLockDtl> activeLockDtls) {
        List<InventoryItem> inventoryItemList = new ArrayList<>();
        for (InventoryActiveLockDtl dtl : activeLockDtls) {
        	String barcode = dtl.getBarcode();
        	if (StringUtils.isBlank(barcode)) continue;
            InventoryItem item = new InventoryItem.Builder()
                    .customerCode("PUMA")
                    .invTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                    .ownerCode("PUMA_TOP0I01")
                    .warehouseCode(adjust.getWarehouseCode())
                    .upc(barcode)
                    .invQty(dtl.getBalanceLockQty())
                    .uniqueKey(System.currentTimeMillis() + "")
                    .sourceSys("HUB")
                    .invStatusCode(1)
                    .build();
            inventoryItemList.add(item);
        }
        return inventoryItemList;
    }

	@Override
	protected BrandMerchantCodeEnums merchantCodeEnum() {
		return BrandMerchantCodeEnums.PUSFS;
	}

	@Override
	protected OcsOrderSourceConfigChannelTypeEnum channelTypeEnum() {
		return OcsOrderSourceConfigChannelTypeEnum.PPF;
	}
}
