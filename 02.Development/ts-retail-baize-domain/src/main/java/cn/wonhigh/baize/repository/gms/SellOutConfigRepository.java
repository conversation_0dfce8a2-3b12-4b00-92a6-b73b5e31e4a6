/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
import cn.wonhigh.baize.model.entity.gms.ItemAttr;
import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import topmall.framework.repository.IRepository;

import java.util.List;
import java.util.Map;

@Mapper
public interface SellOutConfigRepository extends IRepository<SellOutConfig, String> {

    Integer batchInsert(@Param("list") List<SellOutConfig> list);

    List<SellOutConfig> selectByIds(@Param("list") List<Integer> ids);

    List<ItemAttr> selectShareClassifyList(@Param("params") Map<String, Object> params);

    int selectSellOutCount(@Param("params") Map<String, Object> query);

    List<SellOutConfigDto> selectSellOutList(@Param("params") Map<String, Object> query, @Param("page") Pagenation page);

    int updateStatusByParams(@Param("params") Map<String, Object> map);

    List<SellOutConfig> selectByUniqueList(@Param("type") Integer type, @Param("list") List<Tuple3<String, String, String>> list);

    void selectByParamsForHandler(@Param("params") Map<String, Object> params, ResultHandler<SellOutConfig> resultHandler);

}