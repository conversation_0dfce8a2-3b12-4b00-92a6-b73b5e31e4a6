/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.entity.gms.Brand;

import cn.mercury.manager.IManager;
import java.util.List;

public interface IBrandManager extends IManager<Brand,Integer>{
    
    Brand findByUnique(String brandNo) ;

    Integer deleteByUnique(String brandNo);

    List<Brand> selectByUniques(List<String> brandNos);
    
    Integer batchInsert(List<Brand> list);
    
    List<Brand> selectByIds(List<Integer> ids);
}