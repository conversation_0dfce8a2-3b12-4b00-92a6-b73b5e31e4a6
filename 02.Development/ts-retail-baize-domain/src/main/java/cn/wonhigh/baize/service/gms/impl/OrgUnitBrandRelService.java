/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.KeyValue;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel;
import cn.wonhigh.baize.repository.gms.OrgUnitBrandRelRepository;

import cn.wonhigh.baize.service.gms.IOrgUnitBrandRelService;
import org.springframework.util.CollectionUtils;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OrgUnitBrandRelService extends BaseService<OrgUnitBrandRel,Integer> implements  IOrgUnitBrandRelService{
    @Autowired
    private OrgUnitBrandRelRepository repository;

    protected IRepository<OrgUnitBrandRel,Integer> getRepository(){
        return repository;
    }


    @Override
    public List<OrgUnitBrandRel> selectStoreByUnitNo(String orderUnitNo){
        return repository.selectStoreByUnitNo(orderUnitNo);
    }

    @Override
    public List<OrgUnitBrandRel> selectValidByStoreNo(String storeNo){
        HashMap<String, Object> orgUnitBrandRelParams = new HashMap<String, Object>();

        orgUnitBrandRelParams.put("storeNo", storeNo);
        orgUnitBrandRelParams.put("status", 1);// 只查有效的
        return repository.selectByParams(orgUnitBrandRelParams,null);
    }

    @Override
    public Set<String> selectValidBrandNoByStoreNo(String storeNo){
        return selectValidByStoreNo(storeNo).stream().map(v -> v.getBrandNo())
                .collect(Collectors.toSet());
    }



    @Override
    public Map<String, KeyValue<String,String>> selectValidStoreNoZones(List<String> storeNos){
        int pageSize = 3000;
        Map<String, KeyValue<String,String>> result = new HashMap<>();
        List<List<String>> partition = Lists.partition(storeNos, pageSize);
        for (List<String> parStoreNos : partition) {
            List<OrgUnitBrandRel> orgUnitBrandRels = repository.selectValidStoreNoZones(parStoreNos);
            for (OrgUnitBrandRel orgUnitBrandRel : orgUnitBrandRels) {
                result.put(orgUnitBrandRel.getStoreNo(), new KeyValue<>(orgUnitBrandRel.getZoneNo(),  orgUnitBrandRel.getZoneName()));
            }
        }
        return result;
    }


    @Override
    public Set<String> selectValidStoreNos(List<String> storeNos, String zoneNo){
        int pageSize = 3000;
        Set<String> result = new HashSet<>();
        List<List<String>> partition = Lists.partition(storeNos, pageSize);
        for (List<String> parStoreNos : partition) {
            Set<String> strings = repository.selectValidStoreNos(parStoreNos, zoneNo);
            if (!CollectionUtils.isEmpty(strings)) {
                result.addAll(strings);

            }
        }
        return result;
    }

    @Override
    public List<KeyValue<String,String>> pageValidShop(Collection<String> zoneNos,
                                                       Collection<String> brandNos,
                                                       String storeNoOrNameMatch,
                                                       int startRowNum,
                                                       int pageSize){
        List<OrgUnitBrandRel> orgUnitBrandRels = repository.pageValidShop(zoneNos, brandNos, storeNoOrNameMatch, startRowNum, pageSize);
        return orgUnitBrandRels.stream().map(orgUnitBrandRel -> new KeyValue<>(orgUnitBrandRel.getStoreNo(), orgUnitBrandRel.getStoreName())).collect(Collectors.toList());

    }

    @Override
    public int countValidShop(Collection<String> zoneNos,
                              Collection<String> brandNos,
                              String storeNoOrNameMatch){
        return repository.countValidShop(zoneNos, brandNos, storeNoOrNameMatch);
    }
}