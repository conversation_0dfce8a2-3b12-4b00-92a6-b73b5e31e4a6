/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderReturnDtl;
import cn.wonhigh.baize.repository.ios.RetailOrderReturnDtlRepository;

import cn.wonhigh.baize.service.ios.IRetailOrderReturnDtlService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class RetailOrderReturnDtlService extends BaseService<RetailOrderReturnDtl,String> implements  IRetailOrderReturnDtlService{
    @Autowired
    private RetailOrderReturnDtlRepository repository;

    protected IRepository<RetailOrderReturnDtl,String> getRepository(){
        return repository;
    }

    
}