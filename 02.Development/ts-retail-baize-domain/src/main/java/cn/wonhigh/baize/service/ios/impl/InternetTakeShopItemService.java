package cn.wonhigh.baize.service.ios.impl;

import cn.wonhigh.baize.model.entity.ios.InternetTakeShopItem;
import cn.wonhigh.baize.repository.ios.InternetTakeShopItemRepository;
import cn.wonhigh.baize.service.ios.IInternetTakeShopItemService;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2025/3/28 11:40
 */
@Service("internetTakeShopItemService")
public class InternetTakeShopItemService extends BaseService<InternetTakeShopItem, String> implements IInternetTakeShopItemService {

    @Resource(type = InternetTakeShopItemRepository.class)
    private InternetTakeShopItemRepository repository;

    @Override
    protected IRepository<InternetTakeShopItem, String> getRepository() {
        return repository;
    }
}
