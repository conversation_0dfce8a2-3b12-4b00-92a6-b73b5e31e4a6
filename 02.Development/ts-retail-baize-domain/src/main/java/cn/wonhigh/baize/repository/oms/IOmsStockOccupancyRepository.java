package cn.wonhigh.baize.repository.oms;

import cn.wonhigh.baize.model.dto.OmsStockOccupancyDTO;
import cn.wonhigh.baize.model.entity.oms.OmsStockOccupancy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;

/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.repository.oms
 * @Project：ts-retail-baize
 * @name：IOmsStockOccupancyRepository
 * @Date：2024/12/27 18:36
 * @Filename：IOmsStockOccupancyRepository
 * @description：
 */
@Mapper
public interface IOmsStockOccupancyRepository  extends IRepository<OmsStockOccupancy, Long> {

    OmsStockOccupancyDTO queryLockStockQty(@Param("warehouseId") String warehouseId, @Param("skuId") String skuId);

}
