/** q **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct;
import topmall.framework.service.IService;
import java.util.List;
import java.util.Map;

public interface ICommodityCorpMatchProductService extends IService<CommodityCorpMatchProduct,String>{
      
    Integer batchInsert(List<CommodityCorpMatchProduct> list);
    
    List<CommodityCorpMatchProduct> selectByIds(List<Integer> ids);

    List<CommodityCorpMatchProduct> selectItemSkuByParams(Map<String, Object> params);
}