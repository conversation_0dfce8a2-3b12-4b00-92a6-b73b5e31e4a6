/** zkh **/
package cn.wonhigh.baize.manager.gms;


import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.ZoneInfo;

import java.util.List;

public interface IZoneInfoManager extends IManager<ZoneInfo,Integer>{
    
    ZoneInfo findByUnique(String zoneNo) ;

    Integer deleteByUnique(String zoneNo);

    List<ZoneInfo> selectByUniques(List<String> zoneNos);
    
    Integer batchInsert(List<ZoneInfo> list);
    
    List<ZoneInfo> selectByIds(List<Integer> ids);
}