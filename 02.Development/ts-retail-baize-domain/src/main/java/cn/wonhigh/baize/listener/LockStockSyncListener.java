package cn.wonhigh.baize.listener;

import cn.mercury.basic.FlatMessage;
import cn.mercury.basic.query.Q;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.model.dto.IcsInventoryOmsLockDto;
import cn.wonhigh.baize.model.dto.OmsStockOccupancyDTO;
import cn.wonhigh.baize.model.entity.oms.OmsProductWmsMapping;
import cn.wonhigh.baize.model.entity.oms.OmsWarehouse;
import cn.wonhigh.baize.service.gms.impl.IcsInventoryOmsLockService;
import cn.wonhigh.baize.service.oms.IOmsProductWmsMappingService;
import cn.wonhigh.baize.service.oms.IOmsStockOccupancyService;
import cn.wonhigh.baize.service.oms.IOmsWarehouseService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.listener
 * @Project：ts-retail-baize
 * @name：LockStockSyncListener
 * @Date：2024/12/27 14:14
 * @Filename：LockStockSyncListener
 * @description：
 */
@Component
public class LockStockSyncListener {

    private static final Logger logger = LoggerFactory.getLogger(LockStockSyncListener.class);

    @Autowired
    private IOmsStockOccupancyService omsStockOccupancyService;
    @Autowired
    private IcsInventoryOmsLockService icsInventoryOmsLockService;
    @Autowired
    private IOmsProductWmsMappingService omsProductWmsMappingService;
    @Autowired
    private IOmsWarehouseService omsWarehouseService;

    static final List<String> includeTables = Arrays.asList(
            "oms_stock_occupancy"
    );

    @KafkaListener(topics = {"${tidb.cdc.oms.topic:tidb_cdc_retail_oms}"})
    public void listenerMessage(ConsumerRecord<String, String> record) {
        if (logger.isDebugEnabled()) {
            logger.debug("接收到kafka消息键为:{},消息值为:{},消息头为:{},消息分区为:{},消息主题为:{}", record.key(), record.value(), record.headers(), record.partition(), record.topic());
        }
        if (StringUtils.isBlank(record.value())) {
            return;
        }
        FlatMessage flatMessage = JsonUtils.fromJson(record.value(), FlatMessage.class);
        if(includeTables.contains(flatMessage.getTable())) {
            Map<String, Object> data = flatMessage.getData().get(0);
            String skuId = (String) data.get("sku_id");
            String warehouseId = (String) data.get("warehouse_id");
            if (StringUtils.isBlank(skuId) ||
                    StringUtils.isBlank(warehouseId) ||
                    (Objects.equals(data.get("status"), "4") && flatMessage.isDeleted())) {
                return;
            }
            List<OmsProductWmsMapping> omsProductWmsMappingList = omsProductWmsMappingService.selectByParams(Q.where("skuId", skuId));
            List<OmsWarehouse> omsWarehouseList = omsWarehouseService.selectByParams(Q.where("warehouseId", warehouseId));
            if (CollectionUtils.isEmpty(omsProductWmsMappingList) || CollectionUtils.isEmpty(omsWarehouseList)) {
                logger.warn("mapping is null,warehouse_id:{},sku_id:{}", warehouseId, skuId);
                return;
            }
            IcsInventoryOmsLockDto icsInventoryOmsLockDto = new IcsInventoryOmsLockDto();
            OmsStockOccupancyDTO omsStockOccupancyDTO = omsStockOccupancyService.queryLockStockQty(omsWarehouseList.get(0).getWarehouseCode(), omsProductWmsMappingList.get(0).getWmsSkuId());
            if (Objects.nonNull(omsStockOccupancyDTO)) {
                icsInventoryOmsLockDto.setVstoreCode(omsStockOccupancyDTO.getVirtualStoreCode());
                icsInventoryOmsLockDto.setVstoreName(omsStockOccupancyDTO.getVirtualStoreName());
                icsInventoryOmsLockDto.setLockQty(omsStockOccupancyDTO.getLockQty());
                icsInventoryOmsLockDto.setSkuNo(omsStockOccupancyDTO.getSkuCode());
            } else {
                logger.warn("getQuantity is null,warehouse_id:{},sku_id:{}", warehouseId, skuId);
                icsInventoryOmsLockDto.setVstoreCode(omsWarehouseList.get(0).getWarehouseCode());
                icsInventoryOmsLockDto.setVstoreName(omsWarehouseList.get(0).getWarehouseName());
                icsInventoryOmsLockDto.setSkuNo(omsProductWmsMappingList.get(0).getWmsSkuId());
                icsInventoryOmsLockDto.setLockQty(0);
            }
            if (StringUtils.isNotEmpty(icsInventoryOmsLockDto.getVstoreCode()) && StringUtils.isNotEmpty(icsInventoryOmsLockDto.getSkuNo())) {
                icsInventoryOmsLockService.updateStock(Collections.singletonList(icsInventoryOmsLockDto));
            }
        }
    }



    @PostConstruct
    public void init() {
        logger.info("初始化kafka消费者");
    }


}
