package cn.wonhigh.baize.repository.oms;

import cn.wonhigh.baize.model.entity.oms.OmsStockUploadStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;

/**
 * (OmsStockUploadStrategy)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-29 23:06:12
 */
@Mapper
public interface OmsStockUploadStrategyRepository extends IRepository<OmsStockUploadStrategy, Long> {
    List<OmsStockUploadStrategy> selectStrategyByVstoreId(@Param("vstoreId") List<Long> vstoreIds);
}
