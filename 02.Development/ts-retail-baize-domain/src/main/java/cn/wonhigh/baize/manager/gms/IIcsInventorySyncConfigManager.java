/** q **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;

import java.util.List;

public interface IIcsInventorySyncConfigManager extends IManager<IcsInventorySyncConfig,String> {
    
    Integer batchInsert(List<IcsInventorySyncConfig> list);
    
    List<IcsInventorySyncConfig> selectByIds(List<Integer> ids);

    List<IcsInventorySyncConfig> selectShopPageByParams(Query query, Pagenation pagenation);
    Integer selectShopCountByParams(Query var1) throws ManagerException;


    List<InternetVirtualWarehouseInfo> selectVstoreListByParams(Query query);

    Integer countByChannel(Query var1) throws ManagerException;

    List<IcsInventorySyncConfig> pageByChannel(Query var1, Pagenation var2) throws ManagerException;

    List<IcsInventorySyncConfig> selectChannelVstoreInfo(Query query) throws ManagerException;
}