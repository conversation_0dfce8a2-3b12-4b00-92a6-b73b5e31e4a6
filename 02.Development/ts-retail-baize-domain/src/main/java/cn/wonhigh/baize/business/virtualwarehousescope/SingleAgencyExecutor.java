package cn.wonhigh.baize.business.virtualwarehousescope;

import cn.mercury.basic.UUID;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import cn.wonhigh.baize.utils.common.QueryUtil;
import org.apache.commons.lang.StringUtils;
import topmall.framework.security.Authorization;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SingleAgencyExecutor extends AbstractVirtualWarehouseScopeSaveExecutor {

    public SingleAgencyExecutor(InternetVirtualWarehouseScopeSave saveData) {
        super(saveData);
    }


    @Override
    public void addData () {
        if (this.saveData.getAddDtls() != null) {
            for (InternetVirtualWarehouseScopeSave.SaveDtl addDtl : this.saveData.getAddDtls()) {
                addData(addDtl);
            }
        }
    }


    private void addData(InternetVirtualWarehouseScopeSave.SaveDtl temp) {

        InternetVirtualWarehouseScope virtualWarehouseScope = new InternetVirtualWarehouseScope();
        String uuid = UUID.gernerate();
        virtualWarehouseScope.setId(uuid);
        virtualWarehouseScope.setVstoreCode(saveData.getVstoreCode());
        virtualWarehouseScope.setVstoreName(this.internetVirtualWarehouseInfo.getVstoreName());
        virtualWarehouseScope.setVstoreType(this.internetVirtualWarehouseInfo.getVstoreType());
        virtualWarehouseScope.setStoreType(saveData.getStoreType());
        virtualWarehouseScope.setMoreStoreFlag(Integer.valueOf(saveData.getMoreStoreFlag()));
        virtualWarehouseScope.setStoreNo(temp.getStoreNo());
        virtualWarehouseScope.setStoreName(temp.getStoreName());
        virtualWarehouseScope.setOrderUnitNo(saveData.getOrderUnitNo());
        virtualWarehouseScope.setOrderUnitName(saveData.getOrderUnitName());
        virtualWarehouseScope.setInventoryType(saveData.getInventoryType());
        virtualWarehouseScope.setStatus(this.saveData.getStatus());
        virtualWarehouseScope.setCreateUser(Authorization.getUser().getName());
        Date date = new Date();
        virtualWarehouseScope.setCreateTime(date);
        virtualWarehouseScope.setUpdateUser(Authorization.getUser().getName());
        virtualWarehouseScope.setUpdateTime(date);
        // 查询是否有删除的（禁用）记录，如果有，则先删除，再添加
        Map<String,Object> params = new HashMap<>();
        params.put("vstoreCode", saveData.getVstoreCode());
        params.put("vstoreType", this.internetVirtualWarehouseInfo.getVstoreType());
        params.put("storeType", saveData.getStoreType());
        params.put("orderUnitNo", saveData.getOrderUnitNo());
        params.put("storeNo", temp.getStoreNo());
        params.put("status", 0);
        params.put("moreStoreFlag", 0);
        List<InternetVirtualWarehouseScope> listExit = this.internetVirtualWarehouseScopeManager.selectByParams(QueryUtil.mapToQuery(params));
        for (InternetVirtualWarehouseScope virtual : listExit) {
            this.internetVirtualWarehouseScopeManager.deleteByPrimaryKey(virtual.getId());
        }

//						virtualWarehouseScopeManager.add(virtualWarehouseScope);

        extracted(virtualWarehouseScope);
        //verifyVirtualWarehouseScopeAdd(virtualWarehouseInfo,virtualWarehouseScope,vstoreCode);
        String inventoryTypeStr = "";
        //转换数字含义
        if(virtualWarehouseScope.getInventoryType()==1)
        {
            inventoryTypeStr = "共享";
        }else if(virtualWarehouseScope.getInventoryType()==2){
            inventoryTypeStr = "独享";
        }else if(virtualWarehouseScope.getInventoryType()==3){
            inventoryTypeStr = "电商";
        }
        String storeName = this.internetVirtualWarehouseInfo.getVstoreName();
        if (StringUtils.isEmpty(virtualWarehouseScope.getStoreNo())) {
            storeName = "所有店";
        }
        /*InternetSystemLogs internetSystemLogs = new InternetSystemLogs();
        internetSystemLogs.setCreateUser(CurrentUser.getCurrentUser().getUsername());
        internetSystemLogs.setOpscode("insert");
        internetSystemLogs.setSyscode("1002");
        internetSystemLogs.setSysname("虚拟仓-库存范围");
        internetSystemLogs.setKeyword1(vstoreCode);
        internetSystemLogs.setKeyword1info("虚仓编码:"+vstoreCode+"("+virtualWarehouseInfo.getVstoreName()+")");
        internetSystemLogs.setKeyword2(virtualWarehouseScope.getStoreNo());
        internetSystemLogs.setKeyword2info("机构:"+virtualWarehouseScope.getStoreNo()+"("+storeName+")");
        internetSystemLogs.setRemark("新增：虚仓编码:"+vstoreCode+"("+virtualWarehouseInfo.getVstoreName()+");机构:"+
                virtualWarehouseScope.getStoreNo()+"("+(virtualWarehouseScope.getMoreStoreFlag()==1?"所有店":virtualWarehouseScope.getStoreName())+");货管:"+
                virtualWarehouseScope.getOrderUnitNo()+"("+virtualWarehouseScope.getOrderUnitName()+");库存类型:"+inventoryTypeStr);
        LOGGER.info("insert internetSystemLogs={}",internetSystemLogs);
        internetSystemLogsManager.add(internetSystemLogs);*/
    }
}