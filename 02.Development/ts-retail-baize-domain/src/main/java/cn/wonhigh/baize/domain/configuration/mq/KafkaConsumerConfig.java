package cn.wonhigh.baize.domain.configuration.mq;

import cn.hutool.core.util.StrUtil;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author：wlw
 * @Package：cn.wonhigh.baize.domain.configuration.mq
 * @Project：ts-retail-baize
 * @name：KafkaConsumerConfig
 * @Date：2024/12/27 15:03
 * @Filename：KafkaConsumerConfig
 * @description：
 */
@Configuration
@EnableKafka
public class KafkaConsumerConfig implements InitializingBean {
    private static final Logger logger = LoggerFactory.getLogger(KafkaConsumerConfig.class);

    @Value("${cdc.kafka.bootstrap.servers}")
    private String BOOTSTRAP_SERVERS_CONFIG;

    @Value("${kafka.group.id:baize-group}")
    private String GROUP_ID_CONFIG;




    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS_CONFIG);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, GROUP_ID_CONFIG);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
        return new DefaultKafkaConsumerFactory<>(props);
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(3);
        factory.getContainerProperties().setPollTimeout(3000);
        return factory;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        logger.info("KafkaConsumerConfig init BOOTSTRAP_SERVERS_CONFIG:{}", BOOTSTRAP_SERVERS_CONFIG);
        logger.info("KafkaConsumerConfig init kafka.group.id:{}", GROUP_ID_CONFIG);
        System.out.println("KafkaConsumerConfig init");
        if (StrUtil.isBlank(BOOTSTRAP_SERVERS_CONFIG)) {
            throw new IllegalArgumentException("cdc.kafka.bootstrap.servers is empty");
        }
    }
}
