package cn.wonhigh.baize.domain.configuration.datasource;

import cn.mercury.utils.DateUtil;
import com.alibaba.druid.pool.DruidPooledConnection;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.jboss.netty.util.internal.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.ConcurrentMap;

@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class, method = "queryCursor", args = {MappedStatement.class, Object.class, RowBounds.class})
})
public class ApOperationInterceptor implements Interceptor {
    private static final Logger logger = LoggerFactory.getLogger("cn.wonhigh.baize.repository.ApOperationInterceptor");

    private static final ConcurrentMap<Connection,Date> CONN_SNAP = new ConcurrentHashMap<>();

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (!isSlaveDataSource()) {
            return invocation.proceed();
        }
        prohibitChangeData(invocation);
        try {
            executeBeforeSql(invocation);
            return invocation.proceed();
        } finally {
            // 每次执行sql都有前置处理,所有这个方法可以省略
            // executeAfterSql(invocation);
        }
    }


    @Override
    public Object plugin(Object target) {
        if (!isSlaveDataSource()) {
            return target;
        }

        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        }

        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 可配置拦截规则
    }

    // 判断当前是否使用从库数据源
    private boolean isSlaveDataSource() {
        // 从 ThreadLocal 中获取当前数据源标识
        if (ApDataSourceSwitch.openedAp()) {
            return true;
        }
        return false;
    }



    // 禁止数据修改操作
    private void prohibitChangeData(Invocation invocation) {
        Object target = invocation.getTarget();
        if (!(target instanceof Executor)) {
            return;
        }

        String methodName = invocation.getMethod().getName();
        if (!methodName.equalsIgnoreCase("update")) {
            return;
        }

        // 获取 MappedStatement 和参数
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];

        // 获取 SQL
        String sql = ms.getBoundSql(parameter).getSql().trim();
        sql = sql.replaceAll("[\\s]+", " ").trim();

        throw new RuntimeException("从库禁止执行更新操作: " + sql);
    }

    private void executeBeforeSql(Invocation invocation) throws Exception {
        if (!querySql(invocation)) {
            return;
        }

        Connection connection = getConnectionFromInvocation(invocation);

        Date snapshot = null;
        ApDataSourceSwitch apDataSourceSwitch = ApDataSourceSwitch.apSwitch();
        if (apDataSourceSwitch != null
                && apDataSourceSwitch.isUseAp()
                && apDataSourceSwitch.getSnapshotTime() != null) {
            snapshot = apDataSourceSwitch.getSnapshotTime();
        }

        //同一个连接不会被多个线程持有
        Connection physicalConn = null;
        if(connection instanceof DruidPooledConnection){
            DruidPooledConnection druidPooledConnection = (DruidPooledConnection) connection;
            physicalConn = druidPooledConnection.getConnectionHolder().getConnection();
            if (physicalConn != null) {
                Date dateAlreadySet = CONN_SNAP.get(physicalConn);
                if(Objects.equals(dateAlreadySet, snapshot)){
                    logger.debug("当前连接已经设置快照时间:{}", dateAlreadySet == null
                            ? "" : DateUtil.format(dateAlreadySet, DateUtil.LONG_DATE_FORMAT));
                    return;
                }
            }
        }

        executeSqls(connection, Arrays.asList(buildSnapSql(snapshot)), "前置");

        if(physicalConn != null){
            CONN_SNAP.put(physicalConn, snapshot);
        }
    }

    private void executeAfterSql(Invocation invocation) throws Exception {
        if (!querySql(invocation)) {
            return;
        }

        Connection connection = getConnectionFromInvocation(invocation);

        executeSqls(connection, Arrays.asList(buildSnapSql(null)), "后置");
    }


    private String buildSnapSql(Date snapshot){
        if (snapshot == null) {
            return "set @@tidb_snapshot=\"\"";
        }
        String format = DateUtil.format(snapshot, DateUtil.LONG_DATE_FORMAT);
        return  "set @@tidb_snapshot=\"" + format + "\"";
    }

    private Connection getConnectionFromInvocation(Invocation invocation) throws SQLException {
        Object target = invocation.getTarget();
        if (target instanceof Executor) {
            Executor executor = (Executor) target;
            return executor.getTransaction().getConnection();
        }
        throw new IllegalStateException("Cannot obtain connection from the current invocation target");
    }

    private boolean querySql(Invocation invocation) {
        Object target = invocation.getTarget();
        if (!(target instanceof Executor)) {
            return false;
        }

        String methodName = invocation.getMethod().getName();
        if (methodName.equalsIgnoreCase("query")) {
            return true;
        }
        if (methodName.equalsIgnoreCase("queryCursor")) {
            return true;
        }
        return false;
    }


    // 执行 SQL 列表
    private void executeSqls(Connection connection, List<String> sqls, String type) throws Exception {
        if (sqls == null || sqls.isEmpty()) {
            return;
        }

        // logger.debug("开始执行{}SQL，共{}条", type, sqls.size());

        for (String sql : sqls) {
            if (sql == null || sql.trim().isEmpty()) {
                continue;
            }

            long startTime = System.currentTimeMillis();
            try (Statement statement = connection.createStatement()) {
                statement.execute(sql);
                long endTime = System.currentTimeMillis();
                logger.debug("{}SQL: {} 执行完成，耗时:{}ms", type, sql, (endTime - startTime));
            } catch (Exception e) {
                logger.error("{}SQL: {} 执行失败", type, sql, e);
                // 可根据配置决定是否抛出异常
                throw new RuntimeException(type + "SQL 执行失败", e);
            }
        }
    }
}