/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.LookupEntry;
import cn.wonhigh.baize.repository.gms.LookupEntryRepository;

import cn.wonhigh.baize.service.gms.ILookupEntryService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class LookupEntryService extends BaseService<LookupEntry,String> implements  ILookupEntryService{
    @Autowired
    private LookupEntryRepository repository;

    protected IRepository<LookupEntry,String> getRepository(){
        return repository;
    }
      
	public List<LookupEntry> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<LookupEntry> list) {
    	return repository.batchInsert(list);
    }
}