package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.InternetAvailableInventory;
import cn.wonhigh.baize.model.entity.gms.VstoreStoreAvailableInventory;
import cn.wonhigh.baize.repository.gms.InternetSearchInventoryRepository;
import cn.wonhigh.baize.service.gms.IInventorySearchService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Spliterator;
import java.util.Spliterators;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

@Service
public class InventorySearchService implements IInventorySearchService {

    @Resource
    private InternetSearchInventoryRepository repository;

    @Override
    public List<InternetAvailableInventory> findAvailableInOneStore(Map<String, Object> params) {
        return repository.findAvailableInOneStore(params);
    }


    @Override
    public Stream<List<VstoreStoreAvailableInventory>> streamVstoreQty(List<String> skuNos,
                                                                       List<String> vstoreCodeList) {

        int pageSize = 5000;
        Iterator<List<VstoreStoreAvailableInventory>> iterator = new Iterator<List<VstoreStoreAvailableInventory>>() {
            int pageId = 1;

            List<VstoreStoreAvailableInventory> result = null;

            @Override
            public boolean hasNext() {
                // 最后一次没查询满代表后续没数据了，则返回false
                if (result != null && !result.isEmpty() && result.size() < pageSize) {
                    return false;
                }

                result = pageVstoreQty(skuNos, vstoreCodeList, pageId, pageSize);

                if (result == null || result.isEmpty()) {
                    return false;
                }
                pageId++;
                return true;
            }

            @Override
            public List<VstoreStoreAvailableInventory> next() {
                return result;
            }
        };
        Spliterator<List<VstoreStoreAvailableInventory>> spliterator = Spliterators.spliteratorUnknownSize(iterator, Spliterator.NONNULL);
        return StreamSupport.stream(spliterator, false);

    }

    @Override
    public List<VstoreStoreAvailableInventory> pageVstoreQty(List<String> skuNos,
                                                             List<String> vstoreCodeList,
                                                             int pageNum,
                                                             int pageSize) {
        return repository.queryVstoreQtyDetail(skuNos, vstoreCodeList, (pageNum - 1) * pageSize, pageSize);
    }


    @Override
    public int countVstoreQty(List<String> skuNos,
                              List<String> vstoreCodeList) {
        Integer counted = repository.countVstoreQty(skuNos, vstoreCodeList);
        return counted == null ? 0 : counted;
    }
}
