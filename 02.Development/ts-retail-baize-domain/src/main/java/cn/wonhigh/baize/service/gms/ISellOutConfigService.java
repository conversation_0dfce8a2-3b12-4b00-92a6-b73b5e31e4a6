/**  **/
package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
import cn.wonhigh.baize.model.entity.gms.ItemAttr;
import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
import org.apache.ibatis.session.ResultHandler;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import topmall.framework.service.IService;

import java.util.List;
import java.util.Map;

public interface ISellOutConfigService extends IService<SellOutConfig,String>{
      
    Integer batchInsert(List<SellOutConfig> list);
    
    List<SellOutConfig> selectByIds(List<Integer> ids);

    List<ItemAttr> getShareClassifyList(Query query);

    int selectSellOutCount(Query query);

    List<SellOutConfigDto> selectSellOutList(Query query, Pagenation page);

	int updateStatusByParams(Query q);

    List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list);

    void selectByParamsForHandler(Map<String, Object> map, ResultHandler<SellOutConfig> o);
}