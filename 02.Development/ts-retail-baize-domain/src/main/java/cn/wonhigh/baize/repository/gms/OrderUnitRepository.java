/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.OrderUnit;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderUnitRepository extends IRepository<OrderUnit,Integer> {
    
    public OrderUnit findByUnique(@Param("orderUnitNo") String orderUnitNo);

    public Integer deleteByUnique(@Param("orderUnitNo") String orderUnitNo);

    public Integer insertForUpdate(OrderUnit entry);

    
}