/**  **/
package cn.wonhigh.baize.service.gms;

import cn.mercury.basic.KeyValue;
import cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel;
import topmall.framework.service.IService;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IOrgUnitBrandRelService extends IService<OrgUnitBrandRel,Integer>{

    /**
     * @description  根据货管编码查实仓和网销店机构
     * @param orderUnitNo 货管编码
     * @return
     */
    List<OrgUnitBrandRel> selectStoreByUnitNo(String orderUnitNo);

    List<OrgUnitBrandRel> selectValidByStoreNo(String storeNo);

    Set<String> selectValidBrandNoByStoreNo(String storeNo);

    /**
     * @param storeNos
     * @return key: storeNo, value: zoneNo, zoneName
     */
    Map<String, KeyValue<String,String>> selectValidStoreNoZones(List<String> storeNos);

    Set<String> selectValidStoreNos(List<String> storeNos, String zoneNo);

    List<KeyValue<String,String>> pageValidShop(Collection<String> zoneNos,
                                                Collection<String> brandNos,
                                                String storeNoOrNameMatch,
                                                int startRowNum,
                                                int pageSize);

    int countValidShop(Collection<String> zoneNos,
                       Collection<String> brandNos,
                       String storeNoOrNameMatch);
}