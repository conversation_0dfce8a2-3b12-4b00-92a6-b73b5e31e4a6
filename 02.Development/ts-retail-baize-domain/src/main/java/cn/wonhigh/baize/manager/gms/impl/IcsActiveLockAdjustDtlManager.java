/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.wonhigh.baize.manager.gms.IIcsActiveLockAdjustDtlManager;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.service.gms.IIcsActiveLockAdjustDtlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.List;
import java.util.Map;

@Service
public class IcsActiveLockAdjustDtlManager extends BaseManager<IcsActiveLockAdjustDtl,String> implements IIcsActiveLockAdjustDtlManager{

    @Autowired
    private IIcsActiveLockAdjustDtlService service;

    protected IService<IcsActiveLockAdjustDtl,String> getService(){
        return service;
    }
    
    @Override
	public List<IcsActiveLockAdjustDtl> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}

	@Override
	public List<IcsActiveLockAdjustDtl> selectActiveDtlList(Map<String, Object> map, Pagenation page) {
		return service.selectActiveDtlList(map, page);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void batchInsertOrUpdate(List<IcsActiveLockAdjustDtl> dtlList) {
		if (CollectionUtils.isEmpty(dtlList)) {
			return;
		}
		service.batchSaveOrUpdateDtl(dtlList);
	}

	@Override
	public void batchUpdateSyncStatus(List<IcsActiveLockAdjustDtl> adjustDtlList, int syncStatus) {
		service.batchUpdateSyncStatus(adjustDtlList, syncStatus);
	}

	@Override
	public Integer batchInsert(List<IcsActiveLockAdjustDtl> list) {
		list.forEach(this::initEntry);
		return service.batchInsert(list);
	}
}