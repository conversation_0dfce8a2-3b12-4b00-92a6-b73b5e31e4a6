package cn.wonhigh.baize.business.active.adjust;

import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjustDtl;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动锁库调整服务接口
 * @date 2025/6/11 10:46
 */
public interface ActiveLockAdjustService {

    /**
     * 校验活动锁库调整单明细
     * @return
     */
    List<IcsActiveLockAdjustDtl> check();

    /**
     * 导入活动锁库调整单明细
     * @param objectList
     */
    void importDtl(List<Object> objectList);

    /**
     * 新增保存活动锁库调整单明细
     * @param adjustDtl
     */
    void saveDtl(IcsActiveLockAdjustDtl adjustDtl);

    /**
     * 审批活动锁库调整单
     */
    void audit();

    /**
     * 取消作废活动锁库调整单
     */
    void cancel();

    /**
     * 失败重传
     */
    void failureSync();

    /**
     * 新增活动锁库调整单
     * @param adjust
     */
    void create(IcsActiveLockAdjust adjust);

    /**
     * 操作生效同步库存
     */
    void effect();
}
