/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderReturn;
import cn.wonhigh.baize.service.ios.IRetailOrderReturnService;
import cn.wonhigh.baize.manager.ios.IRetailOrderReturnManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class RetailOrderReturnManager extends BaseManager<RetailOrderReturn,String> implements IRetailOrderReturnManager{
    @Autowired
    private IRetailOrderReturnService service;

    protected IService<RetailOrderReturn,String> getService(){
        return service;
    }

    
    public RetailOrderReturn findByUnique(String billNo) {
        try {
			return service.findByUnique(billNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String billNo) {
        try {
			return service.deleteByUnique(billNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(RetailOrderReturn entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}