package cn.wonhigh.baize.business.virtualwarehousescope;

import cn.mercury.basic.UUID;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import org.apache.commons.lang3.ObjectUtils;
import topmall.framework.security.Authorization;

import javax.validation.ValidationException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class MultiAgencyExecutor extends AbstractVirtualWarehouseScopeSaveExecutor {




    public MultiAgencyExecutor(InternetVirtualWarehouseScopeSave saveData) {
        super(saveData);
    }


    @Override
    public void addData() {
        InternetVirtualWarehouseScope virtualWarehouseScope = new  InternetVirtualWarehouseScope();
        String uuid = UUID.gernerate();
        virtualWarehouseScope.setId(uuid);
        virtualWarehouseScope.setVstoreCode(this.internetVirtualWarehouseInfo.getVstoreCode());
        virtualWarehouseScope.setVstoreName(this.internetVirtualWarehouseInfo.getVstoreName());
        virtualWarehouseScope.setVstoreType(this.internetVirtualWarehouseInfo.getVstoreType());
        virtualWarehouseScope.setStoreType(this.saveData.getStoreType());
        virtualWarehouseScope.setMoreStoreFlag(Integer.parseInt(this.saveData.getMoreStoreFlag()));
        virtualWarehouseScope.setOrderUnitNo(this.saveData.getOrderUnitNo());
        virtualWarehouseScope.setOrderUnitName(this.saveData.getOrderUnitNo());
        virtualWarehouseScope.setInventoryType(this.saveData.getInventoryType());
        virtualWarehouseScope.setStatus(1);
        virtualWarehouseScope.setCreateUser(Authorization.getUser().getName());
        Date date = new Date();
        virtualWarehouseScope.setCreateTime(date);
        virtualWarehouseScope.setUpdateUser(Authorization.getUser().getName());
        virtualWarehouseScope.setUpdateTime(date);

        deleteScope();

        extracted(virtualWarehouseScope);

    }

    private void deleteScope() {
        Query params = Query.empty();
        params.and("vstoreCode", this.internetVirtualWarehouseInfo.getVstoreCode());
        params.and("vstoreType", this.internetVirtualWarehouseInfo.getVstoreType());
        params.and("storeType", saveData.getStoreType());
        params.and("orderUnitNo", saveData.getOrderUnitNo());
        params.and("status", 0);
        params.and("moreStoreFlag", 1);

        List<InternetVirtualWarehouseScope> listExit = this.internetVirtualWarehouseScopeManager.selectByParams(params);
        if (listExit != null) {
            for (InternetVirtualWarehouseScope virtual : listExit) {
                this.internetVirtualWarehouseScopeManager.deleteByPrimaryKey(virtual.getId());
            }
        }
    }



}