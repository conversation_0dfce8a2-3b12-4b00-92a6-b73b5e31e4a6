/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.ExternalProductMapping;
import org.apache.ibatis.annotations.Mapper;
import reactor.util.function.Tuple3;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface ExternalProductMappingRepository extends IRepository<ExternalProductMapping, String> {

    Integer batchInsert(@Param("list") List<ExternalProductMapping> list);

    List<ExternalProductMapping> selectByIds(@Param("list") List<Integer> ids);

    List<ExternalProductMapping> selectItemSkuByParams(@Param("params") Map<String, Object> params);

    List<ExternalProductMapping> selectItemsByTuple3(@Param("merchantsCode") String merchantsCode, @Param("itemBrandSizeList") List<Tuple3<String, String, String>> itemBrandSizeList);
}