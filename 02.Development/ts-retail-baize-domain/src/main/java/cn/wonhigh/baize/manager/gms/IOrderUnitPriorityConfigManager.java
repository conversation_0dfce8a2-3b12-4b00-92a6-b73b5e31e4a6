/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig;

import cn.mercury.manager.IManager;
import java.util.List;

public interface IOrderUnitPriorityConfigManager extends IManager<OrderUnitPriorityConfig,String>{
    
    Integer batchInsert(List<OrderUnitPriorityConfig> list);
    
    List<OrderUnitPriorityConfig> selectByIds(List<Integer> ids);
}