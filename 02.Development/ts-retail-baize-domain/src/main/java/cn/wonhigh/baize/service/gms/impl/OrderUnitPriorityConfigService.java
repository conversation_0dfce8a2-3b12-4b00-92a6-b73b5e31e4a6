/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig;
import cn.wonhigh.baize.repository.gms.OrderUnitPriorityConfigRepository;

import cn.wonhigh.baize.service.gms.IOrderUnitPriorityConfigService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class OrderUnitPriorityConfigService extends BaseService<OrderUnitPriorityConfig,String> implements  IOrderUnitPriorityConfigService{
    @Autowired
    private OrderUnitPriorityConfigRepository repository;

    protected IRepository<OrderUnitPriorityConfig,String> getRepository(){
        return repository;
    }
      
	public List<OrderUnitPriorityConfig> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<OrderUnitPriorityConfig> list) {
    	return repository.batchInsert(list);
    }
}