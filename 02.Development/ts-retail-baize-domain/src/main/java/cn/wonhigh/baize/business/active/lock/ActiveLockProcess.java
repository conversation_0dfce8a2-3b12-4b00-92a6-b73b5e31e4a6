package cn.wonhigh.baize.business.active.lock;

import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/7/1 11:15
 */
public abstract class ActiveLockProcess implements ActiveLockService {

    protected final InventoryActiveLock inventoryActiveLock;

    public ActiveLockProcess(InventoryActiveLock inventoryActiveLock) {
        this.inventoryActiveLock = inventoryActiveLock;
    }
}
