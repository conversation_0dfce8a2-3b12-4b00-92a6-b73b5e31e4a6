/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.Brand;
import cn.wonhigh.baize.repository.gms.BrandRepository;

import cn.wonhigh.baize.service.gms.IBrandService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

@Service("gmsBrandService")
public class BrandService extends BaseService<Brand,Integer> implements  IBrandService{

    @Resource(type = BrandRepository.class)
    private BrandRepository repository;

    protected IRepository<Brand,Integer> getRepository(){
        return repository;
    }
    
    public Brand findByUnique(String brandNo){
        return repository.findByUnique(brandNo);
    }

    public Integer deleteByUnique(String brandNo){
        return repository.deleteByUnique(brandNo);
    }

    public List<Brand> selectByUniques(List<String> brandNos) {
        return repository.selectByUniques(brandNos);
    }
      
	public List<Brand> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<Brand> list) {
    	return repository.batchInsert(list);
    }
}