package cn.wonhigh.baize.business.active.adjust.puma;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/6/19 14:07
 */
public class InventoryItem {

    private String customerCode;

    private String invTime;

    private String ownerCode;

    private String warehouseCode;

    private String upc;

    private int invQty;

    private String uniqueKey;

    private String sourceSys;

    private int invStatusCode;

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getInvTime() {
        return invTime;
    }

    public void setInvTime(String invTime) {
        this.invTime = invTime;
    }

    public String getOwnerCode() {
        return ownerCode;
    }

    public void setOwnerCode(String ownerCode) {
        this.ownerCode = ownerCode;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getUpc() {
		return upc;
	}

	public void setUpc(String upc) {
		this.upc = upc;
	}

	public int getInvQty() {
        return invQty;
    }

    public void setInvQty(int invQty) {
        this.invQty = invQty;
    }

    public String getUniqueKey() {
        return uniqueKey;
    }

    public void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    public String getSourceSys() {
        return sourceSys;
    }

    public void setSourceSys(String sourceSys) {
        this.sourceSys = sourceSys;
    }

    public int getInvStatusCode() {
        return invStatusCode;
    }

    public void setInvStatusCode(int invStatusCode) {
        this.invStatusCode = invStatusCode;
    }

    public static class Builder {
        private String customerCode;
        private String invTime;
        private String ownerCode;
        private String warehouseCode;
        private String upc;
        private int invQty;
        private String uniqueKey;
        private String sourceSys;
        private int invStatusCode;

        public Builder customerCode(String customerCode) {
            this.customerCode = customerCode;
            return this;
        }
        public Builder invTime(String invTime) {
            this.invTime = invTime;
            return this;
        }
        public Builder ownerCode(String ownerCode) {
            this.ownerCode = ownerCode;
            return this;
        }
        public Builder warehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
            return this;
        }
        public Builder upc(String upc) {
        	this.upc = upc;
        	return this;
        }
        public Builder invQty(int invQty) {
            this.invQty = invQty;
            return this;
        }
        public Builder uniqueKey(String uniqueKey) {
            this.uniqueKey = uniqueKey;
            return this;
        }
        public Builder sourceSys(String sourceSys) {
            this.sourceSys = sourceSys;
            return this;
        }
        public Builder invStatusCode(int invStatusCode) {
            this.invStatusCode = invStatusCode;
            return this;
        }
        public InventoryItem build() {
            InventoryItem item = new InventoryItem();
            item.setCustomerCode(customerCode);
            item.setInvTime(invTime);
            item.setOwnerCode(ownerCode);
            item.setWarehouseCode(warehouseCode);
            item.setUpc(upc);
            item.setInvQty(invQty);
            item.setUniqueKey(uniqueKey);
            item.setSourceSys(sourceSys);
            item.setInvStatusCode(invStatusCode);
            return item;
        }
    }
}
