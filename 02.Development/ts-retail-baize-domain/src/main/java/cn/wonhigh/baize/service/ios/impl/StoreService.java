/**  **/
package cn.wonhigh.baize.service.ios.impl;

import cn.wonhigh.baize.model.entity.ios.Store;
import org.springframework.stereotype.Service;

import cn.wonhigh.baize.repository.ios.StoreRepository;

import cn.wonhigh.baize.service.ios.IStoreService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class StoreService extends BaseService<Store,Integer> implements  IStoreService{
    @Autowired
    private StoreRepository repository;

    protected IRepository<Store,Integer> getRepository(){
        return repository;
    }

    
    public Store findByUnique(String storeNo){
        return repository.findByUnique(storeNo);
    }

    public Integer deleteByUnique(String storeNo){
        return repository.deleteByUnique(storeNo);
    }

    public Integer insertForUpdate(Store entry){
       return repository.insertForUpdate(entry);
    }
    
}