/** zkh **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.InternetAreaRelation;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface InternetAreaRelationRepository extends IRepository<InternetAreaRelation,String> {
    
    Integer batchInsert(@Param("list") List<InternetAreaRelation> list);
    
    List<InternetAreaRelation> selectByIds(@Param("list") List<Integer> ids);

    List<InternetAreaRelation> selectByRetailCodeName(@Param("params")Map<String, Object> params);
}