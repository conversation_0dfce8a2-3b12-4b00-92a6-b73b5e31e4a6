/**
 *
 **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.business.active.adjust.puma.FullSkuInventory;
import cn.wonhigh.baize.business.active.adjust.puma.InventoryItem;
import cn.wonhigh.baize.events.activelockdtl.ActiveLockDtlSyncEvent;
import cn.wonhigh.baize.manager.gms.IInventoryActiveLockManager;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import cn.wonhigh.baize.model.entity.gms.ExternalProductMapping;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLock;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import cn.wonhigh.baize.model.enums.AdjustDtlSyncStatusEnums;
import cn.wonhigh.baize.model.enums.BillTypeEnumsISP;
import cn.wonhigh.baize.model.enums.BrandMerchantCodeEnums;
import cn.wonhigh.baize.service.gms.IExternalProductMappingService;
import cn.wonhigh.baize.service.gms.IInventoryActiveLockDtlService;
import cn.wonhigh.baize.service.gms.IInventoryActiveLockService;
import cn.wonhigh.baize.utils.common.QueryUtil;
import cn.wonhigh.baize.utils.common.baozun.BaoZunHttpUtils;
import cn.wonhigh.baize.utils.common.baozun.BaoZunResponse;
import cn.wonhigh.baize.utils.helpers.ShardingFlagHelper;
import cn.wonhigh.retail.gms.api.service.InventoryOccupiedApi;
import cn.wonhigh.retail.gms.api.vo.ShopInventoryOccupiedDtlDto;
import cn.wonhigh.retail.gms.api.vo.ShopInventoryOccupiedDto;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;
import topmall.framework.function.Actions;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class InventoryActiveLockManager extends BaseManager<InventoryActiveLock, String> implements IInventoryActiveLockManager {

    public static final Logger logger = org.slf4j.LoggerFactory.getLogger(InventoryActiveLockManager.class);

    @Autowired
    private IInventoryActiveLockService service;

    @Autowired
    private IInventoryActiveLockDtlService inventoryActiveLockDtlService;

    @Autowired
    private ShardingFlagHelper shardingFlagHelper;

    @Autowired
    @SuppressWarnings("all")
    private InventoryOccupiedApi inventoryOccupiedApi;

    @Resource
    private IExternalProductMappingService externalProductMappingService;

    protected IService<InventoryActiveLock, String> getService() {
        return service;
    }

    public InventoryActiveLock findByUnique(String billNo) {
        return service.findByUnique(billNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer deleteByUnique(String billNo) {
        Assert.notNull(billNo, "billNo is null");
        inventoryActiveLockDtlService.deleteByParams(QueryUtil.mapToQuery(Collections.singletonMap("billNo", billNo)));
        return service.deleteByParams(QueryUtil.mapToQuery(Collections.singletonMap("billNo", billNo)));
    }


    public List<InventoryActiveLock> selectByUniques(List<String> billNos) {
        return service.selectByUniques(billNos);
    }

    public List<InventoryActiveLock> selectByIds(List<Integer> ids) {
        return service.selectByIds(ids);
    }

    public Integer batchInsert(List<InventoryActiveLock> list) {
        Assert.notEmpty(list, "list is empty");
        list.forEach(this::initEntry);
        return service.batchInsert(list);
    }

    @Override
    public List<InventoryActiveLock> selectByPage(Query query, Pagenation page) throws ManagerException {
        List<InventoryActiveLock> list = super.selectByPage(query, page);
        if (list != null && query.asMap().containsKey("isTotalSum") && Objects.equals(query.asMap().get("isTotalSum").toString(), "true")) {
            List<String> billNos = list.stream().map(InventoryActiveLock::getBillNo).distinct().collect(Collectors.toList());
            List<Map<String, Object>> sumList = inventoryActiveLockDtlService.selectByBillNoQty(billNos);
            if (null != sumList) {
                for (InventoryActiveLock inventoryActiveLock : list) {
                    long totalLockQty = sumList.stream().filter(t -> Objects.equals(t.get("billNo"), inventoryActiveLock.getBillNo())).map(it -> {
                        if (it.containsKey("totalLockQty")) {
                            return NumberUtils.toLong(it.get("totalLockQty").toString());
                        }
                        return 0L;
                    }).findFirst().orElse(0L);

                    inventoryActiveLock.setTotalLockQty(totalLockQty);

                    long totalBalanceLockQty = sumList.stream().filter(t -> Objects.equals(t.get("billNo"), inventoryActiveLock.getBillNo())).map(it -> {
                        if (it.containsKey("totalBalanceLockQty")) {
                            return NumberUtils.toLong(it.get("totalBalanceLockQty").toString());
                        }
                        return 0L;
                    }).findFirst().orElse(0L);
                    inventoryActiveLock.setTotalBalanceLockQty(totalBalanceLockQty);
                }
            }
        }

        return list;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approve(InventoryActiveLock activeLock) {


        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("billNo", activeLock.getBillNo());
        List<InventoryActiveLockDtl> activeLockDtls = inventoryActiveLockDtlService.selectByParams(QueryUtil.mapToQuery(queryMap));

        logger.info("开始活动预占, billNo:{}, activeLockDtls:{}", activeLock.getBillNo(), activeLockDtls.size());
        try {
            this.update(activeLock);
            activeLockOccupiedInventory(activeLock, activeLockDtls);
        } catch (Exception e) {
            logger.error("活动预占失败, billNo:{},  activeLockOccupiedInventory error, msg:{}", activeLock.getBillNo(), e.getMessage(), e);
            throw new ManagerException(e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stop(InventoryActiveLock activeLock) throws ManagerException {
        Assert.notNull(activeLock, "activeLock is null");
        Assert.notNull(activeLock.getBillNo(), "activeLock billNo is null");

        List<InventoryActiveLockDtlOccupied> activeLockDtls = inventoryActiveLockDtlService.selectByInventoryActiveLockDtlQuery(
                InventoryActiveLockDtlQuery.builder()
                        .billNo(activeLock.getBillNo())
                        .channelNo(BrandMerchantCodeEnums.PUSFS.getCode())
                        .isContainerOccupiedQty(Objects.equals(activeLock.getSourcePlatform(), BrandMerchantCodeEnums.PUSFS.getCode()))
                        .build()
        );

        logger.info("开始释放活动预占, billNo:{}, activeLockDtls:{}", activeLock.getBillNo(), activeLockDtls.size());
        try {

            if (BrandMerchantCodeEnums.PUSFS.getCode().equals(activeLock.getSourcePlatform())) {
                List<InventoryActiveLockDtl> zeroInventoryDtls = activeLockDtls.stream()
                        .map(dtl -> {
                            InventoryActiveLockDtlOccupied inventoryActiveLockDtl = new InventoryActiveLockDtlOccupied();
                            BeanUtils.copyProperties(dtl, inventoryActiveLockDtl);
                            inventoryActiveLockDtl.setBalanceLockQty(0);
                            return inventoryActiveLockDtl;
                        }).collect(Collectors.toList());
                syncInventoryToBaoZun(activeLock, zeroInventoryDtls, pair -> {
                    if (!pair.getLeft().isEmpty()) {
                        throw new ManagerException("彪马锁库库存同步失败,终止失败!");
                    }
                }, true);

                // 终止, 更新剩余锁库数量为0
                this.inventoryActiveLockDtlService.batchSaveOrUpdateDtl(zeroInventoryDtls);
            }

            this.update(activeLock);

            disableLockOccupiedInventory(activeLock, activeLockDtls);

        } catch (Exception e) {
            logger.error("释放活动预占失败, billNo:{},  disableLockOccupiedInventory error, msg:{}", activeLock.getBillNo(), e.getMessage(), e);
            throw new ManagerException(e);
        }
    }

    @Override
    public void activeLockOccupiedInventory(InventoryActiveLock activeLock, List<InventoryActiveLockDtl> activeLockDtls) throws Exception {
		Assert.notNull(activeLock, "activeLock is null");
		Assert.notNull(activeLockDtls, "activeLockDtls is null");
		Assert.notEmpty(activeLockDtls, "activeLockDtls is empty");

        // GMS 预占, 支持一个预占单据 => 单机构,单货管下的多明细 进行预占, 所以需要对下面的预占单据进行分组,
        // 组合成下面的明细为 一个机构加货管单位的预占单据, 明细列表按店铺和货管单位分组
        // 把锁库明细根据店铺和货管单位进行分组, 因为锁库明细中存在多条记录是同一个店铺和货管单位的情况
        Map<String, List<InventoryActiveLockDtl>> groupByData = activeLockDtls.stream().collect(Collectors.groupingBy(s -> s.getStoreNo() + "-" + s.getOrderUnitNo()));


        List<ShopInventoryOccupiedDto> shopInventoryOccupiedDtos =  new ArrayList<>();

        for (Map.Entry<String, List<InventoryActiveLockDtl>> stringListEntry : groupByData.entrySet()) {

            // 组合订单号
            String orderNo = activeLock.getBillNo() +"-"+stringListEntry.getKey();
            String orderUnitsNo = stringListEntry.getValue().get(0).getOrderUnitNo();
            String storeNo = stringListEntry.getValue().get(0).getStoreNo();

            // 1. 获取分库标识
            String shardingFlag = shardingFlagHelper.getShardingFlagByOrderUnitNo(orderUnitsNo);

            ShopInventoryOccupiedDtoBuilder shopInventoryOccupiedDtoBuilder = ShopInventoryOccupiedDtoBuilder.builder();
            shopInventoryOccupiedDtoBuilder
                    .orderNo(orderNo)
                    .shopNo(storeNo)
                    .channelNo(activeLock.getBillNo())
                    .shardingFlag(shardingFlag)
                    .dtlList(stringListEntry.getValue())
            ;

            ShopInventoryOccupiedDto shopInventoryOccupiedDto = shopInventoryOccupiedDtoBuilder.build();
            shopInventoryOccupiedDtos.add(shopInventoryOccupiedDto);
        }


        logger.debug("开始调用gms库存预占, {}", shopInventoryOccupiedDtos.size());
        inventoryOccupiedApi.batchShopInventoryOccupied(shopInventoryOccupiedDtos);
        logger.debug("结束调用gms库存预占");
    }

    @Override
    public void disableLockOccupiedInventory(InventoryActiveLock activeLock, List<? extends InventoryActiveLockDtl> activeLockDtls) throws Exception {
        Assert.notNull(activeLock, "activeLock is null");
        Assert.notNull(activeLockDtls, "activeLockDtls is null");
        Assert.notEmpty(activeLockDtls, "activeLockDtls is empty");

        // GMS 预占, 支持一个预占单据 => 单机构,单货管下的多明细 进行预占, 所以需要对下面的预占单据进行分组,
        // 组合成下面的明细为 一个机构加货管单位的预占单据, 明细列表按店铺和货管单位分组
        // 把锁库明细根据店铺和货管单位进行分组, 因为锁库明细中存在多条记录是同一个店铺和货管单位的情况
        Map<String, List<InventoryActiveLockDtl>> groupByData = activeLockDtls.stream().collect(Collectors.groupingBy(s -> s.getStoreNo() + "-" + s.getOrderUnitNo()));

        List<ShopInventoryOccupiedDto> shopInventoryOccupiedDtos =  new ArrayList<>();

        for (Map.Entry<String, List<InventoryActiveLockDtl>> stringListEntry : groupByData.entrySet()) {

                // 组合订单号
                String orderNo = activeLock.getBillNo() +"-"+stringListEntry.getKey();
                String orderUnitsNo = stringListEntry.getValue().get(0).getOrderUnitNo();
                String storeNo = stringListEntry.getValue().get(0).getStoreNo();

                // 1. 获取分库标识
                String shardingFlag = shardingFlagHelper.getShardingFlagByOrderUnitNo(orderUnitsNo);

                ShopInventoryOccupiedDtoBuilder shopInventoryOccupiedDtoBuilder = ShopInventoryOccupiedDtoBuilder.builder();
                shopInventoryOccupiedDtoBuilder
                        .orderNo(orderNo)
                        .shopNo(storeNo)
                        .channelNo(activeLock.getBillNo())
                        .shardingFlag(shardingFlag)
                        .dtlList(stringListEntry.getValue())
                ;

                ShopInventoryOccupiedDto shopInventoryOccupiedDto = shopInventoryOccupiedDtoBuilder.build();
                shopInventoryOccupiedDtos.add(shopInventoryOccupiedDto);
        }

        logger.debug("{},开始调用gms库存释放预占, {}", activeLock.getBillNo(),shopInventoryOccupiedDtos.size());
        boolean result = inventoryOccupiedApi.batchShopInventoryRelease(shopInventoryOccupiedDtos);
        logger.debug("{},结束调用gms库存释放预占, 结果:{}",  activeLock.getBillNo(), result);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncInventory(InventoryActiveLock inventoryActiveLock, List<? extends InventoryActiveLockDtl> activeLockDtls) {
        String sourcePlatform = inventoryActiveLock.getSourcePlatform();
        Assert.isTrue(sourcePlatform.equals(BrandMerchantCodeEnums.PUSFS.getCode()), "锁库单据来源平台错误, 锁库编码:" + inventoryActiveLock.getBillNo());
        if (!CollectionUtils.isEmpty(activeLockDtls)) {
            AtomicInteger count = syncInventoryToBaoZun(inventoryActiveLock, activeLockDtls, pair -> {
                if (!pair.getRight().isEmpty()) {
                    inventoryActiveLockDtlService.batchUpdateSyncStatus(pair.getRight(), AdjustDtlSyncStatusEnums.SYNC_SUCCESS.getValue());
                }
                if (!pair.getLeft().isEmpty()) {
                    inventoryActiveLockDtlService.batchUpdateSyncStatus(pair.getLeft(), AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue());
                }
            }, false);
            logger.info("锁库同步完成, 锁库编码:{}, 同步成功锁库明细数:{}", inventoryActiveLock.getBillNo(), count.get());
        }

        afterCommitAsyncExecute(() -> {
            SpringContext.getContext().publishEvent(new ActiveLockDtlSyncEvent(inventoryActiveLock.getBillNo()));
        });
    }


    public void afterCommitAsyncExecute(Actions.Action action) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization(){
                @Override
                public void afterCommit() {
                    action.run();
                }
            });
        } else {
            action.run();
        }
    }





    private AtomicInteger syncInventoryToBaoZun(InventoryActiveLock inventoryActiveLock,
                                                List<? extends InventoryActiveLockDtl> activeLockDtls,
                                                Consumer<Pair<List<InventoryActiveLockDtl>, List<InventoryActiveLockDtl>>> syncInventoryConsumer,
                                                boolean isThrowException) {
        // 计数器
        AtomicInteger count = new AtomicInteger();
        int size = 200;
        int limit = (activeLockDtls.size() + size - 1) / size;
        Stream.iterate(0, n -> n + 1).limit(limit).forEach(page -> {
            List<InventoryActiveLockDtl> splitList = activeLockDtls.stream().skip(page * size).limit(size).collect(Collectors.toList());
            List<Tuple3<String, String, String>> itemBrandSizeList = splitList.stream().map(item -> Tuples.of(item.getItemCode(), item.getBrandNo(), item.getSizeNo())).collect(Collectors.toList());
            List<ExternalProductMapping> externalProductMappings =  externalProductMappingService.selectItemsByTuple3(inventoryActiveLock.getSourcePlatform(), itemBrandSizeList);


            List<InventoryActiveLockDtl> failureList = new ArrayList<>();
            List<InventoryActiveLockDtl> successList = new ArrayList<>();

            try {
                List<InventoryItem> inventoryItemList = new ArrayList<>();


                for (int i = 0; i < splitList.size(); i++) {
                    InventoryActiveLockDtl item = splitList.get(i);

                    int occupiedQty = 0;
                    if (item instanceof InventoryActiveLockDtlOccupied) {
                       occupiedQty = ((InventoryActiveLockDtlOccupied) item).getOccupiedQty();
                    }

                    InventoryItem inventoryItem = new InventoryItem.Builder()
                            .customerCode("PUMA")
                            .invTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"))
                            .ownerCode("PUMA_TOP0I01")
                            .warehouseCode(inventoryActiveLock.getVstoreCode())
                            .upc(item.getBarcode())
                            .invQty(item.getBalanceLockQty() + occupiedQty)
                            .uniqueKey(String.format("%d_%d", System.currentTimeMillis(), i))
                            .sourceSys("HUB")
                            .invStatusCode(1)
                            .build();
                    // 根据商品编码、品牌编码、尺码编码查询商品映射信息,  如果不存在, 则抛出异常
                    setUpcConsumer(externalProductMappings, item, externalProductMapping -> {
                        // 说明没有查询映射条码
                        if (externalProductMapping.getBarcode() != null) {
                            inventoryItem.setUpc(externalProductMapping.getBarcode());
                            inventoryItemList.add(inventoryItem);
                            successList.add(item);
                        } else {
                            failureList.add(item);
                        }
                    });


                }
                FullSkuInventory fullSkuInventory = new FullSkuInventory.Builder()
                        .sourceMarkCode("routeiosp2_om")
                        .msgId(String.valueOf(System.currentTimeMillis()))
                        .items(inventoryItemList)
                        .build();
                BaoZunResponse response = BaoZunHttpUtils.requestBaoZun(fullSkuInventory, "sku_full_inventory", BaoZunResponse.class);
                if (response.getResponse() == 1) {
                    // 同步成功, 则增加计数器
                    count.addAndGet(successList.size());
                } else {
                    // 说明这个批次都失败了
                    failureList.addAll(successList);
                }
            } catch (Exception e) {
                logger.error("彪马锁库库存同步失败,{}", e.getMessage(), e);
                if (isThrowException) {
                    throw new RuntimeException("彪马锁库库存同步失败", e);
                }
            }

            if (syncInventoryConsumer != null) {
                syncInventoryConsumer.accept(Pair.of(failureList, successList));
            }
        });

        return count;
    }


    private void setUpcConsumer(List<ExternalProductMapping> externalProductMappings, InventoryActiveLockDtl inventoryActiveLockDtl, Consumer<ExternalProductMapping> consumer) {
        ExternalProductMapping findExternalProductMapping = externalProductMappings.stream()
                .filter(externalProductMapping -> externalProductMapping.getProductCode().equals(inventoryActiveLockDtl.getItemCode())
                        && externalProductMapping.getBrandCode().equals(inventoryActiveLockDtl.getBrandNo())
                        && externalProductMapping.getSizeCode().equals(inventoryActiveLockDtl.getSizeNo()))
                .findFirst()
                .orElseGet(() -> {
                    ExternalProductMapping externalProductMapping = new ExternalProductMapping();
                    externalProductMapping.setId(inventoryActiveLockDtl.getId());
                    return externalProductMapping;
                });
        consumer.accept(findExternalProductMapping);
    }

    @Override
    public Integer insert(InventoryActiveLock entry) throws ManagerException {
        Integer i = super.insert(entry);
        return i;
    }

    @Override
    public Integer update(InventoryActiveLock entry) throws ManagerException {
        Integer i = super.update(entry);
        return i;
    }

    static class ShopInventoryOccupiedDtoBuilder {
        private String orderNo;
        private String shopNo;
        private final String billType = BillTypeEnumsISP.salesReturn.getRequestId().toString();
        private String channelNo;
        private String channelNoSale;
        private String businessType = "2";
        private List<ShopInventoryOccupiedDtlDto> dtlList;
        private Integer batchId;
        private Boolean isAccountAtOnce = true;
        private String shardingFlag;


        private ShopInventoryOccupiedDtoBuilder() {
        }


        public ShopInventoryOccupiedDtoBuilder orderNo(String orderNo) {
            this.orderNo = orderNo;
            return this;
        }

        public ShopInventoryOccupiedDtoBuilder shopNo(String shopNo) {
            this.shopNo = shopNo;
            return this;
        }

        public ShopInventoryOccupiedDtoBuilder channelNo(String channelNo) {
            this.channelNo = channelNo;
            return this;
        }

        public ShopInventoryOccupiedDtoBuilder shardingFlag(String shardingFlag) {
            this.shardingFlag = shardingFlag;
            return this;
        }

        public ShopInventoryOccupiedDtoBuilder dtlList(List<InventoryActiveLockDtl> dtlList) {
            Assert.notNull(dtlList, "dtlList is null");

            List<ShopInventoryOccupiedDtlDto> dtlListData = new ArrayList<>();

            for (InventoryActiveLockDtl inventoryActiveLockDtl : dtlList) {
                ShopInventoryOccupiedDtlDto dto = new ShopInventoryOccupiedDtlDto();
                dto.setSkuNo(inventoryActiveLockDtl.getSkuNo());
                dto.setQty(inventoryActiveLockDtl.getBalanceLockQty());
                dto.setOrderUnitNo(inventoryActiveLockDtl.getOrderUnitNo());
                dto.setShopNo(inventoryActiveLockDtl.getStoreNo());
                dto.setShardingFlag(shardingFlag);
                dtlListData.add(dto);
            }

            this.dtlList = dtlListData;
            return this;
        }


        public static ShopInventoryOccupiedDtoBuilder builder() {
            return new ShopInventoryOccupiedDtoBuilder();
        }


        public ShopInventoryOccupiedDto build() {
            Assert.notNull(orderNo, "orderNo is null");
            Assert.notNull(shopNo, "shopNo is null");
            Assert.notNull(channelNo, "channelNo is null");
            Assert.notNull(shardingFlag, "shardingFlag is null");
            Assert.notNull(dtlList, "dtlList is null");


            ShopInventoryOccupiedDto shopInventoryOccupiedDto = new ShopInventoryOccupiedDto();
            shopInventoryOccupiedDto.setOrderNo(orderNo);
            shopInventoryOccupiedDto.setShopNo(shopNo);
            shopInventoryOccupiedDto.setChannelNo(channelNo);
            shopInventoryOccupiedDto.setBillType(billType);
            shopInventoryOccupiedDto.setChannelNoSale(channelNoSale);
            shopInventoryOccupiedDto.setBusinessType(businessType);
            shopInventoryOccupiedDto.setDtlList(dtlList);
            shopInventoryOccupiedDto.setBatchId(batchId);
            shopInventoryOccupiedDto.setIsAccountAtOnce(isAccountAtOnce);
            shopInventoryOccupiedDto.setShardingFlag(shardingFlag);
            return shopInventoryOccupiedDto;
        }

    }
}