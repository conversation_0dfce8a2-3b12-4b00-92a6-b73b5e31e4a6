/**  **/
package cn.wonhigh.baize.service.gms;


import cn.wonhigh.baize.model.entity.gms.BrandInventoryRange;
import cn.wonhigh.baize.model.entity.gms.BrandInventoryRange;
import topmall.framework.service.IService;
import java.util.List;
import java.util.Map;

public interface IBrandInventoryRangeService extends IService<BrandInventoryRange,String>{
      
    Integer batchInsert(List<BrandInventoryRange> list);
    
    List<BrandInventoryRange> selectByIds(List<String> ids);

    int selectShareNoExist(Map<String, Object> paramMaps);

    void updateShare(Map<String, Object> paramMap);

}