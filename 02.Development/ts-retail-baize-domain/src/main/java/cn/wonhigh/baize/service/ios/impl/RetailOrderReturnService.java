/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderReturn;
import cn.wonhigh.baize.repository.ios.RetailOrderReturnRepository;

import cn.wonhigh.baize.service.ios.IRetailOrderReturnService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class RetailOrderReturnService extends BaseService<RetailOrderReturn,String> implements  IRetailOrderReturnService{
    @Autowired
    private RetailOrderReturnRepository repository;

    protected IRepository<RetailOrderReturn,String> getRepository(){
        return repository;
    }

    
    public RetailOrderReturn findByUnique(String billNo){
        return repository.findByUnique(billNo);
    }

    public Integer deleteByUnique(String billNo){
        return repository.deleteByUnique(billNo);
    }

    public Integer insertForUpdate(RetailOrderReturn entry){
       return repository.insertForUpdate(entry);
    }
    
}