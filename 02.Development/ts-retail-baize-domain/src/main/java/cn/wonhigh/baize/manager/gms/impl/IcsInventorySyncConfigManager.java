/** q **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.manager.gms.IIcsInventorySyncConfigManager;
import cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.service.gms.IIcsInventorySyncConfigService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.List;

@Service
public class IcsInventorySyncConfigManager extends BaseManager<IcsInventorySyncConfig,String> implements IIcsInventorySyncConfigManager {
    @Autowired
    private IIcsInventorySyncConfigService service;

    protected IService<IcsInventorySyncConfig, String> getService(){
        return service;
    }
    	
	public List<IcsInventorySyncConfig> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<IcsInventorySyncConfig> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}


	@Override
	public List<IcsInventorySyncConfig> selectShopPageByParams(Query query, Pagenation pagenation) {
		return service.selectShopPageByParams(query, pagenation);
	}

	@Override
	public Integer selectShopCountByParams(Query var1) throws ManagerException {
		return service.selectShopCountByParams(var1);
	}

	@Override
	public List<InternetVirtualWarehouseInfo> selectVstoreListByParams(Query query) {
		return service.selectVstoreListByParams(query);
	}

	@Override
	public Integer countByChannel(Query query) throws ManagerException {
		try {
			return service.countByChannel(this.preHandleQuery(query));
		} catch (Exception var3) {
			throw new ManagerException(var3);
		}
	}

	@Override
	public List<IcsInventorySyncConfig> pageByChannel(Query query, Pagenation page) throws ManagerException {
		try {
			return service.pageByChannel(this.preHandleQuery(query), page);
		} catch (Exception var4) {
			throw new ManagerException(var4);
		}
	}

	@Override
	public List<IcsInventorySyncConfig> selectChannelVstoreInfo(Query query) throws ManagerException {
		try {
			return service.selectChannelVstoreInfo(this.preHandleQuery(query));
		} catch (Exception var4) {
			throw new ManagerException(var4);
		}
	}

}