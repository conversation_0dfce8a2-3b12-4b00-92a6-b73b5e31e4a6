package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.manager.gms.IStockDistirbutionConfigManager;
import cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig;
import cn.wonhigh.baize.service.gms.IStockDistirbutionConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.List;

@Service
public class StockDistirbutionConfigManager extends BaseManager<StockDistirbutionConfig, String> implements IStockDistirbutionConfigManager {
	@Autowired
	private IStockDistirbutionConfigService stockDistirbutionConfigService;

	@Override
	protected IService<StockDistirbutionConfig, String> getService() {
		return stockDistirbutionConfigService;
	}

	@Override
	public Integer countByShop(Query query) throws ManagerException {
		try {
			return stockDistirbutionConfigService.countByShop(this.preHandleQuery(query));
		} catch (Exception var3) {
			throw new ManagerException(var3);
		}
	}

	@Override
	public List<StockDistirbutionConfig> pageByShop(Query query, Pagenation page) throws ManagerException {
		try {
			return stockDistirbutionConfigService.pageByShop(this.preHandleQuery(query), page);
		} catch (Exception var4) {
			throw new ManagerException(var4);
		}
	}

}
