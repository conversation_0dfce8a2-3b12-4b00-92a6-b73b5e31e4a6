/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.ShareInventoryRange;
import java.util.List;
import java.util.Map;

public interface IShareInventoryRangeManager extends IManager<ShareInventoryRange,String>{
    public Integer deleteByUnique(String orderUnitNo);

    int insertSelective(ShareInventoryRange shareInventoryRange);

    int updateShare(Map<String, Object> map);

    Integer batchInsert(List<ShareInventoryRange> list);

    Integer batchUpdate(List<ShareInventoryRange> list);

    Integer findExistInBrandInventoryRange(Map<String, Object> map);

}