/**  **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.repository.gms.IcsActiveLockAdjustRepository;

import cn.wonhigh.baize.service.gms.IIcsActiveLockAdjustService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class IcsActiveLockAdjustService extends BaseService<IcsActiveLockAdjust,String> implements  IIcsActiveLockAdjustService{
    @Autowired
    private IcsActiveLockAdjustRepository repository;

    protected IRepository<IcsActiveLockAdjust,String> getRepository(){
        return repository;
    }
    
    @Override
    public IcsActiveLockAdjust findByUnique(String billNo){
        return repository.findByUnique(billNo);
    }
    @Override
    public Integer deleteByUnique(String billNo){
        return repository.deleteByUnique(billNo);
    }
    @Override
    public List<IcsActiveLockAdjust> selectByUniques(List<String> billNos) {
        return repository.selectByUniques(billNos);
    }
    
    @Override
	public List<IcsActiveLockAdjust> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}

    @Override
    public void updateAdjustStatus(String billNo, Integer status) {
        repository.updateAdjustStatus(billNo, status);
    }

    @Override
    public Integer batchInsert(List<IcsActiveLockAdjust> list) {
    	return repository.batchInsert(list);
    }
}