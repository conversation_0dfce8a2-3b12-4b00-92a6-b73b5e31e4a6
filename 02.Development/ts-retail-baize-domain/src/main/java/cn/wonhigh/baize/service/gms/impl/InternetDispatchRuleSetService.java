/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;
import cn.wonhigh.baize.repository.gms.InternetDispatchRuleSetRepository;

import cn.wonhigh.baize.service.gms.IInternetDispatchRuleSetService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Service
public class InternetDispatchRuleSetService extends BaseService<InternetDispatchRuleSet,Long> implements  IInternetDispatchRuleSetService{
    @Autowired
    private InternetDispatchRuleSetRepository repository;

    protected IRepository<InternetDispatchRuleSet,Long> getRepository(){
        return repository;
    }


    @Override
    public int exitsQueryData(Map<String, Object> query) {
        return repository.exitsQueryData(query);
    }

    @Override
    public long nextId() {
        return repository.nextId();
    }
}