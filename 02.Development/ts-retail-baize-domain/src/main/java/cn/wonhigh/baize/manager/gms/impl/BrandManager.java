/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.Brand;
import cn.wonhigh.baize.service.gms.IBrandService;
import cn.wonhigh.baize.manager.gms.IBrandManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service("gsmBrandManager")
public class BrandManager extends BaseManager<Brand,Integer> implements IBrandManager{
    @Autowired
	@Qualifier("gmsBrandService")
    private IBrandService service;

    protected IService<Brand,Integer> getService(){
        return service;
    }
    
    public Brand findByUnique(String brandNo) {
			  return service.findByUnique(brandNo);
    }

    public Integer deleteByUnique(String brandNo) {
			  return service.deleteByUnique(brandNo);
    }

    public List<Brand> selectByUniques(List<String> brandNos) {
		return service.selectByUniques(brandNos);
	}
    	
	public List<Brand> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<Brand> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}
}