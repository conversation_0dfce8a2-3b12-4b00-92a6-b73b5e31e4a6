/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import topmall.framework.service.IService;
import java.util.List;

public interface IIcsActiveLockAdjustService extends IService<IcsActiveLockAdjust,String>{
    
    IcsActiveLockAdjust findByUnique(String billNo);

    Integer deleteByUnique(String billNo);

    List<IcsActiveLockAdjust> selectByUniques(List<String> billNos);
      
    Integer batchInsert(List<IcsActiveLockAdjust> list);
    
    List<IcsActiveLockAdjust> selectByIds(List<Integer> ids);

    void updateAdjustStatus(String billNo, Integer status);
}