/** Kain **/
package cn.wonhigh.baize.domain.configuration;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.annotation.EnableAsync;
import topmall.framework.domain.codingrule.CodingRuleBuilder;
import topmall.framework.domain.codingrule.CodingRuleRepository;

/**
 * <AUTHOR>
 */
@EnableAsync
@Configuration
@MapperScan(basePackages={"cn.wonhigh.baize.repository.**.*"})
@ComponentScan(basePackages = {"cn.wonhigh.baize","cn.wonhigh.baize.listener", "cn.wonhigh.baize.manager.**.*", "cn.wonhigh.baize.service.**.*"})
@ImportResource(locations = {"classpath:spring-dubbo.xml"})
public class MyDomainAutoConfiguration {

    public MyDomainAutoConfiguration(){
        System.out.println("init ts-retail-baize domain ...");
    }



    @Bean
    @Primary
    public CodingRuleBuilder codingRuleBuilder(SqlSessionFactory factory) throws Exception {


        MapperFactoryBean<CodingRuleRepository> bean = new MapperFactoryBean<>(CodingRuleRepository.class);
        bean.setSqlSessionFactory(factory);
        CodingRuleRepository repository = bean.getObject();
        return new CodingRuleBuilder(repository);
    }

}
