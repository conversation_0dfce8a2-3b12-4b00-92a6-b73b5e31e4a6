package cn.wonhigh.baize.service.gms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig;
import cn.wonhigh.baize.repository.gms.StockDistirbutionConfigMapper;
import cn.wonhigh.baize.service.gms.IStockDistirbutionConfigService;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;
import java.util.List;

@Service
public class StockDistirbutionConfigService extends BaseService<StockDistirbutionConfig, String> implements IStockDistirbutionConfigService {
    @Resource
    private StockDistirbutionConfigMapper stockDistirbutionConfigMapper;

    @Override
    protected IRepository<StockDistirbutionConfig, String> getRepository() {
        return stockDistirbutionConfigMapper;
    }

    @Override
    public Integer countByShop(Query query) {
        return stockDistirbutionConfigMapper.countByShop(query.asMap());
    }

    @Override
    public List<StockDistirbutionConfig> pageByShop(Query query, Pagenation page) {
        return stockDistirbutionConfigMapper.pageByShop(query.asMap(), page, query.getSort());
    }
}