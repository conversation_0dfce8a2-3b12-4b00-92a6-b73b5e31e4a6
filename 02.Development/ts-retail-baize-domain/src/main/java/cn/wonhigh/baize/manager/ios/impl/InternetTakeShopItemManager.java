package cn.wonhigh.baize.manager.ios.impl;

import cn.wonhigh.baize.manager.ios.IInternetTakeShopItemManager;
import cn.wonhigh.baize.model.entity.ios.InternetTakeShopItem;
import cn.wonhigh.baize.service.ios.IInternetTakeShopItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2025/3/28 11:27
 */
@Service("internetTakeShopItemManager")
public class InternetTakeShopItemManager extends BaseManager<InternetTakeShopItem, String> implements IInternetTakeShopItemManager {

    @Autowired
    private IInternetTakeShopItemService service;

    @Override
    protected IService<InternetTakeShopItem, String> getService() {
        return service;
    }
}
