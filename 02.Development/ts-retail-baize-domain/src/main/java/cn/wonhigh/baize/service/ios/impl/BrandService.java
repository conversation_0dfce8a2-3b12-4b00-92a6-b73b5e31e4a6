/** Kain **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.Brand;
import cn.wonhigh.baize.repository.ios.IosBrandRepository;

import cn.wonhigh.baize.service.ios.IBrandService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class BrandService extends BaseService<Brand,Integer> implements  IBrandService{
    @Autowired
    private IosBrandRepository repository;

    protected IRepository<Brand,Integer> getRepository(){
        return repository;
    }

    
    public Brand findByUnique(String brandNo){
        return repository.findByUnique(brandNo);
    }

    public Integer deleteByUnique(String brandNo){
        return repository.deleteByUnique(brandNo);
    }

    public Integer insertForUpdate(Brand entry){
       return repository.insertForUpdate(entry);
    }
    
}
