package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.basic.query.PageResult;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.manager.gms.MdmManager;
import cn.wonhigh.baize.model.dto.mdm.StoreListQueryParamDto;
import cn.wonhigh.baize.model.dto.mdm.StoreListResultDataDto;
import cn.wonhigh.baize.utils.common.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.httpclient.params.DefaultHttpParams;
import org.apache.commons.httpclient.params.HttpParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

@Service
public class MdmManagerImpl implements MdmManager {

    public static final String CHARSET = "UTF-8";

    public static final Logger logger = LoggerFactory.getLogger(MdmManagerImpl.class);

    @Value("${mdm.api.url:https://api-mdm.belle.net.cn}")
    private String mdmApiUrl;
    @Value("${mdm.api.access.token:1a619a62a457980f7280ed893f4ba218}")
    private String accessToken;

    @Override
    public PageResult<StoreListResultDataDto> getStoreList(StoreListQueryParamDto queryParamDto) {
        Assert.notNull(queryParamDto, "查询参数不能为null");

        String url = mdmApiUrl + "/api/org_third_level_source/getSourceList";
        try {
            logger.info("调用mdm api, url:{}", url);
            queryParamDto.setAccessToken(accessToken);
            String res = HttpUtil.get(url + "?" + queryParamDto, CHARSET);

            JsonNode jsonNode = JsonUtils.fromJson(res);
            Integer resultCode = jsonNode.get("result").asInt();
            Integer totalPage = jsonNode.get("totalPage").asInt();
            Integer count = jsonNode.get("count").asInt();
            Integer currentPage = jsonNode.get("currentPage").asInt();

            if (resultCode != 1) {
                return new PageResult<>(new ArrayList<>(), 0);
            }

            List<StoreListResultDataDto> storeListResultDataDtos = jsonNode.get("data")
                    .traverse(JsonUtils.mapper())
                    .readValueAs(
                    new TypeReference<List<StoreListResultDataDto>>() {
                    }
            );

            PageResult<StoreListResultDataDto> pageResult = new PageResult<>(
                    storeListResultDataDtos,
                    count
            );
            return pageResult;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
