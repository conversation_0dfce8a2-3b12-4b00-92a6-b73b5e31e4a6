/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderDtl;
import cn.wonhigh.baize.service.ios.IRetailOrderDtlService;
import cn.wonhigh.baize.manager.ios.IRetailOrderDtlManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class RetailOrderDtlManager extends BaseManager<RetailOrderDtl,String> implements IRetailOrderDtlManager{
    @Autowired
    private IRetailOrderDtlService service;

    protected IService<RetailOrderDtl,String> getService(){
        return service;
    }

    
}