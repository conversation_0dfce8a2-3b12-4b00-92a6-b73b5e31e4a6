/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.dto.ShopCompanyDto;
import cn.wonhigh.baize.model.entity.gms.Shop;

import cn.mercury.manager.IManager;

import java.util.Map;

public interface IShopManager extends IManager<Shop,Integer>{

    
    public Shop findByUnique(String shopNo) ;

    public Integer deleteByUnique(String shopNo);

    public Integer insertForUpdate(Shop entry);

    public ShopCompanyDto selectShopAndCompany(Map<String, Object> map);
}