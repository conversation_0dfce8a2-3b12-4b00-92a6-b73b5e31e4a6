/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrder;
import cn.wonhigh.baize.repository.ios.RetailOrderRepository;

import cn.wonhigh.baize.service.ios.IRetailOrderService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RetailOrderService extends BaseService<RetailOrder,String> implements  IRetailOrderService{
    @Autowired
    private RetailOrderRepository repository;

    protected IRepository<RetailOrder,String> getRepository(){
        return repository;
    }

    
    public RetailOrder findByUnique(String billNo){
        return repository.findByUnique(billNo);
    }

    public Integer deleteByUnique(String billNo){
        return repository.deleteByUnique(billNo);
    }

    public Integer insertForUpdate(RetailOrder entry){
       return repository.insertForUpdate(entry);
    }

    @Override
    public List<RetailOrder> selectByOrderSubNo(String orderSubNo){
        Map param = new HashMap<>();
        param.put("orderSubNo", orderSubNo);
        return repository.selectByParams(param,null);
    }
    
}