/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.ExternalProductMapping;
import cn.wonhigh.baize.service.gms.IExternalProductMappingService;
import cn.wonhigh.baize.manager.gms.IExternalProductMappingManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class ExternalProductMappingManager extends BaseManager<ExternalProductMapping,String>
		implements IExternalProductMappingManager{
    @Autowired
    private IExternalProductMappingService service;

    protected IService<ExternalProductMapping,String> getService(){
        return service;
    }
    	
	public List<ExternalProductMapping> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<ExternalProductMapping> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}

	@Override
	public List<ExternalProductMapping> selectItemSkuByParams(Map<String, Object> params) {
		return service.selectItemSkuByParams(params);
	}
}