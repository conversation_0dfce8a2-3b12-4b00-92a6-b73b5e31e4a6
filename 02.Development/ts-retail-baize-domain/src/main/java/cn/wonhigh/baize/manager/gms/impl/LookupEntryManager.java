/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.LookupEntry;
import cn.wonhigh.baize.service.gms.ILookupEntryService;
import cn.wonhigh.baize.manager.gms.ILookupEntryManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class LookupEntryManager extends BaseManager<LookupEntry,String> implements ILookupEntryManager{
    @Autowired
    private ILookupEntryService service;

    protected IService<LookupEntry,String> getService(){
        return service;
    }
    	
	public List<LookupEntry> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<LookupEntry> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}
}