/**  **/
package cn.wonhigh.baize.service.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.ExternalProductMapping;
import cn.wonhigh.baize.repository.gms.ExternalProductMappingRepository;

import cn.wonhigh.baize.service.gms.IExternalProductMappingService;
import reactor.util.function.Tuple3;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class ExternalProductMappingService extends BaseService<ExternalProductMapping,String> implements  IExternalProductMappingService{
    @Autowired
    private ExternalProductMappingRepository repository;

    protected IRepository<ExternalProductMapping,String> getRepository(){
        return repository;
    }
      
	public List<ExternalProductMapping> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<ExternalProductMapping> list) {
    	return repository.batchInsert(list);
    }

    public List<ExternalProductMapping> selectItemSkuByParams(Map<String, Object> params) {
        return repository.selectItemSkuByParams(params);
    }

    @Override
    public List<ExternalProductMapping> selectItemsByTuple3(String merchantsCode, List<Tuple3<String, String, String>> itemBrandSizeList) {
        return repository.selectItemsByTuple3(merchantsCode, itemBrandSizeList);
    }
}