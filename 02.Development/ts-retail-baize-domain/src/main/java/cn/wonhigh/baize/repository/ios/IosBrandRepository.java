/** Kain **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.Brand;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;

import org.apache.ibatis.annotations.Param;

@Mapper
public interface IosBrandRepository extends IRepository<Brand,Integer> {
    
    public Brand findByUnique(@Param("brandNo") String brandNo);

    public Integer deleteByUnique(@Param("brandNo") String brandNo);

    public Integer insertForUpdate(Brand entry);
    
}
