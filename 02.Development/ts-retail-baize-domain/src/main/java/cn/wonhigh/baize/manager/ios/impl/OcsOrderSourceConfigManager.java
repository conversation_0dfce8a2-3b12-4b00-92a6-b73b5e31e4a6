/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.OcsOrderSourceConfig;
import cn.wonhigh.baize.service.ios.IOcsOrderSourceConfigService;
import cn.wonhigh.baize.manager.ios.IOcsOrderSourceConfigManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class OcsOrderSourceConfigManager extends BaseManager<OcsOrderSourceConfig,String> implements IOcsOrderSourceConfigManager{
    @Autowired
    private IOcsOrderSourceConfigService service;

    protected IService<OcsOrderSourceConfig,String> getService(){
        return service;
    }

    
    public OcsOrderSourceConfig findByUnique(String shopNo) {
        try {
			return service.findByUnique(shopNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String shopNo) {
        try {
			return service.deleteByUnique(shopNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(OcsOrderSourceConfig entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}