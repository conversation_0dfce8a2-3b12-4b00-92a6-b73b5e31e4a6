package cn.wonhigh.baize.service.oms.impl;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.dto.WarehouseRatioDto;
import cn.wonhigh.baize.model.entity.oms.OmsWarehouse;
import cn.wonhigh.baize.repository.oms.OmsWarehouseRepository;
import cn.wonhigh.baize.service.oms.IInventoryBoardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import java.util.List;
@Service
public class InventoryBoardServiceImpl extends BaseService<OmsWarehouse,Long> implements IInventoryBoardService {
    @Autowired
    private OmsWarehouseRepository omsWarehouseRepository;

    @Override
    protected IRepository getRepository() {
        return omsWarehouseRepository;
    }

    @Override
    public OmsWarehouse findByPrimaryKey(Long aLong) {
        return null;
    }

    @Override
    public Integer deleteByPrimaryKey(Long aLong) {
        return 0;
    }
}
