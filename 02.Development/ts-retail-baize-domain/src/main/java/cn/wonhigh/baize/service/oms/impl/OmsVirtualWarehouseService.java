package cn.wonhigh.baize.service.oms.impl;

import cn.wonhigh.baize.model.entity.oms.OmsVirtualWarehouse;
import cn.wonhigh.baize.repository.oms.OmsVirtualWarehouseRepository;
import cn.wonhigh.baize.service.oms.IOmsVirtualWarehouseService;

import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;

/**
 * (OmsVirtualWarehouse)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08 14:40:55
 */
@Service("omsVirtualWarehouseService")
public class OmsVirtualWarehouseService extends BaseService<OmsVirtualWarehouse, Long> implements IOmsVirtualWarehouseService {
    @Resource
    private OmsVirtualWarehouseRepository repository;

    protected IRepository<OmsVirtualWarehouse, Long> getRepository() {
        return repository;
    }

}