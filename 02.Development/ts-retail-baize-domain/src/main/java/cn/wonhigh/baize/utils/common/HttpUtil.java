
package cn.wonhigh.baize.utils.common;

import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.nio.charset.Charset;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

public class HttpUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);

    public static final int TIME_OUT = 10 * 60 * 1000;
    private static PoolingHttpClientConnectionManager connMgr;
    private static RequestConfig requestConfig;
    private static final String JSON = "application/json;charset=UTF-8";
    private static final String FORM = "application/x-www-form-urlencoded";

	static {
        // 设置连接池
        connMgr = new PoolingHttpClientConnectionManager();
        // 设置连接池大小
        connMgr.setMaxTotal(100);
        connMgr.setDefaultMaxPerRoute(connMgr.getMaxTotal());
        // 空闲超过30秒,则重新连接
        connMgr.setValidateAfterInactivity(30000);

        RequestConfig.Builder configBuilder = RequestConfig.custom();
        // 设置连接超时
        configBuilder.setConnectTimeout(TIME_OUT);
        // 设置读取超时
        configBuilder.setSocketTimeout(TIME_OUT);
        // 设置从连接池获取连接实例的超时
        configBuilder.setConnectionRequestTimeout(TIME_OUT);

        requestConfig = configBuilder.build();
    }

    /**
     *
     * @return 返回json对象
     * @throws Exception
     */
    public static String post(String url, String body, String charset, String contentType) throws Exception {
        // http部分
        CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory()).setConnectionManager(connMgr)
                .setDefaultRequestConfig(requestConfig).build();
        HttpPost post = new HttpPost(url);
        post.setConfig(requestConfig);
        CloseableHttpResponse response = null;

        StringEntity entity = new StringEntity(body, Charset.forName(charset));
        entity.setContentType(contentType);
        post.setEntity(entity);
        response = httpClient.execute(post);
        String result = null;
        if (response != null) {
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
                result = EntityUtils.toString(resEntity, charset);
            }
        }
        return result;

    }

    public static String get(String url, String charset) throws Exception {

        LOGGER.info("Http Get URL:{}", url);

        // http部分
        CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory(createSSLConnSocketFactory())
                .setConnectionManager(connMgr)
                .setDefaultRequestConfig(requestConfig).build();
        HttpGet get = new HttpGet(url);
        get.setConfig(requestConfig);
        get.setHeader("Content-Type", JSON);
        CloseableHttpResponse response = null;

        response = httpClient.execute(get);
        String result = null;
        if (response != null) {
            HttpEntity resEntity = response.getEntity();
            if (resEntity != null) {
//                LOGGER.info("get url:{}", url);
                result = EntityUtils.toString(resEntity, charset);
                LOGGER.info("get result:{}", result);
            }
        }
        return result;
    }

    private static SSLConnectionSocketFactory createSSLConnSocketFactory() {
        SSLConnectionSocketFactory sslsf = null;
        try {
            SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, new TrustStrategy() {
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            sslsf = new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        } catch (GeneralSecurityException e) {
            LOGGER.error("HttpUtil.SSLConnectionSocketFactory() throw GeneralSecurityException", e);
        }
        return sslsf;
    }
}
