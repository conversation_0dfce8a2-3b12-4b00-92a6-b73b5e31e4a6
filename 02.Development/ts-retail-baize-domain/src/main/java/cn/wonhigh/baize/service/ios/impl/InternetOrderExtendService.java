/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderExtend;
import cn.wonhigh.baize.repository.ios.InternetOrderExtendRepository;

import cn.wonhigh.baize.service.ios.IInternetOrderExtendService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetOrderExtendService extends BaseService<InternetOrderExtend,String> implements  IInternetOrderExtendService{
    @Autowired
    private InternetOrderExtendRepository repository;

    protected IRepository<InternetOrderExtend,String> getRepository(){
        return repository;
    }

    
    public InternetOrderExtend findByUnique(String orderSubNo){
        return repository.findByUnique(orderSubNo);
    }

    public Integer deleteByUnique(String orderSubNo){
        return repository.deleteByUnique(orderSubNo);
    }

    public Integer insertForUpdate(InternetOrderExtend entry){
       return repository.insertForUpdate(entry);
    }
    
}