/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel;
import cn.wonhigh.baize.service.gms.IOrgUnitBrandRelService;
import cn.wonhigh.baize.manager.gms.IOrgUnitBrandRelManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


@Service
public class OrgUnitBrandRelManager extends BaseManager<OrgUnitBrandRel,Integer> implements IOrgUnitBrandRelManager{
    @Autowired
    private IOrgUnitBrandRelService service;

    protected IService<OrgUnitBrandRel,Integer> getService(){
        return service;
    }


    @Override
    public List<OrgUnitBrandRel> selectStoreByUnitNo(String orderUnitNo){
        return service.selectStoreByUnitNo(orderUnitNo);
    }

}