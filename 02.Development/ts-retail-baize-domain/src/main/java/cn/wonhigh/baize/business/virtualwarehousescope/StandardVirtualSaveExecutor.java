package cn.wonhigh.baize.business.virtualwarehousescope;

import cn.mercury.basic.UUID;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import org.springframework.beans.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2024/9/13 10:49
 */
public class StandardVirtualSaveExecutor  extends AbstractVirtualWarehouseScopeSaveExecutor{

    public StandardVirtualSaveExecutor(InternetVirtualWarehouseScopeSave saveData) {
        super(saveData);
    }

    @Override
    public void addData() {
        if (this.saveData.getAddDtls() != null) {
            //查询出所属子仓
            List<InternetVirtualWarehouseInfo> childVirtual = internetVirtualWarehouseInfoManager.selectByParams(
                    Query.empty().where("parentVstoreCode", this.internetVirtualWarehouseInfo.getVstoreCode())
                            .and("vstoreType", 2));
            //批量添加的货管
            List<InternetVirtualWarehouseScope> addScopes = new ArrayList<>();
            Date date = new Date();
            for (InternetVirtualWarehouseScopeSave.SaveDtl addDtl : this.saveData.getAddDtls()) {
                if(StringUtils.isBlank(addDtl.getId())){
                    InternetVirtualWarehouseScope scope = buildScope(addDtl, date);
                    addScopes.add(scope);
                    if(!CollectionUtils.isEmpty(childVirtual)){
                        //添加子仓货管
                        buildChildScope(scope, childVirtual, addScopes);
                    }
                }
               /* if(StringUtils.isNotEmpty(addDtl.getId())){
                    if(!addDtl.getIsEdit()){
                        continue;
                    }
                    InternetVirtualWarehouseScope updateScope = InternetVirtualWarehouseScope.build()
                            .id(addDtl.getId()).status(addDtl.getStatus()).object();
                    //修改货管
                    this.internetVirtualWarehouseScopeManager.update(updateScope);
                    updateScope.setStoreNo(addDtl.getStoreNo());
                    updateScope.setOrderUnitNo(addDtl.getOrderUnitNo());
                    updateScope.setVstoreCode(this.internetVirtualWarehouseInfo.getVstoreCode());
                    updateLogs(updateScope);
                    //同时修改子仓货管
                    if(!CollectionUtils.isEmpty(childVirtual)){
                        List<String> listVstoreCode = childVirtual.stream().map(InternetVirtualWarehouseInfo::getVstoreCode).collect(Collectors.toList());
                        Map<String, Object> paramsMap = new HashMap<>();
                        paramsMap.put("listVstoreCode", listVstoreCode);
                        paramsMap.put("status", addDtl.getStatus());
                        paramsMap.put("orderUnitNo", addDtl.getOrderUnitNo());
                        paramsMap.put("storeNo", addDtl.getStoreNo());
                        paramsMap.put("updateUser", Authorization.getUser().getName());
                        this.internetVirtualWarehouseScopeManager.updateAllScope(paramsMap);
                    }
                } else {
                    InternetVirtualWarehouseScope scope = buildScope(addDtl, date);
                    addScopes.add(scope);
                    if(!CollectionUtils.isEmpty(childVirtual)){
                        //添加子仓货管
                        buildChildScope(scope, childVirtual, addScopes);
                    }
                }*/
            }
            if(addScopes.size() > 0){
                internetVirtualWarehouseScopeManager.insertBatch(addScopes);
                saveLogs(addScopes);
            }
        }
    }

    //构造新增的子仓货管
    void buildChildScope(InternetVirtualWarehouseScope scope, List<InternetVirtualWarehouseInfo> childVirtual,
                    List<InternetVirtualWarehouseScope> addScopes){
        //添加子仓货管
        for (InternetVirtualWarehouseInfo virtualWarehouseInfo : childVirtual) {
            InternetVirtualWarehouseScope childScope = new InternetVirtualWarehouseScope();
            BeanUtils.copyProperties(scope, childScope);
            childScope.setId(UUID.gernerate());
            childScope.setVstoreCode(virtualWarehouseInfo.getVstoreCode());
            childScope.setVstoreName(virtualWarehouseInfo.getVstoreName());
            childScope.setVstoreType(virtualWarehouseInfo.getVstoreType());
            addScopes.add(childScope);
        }
    }

}
