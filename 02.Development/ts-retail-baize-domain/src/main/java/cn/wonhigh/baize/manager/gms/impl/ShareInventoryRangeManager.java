/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.manager.gms.IShareInventoryRangeManager;
import cn.wonhigh.baize.model.entity.gms.ShareInventoryRange;
import cn.wonhigh.baize.service.gms.IShareInventoryRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;
import java.util.List;
import java.util.Map;

@Service
public class ShareInventoryRangeManager extends BaseManager<ShareInventoryRange,String> implements IShareInventoryRangeManager {
    @Autowired
    private IShareInventoryRangeService service;

    protected IService<ShareInventoryRange,String> getService(){
        return service;
    }

    public Integer deleteByUnique(String orderUnitNo) {
        try {
			return service.deleteByPrimaryKey(orderUnitNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    @Override
    public int insertSelective(ShareInventoryRange shareInventoryRange) {
        return service.insertSelective(shareInventoryRange);
    }

    @Override
    public int updateShare(Map<String, Object> map) {
        return service.updateShare(map);
    }

    @Override
    public Integer batchInsert(List<ShareInventoryRange> list) {
        return service.batchInsert(list);
    }

    @Override
    public Integer batchUpdate(List<ShareInventoryRange> list) {
        return service.batchUpdate(list);
    }

    @Override
    public Integer findExistInBrandInventoryRange(Map<String, Object> map) {
        return service.findExistInBrandInventoryRange(map);
    }

}