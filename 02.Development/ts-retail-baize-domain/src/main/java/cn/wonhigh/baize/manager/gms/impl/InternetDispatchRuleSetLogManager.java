/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetLog;
import cn.wonhigh.baize.service.gms.IInternetDispatchRuleSetLogService;
import cn.wonhigh.baize.manager.gms.IInternetDispatchRuleSetLogManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class InternetDispatchRuleSetLogManager extends BaseManager<InternetDispatchRuleSetLog,String> implements IInternetDispatchRuleSetLogManager{
    @Autowired
    private IInternetDispatchRuleSetLogService service;

    protected IService<InternetDispatchRuleSetLog,String> getService(){
        return service;
    }

    
}