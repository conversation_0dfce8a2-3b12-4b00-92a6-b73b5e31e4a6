/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOut;
import cn.wonhigh.baize.repository.ios.RetailOrderOutRepository;

import cn.wonhigh.baize.service.ios.IRetailOrderOutService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class RetailOrderOutService extends BaseService<RetailOrderOut,String> implements  IRetailOrderOutService{
    @Autowired
    private RetailOrderOutRepository repository;

    protected IRepository<RetailOrderOut,String> getRepository(){
        return repository;
    }

    
    public RetailOrderOut findByUnique(String billNo){
        return repository.findByUnique(billNo);
    }

    public Integer deleteByUnique(String billNo){
        return repository.deleteByUnique(billNo);
    }

    public Integer insertForUpdate(RetailOrderOut entry){
       return repository.insertForUpdate(entry);
    }
    
}