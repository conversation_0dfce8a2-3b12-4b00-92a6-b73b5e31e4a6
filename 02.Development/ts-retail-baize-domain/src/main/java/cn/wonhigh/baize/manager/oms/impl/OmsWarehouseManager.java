package cn.wonhigh.baize.manager.oms.impl;

import cn.wonhigh.baize.manager.oms.IOmsWarehouseManager;
import cn.wonhigh.baize.model.entity.oms.OmsWarehouse;
import cn.wonhigh.baize.service.oms.IOmsWarehouseService;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import javax.annotation.Resource;


/**
 * 仓库(OmsWarehouse)
 *
 * <AUTHOR>
 * @since 2024-10-30 11:10:03
 */
@Service("omsWarehouseManager")
public class OmsWarehouseManager extends BaseManager<OmsWarehouse, Long> implements IOmsWarehouseManager {

    @Resource
    private IOmsWarehouseService service;

    protected IService<OmsWarehouse, Long> getService() {
        return service;
    }

}