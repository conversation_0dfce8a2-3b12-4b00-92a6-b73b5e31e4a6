/**  **/
package cn.wonhigh.baize.manager.ios;

import cn.wonhigh.baize.model.entity.ios.InternetOrder;

import cn.mercury.manager.IManager;

public interface IInternetOrderManager extends IManager<InternetOrder,String>{

    
    public InternetOrder findByUnique(String orderSubNo) ;

    public Integer deleteByUnique(String orderSubNo);

    public Integer insertForUpdate(InternetOrder entry);
    
}