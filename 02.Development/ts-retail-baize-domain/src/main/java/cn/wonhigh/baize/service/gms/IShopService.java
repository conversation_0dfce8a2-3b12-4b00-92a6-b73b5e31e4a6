/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.dto.ShopCompanyDto;
import cn.wonhigh.baize.model.entity.gms.Shop;
import topmall.framework.service.IService;

import java.util.Map;

public interface IShopService extends IService<Shop,Integer>{

    
    public Shop findByUnique(String shopNo);

    public Integer deleteByUnique(String shopNo);

    public Integer insertForUpdate(Shop entry);

    ShopCompanyDto selectShopAndCompany(Map<String, Object> map);
}