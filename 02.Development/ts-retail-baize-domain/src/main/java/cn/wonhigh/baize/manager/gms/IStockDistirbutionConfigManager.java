package cn.wonhigh.baize.manager.gms;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig;

import java.util.List;

public interface IStockDistirbutionConfigManager extends IManager<StockDistirbutionConfig, String> {

    Integer countByShop(Query query) throws ManagerException;

    List<StockDistirbutionConfig> pageByShop(Query query, Pagenation page) throws ManagerException;
}
