package cn.wonhigh.baize.service.oms.impl;

import cn.wonhigh.baize.model.entity.oms.OmsStore;
import cn.wonhigh.baize.repository.oms.OmsStoreRepository;
import cn.wonhigh.baize.service.oms.IOmsStoreService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;

import javax.annotation.Resource;

/**
 * (OmsStore)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-29 22:19:20
 */
@Service("omsStoreService")
public class OmsStoreService extends BaseService<OmsStore, Long> implements IOmsStoreService {
    @Autowired
    private OmsStoreRepository repository;

    protected IRepository<OmsStore, Long> getRepository() {
        return repository;
    }

}