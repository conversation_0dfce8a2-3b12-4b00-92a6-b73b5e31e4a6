package cn.wonhigh.baize.domain.configuration.baozun;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: baozun接口配置
 * @date 2025/6/18 18:04
 */
@Configuration
@ConfigurationProperties(prefix = "bz")
public class BaoZunInterfaceConfig {

    private String url;
    private PumaConfig puma;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public PumaConfig getPuma() {
        return puma;
    }

    public void setPuma(PumaConfig puma) {
        this.puma = puma;
    }

    public static class PumaConfig {
    	private String appKey;
    	private String appSecret;

        public String getAppKey() {
            return appKey;
        }
        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }
        public String getAppSecret() {
            return appSecret;
        }
        public void setAppSecret(String appSecret) {
            this.appSecret = appSecret;
        }
    }
}
