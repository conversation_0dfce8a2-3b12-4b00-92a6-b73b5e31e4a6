/**  **/
package cn.wonhigh.baize.service.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderCancelApply;
import cn.wonhigh.baize.repository.ios.InternetOrderCancelApplyRepository;

import cn.wonhigh.baize.service.ios.IInternetOrderCancelApplyService;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;

@Service
public class InternetOrderCancelApplyService extends BaseService<InternetOrderCancelApply,String> implements  IInternetOrderCancelApplyService{
    @Autowired
    private InternetOrderCancelApplyRepository repository;

    protected IRepository<InternetOrderCancelApply,String> getRepository(){
        return repository;
    }

    
}