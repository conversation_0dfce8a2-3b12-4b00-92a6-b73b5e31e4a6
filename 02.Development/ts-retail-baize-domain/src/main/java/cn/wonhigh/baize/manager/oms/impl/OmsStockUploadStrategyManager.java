package cn.wonhigh.baize.manager.oms.impl;

import cn.wonhigh.baize.manager.oms.IOmsStockUploadStrategyManager;
import cn.wonhigh.baize.model.entity.oms.OmsStockUploadStrategy;
import cn.wonhigh.baize.service.oms.IOmsStockUploadStrategyService;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import javax.annotation.Resource;


/**
 * (OmsStockUploadStrategy)
 *
 * <AUTHOR>
 * @since 2024-10-29 23:06:12
 */
@Service("omsStockUploadStrategyManager")
public class OmsStockUploadStrategyManager extends BaseManager<OmsStockUploadStrategy, Long> implements IOmsStockUploadStrategyManager {

    @Resource
    private IOmsStockUploadStrategyService service;

    protected IService<OmsStockUploadStrategy, Long> getService() {
        return service;
    }

}