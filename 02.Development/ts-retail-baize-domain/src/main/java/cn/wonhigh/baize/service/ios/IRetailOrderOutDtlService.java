/**  **/
package cn.wonhigh.baize.service.ios;

import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOutDtl;
import topmall.framework.service.IService;

import java.util.List;

public interface IRetailOrderOutDtlService extends IService<RetailOrderOutDtl,String>{


	List<RetailOrderOutDtl> selectPageForReport(Query query, Pagenation pagenation);
	int selectPageForReportCount(Query query);
}