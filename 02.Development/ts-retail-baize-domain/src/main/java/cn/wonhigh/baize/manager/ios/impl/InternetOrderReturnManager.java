/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import cn.wonhigh.baize.manager.ios.IInternetOrderReturnManager;
import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrderReturn;
import cn.wonhigh.baize.service.ios.IInternetOrderReturnService;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;


@Service
public class InternetOrderReturnManager extends BaseManager<InternetOrderReturn,String> implements IInternetOrderReturnManager {
    @Autowired
    private IInternetOrderReturnService service;

    protected IService<InternetOrderReturn,String> getService(){
        return service;
    }

    
}