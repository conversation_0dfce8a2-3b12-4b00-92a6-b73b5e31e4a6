/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.manager.gms.ICompanyManager;
import cn.wonhigh.baize.model.entity.gms.Company;
import cn.wonhigh.baize.service.gms.ICompanyService;
import org.springframework.stereotype.Service;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class CompanyManager extends BaseManager<Company,Integer> implements ICompanyManager {
    @Autowired
    private ICompanyService service;

    protected IService<Company,Integer> getService(){
        return service;
    }
    
    public Company findByUnique(String companyNo) {
			  return service.findByUnique(companyNo);
    }

    public Integer deleteByUnique(String companyNo) {
			  return service.deleteByUnique(companyNo);
    }

    public List<Company> selectByUniques(List<String> companyNos) {
		return service.selectByUniques(companyNos);
	}
    	
	public List<Company> selectByIds(List<Integer> ids) {
		return service.selectByIds(ids);
	}
	
	public Integer batchInsert(List<Company> list) {
		list.stream().forEach(t -> initEntry(t));
		return service.batchInsert(list);
	}
}