/** zkh **/
package cn.wonhigh.baize.service.gms.impl;

import cn.wonhigh.baize.model.entity.gms.ZoneInfo;
import cn.wonhigh.baize.repository.gms.ZoneInfoRepository;
import cn.wonhigh.baize.service.gms.IZoneInfoService;
import org.springframework.stereotype.Service;
import topmall.framework.repository.IRepository;
import topmall.framework.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

@Service
public class ZoneInfoService extends BaseService<ZoneInfo,Integer> implements IZoneInfoService {
    @Autowired
    private ZoneInfoRepository repository;

    protected IRepository<ZoneInfo,Integer> getRepository(){
        return repository;
    }
    
    public ZoneInfo findByUnique(String zoneNo){
        return repository.findByUnique(zoneNo);
    }

    public Integer deleteByUnique(String zoneNo){
        return repository.deleteByUnique(zoneNo);
    }

    public List<ZoneInfo> selectByUniques(List<String> zoneNos) {
        return repository.selectByUniques(zoneNos);
    }
      
	public List<ZoneInfo> selectByIds(List<Integer> ids) {
		return repository.selectByIds(ids);
	}
	
    public Integer batchInsert(List<ZoneInfo> list) {
    	return repository.batchInsert(list);
    }
}