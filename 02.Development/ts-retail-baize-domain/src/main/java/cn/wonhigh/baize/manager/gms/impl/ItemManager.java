/**  **/
package cn.wonhigh.baize.manager.gms.impl;

import cn.wonhigh.baize.manager.gms.IBrandManager;
import cn.wonhigh.baize.manager.gms.IItemManager;
import cn.wonhigh.baize.model.entity.gms.Brand;
import cn.wonhigh.baize.model.entity.gms.Item;
import cn.wonhigh.baize.model.entity.gms.ItemBaseInfo;
import cn.wonhigh.baize.service.gms.IBrandService;
import cn.wonhigh.baize.service.gms.IItemService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import topmall.framework.manager.BaseManager;
import topmall.framework.service.IService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("itemManager")
public class ItemManager extends BaseManager<Item,Integer> implements IItemManager {

    @Autowired
    private IItemService service;

    protected IService<Item,Integer> getService(){
        return service;
    }

	@Override
	public ItemBaseInfo findSku(String brandNo ,String itemCode, String sizeNo) {
		if(StringUtils.isBlank(brandNo) ||StringUtils.isBlank(itemCode) || StringUtils.isBlank(sizeNo)){
			return null;
		}
		Map<String,Object> params = new HashMap<>();
		params.put("code",itemCode);
		params.put("sizeNo",sizeNo);
		params.put("brandNo",brandNo);
		List<ItemBaseInfo> itemList = service.queryItemByParams(params);
		if(!itemList.isEmpty()){
			return itemList.get(0);
		}
		return null;
	}

}