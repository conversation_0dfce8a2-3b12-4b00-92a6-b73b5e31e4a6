/**
 *
 **/
package cn.wonhigh.baize.manager.gms;


import cn.mercury.basic.query.Pagenation;
import cn.mercury.manager.IManager;
import cn.mercury.manager.ManagerException;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeStatusChangeDto;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeValidateDto;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;

import javax.validation.ValidationException;
import java.util.List;
import java.util.Map;

public interface IInternetVirtualWarehouseScopeManager extends IManager<InternetVirtualWarehouseScope, String> {


    List<InternetVirtualWarehouseScope> selectScopeListByPage(Map<String, Object> var1, Pagenation var2);
    int selectScopeListCount(Map<String, Object> var1);


    List<InternetVirtualWarehouseScope> selectStoreByPage(Map<String, Object> var1, Pagenation var2);

    int selectStoreCount(Map<String, Object> var1);

    /**
     * 查询机构是否存在
     * @param params
     * @return
     */
    List<InternetVirtualWarehouseScope> selectExistStore(Map<String, Object> params);

    /**
     * 查询虚仓下是否有所有店或者单店的范围
     * @param params
     * @return
     */
    List<InternetVirtualWarehouseScope> selectExistShop(Map<String, Object> params);


    /**
     * 验证数据
     *
     * @throws ValidationException 验证失败抛出异常
     */
    void validateData(InternetVirtualWarehouseScopeValidateDto validateDto, InternetVirtualWarehouseInfo info) throws ValidationException;

    void saveInternetVirtualWarehouseScope(InternetVirtualWarehouseScopeSave save, InternetVirtualWarehouseInfo info) throws ManagerException;


    /**
     * 修改所有子仓库存类型
     * @param params
     * @return
     */
    int updateAllScope(Map<String, Object> params);

    /**
     * 总仓删除所有店时，子仓删除
     * @param params
     * @return
     */
    int deleteAllShopScope(Map<String, Object> params);

    /**
     * 总仓删除指定店或者仓时，子仓删除
     * @param params
     * @return
     */
    int deleteStoreScope(Map<String, Object> params);


    /**
     * 查询机构货管信息
     * @param params
     * @return
     */
    List<InternetVirtualWarehouseScope> selectScopeInfo(Map<String, Object> params);



    /**
     * 批量删除
     * @param dataIds
     * @return
     */
    void deleteData(List<String> dataIds);

    /**
     * 修改状态
     * @param statusChangeDto
     * @return
     */
    void statusChange(InternetVirtualWarehouseScopeStatusChangeDto statusChangeDto, InternetVirtualWarehouseInfo virtualWarehouseInfo);

}