/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.entity.gms.InternetSystemLogs;

import cn.mercury.manager.IManager;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface IInternetSystemLogsManager extends IManager<InternetSystemLogs,Long>{

    public List<Map<String, Object>> selectSystemLogsByKey2(String billNo, List<Integer> sysCodes);
	public List<Map<String, Object>> selectSystemLogsByKey1(String billNo, int type, Function<String, String> optFun);
}