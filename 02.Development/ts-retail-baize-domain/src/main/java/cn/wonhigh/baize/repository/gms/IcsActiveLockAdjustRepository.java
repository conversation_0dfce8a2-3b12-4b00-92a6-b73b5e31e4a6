/**  **/
package cn.wonhigh.baize.repository.gms;

import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import org.apache.ibatis.annotations.Mapper;
import topmall.framework.repository.IRepository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface IcsActiveLockAdjustRepository extends IRepository<IcsActiveLockAdjust,String> {
    
    IcsActiveLockAdjust findByUnique(@Param("billNo") String billNo);

    Integer deleteByUnique(@Param("billNo") String billNo);

    List<IcsActiveLockAdjust> selectByUniques(@Param("list") List<String> billNos);
    
    Integer batchInsert(@Param("list") List<IcsActiveLockAdjust> list);
    
    List<IcsActiveLockAdjust> selectByIds(@Param("list") List<Integer> ids);

    void updateAdjustStatus(@Param("billNo") String billNo, @Param("status") Integer status);
}