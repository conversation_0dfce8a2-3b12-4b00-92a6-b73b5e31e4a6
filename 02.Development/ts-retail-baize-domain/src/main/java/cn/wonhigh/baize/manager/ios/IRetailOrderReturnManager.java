/**  **/
package cn.wonhigh.baize.manager.ios;

import cn.wonhigh.baize.model.entity.ios.RetailOrderReturn;

import cn.mercury.manager.IManager;

public interface IRetailOrderReturnManager extends IManager<RetailOrderReturn,String>{

    
    public RetailOrderReturn findByUnique(String billNo) ;

    public Integer deleteByUnique(String billNo);

    public Integer insertForUpdate(RetailOrderReturn entry);
    
}