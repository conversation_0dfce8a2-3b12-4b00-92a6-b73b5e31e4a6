/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.Brand;
import topmall.framework.service.IService;
import java.util.List;

public interface IBrandService extends IService<Brand,Integer>{
    
    Brand findByUnique(String brandNo);

    Integer deleteByUnique(String brandNo);

    List<Brand> selectByUniques(List<String> brandNos);
      
    Integer batchInsert(List<Brand> list);
    
    List<Brand> selectByIds(List<Integer> ids);
}