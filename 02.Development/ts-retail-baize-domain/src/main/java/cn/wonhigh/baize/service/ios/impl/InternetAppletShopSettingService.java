package cn.wonhigh.baize.service.ios.impl;

import cn.wonhigh.baize.model.entity.ios.InternetAppletShopSetting;
import cn.wonhigh.baize.repository.ios.InternetAppletShopSettingRepository;
import cn.wonhigh.baize.service.ios.IInternetAppletShopSettingService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wudong
 * @create: 2025-06-04 17:44
 **/
@Service
public class InternetAppletShopSettingService implements IInternetAppletShopSettingService {
    @Autowired
    private InternetAppletShopSettingRepository repository;

    @Override
    public boolean insert(InternetAppletShopSetting entity) {
        Integer insert = repository.insert(entity);
        return insert != null && insert > 0;
    }

    @Override
    public boolean updateByShopNo(InternetAppletShopSetting entity) {
        Integer update = repository.updateByShopNo(entity);
        return update != null && update > 0;
    }

    @Override
    public InternetAppletShopSetting findByShopNo(String shopNo) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopNo", shopNo);
        List<InternetAppletShopSetting> list = repository.selectByParams(params, null);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<InternetAppletShopSetting> selectSdByShopNos(List<String> shopNos) {
        if (CollectionUtils.isEmpty(shopNos)) {
            return new ArrayList<>();
        }
        List<InternetAppletShopSetting> result = new ArrayList<>(shopNos.size());
        int pageSize = 5000;
        List<List<String>> partition = Lists.partition(shopNos, pageSize);
        for (List<String> strings : partition) {
            Map<String, Object> params = new HashMap<>();
            params.put("shopNoList", strings);

            List<InternetAppletShopSetting> internetAppletShopSettings = repository.selectByParams(params, null);
            if (!internetAppletShopSettings.isEmpty()) {
                List<InternetAppletShopSetting> collect = internetAppletShopSettings.stream().filter(InternetAppletShopSetting::sdShop).collect(Collectors.toList());
                result.addAll(collect);
            }
        }

        return result;
    }
}
