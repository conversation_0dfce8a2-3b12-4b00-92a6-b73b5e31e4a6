/**  **/
package cn.wonhigh.baize.service.ios;

import cn.wonhigh.baize.model.entity.ios.RetailOrder;
import topmall.framework.service.IService;

import java.util.List;

public interface IRetailOrderService extends IService<RetailOrder,String>{

    
    public RetailOrder findByUnique(String billNo);

    public Integer deleteByUnique(String billNo);

    public Integer insertForUpdate(RetailOrder entry);

    List<RetailOrder> selectByOrderSubNo(String orderSubNo);
}