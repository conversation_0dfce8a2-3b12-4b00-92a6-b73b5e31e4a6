/**  **/
package cn.wonhigh.baize.service.gms;

import cn.wonhigh.baize.model.entity.gms.Company;
import topmall.framework.service.IService;
import java.util.List;

public interface ICompanyService extends IService<Company,Integer>{
    
    Company findByUnique(String companyNo);

    Integer deleteByUnique(String companyNo);

    List<Company> selectByUniques(List<String> companyNos);
      
    Integer batchInsert(List<Company> list);
    
    List<Company> selectByIds(List<Integer> ids);
}