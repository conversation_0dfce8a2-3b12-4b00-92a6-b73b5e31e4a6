package cn.wonhigh.baize.manager.gms;

import cn.mercury.basic.query.PageResult;
import cn.wonhigh.baize.model.dto.mdm.StoreListQueryParamDto;
import cn.wonhigh.baize.model.dto.mdm.StoreListResultDataDto;

public interface MdmManager {

    /**
     * 根据参数, 调用mdm api接口, 查询门店列表
     * @param queryParamDto 查询参数
     * @return 分页数据
     */
    PageResult<StoreListResultDataDto> getStoreList(StoreListQueryParamDto queryParamDto);
}
