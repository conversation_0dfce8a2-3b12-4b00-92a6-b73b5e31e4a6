package cn.wonhigh.baize.domain.configuration.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 */
@Configuration
@MapperScan(basePackages = {
		OmsDataSourceConfiguration.BASE_PACKAGE }, sqlSessionFactoryRef = "omsSqlSessionFactory")
@Order(-10)
public class OmsDataSourceConfiguration {

	public static final String BASE_PACKAGE = "cn.wonhigh.baize.repository.oms";

	public OmsDataSourceConfiguration() {
		System.err.println("init oms default datasource ...");
	}
	
	@Bean("omsDatasource")
	@ConfigurationProperties("retail.oms.datasource")
	public DataSource dataSourceGms() {
		return DataSourceBuilder.create().type(DruidDataSource.class).build();
	}
	
	@Bean("omsSqlSessionFactory")
	public SqlSessionFactory sqlSessionFactory(@Qualifier("omsDatasource") DataSource ds) throws Exception {

		SqlSessionFactoryBean bean = new SqlSessionFactoryBean();

		bean.setDataSource(ds);

		Resource rs = new PathMatchingResourcePatternResolver()
				.getResources("mybatis-oms-config.xml")[0];
		bean.setConfigLocation(rs);
		bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("mapper/oms/*.xml"));
 
		return bean.getObject();
	}

 
	@Bean("omsSqlSessionTemplate")
	public SqlSessionTemplate sqlSessionTemplate(@Qualifier("omsSqlSessionFactory") SqlSessionFactory factory) {
		return new SqlSessionTemplate(factory);
	}

	@Bean("omsTransactionManager")
	public DataSourceTransactionManager transactionManager(@Qualifier("omsDatasource") DataSource ds) {
		return new DataSourceTransactionManager(ds);
	}

}
