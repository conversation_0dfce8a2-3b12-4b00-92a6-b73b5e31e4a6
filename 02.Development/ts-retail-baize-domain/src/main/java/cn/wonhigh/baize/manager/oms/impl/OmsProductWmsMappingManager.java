package cn.wonhigh.baize.manager.oms.impl;

import cn.wonhigh.baize.model.entity.oms.OmsProductWmsMapping;
import cn.wonhigh.baize.service.oms.IOmsProductWmsMappingService;
import cn.wonhigh.baize.manager.oms.IOmsProductWmsMappingManager;

import org.springframework.stereotype.Service;
import topmall.framework.service.IService;
import topmall.framework.manager.BaseManager;

import javax.annotation.Resource;


/**
 * (OmsProductWmsMapping)
 *
 * <AUTHOR>
 * @since 2024-12-28 11:21:23
 */
@Service("omsProductWmsMappingManager")
public class OmsProductWmsMappingManager extends BaseManager<OmsProductWmsMapping, Long> implements IOmsProductWmsMappingManager {

    @Resource
    private IOmsProductWmsMappingService service;

    protected IService<OmsProductWmsMapping, Long> getService() {
        return service;
    }

}