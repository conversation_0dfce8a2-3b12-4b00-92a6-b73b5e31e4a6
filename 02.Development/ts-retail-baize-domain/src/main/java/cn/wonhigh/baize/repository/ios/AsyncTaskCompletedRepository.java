/**
 *
 **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.AsyncTaskCompleted;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

import java.util.List;

@Mapper
public interface AsyncTaskCompletedRepository extends IRepository<AsyncTaskCompleted, String> {
    AsyncTaskCompleted getLastCreateByBillNos(@Param("taskType") int taskType, @Param("billNos") List<String> billNos);
}