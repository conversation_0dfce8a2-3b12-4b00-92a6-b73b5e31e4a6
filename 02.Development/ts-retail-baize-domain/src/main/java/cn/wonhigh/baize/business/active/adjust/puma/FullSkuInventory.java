package cn.wonhigh.baize.business.active.adjust.puma;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2025/6/19 14:06
 */
@XmlRootElement(name = "fullSkuInventorys")
public class FullSkuInventory {

    private String sourceMarkCode;

    private String msgId;

    private List<InventoryItem> fullSkuInventory;

    public String getSourceMarkCode() {
        return sourceMarkCode;
    }

    public void setSourceMarkCode(String sourceMarkCode) {
        this.sourceMarkCode = sourceMarkCode;
    }

    public List<InventoryItem> getFullSkuInventory() {
        return fullSkuInventory;
    }

    public void setFullSkuInventory(List<InventoryItem> fullSkuInventory) {
        this.fullSkuInventory = fullSkuInventory;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public static class Builder {
        private String sourceMarkCode;
        private String msgId;
        private List<InventoryItem> items;

        public Builder sourceMarkCode(String sourceMarkCode) {
            this.sourceMarkCode = sourceMarkCode;
            return this;
        }
        public Builder msgId(String msgId) {
            this.msgId = msgId;
            return this;
        }
        public Builder items(List<InventoryItem> items) {
            this.items = items;
            return this;
        }
        public FullSkuInventory build() {
            FullSkuInventory inventory = new FullSkuInventory();
            inventory.setSourceMarkCode(sourceMarkCode);
            inventory.setMsgId(msgId);
            inventory.setFullSkuInventory(items);
            return inventory;
        }
    }
}
