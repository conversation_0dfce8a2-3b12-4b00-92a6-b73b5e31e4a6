/**  **/
package cn.wonhigh.baize.manager.ios.impl;

import org.springframework.stereotype.Service;
import cn.wonhigh.baize.model.entity.ios.InternetOrder;
import cn.wonhigh.baize.service.ios.IInternetOrderService;
import cn.wonhigh.baize.manager.ios.IInternetOrderManager;
import topmall.framework.service.IService;

import topmall.framework.manager.BaseManager;
import org.springframework.beans.factory.annotation.Autowired;
import cn.mercury.manager.ManagerException;


@Service
public class InternetOrderManager extends BaseManager<InternetOrder,String> implements IInternetOrderManager{
    @Autowired
    private IInternetOrderService service;

    protected IService<InternetOrder,String> getService(){
        return service;
    }

    
    public InternetOrder findByUnique(String orderSubNo) {
        try {
			return service.findByUnique(orderSubNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer deleteByUnique(String orderSubNo) {
        try {
			return service.deleteByUnique(orderSubNo);
		} catch (Exception e) {
			throw new ManagerException(e);
		}
    }

    public Integer insertForUpdate(InternetOrder entry){
        try {
        return service.insertForUpdate(entry);
      } catch (Exception e) {
        throw new ManagerException(e);
      }
    }

    
}