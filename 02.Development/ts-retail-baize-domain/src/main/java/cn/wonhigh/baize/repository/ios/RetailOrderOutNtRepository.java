/**
 *
 **/
package cn.wonhigh.baize.repository.ios;

import cn.wonhigh.baize.model.entity.ios.RetailOrderOutNt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import topmall.framework.repository.IRepository;

@Mapper
public interface RetailOrderOutNtRepository extends IRepository<RetailOrderOutNt, String> {
    RetailOrderOutNt findByBillNo(@Param("billNo") String billNO);
}