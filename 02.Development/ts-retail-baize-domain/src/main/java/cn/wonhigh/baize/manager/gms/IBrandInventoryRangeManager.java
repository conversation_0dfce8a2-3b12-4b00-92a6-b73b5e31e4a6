/**  **/
package cn.wonhigh.baize.manager.gms;

import cn.wonhigh.baize.model.entity.gms.BrandInventoryRange;

import cn.mercury.manager.IManager;
import java.util.List;
import java.util.Map;

public interface IBrandInventoryRangeManager extends IManager<BrandInventoryRange,String>{
    
    Integer batchInsert(List<BrandInventoryRange> list);
    
    List<BrandInventoryRange> selectByIds(List<String> ids);

    public int selectShareNoExist(Map<String, Object> paramMaps);

    public void updateShare(Map<String, Object> paramMap);
}