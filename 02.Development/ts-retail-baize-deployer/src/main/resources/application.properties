# PROFILES
## dev | prod | test
spring.profiles.active=default,dev

topmall.app.name=ts-retail-baize-deployer

# apollo
app.id=ts-retail-baize
apollo.cluster=default
apollo.meta=http://apollo-config-dev.ts.wonhigh.cn
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=deployer,application,retail.public
apollo.bootstrap.eagerLoad.enabled=true

# dubbo
dubbo.annotation.package=cn.wonhigh.baize.api,cn.wonhigh.baize.manager.impl,cn.wonhigh.baize.api.service
db.server.auto=false
db.server=