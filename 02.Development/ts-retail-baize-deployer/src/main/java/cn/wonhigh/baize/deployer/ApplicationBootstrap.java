/** Kain **/
package cn.wonhigh.baize.deployer;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import topmall.framework.servicemodel.Bootstrap;

@EnableApolloConfig
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class ApplicationBootstrap //extends Bootstrap
{

    public static void main(String[] args)   {
        Bootstrap.run(args, ApplicationBootstrap.class);
    }
}
