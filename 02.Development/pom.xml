<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.wonhigh.topmall</groupId>
        <artifactId>topmall</artifactId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <groupId>cn.wonhigh.baize</groupId>
    <artifactId>ts-retail-baize</artifactId>
    <name>ts-retail-baize</name>
    <url>http://maven.apache.org</url>
    <packaging>pom</packaging>
    <version>1.0.28-SNAPSHOT</version>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <uc.api.version>1.0.8-TS-SNAPSHOT</uc.api.version>
        <mysql.version>5.1.47</mysql.version>
        <poi.version>3.17</poi.version>
        <iis.api.version>2.0.14-TS-SNAPSHOT</iis.api.version>
        <gms.api.version>2.1.0-TS-SNAPSHOT</gms.api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.wonhigh.topmall</groupId>
                <artifactId>ts-dependencies-parent</artifactId>
                <version>3.0.0-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>

                <groupId>cn.wonhigh.retail</groupId>
                <artifactId>retail-uc-api-client</artifactId>
                <version>${uc.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.wonhigh</groupId>
                        <artifactId>base-framework-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.wonhigh</groupId>
                        <artifactId>base-framework-common-lang</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>cn.wonhigh.retail</groupId>
                <artifactId>retail-gms-api-client</artifactId>
                <version>${gms.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.wonhigh.logistics</groupId>
                        <artifactId>logistics-dop-dubbox-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modules>
        <module>ts-retail-baize-app</module>
        <module>ts-retail-baize-api</module>
        <module>ts-retail-baize-deployer</module>
        <module>ts-retail-baize-domain</module>
        <module>ts-retail-baize-model</module>
        <module>ts-retail-baize-service</module>
        <module>ts-retail-baize-web</module>
    </modules>
</project>