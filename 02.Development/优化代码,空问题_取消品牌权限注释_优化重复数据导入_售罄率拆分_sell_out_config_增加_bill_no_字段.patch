Subject: [PATCH] 优化代码,空问题
取消品牌权限注释
优化重复数据导入
售罄率拆分
sell_out_config 增加 bill_no 字段
---
Index: 02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigMapper.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigMapper.xml b/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigMapper.xml
--- a/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigMapper.xml	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigMapper.xml	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -4,8 +4,8 @@
     <!-- auto generate  -->
     <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.SellOutConfig">
                 
-        <id column="id" property="id" jdbcType="CHAR" />	
-        
+        <id column="id" property="id" jdbcType="CHAR" />
+        <result column="bill_no" property="billNo" jdbcType="CHAR" />
         <result column="type" property="type" jdbcType="TINYINT" />	
         <result column="brand_no" property="brandNo" jdbcType="CHAR" />	
         <result column="brand_name" property="brandName" jdbcType="VARCHAR" />	
@@ -26,7 +26,7 @@
     </resultMap>
 
     <sql id="column_list">
-        `id`,`type`,`brand_no`,`brand_name`,`classify_code`,`classify_name`,`classify_value_code`,`classify_value_name`,`item_no`,`size_no`,`item_code`,`target_field`,`target_value`,`status`,`create_user`,`create_time`,`update_user`,`update_time`
+        `id`,`bill_no`,`type`,`brand_no`,`brand_name`,`classify_code`,`classify_name`,`classify_value_code`,`classify_value_name`,`item_no`,`size_no`,`item_code`,`target_field`,`target_value`,`status`,`create_user`,`create_time`,`update_user`,`update_time`
     </sql>
 
     <sql id="condition">
@@ -37,8 +37,11 @@
         
         	<if test="null!=params.id  and ''!=params.id ">
 				 AND `id`=#{params.id}
-            </if>	
-        
+            </if>
+            <if test="null!=params.billNo  and ''!=params.billNo ">
+				 AND `bill_no`=#{params.billNo}
+            </if>
+
         	<if test="null!=params.type ">
 				 AND `type`=#{params.type}
             </if>	
@@ -203,6 +206,20 @@
         </if>
     </select>
 
+    <select id="selectByParamsForHandler"
+            resultMap="baseResultMap"
+            parameterType="map"
+            resultSetType="FORWARD_ONLY"
+            fetchSize="-2147483648">
+        SELECT
+        <include refid="column_list" />
+        FROM sell_out_config
+        <where>
+            <include refid="condition" />
+        </where>
+        ORDER BY bill_no desc
+    </select>
+
     <delete id="deleteByPrimaryKey">
         DELETE FROM sell_out_config
         WHERE id = #{id}
@@ -233,6 +250,10 @@
             <if test="id != null">
                 `id`,
             </if>
+
+            <if test="billNo != null">
+                `bill_no`,
+            </if>
             
             <if test="type != null">
                 `type`,
@@ -308,6 +329,10 @@
             <if test="id != null">
                 #{id},
             </if>
+
+            <if test="billNo != null">
+                #{billNo},
+            </if>
             
             <if test="type != null">
                 #{type},
@@ -384,12 +409,15 @@
         INSERT INTO sell_out_config (<include refid="column_list"></include>)
         values 
         <foreach collection="list" item="item" separator=",">
-            (#{item.id}, #{item.type}, #{item.brandNo}, #{item.brandName}, #{item.classifyCode}, #{item.classifyName}, #{item.classifyValueCode}, #{item.classifyValueName}, #{item.itemNo}, #{item.sizeNo}, #{item.itemCode}, #{item.targetField}, #{item.targetValue}, #{item.status}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime})
+            (#{item.id}, #{item.billNo}, #{item.type}, #{item.brandNo}, #{item.brandName}, #{item.classifyCode}, #{item.classifyName}, #{item.classifyValueCode}, #{item.classifyValueName}, #{item.itemNo}, #{item.sizeNo}, #{item.itemCode}, #{item.targetField}, #{item.targetValue}, #{item.status}, #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime})
         </foreach>
     </insert>
     <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfig">
         UPDATE sell_out_config
         <set>
+            <if test="billNo != null">
+                `bill_no` = #{billNo},
+            </if>
             
             <if test="type != null">
                 `type` = #{type},
@@ -451,6 +479,9 @@
     <update id="updateStatusByParams" parameterType="map">
     	update sell_out_config set status = #{params.status}
         <where>
+            <if test="null!=params.brandNo  and ''!=params.brandNo ">
+                AND `brand_no`=#{params.brandNo}
+            </if>
             <if test="null!=params.classifyCode  and ''!=params.classifyCode ">
                 AND `classify_code`=#{params.classifyCode}
             </if>
@@ -555,14 +586,6 @@
                 </choose>
             FROM sell_out_config
             <where>
-                <choose>
-                    <when test="null!=params.selectType and 'item'.equals(params.selectType)">
-                        item_no is not null
-                    </when>
-                    <otherwise>
-                        item_no is null
-                    </otherwise>
-                </choose>
                 <include refid="condition" />
             </where>
             <choose>
@@ -578,6 +601,32 @@
         LIMIT ${page.startRowNum},${page.pageSize}
 
      </select>
+
+    <select id="selectByUniqueList" resultMap="baseResultMap">
+        SELECT
+        <include refid="column_list"/>
+        FROM sell_out_config
+        <where>
+            <if test="list!=null and list.size>0">
+                and
+                <choose>
+                    <when test="type == 1">
+                        (brand_no, item_no, size_no) in
+                        <foreach collection="list" item="item" open="(" separator="," close=")">
+                            (#{item.t1}, #{item.t2}, #{item.t3})
+                        </foreach>
+                    </when>
+                    <otherwise>
+                        (brand_no, classify_code, classify_value_code) in
+                        <foreach collection="list" item="item" open="(" separator="," close=")">
+                            (#{item.t1}, #{item.t2}, #{item.t3})
+                        </foreach>
+                    </otherwise>
+                </choose>
+            </if>
+            and type = #{type}
+        </where>
+    </select>
 
     <!-- auto generate end-->
 </mapper>
Index: 02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfig.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfig.java b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfig.java
--- a/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfig.java	(revision 251a5caff62ce911303e75f686b383bde6c7a789)
+++ b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfig.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -7,13 +7,18 @@
 import cn.mercury.annotation.Label;
 import java.util.Date;
 import java.math.BigDecimal;
-/** 
+import java.util.List;
+
+/**
 *auto generate start ,don't modify
 * 售罄配置表
 **/
 public class SellOutConfig  extends cn.mercury.domain.BasicEntity  {
 
     private static final long serialVersionUID = 1726282483448L;
+
+    @Label("单据编号")
+    private String billNo;
     
     //状态 (0-按分类,1-按商品)
     @Label(value = "状态", defaultVal = "0") 
@@ -69,6 +74,24 @@
 
     private String statusName;
 
+    private List<SellOutConfigDtl> sellOutConfigDtlList;
+
+    public List<SellOutConfigDtl> getSellOutConfigDtlList() {
+        return sellOutConfigDtlList;
+    }
+
+    public void setSellOutConfigDtlList(List<SellOutConfigDtl> sellOutConfigDtlList) {
+        this.sellOutConfigDtlList = sellOutConfigDtlList;
+    }
+
+    public String getBillNo() {
+        return billNo;
+    }
+
+    public void setBillNo(String billNo) {
+        this.billNo = billNo;
+    }
+
     public String getStatusName() {
         return statusName;
     }
@@ -199,6 +222,14 @@
             this.query.where("id", value);
             return this;
         }
+
+        public SellOutConfigBuilder billNo(String value ){
+            this.obj.billNo = value;
+            if( query == null  )
+                query = new Query();
+            this.query.where("billNo", value);
+            return this;
+        }
         
         public SellOutConfigBuilder type(Integer value ){
             this.obj.type = value;
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigDtlManager.java
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigDtlManager.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigDtlManager.java
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigDtlManager.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,17 @@
+/**  **/
+package cn.wonhigh.baize.manager.gms;
+
+import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
+
+import cn.mercury.manager.IManager;
+import java.util.List;
+
+public interface ISellOutConfigDtlManager extends IManager<SellOutConfigDtl,String>{
+    
+    Integer batchInsert(List<SellOutConfigDtl> list);
+    
+    List<SellOutConfigDtl> selectByIds(List<Integer> ids);
+
+    void deleteByBillNo(List<String> collect);
+
+}
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigManager.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigManager.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigManager.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigManager.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/ISellOutConfigManager.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -7,12 +7,14 @@
 import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
 import cn.wonhigh.baize.model.entity.gms.ItemAttr;
 import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
+import org.apache.ibatis.session.ResultHandler;
+import reactor.util.function.Tuple2;
+import reactor.util.function.Tuple3;
 
 import java.util.List;
 
 public interface ISellOutConfigManager extends IManager<SellOutConfig,String>{
-    
-    Integer batchInsert(List<SellOutConfig> list);
+
     
     List<SellOutConfig> selectByIds(List<Integer> ids);
 
@@ -23,4 +25,10 @@
     List<SellOutConfigDto> selectSellOutList(Query query, Pagenation page);
 
 	int updateStatusByParams(Query q);
+
+    List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list);
+
+    int deleteByUnique(Integer type, Tuple3<String, String, String> tuple3);
+
+    void selectByParamsForHandler(Query query, ResultHandler<SellOutConfig> o);
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigDtlManager.java
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigDtlManager.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigDtlManager.java
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigDtlManager.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,43 @@
+/**  **/
+package cn.wonhigh.baize.manager.gms.impl;
+
+import org.springframework.stereotype.Service;
+import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
+import cn.wonhigh.baize.service.gms.ISellOutConfigDtlService;
+import cn.wonhigh.baize.manager.gms.ISellOutConfigDtlManager;
+import topmall.framework.service.IService;
+
+import topmall.framework.manager.BaseManager;
+import org.springframework.beans.factory.annotation.Autowired;
+import java.util.List;
+
+@Service
+public class SellOutConfigDtlManager extends BaseManager<SellOutConfigDtl,String> implements ISellOutConfigDtlManager{
+    @Autowired
+    private ISellOutConfigDtlService service;
+
+    protected IService<SellOutConfigDtl,String> getService(){
+        return service;
+    }
+    
+    @Override
+	public List<SellOutConfigDtl> selectByIds(List<Integer> ids) {
+		return service.selectByIds(ids);
+	}
+	@Override
+	public Integer batchInsert(List<SellOutConfigDtl> list) {
+        if (list == null) {
+            return 0;
+        }
+		list.forEach(this::initEntry);
+		return service.batchInsert(list);
+	}
+
+    @Override
+    public void deleteByBillNo(List<String> collect) {
+        if (collect == null || collect.isEmpty()) {
+            return;
+        }
+        service.deleteByBillNo(collect);
+    }
+}
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigManager.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigManager.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigManager.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigManager.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/manager/gms/impl/SellOutConfigManager.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -3,23 +3,38 @@
 
 import cn.mercury.basic.query.Pagenation;
 import cn.mercury.basic.query.Query;
+import cn.mercury.manager.ManagerException;
+import cn.wonhigh.baize.manager.gms.ISellOutConfigDtlManager;
 import cn.wonhigh.baize.manager.gms.ISellOutConfigManager;
 import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
 import cn.wonhigh.baize.model.entity.gms.ItemAttr;
 import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
+import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
+import cn.wonhigh.baize.service.gms.ISellOutConfigDtlService;
 import cn.wonhigh.baize.service.gms.ISellOutConfigService;
+import org.apache.ibatis.session.ResultHandler;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;
+import org.springframework.transaction.annotation.Transactional;
+import org.springframework.util.Assert;
+import reactor.util.function.Tuple2;
+import reactor.util.function.Tuple3;
 import topmall.framework.manager.BaseManager;
 import topmall.framework.service.IService;
 
+import java.util.ArrayList;
+import java.util.Collections;
 import java.util.List;
+import java.util.stream.Collectors;
 
 @Service
 public class SellOutConfigManager extends BaseManager<SellOutConfig,String> implements ISellOutConfigManager{
     @Autowired
     private ISellOutConfigService service;
 
+    @Autowired
+    private ISellOutConfigDtlManager sellOutConfigDtlManager;
+
     protected IService<SellOutConfig,String> getService(){
         return service;
     }
@@ -44,15 +59,58 @@
 	}
 
 
-
-
-	public Integer batchInsert(List<SellOutConfig> list) {
-		list.stream().forEach(t -> initEntry(t));
-		return service.batchInsert(list);
-	}
-
-	@Override
+    @Override
+    @Transactional
+    public Integer batchSave(List<SellOutConfig> inserted, List<SellOutConfig> updated, List<SellOutConfig> deleted) throws ManagerException {
+
+        List<SellOutConfigDtl> configDtls = new ArrayList<>();
+        if (inserted != null && !inserted.isEmpty()) {
+            inserted.forEach(this::initEntry);
+            configDtls.addAll(inserted.stream().map(SellOutConfig::getSellOutConfigDtlList).flatMap(List::stream)
+                    .collect(Collectors.toList()));
+        }
+
+        if (updated != null && !updated.isEmpty()) {
+            updated.forEach(this::initEntry);
+            this.sellOutConfigDtlManager.deleteByBillNo(updated.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList()));
+            configDtls.addAll(updated.stream().map(SellOutConfig::getSellOutConfigDtlList).flatMap(List::stream).collect(Collectors.toList()));
+        }
+
+
+        Integer integer = super.batchSave(inserted, updated, deleted);
+
+        if (!configDtls.isEmpty()) {
+            this.sellOutConfigDtlManager.batchInsert(configDtls);
+        }
+        return integer;
+    }
+
+
+
+    @Override
 	public int updateStatusByParams(Query q) {
 		return service.updateStatusByParams(q);
 	}
+
+    @Override
+    public List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list) {
+        return service.selectByUniqueList(type, list);
+    }
+
+    @Override
+    public int deleteByUnique(Integer type, Tuple3<String, String, String> tuple3) {
+        Assert.notNull(type, "type is null");
+        List<SellOutConfig> sellOutConfigs = this.selectByUniqueList(type, Collections.singletonList(tuple3));
+        if (sellOutConfigs != null && !sellOutConfigs.isEmpty()) {
+            this.sellOutConfigDtlManager.deleteByBillNo(sellOutConfigs.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList()));
+            return this.deleteByParams(Query.Where("id", sellOutConfigs.get(0).getId()));
+        }
+
+        return 0;
+    }
+
+    @Override
+    public void selectByParamsForHandler(Query query, ResultHandler<SellOutConfig> o) {
+        this.service.selectByParamsForHandler(query.asMap(), o);
+    }
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigDtlRepository.java
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigDtlRepository.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigDtlRepository.java
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigDtlRepository.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,17 @@
+/**  **/
+package cn.wonhigh.baize.repository.gms;
+
+import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
+import org.apache.ibatis.annotations.Mapper;
+import topmall.framework.repository.IRepository;
+import org.apache.ibatis.annotations.Param;
+import java.util.List;
+@Mapper
+public interface SellOutConfigDtlRepository extends IRepository<SellOutConfigDtl,String> {
+    
+    Integer batchInsert(@Param("list") List<SellOutConfigDtl> list);
+    
+    List<SellOutConfigDtl> selectByIds(@Param("list") List<Integer> ids);
+
+    int deleteByBillNo(List<String> collect);
+}
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigRepository.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigRepository.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigRepository.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigRepository.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/repository/gms/SellOutConfigRepository.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -7,23 +7,31 @@
 import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
 import org.apache.ibatis.annotations.Mapper;
 import org.apache.ibatis.annotations.Param;
+import org.apache.ibatis.session.ResultHandler;
+import reactor.util.function.Tuple2;
+import reactor.util.function.Tuple3;
 import topmall.framework.repository.IRepository;
 
 import java.util.List;
 import java.util.Map;
 
 @Mapper
-public interface SellOutConfigRepository extends IRepository<SellOutConfig,String> {
-    
+public interface SellOutConfigRepository extends IRepository<SellOutConfig, String> {
+
     Integer batchInsert(@Param("list") List<SellOutConfig> list);
-    
+
     List<SellOutConfig> selectByIds(@Param("list") List<Integer> ids);
 
     List<ItemAttr> selectShareClassifyList(@Param("params") Map<String, Object> params);
 
-    int selectSellOutCount(@Param("params")Map<String,Object> query);
+    int selectSellOutCount(@Param("params") Map<String, Object> query);
 
-    List<SellOutConfigDto> selectSellOutList(@Param("params")Map<String,Object> query, @Param("page")Pagenation page);
+    List<SellOutConfigDto> selectSellOutList(@Param("params") Map<String, Object> query, @Param("page") Pagenation page);
 
-	int updateStatusByParams(@Param("params")Map<String, Object> map);
+    int updateStatusByParams(@Param("params") Map<String, Object> map);
+
+    List<SellOutConfig> selectByUniqueList(@Param("type") Integer type, @Param("list") List<Tuple3<String, String, String>> list);
+
+    void selectByParamsForHandler(@Param("params") Map<String, Object> params, ResultHandler<SellOutConfig> resultHandler);
+
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigDtlService.java
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigDtlService.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigDtlService.java
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigDtlService.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,15 @@
+/**  **/
+package cn.wonhigh.baize.service.gms;
+
+import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
+import topmall.framework.service.IService;
+import java.util.List;
+
+public interface ISellOutConfigDtlService extends IService<SellOutConfigDtl,String>{
+      
+    Integer batchInsert(List<SellOutConfigDtl> list);
+    
+    List<SellOutConfigDtl> selectByIds(List<Integer> ids);
+
+    int deleteByBillNo(List<String> collect);
+}
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigService.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigService.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigService.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/ISellOutConfigService.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -6,9 +6,13 @@
 import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
 import cn.wonhigh.baize.model.entity.gms.ItemAttr;
 import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
+import org.apache.ibatis.session.ResultHandler;
+import reactor.util.function.Tuple2;
+import reactor.util.function.Tuple3;
 import topmall.framework.service.IService;
 
 import java.util.List;
+import java.util.Map;
 
 public interface ISellOutConfigService extends IService<SellOutConfig,String>{
       
@@ -23,4 +27,8 @@
     List<SellOutConfigDto> selectSellOutList(Query query, Pagenation page);
 
 	int updateStatusByParams(Query q);
+
+    List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list);
+
+    void selectByParamsForHandler(Map<String, Object> map, ResultHandler<SellOutConfig> o);
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigDtlService.java
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigDtlService.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigDtlService.java
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigDtlService.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,36 @@
+/**  **/
+package cn.wonhigh.baize.service.gms.impl;
+
+import org.springframework.stereotype.Service;
+import cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl;
+import cn.wonhigh.baize.repository.gms.SellOutConfigDtlRepository;
+
+import cn.wonhigh.baize.service.gms.ISellOutConfigDtlService;
+import topmall.framework.repository.IRepository;
+import topmall.framework.service.BaseService;
+import org.springframework.beans.factory.annotation.Autowired;
+import java.util.List;
+
+@Service
+public class SellOutConfigDtlService extends BaseService<SellOutConfigDtl,String> implements  ISellOutConfigDtlService{
+    @Autowired
+    private SellOutConfigDtlRepository repository;
+
+    protected IRepository<SellOutConfigDtl,String> getRepository(){
+        return repository;
+    }
+    
+    @Override
+	public List<SellOutConfigDtl> selectByIds(List<Integer> ids) {
+		return repository.selectByIds(ids);
+	}
+	@Override
+    public Integer batchInsert(List<SellOutConfigDtl> list) {
+    	return repository.batchInsert(list);
+    }
+
+    @Override
+    public int deleteByBillNo(List<String> collect) {
+        return repository.deleteByBillNo(collect);
+    }
+}
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigService.java b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigService.java
--- a/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigService.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-domain/src/main/java/cn/wonhigh/baize/service/gms/impl/SellOutConfigService.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -8,12 +8,17 @@
 import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
 import cn.wonhigh.baize.repository.gms.SellOutConfigRepository;
 import cn.wonhigh.baize.service.gms.ISellOutConfigService;
+import org.apache.ibatis.session.ResultHandler;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;
+import reactor.util.function.Tuple2;
+import reactor.util.function.Tuple3;
 import topmall.framework.repository.IRepository;
 import topmall.framework.service.BaseService;
 
+import java.util.Collections;
 import java.util.List;
+import java.util.Map;
 
 @Service
 public class SellOutConfigService extends BaseService<SellOutConfig,String> implements  ISellOutConfigService{
@@ -51,4 +56,14 @@
 	public int updateStatusByParams(Query q) {
 		return repository.updateStatusByParams(q.asMap());
 	}
+
+    @Override
+    public List<SellOutConfig> selectByUniqueList(Integer type, List<Tuple3<String, String,String>> list) {
+        return repository.selectByUniqueList(type, list);
+    }
+
+    @Override
+    public void selectByParamsForHandler(Map<String, Object> map, ResultHandler<SellOutConfig> o) {
+        repository.selectByParamsForHandler(map, o);
+    }
 }
\ No newline at end of file
Index: 02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigDtlMapper.xml
===================================================================
diff --git a/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigDtlMapper.xml b/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigDtlMapper.xml
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-domain/src/main/resources/mapper/gms/SellOutConfigDtlMapper.xml	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,271 @@
+<?xml version="1.0" encoding="UTF-8" ?>
+<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
+<mapper namespace="cn.wonhigh.baize.repository.gms.SellOutConfigDtlRepository">
+    <!-- auto generate  -->
+    <resultMap id="baseResultMap" type="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl">
+                
+        <id column="id" property="id" jdbcType="CHAR" />	
+        
+        <result column="update_user" property="updateUser" jdbcType="VARCHAR" />	
+        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />	
+        <result column="bill_no" property="billNo" jdbcType="CHAR" />	
+        <result column="target_field" property="targetField" jdbcType="VARCHAR" />	
+        <result column="target_value" property="targetValue" jdbcType="DECIMAL" />	
+        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />	
+        <result column="create_user" property="createUser" jdbcType="VARCHAR" />	
+    </resultMap>
+
+    <sql id="column_list">
+        `update_user`,`update_time`,`id`,`bill_no`,`target_field`,`target_value`,`create_time`,`create_user`
+    </sql>
+
+    <sql id="condition">
+        <if test="null!=params">
+            <if test="null!=params.queryCondition and ''!=params.queryCondition">
+                AND ${params.queryCondition}
+            </if>
+        
+        	<if test="null!=params.updateUser  and ''!=params.updateUser ">
+				 AND `update_user`=#{params.updateUser}
+            </if>	
+        	<if test="null!=params.updateTime ">
+				 AND `update_time`=#{params.updateTime}
+            </if>	
+        	<if test="null!=params.id  and ''!=params.id ">
+				 AND `id`=#{params.id}
+            </if>	
+        	<if test="null!=params.billNo  and ''!=params.billNo ">
+				 AND `bill_no`=#{params.billNo}
+            </if>
+
+            <!--billNos-->
+            <if test="null!=params.billNos and params.billNos.size()>0 ">
+                AND `bill_no` IN
+                <foreach collection="params.billNos" item="billNo" open="(" close=")" separator=",">
+                    #{billNo}
+                </foreach>
+            </if>
+
+        	<if test="null!=params.targetField  and ''!=params.targetField ">
+				 AND `target_field`=#{params.targetField}
+            </if>	
+        	<if test="null!=params.targetValue ">
+				 AND `target_value`=#{params.targetValue}
+            </if>	
+        	<if test="null!=params.createTime ">
+				 AND `create_time`=#{params.createTime}
+            </if>	
+        	<if test="null!=params.createUser  and ''!=params.createUser ">
+				 AND `create_user`=#{params.createUser}
+            </if>	
+
+            <if test="params.createTimeStart!=null  and ''!=params.createTimeStart ">
+				AND `create_time` &gt;= #{params.createTimeStart}
+			</if>
+        	<if test="params.createTimeEnd!=null  and ''!=params.createTimeEnd ">
+				AND `create_time` &lt;= #{params.createTimeEnd}
+			</if>
+			<if test="params.updateTimeStart!=null  and ''!=params.updateTimeStart ">
+				AND `update_time` &gt;= #{params.updateTimeStart}
+			</if>
+        	<if test="params.updateTimeEnd!=null  and ''!=params.updateTimeEnd ">
+				AND `update_time` &lt;= #{params.updateTimeEnd}
+			</if>
+        </if>
+    </sql>
+
+    <sql id="uniqe_condition">
+        
+    </sql>
+
+    <select id="findByPrimaryKey" resultMap="baseResultMap" >
+        SELECT
+        <include refid="column_list" />
+        FROM sell_out_config_dtl
+        WHERE id = #{id}
+    </select>
+
+    <select id="findByUnique" resultMap="baseResultMap" >
+        SELECT
+        <include refid="column_list" />
+        FROM sell_out_config_dtl
+        <where>
+            <include refid="uniqe_condition" />
+        </where>
+    </select>
+
+    <select id="findByParam" resultMap="baseResultMap" parameterType="map">
+        SELECT
+        <include refid="column_list" />
+        FROM sell_out_config_dtl
+        <where>
+            <include refid="condition" />
+        </where>
+        
+        LIMIT 1
+        
+    </select>
+
+    <select id="selectCount" resultType="java.lang.Integer">
+        SELECT COUNT(1) as s FROM sell_out_config_dtl
+        <where>
+            <include refid="condition" />
+        </where>
+    </select>
+
+    <select id="selectByPage" resultMap="baseResultMap" parameterType="map">
+        
+        SELECT
+        <include refid="column_list" />
+        FROM sell_out_config_dtl
+        <where>
+            <include refid="condition" />
+        </where>
+        <if test="orderby != null and ''!=orderby">
+            ORDER BY ${orderby}
+        </if>
+        LIMIT ${page.startRowNum},${page.pageSize}
+        
+    </select>
+
+    <select id="selectByParams" resultMap="baseResultMap" parameterType="map">
+        SELECT
+        <include refid="column_list" />
+        FROM sell_out_config_dtl
+        <where>
+            <include refid="condition" />
+        </where>
+        <if test="orderby != null and ''!=orderby">
+            ORDER BY ${orderby}
+        </if>
+    </select>
+
+    <delete id="deleteByPrimaryKey">
+        DELETE FROM sell_out_config_dtl
+        WHERE id = #{id}
+    </delete>
+
+    <delete id="deleteByUnique">
+        DELETE FROM sell_out_config_dtl
+        <where>
+            <include refid="uniqe_condition" />
+        </where>
+    </delete>
+
+    <delete id="deleteByParams" parameterType="map">
+        DELETE
+        FROM sell_out_config_dtl
+        <where>
+            <include refid="condition" />
+            <if test="params.ids!=null and ''!=params.ids ">
+                AND id in ( ${params.ids} )
+            </if>
+        </where>
+    </delete>
+
+    <insert id="insert" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl"  >
+        INSERT INTO sell_out_config_dtl
+        <trim prefix="(" suffix=")" suffixOverrides=",">
+            
+            <if test="updateUser != null">
+                `update_user`,
+            </if>
+            <if test="updateTime != null">
+                `update_time`,
+            </if>
+            <if test="id != null">
+                `id`,
+            </if>
+            <if test="billNo != null">
+                `bill_no`,
+            </if>
+            <if test="targetField != null">
+                `target_field`,
+            </if>
+            <if test="targetValue != null">
+                `target_value`,
+            </if>
+            <if test="createTime != null">
+                `create_time`,
+            </if>
+            <if test="createUser != null">
+                `create_user`,
+            </if>
+        </trim>
+        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
+            
+            <if test="updateUser != null">
+                #{updateUser},
+            </if>
+            <if test="updateTime != null">
+                #{updateTime},
+            </if>
+            <if test="id != null">
+                #{id},
+            </if>
+            <if test="billNo != null">
+                #{billNo},
+            </if>
+            <if test="targetField != null">
+                #{targetField},
+            </if>
+            <if test="targetValue != null">
+                #{targetValue},
+            </if>
+            <if test="createTime != null">
+                #{createTime},
+            </if>
+            <if test="createUser != null">
+                #{createUser},
+            </if>
+        </trim>
+    </insert>
+
+    <insert id="batchInsert" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl" useGeneratedKeys="true" keyProperty="id">
+        INSERT INTO sell_out_config_dtl (<include refid="column_list"></include>)
+        values 
+        <foreach collection="list" item="item" separator=",">
+            (#{item.updateUser}, #{item.updateTime}, #{item.id}, #{item.billNo}, #{item.targetField}, #{item.targetValue}, #{item.createTime}, #{item.createUser})
+        </foreach>
+    </insert>
+    <update id="update" parameterType="cn.wonhigh.baize.model.entity.gms.SellOutConfigDtl">
+        UPDATE sell_out_config_dtl
+        <set>
+            
+            <if test="billNo != null">
+                `bill_no` = #{billNo},
+            </if> 
+            <if test="targetField != null">
+                `target_field` = #{targetField},
+            </if> 
+            <if test="targetValue != null">
+                `target_value` = #{targetValue},
+            </if> 
+            update_time =  now() 
+        </set>
+        
+        
+        WHERE id = #{id}
+                
+    </update>
+
+	<select id="selectByIds" resultMap="baseResultMap">
+        SELECT
+        <include refid="column_list"/>
+        FROM sell_out_config_dtl 
+        where id in 
+        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
+          #{item}
+      	</foreach>
+    </select>
+
+    <delete id="deleteByBillNo">
+        DELETE FROM sell_out_config_dtl
+        WHERE bill_no in
+        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
+          #{item}
+      	</foreach>
+    </delete>
+
+    <!-- auto generate end-->
+</mapper>
\ No newline at end of file
Index: 02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigClassifyDto.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigClassifyDto.java b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigClassifyDto.java
--- a/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigClassifyDto.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigClassifyDto.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -31,7 +31,7 @@
 
 
     public String getUk() {
-        return getClassifyCode() + "_" + getClassifyValueCode();
+        return getBrandNo() + "_" + getClassifyCode() + "_" + getClassifyValueCode();
     }
 
     public String getClassifyCode() {
Index: 02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigItemDto.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigItemDto.java b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigItemDto.java
--- a/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigItemDto.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/dto/sellOutConfig/SellOutConfigItemDto.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -25,7 +25,7 @@
 
     @JsonProperty
     public String getUk() {
-        return itemNo + "_" + sizeNo;
+        return getBrandNo() + "_" + itemNo + "_" + sizeNo;
     }
 
     public String getItemCode() {
Index: 02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfigDtl.java
===================================================================
diff --git a/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfigDtl.java b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfigDtl.java
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-model/src/main/java/cn/wonhigh/baize/model/entity/gms/SellOutConfigDtl.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,144 @@
+/**  **/
+package cn.wonhigh.baize.model.entity.gms;
+
+import org.apache.commons.lang.builder.ToStringBuilder;
+import cn.mercury.domain.AbstractEntryBuilder;
+import cn.mercury.basic.query.Query;
+import cn.mercury.annotation.Label;
+import java.util.Date;
+import java.math.BigDecimal;
+/** 
+*auto generate start ,don't modify
+* 售罄率明细表
+**/
+public class SellOutConfigDtl  extends cn.mercury.domain.BasicEntity  {
+
+    private static final long serialVersionUID = 1754883307407L;
+    
+    //主表单据编号
+    @Label("主表单据编号") 
+    private String billNo;
+    
+    //目标字段 (1,2,3...91)
+    @Label("目标字段") 
+    private String targetField;
+    
+    //目标值
+    @Label("目标值") 
+    private BigDecimal targetValue;
+    
+    
+    public String getBillNo(){
+        return  billNo;
+    }
+    public void setBillNo(String val ){
+        billNo = val;
+    }
+    
+    public String getTargetField(){
+        return  targetField;
+    }
+    public void setTargetField(String val ){
+        targetField = val;
+    }
+    
+    public BigDecimal getTargetValue(){
+        return  targetValue;
+    }
+    public void setTargetValue(BigDecimal val ){
+        targetValue = val;
+    }
+    
+    @Override
+	public String toString() {
+         return ToStringBuilder.reflectionToString(this);
+	}
+	    
+    public SellOutConfigDtlBuilder build(){
+        return new SellOutConfigDtlBuilder(this);
+    }
+
+    public static class SellOutConfigDtlBuilder extends AbstractEntryBuilder<SellOutConfigDtl>{
+
+        private SellOutConfigDtlBuilder(SellOutConfigDtl entry){
+            this.obj = entry;
+        }
+
+       @Override
+		public SellOutConfigDtl object() {
+			return this.obj;
+		}
+
+        
+        public SellOutConfigDtlBuilder updateUser(String value ){
+            this.obj.setUpdateUser(value);
+            
+            if( query == null  )
+                query = new Query();
+            this.query.where("updateUser", value);
+            return this;
+        }
+        
+        public SellOutConfigDtlBuilder updateTime(Date value ){
+            this.obj.setUpdateTime(value);
+            
+            if( query == null  )
+                query = new Query();
+            this.query.where("updateTime", value);
+            return this;
+        }
+        
+        public SellOutConfigDtlBuilder id(String value ){
+            this.obj.setId(value);
+            
+            if( query == null  )
+                query = new Query();
+            this.query.where("id", value);
+            return this;
+        }
+        
+        public SellOutConfigDtlBuilder billNo(String value ){
+            this.obj.billNo = value;
+            if( query == null  )
+                query = new Query();
+            this.query.where("billNo", value);
+            return this;
+        }
+        
+        public SellOutConfigDtlBuilder targetField(String value ){
+            this.obj.targetField = value;
+            if( query == null  )
+                query = new Query();
+            this.query.where("targetField", value);
+            return this;
+        }
+        
+        public SellOutConfigDtlBuilder targetValue(BigDecimal value ){
+            this.obj.targetValue = value;
+            if( query == null  )
+                query = new Query();
+            this.query.where("targetValue", value);
+            return this;
+        }
+        
+        public SellOutConfigDtlBuilder createTime(Date value ){
+            this.obj.setCreateTime(value);
+            
+            if( query == null  )
+                query = new Query();
+            this.query.where("createTime", value);
+            return this;
+        }
+        
+        public SellOutConfigDtlBuilder createUser(String value ){
+            this.obj.setCreateUser(value);
+            
+            if( query == null  )
+                query = new Query();
+            this.query.where("createUser", value);
+            return this;
+        }
+        
+    }
+/** auto generate end,don't modify */
+}
\ No newline at end of file
Index: 02.Development/ts-retail-baize-web/src/main/java/cn/wonhigh/baize/web/controller/SellOutConfigController.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/02.Development/ts-retail-baize-web/src/main/java/cn/wonhigh/baize/web/controller/SellOutConfigController.java b/02.Development/ts-retail-baize-web/src/main/java/cn/wonhigh/baize/web/controller/SellOutConfigController.java
--- a/02.Development/ts-retail-baize-web/src/main/java/cn/wonhigh/baize/web/controller/SellOutConfigController.java	(revision 6ec137aeba86a6f9edf5a9b26bec1ecf13146f69)
+++ b/02.Development/ts-retail-baize-web/src/main/java/cn/wonhigh/baize/web/controller/SellOutConfigController.java	(revision 7c2bd97a1cc0a408474727be9b31d29b076806ac)
@@ -1,25 +1,35 @@
 /** by  **/
 package cn.wonhigh.baize.web.controller;
 
+import cn.hutool.core.util.IdUtil;
 import cn.hutool.core.util.StrUtil;
 import cn.mercury.annotation.JsonVariable;
 import cn.mercury.basic.query.PageResult;
 import cn.mercury.basic.query.Pagenation;
 import cn.mercury.basic.query.Query;
 import cn.mercury.excel.ExcelColumn;
+import cn.mercury.manager.IEntryResultHandler;
 import cn.mercury.manager.IManager;
+import cn.mercury.security.IUser;
+import cn.mercury.utils.DateUtil;
 import cn.wonhigh.baize.manager.gms.IBrandManager;
 import cn.wonhigh.baize.manager.gms.IItemManager;
+import cn.wonhigh.baize.manager.gms.ISellOutConfigDtlManager;
 import cn.wonhigh.baize.manager.gms.ISellOutConfigManager;
+import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigClassifyDto;
 import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
-import cn.wonhigh.baize.model.entity.gms.Brand;
-import cn.wonhigh.baize.model.entity.gms.ItemAttr;
-import cn.wonhigh.baize.model.entity.gms.ItemBaseInfo;
-import cn.wonhigh.baize.model.entity.gms.SellOutConfig;
+import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigItemDto;
+import cn.wonhigh.baize.model.entity.gms.*;
 import cn.wonhigh.retail.uc.common.api.model.AuthorityUserBrand;
 import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
 import com.alibaba.dubbo.config.annotation.Reference;
 import com.alibaba.excel.EasyExcel;
+import com.alibaba.excel.ExcelWriter;
+import com.alibaba.excel.support.ExcelTypeEnum;
+import com.alibaba.excel.write.builder.ExcelWriterBuilder;
+import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
+import com.alibaba.excel.write.metadata.WriteSheet;
+import com.google.common.collect.Lists;
 import com.yougou.logistics.base.common.exception.RpcException;
 import org.apache.commons.collections4.MapUtils;
 import org.apache.commons.lang.StringUtils;
@@ -27,10 +37,17 @@
 import org.slf4j.LoggerFactory;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Controller;
+import org.springframework.util.LinkedMultiValueMap;
+import org.springframework.util.MultiValueMap;
 import org.springframework.web.bind.annotation.RequestMapping;
 import org.springframework.web.bind.annotation.ResponseBody;
 import org.springframework.web.multipart.MultipartFile;
+import org.springframework.web.multipart.MultipartHttpServletRequest;
 import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
+import reactor.util.function.Tuple2;
+import reactor.util.function.Tuple3;
+import reactor.util.function.Tuples;
+import topmall.framework.io.excel.EntryResultHandler;
 import topmall.framework.security.Authorization;
 import topmall.framework.web.controller.BaseController;
 import topmall.framework.web.vo.ApiResult;
@@ -38,10 +55,15 @@
 import javax.servlet.http.HttpServletRequest;
 import javax.servlet.http.HttpServletResponse;
 import java.io.IOException;
+import java.io.OutputStream;
 import java.math.BigDecimal;
+import java.net.URLEncoder;
 import java.util.*;
+import java.util.function.Function;
 import java.util.stream.Collectors;
 
+import static cn.mercury.utils.DateUtil.LONG_DATE_FORMAT;
+
 
 @Controller
 @RequestMapping("/sell/out/config")
@@ -49,6 +71,9 @@
     @Autowired
     private ISellOutConfigManager manager;
 
+    @Autowired
+    private ISellOutConfigDtlManager sellOutConfigDtlManager;
+
     @Autowired
     private IBrandManager brandManager;
 
@@ -65,7 +90,7 @@
     }
 
     private int taget_idx = 91;
-    
+
     @Override
 	protected String getTemplateFolder() {
 		 return "/baize/SellOutConfig";
@@ -153,34 +178,76 @@
             String selectType = query.asMap().get("selectType").toString();
             boolean isSelectItem = selectType.equals("item");
             query = isSelectItem? query.and("type","1") : query.and("type","0");
-            int c = manager.selectSellOutCount(query);
+            int c = manager.selectCount(query);
             if(c == 0){
                 return ApiResult.ok(new PageResult(new ArrayList<>(),c));
             }
-            List<SellOutConfigDto> outList = manager.selectSellOutList(query,page);
+            List<SellOutConfig> outList = manager.selectByPage(query,page);
+
+            if (outList!=null) {
+                List<SellOutConfigDto> configDtos = new ArrayList<>(outList.size());
+                List<String> billNos = outList.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList());
+
+                List<SellOutConfigDtl>  dtlList = sellOutConfigDtlManager.selectByParams(new Query().and("billNos",billNos));
+
+                for (SellOutConfig sellOutConfig : outList) {
 
-            Optional.ofNullable(outList).ifPresent(list -> {
-                list.forEach(d -> {
-                    d.setStatusName(Objects.equals(d.getStatus(), 0)? "启用" : "禁用");
-                    Arrays.stream(d.getGroupValue().split(","))
-                            .forEach(item -> {
-                                String[] parts = StrUtil.splitToArray(item, ":");
-                                if (parts.length == 2) {
-                                    d.getExt().put("F"+parts[0], parts[1]+"%");
-                                }
-                            })
-                    ;
-                });
-            });
+                    SellOutConfigDto sellOutConfigDto = getSellOutConfigDto(sellOutConfig, isSelectItem, selectType, dtlList);
+                    configDtos.add(sellOutConfigDto);
+                }
+                return ApiResult.ok(new PageResult(configDtos,c));
+            }
 
-            return ApiResult.ok(new PageResult(outList,c));
+            return ApiResult.ok(new PageResult(new ArrayList<>(),0));
         } catch (Exception e) {
             logger.error("查询列名集合异常", e);
             return ApiResult.error("查询列名集合异常");
         }
     }
-    
-	@ResponseBody
+
+    private SellOutConfigDto getSellOutConfigDto(SellOutConfig sellOutConfig, boolean isSelectItem, String selectType, List<SellOutConfigDtl> dtlList) {
+        SellOutConfigDto sellOutConfigDto = null;
+
+        if (isSelectItem) {
+            sellOutConfigDto = new SellOutConfigItemDto();
+        } else {
+            sellOutConfigDto = new SellOutConfigClassifyDto();
+        }
+
+        sellOutConfigDto.setBrandNo(sellOutConfig.getBrandNo());
+        sellOutConfigDto.setBrandName(sellOutConfig.getBrandName());
+
+        if (sellOutConfigDto instanceof SellOutConfigItemDto) {
+            ((SellOutConfigItemDto) sellOutConfigDto).setItemCode(sellOutConfig.getItemCode());
+            ((SellOutConfigItemDto) sellOutConfigDto).setItemNo(sellOutConfig.getItemNo());
+            ((SellOutConfigItemDto) sellOutConfigDto).setSizeNo(sellOutConfig.getSizeNo());
+        } else {
+            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyCode(sellOutConfig.getClassifyCode());
+            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyName(sellOutConfig.getClassifyName());
+            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyValueCode(sellOutConfig.getClassifyValueCode());
+            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyValueName(sellOutConfig.getClassifyValueName());
+
+        }
+
+        sellOutConfigDto.setUpdateTime(DateUtil.format(sellOutConfig.getUpdateTime(), LONG_DATE_FORMAT));
+        sellOutConfigDto.setUpdateUser(sellOutConfig.getUpdateUser());
+        sellOutConfigDto.setStatus(sellOutConfig.getStatus());
+        sellOutConfigDto.setStatusName(sellOutConfig.getStatus() == 0 ? "启用" : "禁用");
+        sellOutConfigDto.setSelectType(selectType);
+
+        List<SellOutConfigDtl> sortAfter = dtlList.stream()
+                .filter(dtl -> Objects.equals(dtl.getBillNo(), sellOutConfig.getBillNo()))
+                .collect(Collectors.toList());
+
+        Map<String, Object> ext = new TreeMap<>(Comparator.comparingInt(a -> Integer.parseInt(a.substring(1))));
+        sortAfter.forEach(dtl -> {
+            ext.put("F" + dtl.getTargetField(), dtl.getTargetValue().toPlainString() + "%");
+        });
+        sellOutConfigDto.setExt(ext);
+        return sellOutConfigDto;
+    }
+
+    @ResponseBody
     @RequestMapping({"/del_sell_config"})
     public ApiResult<Integer> deleteSellOut(Query query, Pagenation page) {
     	String uk = (String) query.asMap().get("uk");
@@ -188,7 +255,7 @@
             return ApiResult.error("参数异常!selectType为空");
         }
     	if(StringUtils.isBlank(uk)) {
-    		return ApiResult.ok(0); 
+    		return ApiResult.ok(0);
     	}
         try {
             int c = 0;
@@ -196,29 +263,29 @@
             String selectType = query.asMap().get("selectType").toString();
             boolean isSelectItem = "item".equals(selectType);
             for (String str : uks) {
-				if(isSelectItem) {
-					 String itemNo = str.split("_")[0];
-					 String sizeNo = str.split("_")[1];
-					 if(StringUtils.isNotBlank(itemNo) && StringUtils.isNotBlank(sizeNo)) {
-						 manager.deleteByParams(new Query().and("itemNo",itemNo).and("sizeNo",sizeNo));
-						 c++;
-					 }
-				}else {
-					 String classifyCode = str.split("_")[0];
-					 String classifyValueCode = str.split("_")[1];
-					 if(StringUtils.isNotBlank(classifyCode) && StringUtils.isNotBlank(classifyValueCode)) {
-						 manager.deleteByParams(new Query().and("classifyCode",classifyCode).and("classifyValueCode",classifyValueCode));
-						 c++;
-					 }
-				}
-			}
+                if (isSelectItem) {
+                    String brandNo = str.split("_")[0];
+                    String itemNo = str.split("_")[1];
+                    String sizeNo = str.split("_")[2];
+
+                    Tuple3<String, String, String> tuple3 = Tuples.of(brandNo, itemNo, sizeNo);
+
+                    c+=manager.deleteByUnique(1,tuple3);
+                } else {
+                    String brandNo = str.split("_")[0];
+                    String classifyCode = str.split("_")[1];
+                    String classifyValueCode = str.split("_")[2];
+                    Tuple3<String, String, String> tuple3 = Tuples.of(brandNo, classifyCode, classifyValueCode);
+                    c+=manager.deleteByUnique(0,tuple3);
+                }
+            }
             return ApiResult.ok(c);
         } catch (Exception e) {
             logger.error("删除异常", e);
             return ApiResult.error("删除异常");
         }
     }
-	
+
 	@ResponseBody
     @RequestMapping({"/update_sell_config"})
     public ApiResult<Integer> updateSellOut(Query query, Pagenation page) {
@@ -227,7 +294,7 @@
             return ApiResult.error("参数异常!selectType为空");
         }
     	if(StringUtils.isBlank(uk)) {
-    		return ApiResult.ok(0); 
+    		return ApiResult.ok(0);
     	}
         try {
             int c = 0;
@@ -236,21 +303,27 @@
             String selectType = query.asMap().get("selectType").toString();
             boolean isSelectItem = "item".equals(selectType);
             for (String str : uks) {
-				if(isSelectItem) {
-					 String itemNo = str.split("_")[0];
-					 String sizeNo = str.split("_")[1];
-					 if(StringUtils.isNotBlank(itemNo) && StringUtils.isNotBlank(sizeNo)) {
-						 manager.updateStatusByParams(new Query().and("itemNo",itemNo).and("sizeNo",sizeNo).and("status",status));
-						 c++;
-					 }
-				}else {
-					 String classifyCode = str.split("_")[0];
-					 String classifyValueCode = str.split("_")[1];
-					 if(StringUtils.isNotBlank(classifyCode) && StringUtils.isNotBlank(classifyValueCode)) {
-						 manager.updateStatusByParams(new Query().and("classifyCode",classifyCode).and("classifyValueCode",classifyValueCode).and("status",status));
-						 c++;
-					 }
-				}
+                if (isSelectItem) {
+                    String brandNo = str.split("_")[0];
+                    String itemNo = str.split("_")[1];
+                    String sizeNo = str.split("_")[2];
+                    if (StringUtils.isNotBlank(itemNo) && StringUtils.isNotBlank(sizeNo)) {
+                        manager.updateStatusByParams(new Query().and("itemNo", itemNo).and("sizeNo", sizeNo)
+                                        .and("brandNo", brandNo)
+                                .and("status", status));
+                        c++;
+                    }
+                } else {
+                    String brandNo = str.split("_")[0];
+                    String classifyCode = str.split("_")[1];
+                    String classifyValueCode = str.split("_")[2];
+                    if (StringUtils.isNotBlank(classifyCode) && StringUtils.isNotBlank(classifyValueCode)) {
+                        manager.updateStatusByParams(new Query().and("classifyCode", classifyCode).and("classifyValueCode", classifyValueCode)
+                                        .and("brandNo", brandNo)
+                                .and("status", status));
+                        c++;
+                    }
+                }
 			}
             return ApiResult.ok(c);
         } catch (Exception e) {
@@ -258,8 +331,8 @@
             return ApiResult.error("更新异常");
         }
     }
-	
-    private List<Object> readExceData(MultipartFile multipartFile) {
+
+    private List<Object> readExcelData(MultipartFile multipartFile) {
         List<Object> objectList = Collections.emptyList();
         try {
             objectList = EasyExcel.read(multipartFile.getInputStream())
@@ -273,7 +346,7 @@
     }
 
 
-    private boolean checkAndSetBrand(String brandNo,List<SellOutConfig> sellList, List<String> authorityBrandNoList){
+    private boolean checkAndSetBrand(String brandNo, SellOutConfig sellOutConfig, List<String> authorityBrandNoList){
         if (!authorityBrandNoList.contains(brandNo)) {
             return false;
         }
@@ -281,14 +354,12 @@
         if(null == b){
             return false;
         }
-        sellList.stream().forEach(d -> {
-            d.setBrandNo(brandNo);
-            d.setBrandName(b.getName());
-        });
+        sellOutConfig.setBrandNo(brandNo);
+        sellOutConfig.setBrandName(b.getName());
         return true;
     }
 
-    private boolean checkAndSetItem(String importType ,String brandNo,String itemCode,String sizeNo ,List<SellOutConfig> sellList){
+    private boolean checkAndSetItem(String importType ,String brandNo,String itemCode,String sizeNo ,SellOutConfig config){
         if(!"item".equals(importType)){
             return true;
         }
@@ -296,15 +367,14 @@
         if(null == i || i.getStatus() != 1){
             return false;
         }
-        sellList.stream().forEach(d -> {
-            d.setItemCode(itemCode);
-            d.setSizeNo(sizeNo);
-            d.setItemNo(i.getItemNo());
-        });
+        config.setItemCode(itemCode);
+        config.setSizeNo(sizeNo);
+        config.setItemNo(i.getItemNo());
+
         return true;
     }
 
-    private boolean checkAndSetShareClassify(String importType ,String brandNo,String classifyValue ,List<SellOutConfig> sellList){
+    private boolean checkAndSetShareClassify(String importType ,String brandNo,String classifyValue ,SellOutConfig config){
         if("item".equals(importType)){
             return true;
         }
@@ -315,53 +385,62 @@
         if(attrList.isEmpty()){
             return false;
         }
-        sellList.stream().forEach(d -> {
-            d.setClassifyCode(attrList.get(0).getAttrNo());
-            d.setClassifyName("共享标识");
-            d.setClassifyValueCode(attrList.get(0).getAttrDtlNo());
-            d.setClassifyValueName(attrList.get(0).getAttrDtlName());
-        });
+        config.setClassifyCode(attrList.get(0).getAttrNo());
+        config.setClassifyName("共享标识");
+        config.setClassifyValueCode(attrList.get(0).getAttrDtlNo());
+        config.setClassifyValueName(attrList.get(0).getAttrDtlName());
         return true;
     }
 
     @SuppressWarnings({ "unchecked", "rawtypes" })
-	private String initNewConfigList(List<SellOutConfig> newList ,Map map,String importType){
+	private Tuple2<String , SellOutConfig> initNewConfigList(Map map, String importType){
         boolean isImportItem = "item".equals(importType);
         int startIdx = isImportItem ? 3 : 2;
-        String createUser = Authorization.getUser().getName();
+        String createUser = Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown");
         Date createTime = new Date();
         String errMsg = "";
+
+        SellOutConfig newObj = new SellOutConfig();
+
+        List<SellOutConfigDtl> newDtlList = new ArrayList<>();
+
         for (int i = 1; i <= taget_idx ; i++){
-            SellOutConfig newObj = new SellOutConfig();
-            newObj.setTargetField(String.valueOf(i));
+            SellOutConfigDtl newObjDtl = new SellOutConfigDtl();
+            newObjDtl.setTargetField(String.valueOf(i));
             Double targetVal = MapUtils.getDouble(map,startIdx);
             if(null == targetVal){
-                errMsg = "售罄目标"+String.valueOf(i)+"值格式错误!";
+                errMsg = "售罄目标"+ i +"值格式错误!";
                 break;
             }
-            if(targetVal.doubleValue() < 0 || targetVal.doubleValue() >100){
-                errMsg = "售罄目标"+String.valueOf(i)+"值范围错误,正确数值范围0<=x<=100";
+            if(targetVal < 0 || targetVal >100){
+                errMsg = "售罄目标"+ i +"值范围错误,正确数值范围0<=x<=100";
                 break;
             }
-            newObj.setTargetValue(new BigDecimal(targetVal).setScale(1,BigDecimal.ROUND_HALF_UP));
-            newObj.setCreateUser(createUser);
-            newObj.setCreateTime(createTime);
-            newObj.setUpdateUser(createUser);
-            newObj.setUpdateTime(createTime);
-            newObj.setType(isImportItem ? 1 : 0);
-            newList.add(newObj);
-            startIdx++;
-        }
-        return errMsg;
+            newObjDtl.setTargetValue(new BigDecimal(targetVal).setScale(1,BigDecimal.ROUND_HALF_UP));
+            newDtlList.add(newObjDtl);
+            startIdx++;
+        }
+
+
+        newObj.setCreateUser(createUser);
+        newObj.setCreateTime(createTime);
+        newObj.setUpdateUser(createUser);
+        newObj.setUpdateTime(createTime);
+        newObj.setType(isImportItem ? 1 : 0);
+        newObj.setTargetValue(BigDecimal.ZERO);
+        newObj.setTargetField(BigDecimal.ZERO.toString());
+        newObj.setSellOutConfigDtlList(newDtlList);
+
+        return Tuples.of(errMsg, newObj);
     }
 
     @SuppressWarnings({ "rawtypes", "unchecked" })
     @ResponseBody
 	@RequestMapping("/import")
     public ApiResult<?> importData(HttpServletRequest request) {
-        StandardMultipartHttpServletRequest multipartHttpServletRequest = null;
-        if (request instanceof StandardMultipartHttpServletRequest) {
-            multipartHttpServletRequest = (StandardMultipartHttpServletRequest) request;
+        MultipartHttpServletRequest multipartHttpServletRequest = null;
+        if (request instanceof MultipartHttpServletRequest) {
+            multipartHttpServletRequest = (MultipartHttpServletRequest) request;
         }
         if (multipartHttpServletRequest == null) {
             return ApiResult.error("File is not found");
@@ -371,7 +450,7 @@
             return ApiResult.error("File is not found");
         }
         // 读取数据
-        List<Object> objectList = readExceData(multipartFile);
+        List<Object> objectList = readExcelData(multipartFile);
         if (objectList == null || objectList.isEmpty()) {
             return ApiResult.error("Excel数据为空");
         }
@@ -390,45 +469,120 @@
             return ApiResult.error("获取用户品牌权限失败");
         }
         int rowNum = 1;
+
+        List<SellOutConfig> configList = new ArrayList<>();
+
         // 校验数据
         for (int i = 0; i < objectList.size(); i++) {
             LinkedHashMap map = (LinkedHashMap) objectList.get(i);
             String brandNo = MapUtils.getString(map, 0);
-            List<SellOutConfig> configList = new ArrayList<>();
-            String errMsg  = this.initNewConfigList(configList,map,importType);
+
+            //T1 错误信息, T2 新数据
+            Tuple2<String, SellOutConfig> returnData = this.initNewConfigList(map,importType);
+            String errMsg = returnData.getT1();
+            SellOutConfig sellOutConfig = returnData.getT2();
             if(StringUtils.isNotBlank(errMsg)){
                 errorMsg.append(",").append("第" + rowNum + "行数据不正确, "+errMsg);
                 continue ;
             }
-            boolean checkBrand = this.checkAndSetBrand(brandNo,configList, authorityBrandNoList);
+            boolean checkBrand = this.checkAndSetBrand(brandNo,sellOutConfig, authorityBrandNoList);
             if(!checkBrand){
                 errorMsg.append(",").append("第" + rowNum + "行数据不正确,品牌"+brandNo+";校验不通过");
                 continue ;
             }
             String itemCode = MapUtils.getString(map, 1);
             String sizeNo = MapUtils.getString(map, 2);
-            boolean checkItem = this.checkAndSetItem(importType,brandNo,itemCode,sizeNo,configList);
+            boolean checkItem = this.checkAndSetItem(importType,brandNo,itemCode,sizeNo,sellOutConfig);
             if(!checkItem){
                 errorMsg.append(",").append("第" + rowNum + "行数据不正确,商品"+itemCode+";尺码"+sizeNo+";校验不通过");
                 continue ;
             }
             String shareClassify = MapUtils.getString(map, 1);
-            boolean checkShareClassify = this.checkAndSetShareClassify(importType,brandNo,shareClassify,configList);
+            boolean checkShareClassify = this.checkAndSetShareClassify(importType,brandNo,shareClassify,sellOutConfig);
             if(!checkShareClassify){
                 errorMsg.append(",").append("第" + rowNum + "行数据不正确,共享标识"+shareClassify+";校验不通过");
                 continue ;
             }
-            if("item".equals(importType)){
-                manager.deleteByParams(new Query().and("itemNo",configList.get(0).getItemNo()).and("sizeNo",sizeNo));
-            }else{
-                manager.deleteByParams(new Query().and("classifyCode",configList.get(0).getClassifyCode()).and("classifyValueCode",configList.get(0).getClassifyValueCode()));
-            }
+
             rowNum++;
-            this.manager.batchInsert(configList);
+            configList.add(sellOutConfig);
         }
         if(errorMsg.length() > 0) {
             return ApiResult.error(errorMsg.toString());
         }
+
+        if(configList.isEmpty()){
+            return ApiResult.error("导入数据为空");
+        }
+
+        // 导入的数据, 分别按导入的类型进行存放
+        MultiValueMap<Integer, SellOutConfig> multiValueMap = new LinkedMultiValueMap<>();
+        configList.forEach(c -> multiValueMap.add(c.getType(), c));
+
+        multiValueMap.forEach((k,vList) -> {
+
+            // 把每个类型的数据, 按照唯一键进行分组, 如果有重复的数据,则取第一条数据
+            Map<Tuple3<String,String, String>, SellOutConfig> groupMap = vList.stream().collect(Collectors.toMap(c ->
+                    {
+                        // k == 1 , 说明是按商品导入, 否则是按分类导入
+                        if(k == 1){
+                            return Tuples.of( c.getBrandNo(), c.getItemNo(),c.getSizeNo());
+                        }else{
+                            return Tuples.of(c.getBrandNo(),c.getClassifyCode(),c.getClassifyValueCode());
+                        }
+                    }, Function.identity(), (s1, s2) -> s1));
+
+
+            Set<Tuple3<String,String,String>> keySet = groupMap.keySet();
+
+            List<SellOutConfig> findDataAll = Lists.partition(new ArrayList<>(keySet), 200)
+                    .parallelStream().map(list -> this.manager.selectByUniqueList(k, list))
+                    .flatMap(Collection::stream).collect(Collectors.toList());
+
+
+            // T1 新增, T2 更新
+            Tuple2<List<SellOutConfig>, List<SellOutConfig>> insertOrUpdate = Tuples.of(new ArrayList<>(), new ArrayList<>());
+
+            groupMap.values().forEach(c -> {
+                SellOutConfig exited = findDataAll.stream().filter(
+                        item -> {
+                            if (k == 1) {
+                                return c.getBrandNo().equals(item.getBrandNo()) &&
+                                        c.getItemNo().equals(item.getItemNo()) && c.getSizeNo().equals(item.getSizeNo());
+                            } else {
+                                return c.getBrandNo().equals(item.getBrandNo()) &&
+                                        c.getClassifyCode().equals(item.getClassifyCode()) && c.getClassifyValueCode().equals(item.getClassifyValueCode());
+                            }
+                        }
+                ).findFirst().orElse(null);
+
+                // 等于null 说明, 在数据库中不存在, 则需要新增
+                if(null != exited){
+                    c.setId(exited.getId());
+                    c.setBillNo(exited.getBillNo());
+                    Optional.ofNullable(c.getSellOutConfigDtlList()).ifPresent(list -> {
+                        list.forEach(dtl -> {
+                            dtl.setBillNo(exited.getBillNo());
+                        });
+                    });
+                    // 放入新增的数组
+                    insertOrUpdate.getT2().add(c);
+                } else {
+                    c.setBillNo(IdUtil.getSnowflakeNextIdStr());
+                    Optional.ofNullable(c.getSellOutConfigDtlList()).ifPresent(list -> {
+                        list.forEach(dtl -> {
+                            dtl.setBillNo(c.getBillNo());
+                        });
+                    });
+                    // 放入更新的数组
+                    insertOrUpdate.getT1().add(c);
+                }
+            });
+
+            this.manager.batchSave(insertOrUpdate.getT1(), insertOrUpdate.getT2(), null);
+
+        });
+
         return ApiResult.ok("导入成功");
     }
 
@@ -438,38 +592,60 @@
 
         try {
             boolean isExportItem = "item".equals(query.asMap().get("selectType"));
-            int pageIndex = 1;
-            int pageSize = 10000;
-            Map<String,Map> resultMap = new HashMap<>();
-            query.and("type",isExportItem ? 1 : 0).and("orderby","id desc");
-            while(true){
-                List<SellOutConfig> list = this.manager.selectByPage(query,new Pagenation(pageIndex++,pageSize));
-                list.stream().forEach(d -> {
-                    String key = isExportItem ? d.getItemNo()+"_"+d.getSizeNo() : d.getClassifyCode()+"_"+d.getClassifyValueCode();
-                    Map dataMap = resultMap.get(key);
-                    if(null == dataMap){
-                        dataMap = new HashMap();
-                        dataMap.put("brandNo",d.getBrandNo());
-                        dataMap.put("brandName",d.getBrandName());
-                        dataMap.put("createUser",d.getCreateUser());
-                        dataMap.put("createTime",d.getCreateTime());
-                        dataMap.put("updateUser",d.getUpdateUser());
-                        dataMap.put("updateTime",d.getUpdateTime());
-                        dataMap.put("statusName",d.getStatusName());
-                        dataMap.put("itemCode",d.getItemCode());
-                        dataMap.put("sizeNo",d.getSizeNo());
-                        dataMap.put("classifyName",d.getClassifyName());
-                        dataMap.put("classifyValueName",d.getClassifyValueName());
-                        resultMap.put(key,dataMap);
-                    }
-                    dataMap.put("F"+d.getTargetField(),d.getTargetValue().toPlainString()+"%");
-                });
-                if(list.size() < pageSize){
-                    break ;
-                }
-            }
-            String fileName =  isExportItem ? "售罄目标配置导出-按商品.xlsx"  : "售罄目标配置导出-按分类.xlsx";
-            EasyExcel.write(response.getOutputStream()).withTemplate(getClass().getResourceAsStream("/template/"+fileName)).sheet().doFill(resultMap.values());
+            query.and("type",isExportItem ? 1 : 0);
+
+            List<SellOutConfig> allData = new ArrayList<>();
+            this.manager.selectByParamsForHandler(query, config -> {
+                allData.add(config.getResultObject());
+            });
+
+            List<Map<String,Object>> allDataMap = new ArrayList<>();
+            Lists.partition(allData, 50)
+                    .parallelStream()
+                    .peek(
+                            list -> {
+                                List<String> billNos = list.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList());
+                                List<SellOutConfigDtl>  dtlList = sellOutConfigDtlManager.selectByParams(new Query().and("billNos",billNos));
+
+                                for (SellOutConfig sellOutConfig : list) {
+                                    sellOutConfig.setSellOutConfigDtlList(dtlList.stream()
+                                            .filter(dtl -> dtl.getBillNo().equals(sellOutConfig.getBillNo()))
+                                            .collect(Collectors.toList()));
+                                }
+                            }
+                    ).forEach(list -> {
+                        for (SellOutConfig d : list) {
+                            Map<String, Object> dataMap = new HashMap<>();
+                            dataMap.put("brandNo",d.getBrandNo());
+                            dataMap.put("brandName",d.getBrandName());
+                            dataMap.put("createUser",d.getCreateUser());
+                            dataMap.put("createTime",d.getCreateTime());
+                            dataMap.put("updateUser",d.getUpdateUser());
+                            dataMap.put("updateTime",d.getUpdateTime());
+                            dataMap.put("statusName",d.getStatusName());
+                            dataMap.put("itemCode",d.getItemCode());
+                            dataMap.put("sizeNo",d.getSizeNo());
+                            dataMap.put("classifyName",d.getClassifyName());
+                            dataMap.put("classifyValueName",d.getClassifyValueName());
+
+                            d.getSellOutConfigDtlList().stream()
+                                    .sorted(Comparator.comparing(SellOutConfigDtl::getTargetField))
+                                    .forEach(dtl -> {
+                                dataMap.put("F" + dtl.getTargetField(), dtl.getTargetValue().toPlainString() + "%");
+                            });
+                            allDataMap.add(dataMap);
+                        }
+                    })
+            ;
+            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
+            String fileName =  isExportItem ? "售罄目标配置导出-按商品"  : "售罄目标配置导出-按分类";
+            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
+            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
+            response.setCharacterEncoding("utf-8");
+            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");
+
+            OutputStream outputStream = response.getOutputStream();
+            EasyExcel.write(outputStream).withTemplate(getClass().getResourceAsStream("/template/"+fileName + ".xlsx")).sheet().doFill(allDataMap);
         } catch (Exception e) {
             logger.error(e.getMessage(), e);
         }
Index: 02.Development/ts-retail-baize-web/src/test/java/cn/wonhigh/baize/controller/SellOutConfigControllerTest.java
===================================================================
diff --git a/02.Development/ts-retail-baize-web/src/test/java/cn/wonhigh/baize/controller/SellOutConfigControllerTest.java b/02.Development/ts-retail-baize-web/src/test/java/cn/wonhigh/baize/controller/SellOutConfigControllerTest.java
new file mode 100644
--- /dev/null	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
+++ b/02.Development/ts-retail-baize-web/src/test/java/cn/wonhigh/baize/controller/SellOutConfigControllerTest.java	(revision e51e9b39936e96817caacb0f98a1c5447ea2e7ee)
@@ -0,0 +1,55 @@
+package cn.wonhigh.baize.controller;
+
+import cn.wonhigh.baize.BaseTest;
+import org.apache.commons.io.FileUtils;
+import org.apache.commons.io.IOUtils;
+import org.junit.jupiter.api.Test;
+import org.springframework.mock.web.MockMultipartFile;
+import org.springframework.test.web.servlet.MockMvc;
+
+import javax.annotation.Resource;
+import java.io.File;
+import java.io.FileInputStream;
+import java.io.InputStream;
+import java.io.OutputStream;
+
+import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
+import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
+import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
+import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
+
+public class SellOutConfigControllerTest extends BaseTest {
+
+    @Resource
+    private MockMvc mockMvc;
+
+
+    @Test
+    public void testExport () throws Exception{
+        mockMvc.perform(
+                        get("/sell/out/config/exportExcel?selectType=item")
+                )
+                .andDo(s -> {
+                    OutputStream outputStream = s.getResponse().getOutputStream();
+
+                })
+                .andExpect(status().isOk());
+    }
+
+
+    @Test
+    public void testImport() throws Exception {
+
+        InputStream inputStream = FileUtils.openInputStream(new java.io.File("D:\\Users\\Jason\\Downloads\\售罄目标配置导入-按商品.xlsx"));
+        MockMultipartFile file = new MockMultipartFile("excelFile",inputStream);
+
+        mockMvc.perform(
+                        multipart("/sell/out/config/import?selectType=item")
+                                .file(file)
+                )
+                .andDo(print())
+                .andExpect(status().isOk());
+
+        inputStream.close();
+    }
+}
