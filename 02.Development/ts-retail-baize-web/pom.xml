<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.wonhigh.baize</groupId>
        <artifactId>ts-retail-baize</artifactId>
        <version>1.0.28-SNAPSHOT</version>
    </parent>
    <artifactId>ts-retail-baize-web</artifactId>
    <name>ts-retail-baize-web</name>
    <url>http://maven.apache.org</url>
    <packaging>jar</packaging>

    <properties>
        <maven.build.timestamp.format>yyyyMMddHHmm</maven.build.timestamp.format>
        <timestamp>${maven.build.timestamp}</timestamp>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.wonhigh.baize</groupId>
            <artifactId>ts-retail-baize-domain</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-security-retail</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.wonhigh.topmall</groupId>
            <artifactId>topmall-framework-io</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>config/**</exclude>
                        <exclude>*.properties</exclude>
                        <exclude>static/**</exclude>
                        <exclude>templates/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-zip</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptors>
                                <descriptor>src/main/resources/assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>