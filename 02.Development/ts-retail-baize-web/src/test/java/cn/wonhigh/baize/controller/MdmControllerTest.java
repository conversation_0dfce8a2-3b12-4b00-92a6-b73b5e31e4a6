package cn.wonhigh.baize.controller;

import cn.wonhigh.baize.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class MdmControllerTest extends BaseTest {

    @Resource
    private MockMvc mockMvc;


    @Test
    void testMvc() throws Exception {
        for (int i = 1; i <= 10; i++) {
            mockMvc.perform(get("/mdm/store/list?page=" + i + "&pageSize=10"))
                    .andDo(print())
                    .andExpect(status().isOk());
        }
    }
}
