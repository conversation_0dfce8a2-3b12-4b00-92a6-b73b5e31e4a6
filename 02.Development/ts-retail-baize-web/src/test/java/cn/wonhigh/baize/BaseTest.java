package cn.wonhigh.baize;

import cn.wonhigh.baize.web.WebApplicationBootstrap;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(classes = WebApplicationBootstrap.class, webEnvironment =  SpringBootTest.WebEnvironment.MOCK
        ,properties = {
        "topmall.web.security.enable=false",
        "topmall.security.enable=false",
        "file.encoding=UTF-8",
        "logFilePath=/data/logs/baize"
}
)
@ActiveProfiles(value = {"default", "test"})
@AutoConfigureMockMvc
public abstract class BaseTest {
}
