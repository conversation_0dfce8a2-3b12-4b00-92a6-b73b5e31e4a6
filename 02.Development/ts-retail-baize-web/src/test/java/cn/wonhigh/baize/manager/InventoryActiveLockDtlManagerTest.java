package cn.wonhigh.baize.manager;

import cn.wonhigh.baize.BaseTest;
import cn.wonhigh.baize.manager.gms.impl.InventoryActiveLockDtlManager;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.List;


public class InventoryActiveLockDtlManagerTest extends BaseTest {

    @Resource
    private InventoryActiveLockDtlManager inventoryActiveLockDtlManager;


    @Test
    void selectByInventoryActiveLockDtlQueryTest() {
        InventoryActiveLockDtlQuery query = InventoryActiveLockDtlQuery
                .builder().billNo("LI2508110002")
                .isContainerOccupiedQty(true)
                .channelNo("PUSFS")
                .build();
        List<InventoryActiveLockDtlOccupied> inventoryActiveLockDtlOccupieds = inventoryActiveLockDtlManager.selectByInventoryActiveLockDtlQuery(query);
        assert !inventoryActiveLockDtlOccupieds.isEmpty();
    }
}
