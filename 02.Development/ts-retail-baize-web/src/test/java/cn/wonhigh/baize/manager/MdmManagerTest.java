package cn.wonhigh.baize.manager;

import cn.mercury.basic.query.PageResult;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.BaseTest;
import cn.wonhigh.baize.manager.gms.MdmManager;
import cn.wonhigh.baize.model.dto.mdm.StoreListQueryParamDto;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

class MdmManagerTest extends BaseTest {

    @Resource
    private MdmManager manager;

    @Test
    void getStoreList() {
        StoreListQueryParamDto queryParamDto = new StoreListQueryParamDto();
        queryParamDto.setValidateSourceNo("ADHZLA");
        PageResult<?> pageResult = manager.getStoreList(queryParamDto);

        System.out.println(JsonUtils.toJson(pageResult));
    }
}