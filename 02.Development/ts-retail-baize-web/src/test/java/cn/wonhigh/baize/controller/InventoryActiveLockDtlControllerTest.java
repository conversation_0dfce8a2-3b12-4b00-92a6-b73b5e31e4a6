package cn.wonhigh.baize.controller;

import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.BaseTest;
import cn.wonhigh.baize.model.entity.gms.InventoryActiveLockDtl;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.apache.commons.io.FileUtils;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@TestPropertySource(properties = {
        "topmall.web.security.enable=false",
        "topmall.security.enable=false"
})
class InventoryActiveLockDtlControllerTest extends BaseTest {
    @Resource
    private MockMvc mockMvc;


    @Test
    void testDtlImport() throws Exception {

        InputStream inputStream = FileUtils.openInputStream(new java.io.File("D:\\Users\\Jason\\Downloads\\锁库活动明细导入模板 (7).xlsx"));
        MockMultipartFile file = new MockMultipartFile("excelFile",inputStream);

        /*
        * post("/inventory/active/lock/dtl/import?billNo=LI2507210021&sourcePlatform=PUSFS")
                                .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                                .param("excelFile", "D:\\Users\\Jason\\Downloads\\锁库活动明细导入模板 (6).xlsx")
        * */

        mockMvc.perform(
                        multipart("/inventory/active/lock/dtl/import?billNo=LI2507220032&sourcePlatform=PUSFS")
                                .file(file)
                )
                .andDo(print())
                .andExpect(status().isOk());

        inputStream.close();
    }

    @Test
    void testDtlCreate() throws Exception {
        String jsonStr = "{\n" +
                "            \"id\": \"ea807f005f9f11f05e37be7673d7cc60\",\n" +
                "                \"bill_no\": \"LI2507110024\",\n" +
                "                \"order_unit_name\": \"中央仓\",\n" +
                "                \"order_unit_no\": \"Z091T\",\n" +
                "                \"store_type\": null,\n" +
                "                \"store_name\": \"总部成都电商仓\",\n" +
                "                \"store_no\": \"Z028T\",\n" +
                "                \"sku_no\": \"20160707079767\",\n" +
                "                \"brand_no\": \"PU01\",\n" +
                "                \"item_code\": \"000000-001\",\n" +
                "                \"size_no\": \"11\",\n" +
                "                \"barcode\": \"000000-00111\",\n" +
                "                \"lock_qty\": 1,\n" +
                "                \"balance_lock_qty\": 1,\n" +
                "                \"sync_status\": 0,\n" +
                "                \"update_user\": \"孙明桥-体育\",\n" +
                "                \"update_time\": \"2025-07-11 17:22:37\",\n" +
                "                \"create_user\": \"孙明桥-体育\",\n" +
                "                \"create_time\": \"2025-07-11 17:14:31\"\n" +
                "        }";
        InventoryActiveLockDtl lockDtl
                = JsonUtils.mapper()
                .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE)
                .readValue(jsonStr, InventoryActiveLockDtl.class);

        List<BasicNameValuePair> list = new ArrayList<>();

        Field[] fields = lockDtl.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object o = field.get(lockDtl);
                if (o != null) {
                    list.add(new BasicNameValuePair(field.getName(), o.toString()));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        mockMvc.perform(
                        post("/inventory/active/lock/dtl/create")
                                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                                .content(
                                        EntityUtils.toString(new UrlEncodedFormEntity(list))
                                )

                )
                .andDo(print())
                .andExpect(status().isOk());
    }
}
