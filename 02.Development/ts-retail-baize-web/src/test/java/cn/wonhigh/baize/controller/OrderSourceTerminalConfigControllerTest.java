package cn.wonhigh.baize.controller;

import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.BaseTest;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @see cn.wonhigh.baize.web.controller.OrderSourceTerminalConfigController
 */
public class OrderSourceTerminalConfigControllerTest extends BaseTest {

    @Resource
    private MockMvc mockMvc;


    @Test
    void testCreate() throws Exception{
        String json = "{\"thirdChannelNo\":\"SHHZ-HPC-ADHZLA\",\"thirdChannelName\":\"杭州上城九和路七堡花园城AD临特(荟品仓)\",\"secondPlatform\":\"SHHZ-HPC\",\"secondPlatformName\":\"荟品仓\",\"platform\":\"SHHZ\",\"platformName\":\"商户合作\",\"thirdPlatform\":\"ADHZLA\",\"thirdPlatformName\":\"杭州上城七堡花园城AD临特\",\"companyName\":\"滔搏企业发展（上海）有限公司-华东\",\"companyNo\":\"S9001\",\"terminal\":\"MMP\", \"terminalName\":\"MMP\"}";

        JsonNode node = JsonUtils.fromJson(json);

        List<BasicNameValuePair> basicNameValuePairs = new ArrayList<>();
        node.fieldNames().forEachRemaining(i -> {
            basicNameValuePairs.add(new BasicNameValuePair(i, node.get(i).asText()));
        });


        mockMvc.perform(
                post("/order/source/terminal/config/saveData")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                        .content(EntityUtils.toString(new UrlEncodedFormEntity(basicNameValuePairs, "utf-8")))
                        .characterEncoding("utf-8")
        ).andDo(print()).andExpect(status().isOk());
    }
}
