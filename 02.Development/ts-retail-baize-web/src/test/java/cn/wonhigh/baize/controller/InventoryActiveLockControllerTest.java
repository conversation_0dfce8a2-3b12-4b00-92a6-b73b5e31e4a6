package cn.wonhigh.baize.controller;

import cn.wonhigh.baize.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


class InventoryActiveLockControllerTest extends BaseTest {
    @Resource
    private MockMvc mockMvc;


    @Test
    void testMvc() throws Exception {
        mockMvc.perform(get("/inventory/active/lock/approve?id=ea982534627711f067b5b5ecc6182ce0")
                )
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void testCancel() throws Exception {
        mockMvc.perform(post("/inventory/active/lock/cancel")
                        .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                        .param("id", "4a145f84e05a11f063ba8172fa486240")
                )
                .andDo(print())
                .andExpect(status().isOk());
    }


    @Test
    void testLogs() throws Exception {
        mockMvc.perform(get("/inventory/active/lock/log?billNo=LI2507230021")
                )
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void testEffect() throws Exception {
        mockMvc.perform(get("/inventory/active/lock/effect?id=8e15d98c5b4211f067a49d870deb0950")
                )
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void testSync() throws Exception {
        mockMvc.perform(get("/inventory/active/lock/sync?id=8e15d98c5b4211f067a49d870deb0950")
                )
                .andDo(print())
                .andExpect(status().isOk());
    }
}
