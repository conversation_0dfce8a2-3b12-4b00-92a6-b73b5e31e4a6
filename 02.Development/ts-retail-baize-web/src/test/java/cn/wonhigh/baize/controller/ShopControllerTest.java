package cn.wonhigh.baize.controller;

import cn.wonhigh.baize.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;


public class ShopControllerTest extends BaseTest {


    @Resource
    private MockMvc mockMvc;

    @Test
    void testFindByNo() throws Exception {
        mockMvc.perform(
                get("/shop/get")
                        .queryParam("shopNo", "NKT564")
        ).andDo(print()).andReturn();
    }

    @Test
    void testShopAndCompany() throws Exception {
        mockMvc.perform(
                get("/shop/shopAndCompany")
                        .queryParam("shopNo", "ADHZLA")
                        .queryParam("&_", "1752724448589")
        ).andDo(print()).andReturn();
    }
}
