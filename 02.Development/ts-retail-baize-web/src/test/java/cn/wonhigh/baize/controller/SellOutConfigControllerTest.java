package cn.wonhigh.baize.controller;

import cn.wonhigh.baize.BaseTest;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class SellOutConfigControllerTest extends BaseTest {

    @Resource
    private MockMvc mockMvc;


    @Test
    public void testExport () throws Exception{
        mockMvc.perform(
                        get("/sell/out/config/exportExcel?selectType=item")
                )
                .andDo(s -> {
                    OutputStream outputStream = s.getResponse().getOutputStream();

                })
                .andExpect(status().isOk());
    }


    @Test
    public void testImport() throws Exception {

        InputStream inputStream = FileUtils.openInputStream(new java.io.File("D:\\Users\\Jason\\Downloads\\售罄目标配置导入-按商品.xlsx"));
        MockMultipartFile file = new MockMultipartFile("excelFile",inputStream);

        mockMvc.perform(
                        multipart("/sell/out/config/import?selectType=item")
                                .file(file)
                )
                .andDo(print())
                .andExpect(status().isOk());

        inputStream.close();
    }
}
