package cn.wonhigh.baize.web.controller;

import cn.mercury.annotation.JsonVariable;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.data.metadata.DataEntry;
import cn.mercury.domain.DataChangeEntry;
import cn.mercury.domain.Tupe;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.functions.Function1;
import cn.mercury.manager.IManager;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.IIcsInventorySyncConfigManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.manager.gms.IOrderSourceTerminalConfigManager;
import cn.wonhigh.baize.model.entity.gms.IcsInventorySyncConfig;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import cn.wonhigh.baize.model.enums.ChannelTypeEnum;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import cn.wonhigh.retail.uc.common.api.model.AuthorityUserVirtualWarehouse;
import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
import com.alibaba.dubbo.config.annotation.Reference;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import topmall.framework.security.Authorization;
import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/icsInventorySyncConfig")
public class IcsInventorySyncConfigController extends ApiController<IcsInventorySyncConfig, String> {
    @Autowired
    private IIcsInventorySyncConfigManager manager;

    protected IManager<IcsInventorySyncConfig, String> getManager() {
        return manager;
    }

    @Reference
    private AuthorityUserDataApi authorityUserDataApi;
    @Resource
    private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;
    @Resource
    private IOrderSourceTerminalConfigManager orderSourceTerminalConfigManager;

    @ResponseBody
    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/list")
    @Override
    public ApiResult selectByPage(Query query, Pagenation page) {
        //查询虚仓权限
        Map<String, String> infosMap = new HashMap<>();
        ApiResult result = getAuthorityUserVirtualWarehouse(query, infosMap);
        if (result != null) {
            return result;
        }
        long total = page.getTotal();
        if (total <= 0) {
            total = manager.selectCount(query);
        }
        if (total == 0) {
            return ApiResult.ok(new PageResult<DataEntry>(null, total));
        }
        List<IcsInventorySyncConfig> rows = manager.selectByPage(query, page);
        return ApiResult.ok(new PageResult<>(rows, total));
    }


    @ResponseBody
    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/listChannel")
    public ApiResult selectChannelByPage(Query query, Pagenation page) {
        //查询虚仓权限
        Map<String, String> infosMap = new HashMap<>();
        ApiResult result = getAuthorityUserVirtualWarehouse(query, infosMap);
        if (result != null) {
            return result;
        }
        Map<String, Object> param = query.asMap();
        param.keySet().stream().forEach(x -> {
            if (!StringUtils.isEmpty(param.get(x))) {
                query.and(x, param.get(x).toString().trim());
            }
        });
        query.orderby("update_time", Boolean.TRUE);
        long total = page.getTotal();
        if (total <= 0L) {
            total = (long) manager.countByChannel(query);
        }
        if (total == 0L) {
            return ApiResult.ok(new PageResult((List) null, total));
        } else {
            List<IcsInventorySyncConfig> rows = manager.pageByChannel(query, page);
            return ApiResult.ok(new PageResult(rows, total));
        }
    }

    public ApiResult getAuthorityUserVirtualWarehouse(Query query, Map<String, String> infosMap) {
        List<AuthorityUserVirtualWarehouse> authorityUserVirtualWarehouses = new ArrayList<>();
        Integer userId = Integer.parseInt(Authorization.getUser().getId());
        try {
            authorityUserVirtualWarehouses = authorityUserDataApi.userVirtualWarehouse(userId);
        } catch (Exception e1) {
            return ApiResult.error("根据userId查询聚合仓权限接口异常");
        }
        if (authorityUserVirtualWarehouses == null || CollectionUtils.isEmpty(authorityUserVirtualWarehouses)) {// 没有虚仓权限
            return ApiResult.ok(new PageResult<DataEntry>(null, 0));
        }
        List<String> vstoreCodeList = new ArrayList<>();
        for (AuthorityUserVirtualWarehouse authorityUserVirtualWarehouse : authorityUserVirtualWarehouses) {
            infosMap.put(authorityUserVirtualWarehouse.getVstoreCode(), authorityUserVirtualWarehouse.getVstoreName());
            vstoreCodeList.add(authorityUserVirtualWarehouse.getVstoreCode());
        }
        query.and("vstoreCodeList", vstoreCodeList);
        query.and("organTypeNo", Authorization.getUser().getOrganTypeNo());
        return null;
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/create")
    @ApiIgnore
    public ApiResult<IcsInventorySyncConfig> create(IcsInventorySyncConfig entry) {
        //校验channel_type、shop_no、vstore_code是否唯一
        List<InternetVirtualWarehouseInfo> virtualWarehouses = internetVirtualWarehouseInfoManager.selectByParams(new Query());
        Map<String, InternetVirtualWarehouseInfo> virtualWarehousesMap = virtualWarehouses.stream().collect(Collectors.toMap(InternetVirtualWarehouseInfo::getVstoreCode,
                internetVirtualWarehouseInfo -> internetVirtualWarehouseInfo));
        List<IcsInventorySyncConfig> list = manager.selectByParams(new Query().and("channelType", entry.getChannelType()).and("channelNo", entry.getChannelNo()));
        if (list.size() > 0) {
            //1、同一渠道店铺对应的聚合仓不能重复
            List<String> existsVstoreCodeList = list.stream().map(IcsInventorySyncConfig::getVstoreCode).collect(Collectors.toList());
            if (existsVstoreCodeList.contains(entry.getVstoreCode())) {
                return ApiResult.error("该渠道编码对应的聚合仓编码已存在");
            }
            if (entry.getChannelType() == 2) {
                //2、同一渠道店铺不允许标准和非标准仓同时存在
                String vstoreCode = list.get(0).getVstoreCode();
                Integer existsVstoreMold = virtualWarehousesMap.get(vstoreCode).getVstoreMold();
                Integer vstoreMold = virtualWarehousesMap.get(entry.getVstoreCode()).getVstoreMold();
                if (existsVstoreMold != vstoreMold) {
                    return ApiResult.error("新加聚合仓类型:" + vstoreMold + "和数据库类型:" + existsVstoreMold + "不一致");
                }
                //3、同一渠道店铺不允许标准聚合仓和对应的冗余仓同时存在
                if (vstoreMold == 1) {
                    if (entry.getVstoreCode().contains("-")) {
                        if (existsVstoreCodeList.contains(entry.getVstoreCode().substring(0, entry.getVstoreCode().indexOf("-")))) {
                            return ApiResult.error("该渠道编码对应的聚合仓已存在");
                        }
                    } else {
                        if (existsVstoreCodeList.contains(entry.getVstoreCode() + "-RY")) {
                            return ApiResult.error("该渠道编码对应的冗余仓已存在");
                        }
                    }
                }
            }
        }
        //4、赋值聚合仓名称
        entry.setVstoreName(virtualWarehousesMap.get(entry.getVstoreCode()).getVstoreName());
        if (entry.getChannelType() == 1) {
            entry.setChannelNo(entry.getVstoreCode());
            entry.setChannelName(entry.getVstoreName());
        }
        getManager().insert(entry);
        return ApiResult.ok(entry);
    }

    @Override
    public ApiResult<Integer> batchSave(DataChangeEntry<IcsInventorySyncConfig> datas) {
        if (!CollectionUtils.isEmpty(datas.getInserted())) {
            Map<String, List<IcsInventorySyncConfig>> configMap = new HashMap<>();
            for (IcsInventorySyncConfig addConfig : datas.getInserted()) {
                if (addConfig.getSharingRatio() == null) {
                    return ApiResult.error("共享比例不能为空");
                }
                if(!StringUtils.hasText(addConfig.getVstoreCode())){
                    return ApiResult.error("聚合仓编码不能为空");
                }
                //校验数据是否重复，channelNo+vstoreCode不能重复
                //XCX渠道，聚合仓校验，标准和对应冗余仓不能同时存在，标准和非标准不能同时存在
                InternetVirtualWarehouseInfo vstoreInfo = internetVirtualWarehouseInfoManager.findByUnique(addConfig.getVstoreCode());
                if (vstoreInfo == null) {
                    return ApiResult.error(String.format("聚合仓：%s，不存在", addConfig.getVstoreCode()));
                }
                if (addConfig.getChannelType() == ChannelTypeEnum.XCX.getCode()) {
                    if(!StringUtils.hasText(addConfig.getChannelNo())){
                        return ApiResult.error("渠道编码不能为空");
                    }
                    List<OrderSourceTerminalConfig> configs = orderSourceTerminalConfigManager.selectByParams(Query.Where("thirdPlatform", addConfig.getChannelNo()));
                    if(CollectionUtils.isEmpty(configs)){
                        return ApiResult.error(String.format("渠道编码：%s，不存在", addConfig.getChannelNo()));
                    }
                    if(!"XCXZBXD".equals(configs.get(0).getTerminal()) && !"XCXDQXD".equals(configs.get(0).getTerminal())){
                        return ApiResult.error(String.format("渠道编码：%s，选择的渠道不属于小程序", addConfig.getChannelNo()));
                    }
                    addConfig.setChannelName(configs.get(0).getThirdPlatformName());
                } else if (addConfig.getChannelType() == ChannelTypeEnum.OMS.getCode()) {
                    addConfig.setChannelNo(vstoreInfo.getVstoreCode());
                    addConfig.setChannelName(vstoreInfo.getVstoreName());
                }
                addConfig.setVstoreMold(vstoreInfo.getVstoreMold());
                addConfig.setVstoreName(vstoreInfo.getVstoreName());
                String key = String.format("%s_%s", addConfig.getChannelNo(), addConfig.getChannelType());
                List<IcsInventorySyncConfig> list = null;
                if (configMap.containsKey(key)) {
                    list = configMap.get(key);
                } else {
                    list = manager.selectChannelVstoreInfo(
                            Query.Where("channelNo", addConfig.getChannelNo()).and("channelType", addConfig.getChannelType()));
                    if (!CollectionUtils.isEmpty(list)) {
                        configMap.put(key, list);
                    } else {
                        configMap.put(key, new ArrayList<>(Arrays.asList(addConfig)));
                    }
                }
                String msg = "";
                for (IcsInventorySyncConfig exitConfig : list) {
                    if (exitConfig.getVstoreCode().equals(addConfig.getVstoreCode())) {
                        msg = "已存在";
                    } else if (addConfig.getChannelType() == ChannelTypeEnum.XCX.getCode()) {
                        if (vstoreInfo.getVstoreMold() == null) {
                            vstoreInfo.setVstoreMold(0);
                        }
                        if (exitConfig.getVstoreMold() == null) {
                            exitConfig.setVstoreMold(0);
                        }
                        if (vstoreInfo.getVstoreMold() != exitConfig.getVstoreMold()) {
                            msg = "与已添加的渠道类型不一致";
                        } else if (vstoreInfo.getVstoreMold() == 1 && vstoreInfo.getVstoreType() == 2 && vstoreInfo.getVstoreCode().startsWith(exitConfig.getVstoreCode())) {
                            msg = "对应的聚合仓总仓已存在";
                        } else if (vstoreInfo.getVstoreMold() == 1 && vstoreInfo.getVstoreType() == 1 && exitConfig.getVstoreCode().startsWith(vstoreInfo.getVstoreCode())) {
                            msg = "对应的冗余仓已存在";
                        }
                    }
                    if (StringUtils.hasText(msg)) {
                        return ApiResult.error(String.format("【渠道类型：%s,渠道：%s,聚合仓：%s】,%s", ChannelTypeEnum.getName(addConfig.getChannelType()), addConfig.getChannelNo(), addConfig.getVstoreCode(), msg));
                    }
                }
                list.add(addConfig);
            }
            for (IcsInventorySyncConfig addConfig : datas.getInserted()) {
                saveLogs(addConfig, OpscodeEnum.ADD);
            }
        }
        if (!CollectionUtils.isEmpty(datas.getUpdated())) {
            for (IcsInventorySyncConfig addConfig : datas.getUpdated()) {
                IcsInventorySyncConfig dbConfig = manager.findByPrimaryKey(addConfig.getId());
                if (dbConfig == null) {
                    return ApiResult.error("数据不存在");
                }
                dbConfig.setStatus(addConfig.getStatus());
                saveLogs(dbConfig, OpscodeEnum.UP);
            }
        }
        return super.batchSave(datas);
    }

    @Override
    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {
        return ExcelExportOperations.builder()
                .fieldFormater("status", (a) -> {
                    return Objects.deepEquals(a, 1) ? "启用" : "禁用";
                })
                .fieldFormater("channelType", (a) -> {
                    return ChannelTypeEnum.getName(Integer.parseInt(ObjectUtils.defaultIfNull(a, -1).toString()));
                })
                .headers(columns)
                .value();
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/export")
    @ApiIgnore
    public ApiResult<Tupe<String, Integer>> export(Query query, String fileName,
                                                   @JsonVariable("_columns") ExcelColumn[][] columns, HttpServletResponse response) {
        //查询虚仓权限
        Map<String, String> infosMap = new HashMap<>();
        ApiResult result = getAuthorityUserVirtualWarehouse(query,infosMap);
        if(result!=null){
            return result;
        }
        query.and("infosMap",infosMap);
        Object channelNos = query.asMap().get("channelNos");
        if(channelNos != null && StringUtils.hasText(channelNos.toString())){
            query.and("channelNoList", new ArrayList<>(Arrays.asList(channelNos.toString().split(","))));
        }
        Integer count = fetchExportRowCount(query);
        if (query.getPagenation() == null) {
            query.setPagenation(new Pagenation(1, Integer.MAX_VALUE));
        }
        query.getPagenation().setTotal(count);
        String ticket = export(columns, fileName, query);
        return ApiResult.ok(new Tupe<String, Integer>(ticket, count));
    }

    protected Function1<IcsInventorySyncConfig, Object> getRowHander(Query query) {
        return (row) -> handerData(row,query.findValue("infosMap"));
    }

    private Object handerData(IcsInventorySyncConfig row,Map<String, String> infosMap) {
        row.setVstoreName(infosMap.get(row.getVstoreCode()));
        row.setStatusName(row.getStatus()==1?"启用":"禁用");
        return row;
    }

    @Override
    public ApiResult<IcsInventorySyncConfig> update(IcsInventorySyncConfig entry) throws JsonManagerException {
        IcsInventorySyncConfig dbConfig = manager.findByPrimaryKey(entry.getId());
        if (dbConfig == null) {
            return ApiResult.error("数据不存在");
        }
        ApiResult<IcsInventorySyncConfig> result = super.update(entry);
        dbConfig.setSharingRatio(entry.getSharingRatio());
        saveLogs(dbConfig, OpscodeEnum.UP);
        return result;
    }

    void saveLogs(IcsInventorySyncConfig item, OpscodeEnum opsCode) {
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                        .setOpscode(opsCode.getCode()).
                        setSyscode(String.valueOf(MenuTypeEnums.ISC.getType())).
                        setSysname(MenuTypeEnums.ISC.getDesc()).
                        setKeyword1(item.getChannelNo()).
                        setKeyword1info("渠道编码:" + item.getChannelNo() + "(" + item.getChannelName() + ")").
                        setKeyword2(item.getVstoreCode()).
                        setKeyword2info("聚合仓编码:" + item.getVstoreCode() + "(" + item.getVstoreName() + ")").
                        setRemark(String.format("%s：渠道编码:%s(%s);聚合仓编码:%s(%s);渠道类型:%s;共享比例:%s;状态:%s",
                                opsCode.getName(), item.getChannelNo(), item.getChannelName(), item.getVstoreCode(),
                                item.getVstoreName(), item.getChannelType(), item.getSharingRatio(), item.getStatus()))
                        .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                        .setCreateTime(new Date())
                        .build()));
    }

    @Override
    public ApiResult<Integer> deleteByParams(Query query) {
        return ApiResult.error("不允许操作");
    }

    @Override
    public ApiResult<Integer> deleteByPrimaryKey(String id) {
        return ApiResult.error("不允许操作");
    }

}