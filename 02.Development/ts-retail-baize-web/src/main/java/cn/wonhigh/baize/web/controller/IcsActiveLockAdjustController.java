/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.mercury.security.IUser;
import cn.wonhigh.baize.business.active.adjust.ActiveLockAdjustProcess;
import cn.wonhigh.baize.manager.gms.IIcsActiveLockAdjustManager;
import cn.wonhigh.baize.manager.gms.IInternetSystemLogsManager;
import cn.wonhigh.baize.model.entity.gms.IcsActiveLockAdjust;
import cn.wonhigh.baize.model.entity.gms.InternetSystemLogs;
import cn.wonhigh.baize.model.enums.AdjustStatusEnums;
import cn.wonhigh.baize.model.enums.AdjustSyncStatusEnums;
import cn.wonhigh.baize.model.enums.AdjustTypeEnums;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.util.function.Tuple3;
import topmall.framework.core.ICodingRuleBuilder;
import topmall.framework.domain.codingrule.DefaultCodingRuleEntry;
import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import java.util.*;


@RestController

@RequestMapping("/ics/active/lock/adjust")
public class IcsActiveLockAdjustController extends ApiController<IcsActiveLockAdjust,String> {

    @Autowired
    private IIcsActiveLockAdjustManager manager;
    @Resource
    private ICodingRuleBuilder codeRuleBuilder;
    @Autowired
    private IInternetSystemLogsManager internetSystemLogsManager;

    protected IManager<IcsActiveLockAdjust,String> getManager(){
        return manager;
    }

    @Override
    public ApiResult<IcsActiveLockAdjust> create(IcsActiveLockAdjust adjust) {
        if (StrUtil.isBlank(adjust.getRefBillNo())) {
            return ApiResult.error("锁库活动单号不能为空");
        }
        DefaultCodingRuleEntry codeRuleEntry = new DefaultCodingRuleEntry("4035", "EX");
        String billNo = codeRuleBuilder.getSerialNo(codeRuleEntry);
        adjust.setBillNo(billNo);
        ActiveLockAdjustProcess process = getProcess(Query.Where("billNo", adjust.getRefBillNo()));
        process.addListener(this::processSaveLogs);
        process.create(adjust);
        return ApiResult.ok(adjust);
    }
    
    @Override
	public ApiResult<IcsActiveLockAdjust> update(IcsActiveLockAdjust adjust) throws JsonManagerException {
    	if (StrUtil.isBlank(adjust.getBillNo())) {
            return ApiResult.error("锁库活动单号不能为空");
        }
    	ActiveLockAdjustProcess process = getProcess(Query.Where("billNo", adjust.getBillNo()));
    	process.addListener(this::processSaveLogs);
    	process.update(adjust);
    	return ApiResult.ok(adjust);
	}

	/**
     * 审核
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.POST},
            value = {"/audit"}
    )
    public ApiResult<?> audit(Query query) {
        ActiveLockAdjustProcess process = getProcess(query);
        process.addListener(this::processSaveLogs);
        process.audit();
        return ApiResult.ok();
    }

    /**
     * 取消作废
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.POST},
            value = {"/cancel"}
    )
    public ApiResult<?> cancel(Query query) {
        ActiveLockAdjustProcess process = getProcess(query);
        process.addListener(this::processSaveLogs);
        process.cancel();
        return ApiResult.ok();
    }

    /**
     * 失败重新同步
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.POST},
            value = {"/sync"}
    )
    public ApiResult<?> failureSync(Query query) {
        getProcess(query).failureSync();
        return ApiResult.ok();
    }

    /**
     * 获取基础数据
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.GET, RequestMethod.POST},
            value = {"/basic_data"}
    )
    public ApiResult<?> basicData() {
        Map<String, Object> params = new HashMap<>();
        params.put("adjustStatus", AdjustStatusEnums.getAdjustStatusList());
        params.put("syncStatus", AdjustSyncStatusEnums.getAdjustSyncStatusList());
        params.put("adjustType", AdjustTypeEnums.getAdjustStatusList());
        return ApiResult.ok(params);
    }

    /**
     * 日志查询
     * @param query
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.GET, RequestMethod.POST},
            value = {"/log"}
    )
    public ApiResult<?> log(Query query) {
        String billNo = query.findValue("billNo");
        List<Map<String, Object>> list = internetSystemLogsManager.selectSystemLogsByKey1(billNo, MenuTypeEnums.ACTIVELOCKADJUST.getType(), this::operationType);
        return ApiResult.ok(list);
    }

    /**
     * 操作生效同步库存
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.GET, RequestMethod.POST},
            value = {"/effect"}
    )
    public ApiResult<?> effect(Query query) {
        ActiveLockAdjustProcess process = getProcess(query);
        process.addListener(this::processSaveLogs);
        process.effect();
        return ApiResult.ok();
    }

    private ActiveLockAdjustProcess getProcess(Query query) {
        String billNo = query.findValue("billNo");
        Assert.isTrue(StrUtil.isNotBlank(billNo), "锁库活动调整单号不能为空");
        return manager.getProcess(billNo);
    }

	private String operationType(String opsCode) {
		OpscodeEnum opsCodeEnum = OpscodeEnum.getOpsCodeEnum(opsCode);
		switch (opsCodeEnum) {
		case ADD:
			return "调整单创建";
		case UP:
			return "调整单修改";
		case AUDIT:
			return "调整单审核";
		case SYNC:
			return "同步库存";
		case FAIL_SYNC:
			return "失败重传";
		default:
			return "未定义操作";
		}
	}
    
    private void processSaveLogs(String methodName, boolean success, Throwable throwable, Tuple3<IcsActiveLockAdjust, IUser, String> tuple) {
        logger.info("optType:{}, success:{}, error:{}", methodName, success, Optional.ofNullable(throwable).map(Throwable::getMessage).orElse(""));
        InternetSystemLogs internetSystemLogs = new InternetSystemLogs();
        internetSystemLogs.setKeyword1(tuple.getT1().getBillNo());
        internetSystemLogs.setKeyword1info("锁库调整单号");
        internetSystemLogs.setKeyword2(tuple.getT1().getRefBillNo());
        internetSystemLogs.setKeyword2info("锁库活动单号");
        internetSystemLogs.setSyscode(MenuTypeEnums.ACTIVELOCKADJUST.getType()+"");
        internetSystemLogs.setSysname(MenuTypeEnums.getTypeDesc(MenuTypeEnums.ACTIVELOCKADJUST.getType()));
        internetSystemLogs.setOpscode(methodName);
        internetSystemLogs.setRemark(tuple.getT3());
        internetSystemLogs.setCreateUser(tuple.getT2().getName());
        internetSystemLogs.setCreateTime(new Date());
        internetSystemLogsManager.insert(internetSystemLogs);
    }
}