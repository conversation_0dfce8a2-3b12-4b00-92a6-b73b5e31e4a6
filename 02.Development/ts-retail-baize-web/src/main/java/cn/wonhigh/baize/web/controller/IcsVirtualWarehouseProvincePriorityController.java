/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.mercury.security.IUser;
import cn.wonhigh.baize.manager.gms.IInternetAreaRelationManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.model.entity.gms.InternetAreaRelation;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import cn.wonhigh.baize.utils.common.QueryUtil;
import com.alibaba.excel.EasyExcel;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import topmall.framework.security.Authorization;
import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;

import org.springframework.web.bind.annotation.RequestMapping;
import cn.mercury.manager.IManager;

import cn.wonhigh.baize.model.entity.gms.IcsVirtualWarehouseProvincePriority;
import  cn.wonhigh.baize.manager.gms.IIcsVirtualWarehouseProvincePriorityManager;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/ics/virtual/warehouse/province/priority")
public class IcsVirtualWarehouseProvincePriorityController extends ApiController<IcsVirtualWarehouseProvincePriority,String> {
    @Autowired
    private IIcsVirtualWarehouseProvincePriorityManager manager;

    @Autowired
    private IInternetAreaRelationManager internetAreaRelationManager;

    @Autowired
    private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;

    private final Cache<String, InternetAreaRelation> internetAreaRelationCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    private final Cache<String, InternetVirtualWarehouseInfo> virtualWarehouseInfoCache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build();

    protected IManager<IcsVirtualWarehouseProvincePriority,String> getManager(){
        return manager;
    }


    @Override
    public ApiResult<IcsVirtualWarehouseProvincePriority> create(IcsVirtualWarehouseProvincePriority entry) {
        if (StringUtils.isBlank(entry.getProvinceNo())) {
            return ApiResult.error("省份编号不能为空");
        }

        if (StringUtils.isBlank(entry.getVstoreCode())) {
            return ApiResult.error("聚合仓编号不能为空");
        }

        InternetAreaRelation areaRelations = getInternetAreaRelation(entry.getProvinceNo()).orElse(null);
        if (areaRelations == null) {
            return ApiResult.error("省份编号不存在");
        }


        InternetVirtualWarehouseInfo virtualWarehouseInfos = getVirtualWarehouseInfo(entry.getVstoreCode()).orElse(null);
        if (virtualWarehouseInfos == null) {
            return ApiResult.error("聚合仓编号不存在");
        }

        //判断是否已存在相同的数据
        List<IcsVirtualWarehouseProvincePriority> findData= this.manager.selectByParams(QueryUtil.mapToQuery(new HashMap<String, Object>() {{
            put("provinceNo", entry.getProvinceNo());
            put("vstoreCode", entry.getVstoreCode());
        }}));

        if(findData!=null && !findData.isEmpty()){
            return ApiResult.error("已存在相同的数据");
        }


        entry.setProvinceName(areaRelations.getRetailName());
        entry.setVstoreName(virtualWarehouseInfos.getVstoreName());
        entry.setStatus(1);




        return super.create(entry);
    }


    @Override
    public ApiResult<IcsVirtualWarehouseProvincePriority> update(IcsVirtualWarehouseProvincePriority entry) throws JsonManagerException {
        if (StringUtils.isBlank(entry.getProvinceNo())) {
            return ApiResult.error("省份编号不能为空");
        }

        if (StringUtils.isBlank(entry.getVstoreCode())) {
            return ApiResult.error("聚合仓编号不能为空");
        }

        InternetAreaRelation areaRelations = getInternetAreaRelation(entry.getProvinceNo()).orElse(null);
        if (areaRelations == null) {
            return ApiResult.error("省份编号不存在");
        }


        InternetVirtualWarehouseInfo virtualWarehouseInfos = getVirtualWarehouseInfo(entry.getVstoreCode()).orElse(null);
        if (virtualWarehouseInfos == null) {
            return ApiResult.error("聚合仓编号不存在");
        }

        //判断是否已存在相同的数据
        List<IcsVirtualWarehouseProvincePriority> findData= this.manager.selectByParams(QueryUtil.mapToQuery(new HashMap<String, Object>() {{
            put("provinceNo", entry.getProvinceNo());
            put("vstoreCode", entry.getVstoreCode());
        }}));

        Optional.ofNullable(findData)
                        .orElse(new ArrayList<>())
                                .stream()
                                    .filter(item -> !item.getId().equals(entry.getId()))
                                        .findFirst()
                                            .ifPresent(item -> {
                                                throw new JsonManagerException("已存在相同的数据");
                                            });


        entry.setProvinceName(areaRelations.getRetailName());
        entry.setVstoreName(virtualWarehouseInfos.getVstoreName());
        
        return super.update(entry);
    }


    @RequestMapping("/import")
    public ApiResult<?> importData(HttpServletRequest request) {
        if (!(request instanceof MultipartHttpServletRequest)) {
            return ApiResult.error("请上传文件");
        }
        MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;

        MultipartFile multipartFile = multipartHttpServletRequest.getFile("file");

        if (multipartFile == null) {
            return ApiResult.error("请上传文件");
        }

        List<Tuple2<Integer, String>> errorList = new ArrayList<>();

        List<IcsVirtualWarehouseProvincePriority> dataList = new ArrayList<>();
        try (InputStream is = multipartFile.getInputStream()) {
            List<Object> objectList = EasyExcel.read(is)
                    .autoTrim(true)
                    .headRowNumber(1)
                    .ignoreEmptyRow(true)
                    .autoCloseStream(true)
                    .autoTrim(true)
                    .doReadAllSync();


            int row = 1;
            for (Object object : objectList) {
                row++;
                if (!(object instanceof LinkedHashMap)) {
                    errorList.add(Tuples.of(row, "数据格式错误"));
                    continue;
                }

                LinkedHashMap<Integer, Object> linkedHashMap = (LinkedHashMap<Integer, Object>) object;

                String provinceNo = MapUtils.getString(linkedHashMap, 0);
                String vstoreCode =MapUtils.getString(linkedHashMap, 1);
                Integer status = Objects.equals(MapUtils.getString(linkedHashMap, 2), "启用") ? 1 : 0 ;

                if (StringUtils.isBlank(provinceNo) || StringUtils.isBlank(vstoreCode)) {
                    errorList.add(Tuples.of(row, "省份编号或聚合仓编号不能为空"));
                    continue;
                }

                InternetAreaRelation areaRelations = getInternetAreaRelation(provinceNo).orElse(null);
                if (areaRelations == null) {
                    errorList.add(Tuples.of(row, "省份编号不存在"));
                    continue;
                }

                InternetVirtualWarehouseInfo virtualWarehouseInfos = getVirtualWarehouseInfo(vstoreCode).orElse(null);
                if (virtualWarehouseInfos == null) {
                    errorList.add(Tuples.of(row, "聚合仓编号不存在"));
                    continue;
                }


                //判断是否已存在相同的数据
                List<IcsVirtualWarehouseProvincePriority> findData= this.manager.selectByParams(QueryUtil.mapToQuery(new HashMap<String, Object>() {{
                    put("provinceNo", provinceNo);
                    put("vstoreCode", vstoreCode);
                }}));


                dataList.add(new IcsVirtualWarehouseProvincePriority() {{
                    setProvinceName(areaRelations.getRetailName());
                    setVstoreName(virtualWarehouseInfos.getVstoreName());
                    setProvinceNo(provinceNo);
                    setVstoreCode(vstoreCode);

                    if (findData != null && !findData.isEmpty()) {
                        setId(findData.get(0).getId());
                        setUpdateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown"));
                        setUpdateTime(new Date());
                    } else {
                        setId(null);
                        setCreateTime(new Date());
                        setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown"));
                    }
                    setStatus(status);
                }});
            }

        } catch (Exception ignored) {
            logger.error("文件解析失败", ignored);
            return ApiResult.error("文件解析失败");
        }

        if (!errorList.isEmpty()) {
            return ApiResult.error("导入失败," + errorList.get(0).getT2());
        }

        // 判断是否有重复的数据, 省和虚仓
        dataList.stream()
                .collect(Collectors.groupingBy(item -> item.getProvinceNo() +";"+ item.getVstoreCode(), Collectors.counting()))
                .entrySet()
                .stream()
                .filter(item -> item.getValue() > 1)
                .findFirst()
                .ifPresent(item -> {
                    throw new JsonManagerException(String.format("导入数据中存在重复的数据, 省编码:%s,虚仓编码:%s", item.getKey().split(";")[0], item.getKey().split(";")[1]));
                });


        this.manager.batchSave(
                dataList.stream()
                        .filter(item -> item.getId() == null)
                        .collect(Collectors.toList()),
                dataList.stream()
                        .filter(item -> item.getId() != null)
                        .collect(Collectors.toList()),
                null
        );

        return ApiResult.ok();
    }

    private Optional<InternetVirtualWarehouseInfo> getVirtualWarehouseInfo(String vstoreCode) {
        InternetVirtualWarehouseInfo virtualWarehouseInfo = virtualWarehouseInfoCache.getIfPresent(vstoreCode);
        if (virtualWarehouseInfo != null) {
            return Optional.of(virtualWarehouseInfo);
        }

        Map<String, Object> virtualWarehouseInfoMap =  new HashMap<>();
        virtualWarehouseInfoMap.put("vstoreCode", vstoreCode);
        List<InternetVirtualWarehouseInfo> virtualWarehouseInfos = internetVirtualWarehouseInfoManager.selectByParams(QueryUtil.mapToQuery(virtualWarehouseInfoMap));

        if (virtualWarehouseInfos != null && !virtualWarehouseInfos.isEmpty()) {
            virtualWarehouseInfoCache.put(vstoreCode, virtualWarehouseInfos.get(0));
        }

        return Optional.ofNullable(virtualWarehouseInfos).orElse(new ArrayList<>(0)).stream().findFirst();
    }

    private Optional<InternetAreaRelation> getInternetAreaRelation(String retailCode) {

        InternetAreaRelation internetAreaRelation = internetAreaRelationCache.getIfPresent(retailCode);
        if (internetAreaRelation != null) {
            return Optional.of(internetAreaRelation);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("retailCode", retailCode);
        map.put("level", 1);
        map.put("status", 1);
        List<InternetAreaRelation> areaRelations = internetAreaRelationManager.selectByRetailCodeName(map);

        if (areaRelations != null && !areaRelations.isEmpty()) {
            internetAreaRelationCache.put(retailCode, areaRelations.get(0));
        }

        return Optional.ofNullable(areaRelations).orElse(new ArrayList<>(0)).stream().findFirst();
    }
}