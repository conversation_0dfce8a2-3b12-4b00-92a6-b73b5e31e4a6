package cn.wonhigh.baize.web.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.IShopManager;
import cn.wonhigh.baize.manager.ios.IInternetTakeShopItemManager;
import cn.wonhigh.baize.model.entity.gms.Shop;
import cn.wonhigh.baize.model.entity.ios.InternetTakeShopItem;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import cn.wonhigh.retail.iis.api.dto.BaseResultDto;
import cn.wonhigh.retail.iis.api.service.InventoryCalculationApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
import topmall.framework.security.Authorization;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2025/3/28 13:57
 */
@RestController
@RequestMapping("/internetTakeShopItem")
public class InternetTakeShopItemController extends ApiController<InternetTakeShopItem, String> {

    @Autowired
    private IInternetTakeShopItemManager manager;

    @Autowired
    private IShopManager shopManager;

    @Reference
    private InventoryCalculationApi inventoryCalculationApi;

    @Override
    protected IManager<InternetTakeShopItem, String> getManager() {
        return manager;
    }

    @Override
    public ApiResult<List<InternetTakeShopItem>> selectByParams(Query query) {
        query.orderby("update_time", Boolean.TRUE);
        return super.selectByParams(query);
    }

    @Override
    public ApiResult<InternetTakeShopItem> create(InternetTakeShopItem entry) {
        String msg = validateShopItem(entry);
        if (StringUtils.isNotEmpty(msg)) {
            return ApiResult.error(msg);
        }
        ApiResult<InternetTakeShopItem> result = super.create(entry);
        saveLogs(entry, OpscodeEnum.ADD);
        return result;
    }

    String validateShopItem(InternetTakeShopItem entry) {
        if (entry == null) {
            return "参数为空";
        }
        if (StringUtils.isBlank(entry.getShopNo())) {
            return "店铺编码为空";
        }
        Shop shop = shopManager.findByUnique(entry.getShopNo());
        if (shop == null) {
            return "店铺编码不存在";
        }
        //校验店铺重复
        List<InternetTakeShopItem> shopItemList = manager.selectByParams(Query.Where("shopNo", entry.getShopNo())
                .and("takeSign", 2));
        if (!CollectionUtils.isEmpty(shopItemList) && shopItemList.size() > 0) {
            return "该店铺已存在";
        }
        //默认值
        entry.setTakeSign(2);
        entry.setShopName(shop.getShortName());
        entry.setChannelNo("GY");
        entry.setChannelName("公用");
        return null;
    }


    @PostMapping("/deleteData")
    public ApiResult<?> deleteInternetVirtualWarehouseScope(@RequestBody List<String> dataIds) {
        if (ObjectUtil.isEmpty(dataIds)) {
            return ApiResult.error("删除数据为空");
        }
        for (String dataId : dataIds) {
            this.manager.deleteByPrimaryKey(dataId);
        }
        return ApiResult.ok();
    }


    @PostMapping("/stockSync")
    public ApiResult<?> stockSync(@RequestBody List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return ApiResult.error("参数为空");
        }
        //数据去重
        dataIds = dataIds.stream().distinct().collect(Collectors.toList());
        if (dataIds.size() > 30) {
            return ApiResult.error("一次只能同步30个店铺的数据");
        }
        for (String id : dataIds) {
            InternetTakeShopItem shopItem = manager.findByPrimaryKey(id);
            if (shopItem == null) {
                return ApiResult.error("店铺不存在");
            }
            try {
                logger.info("开始库存同步,shopNo=" + shopItem.getShopNo());
                BaseResultDto result = inventoryCalculationApi.meituanInventoryCal(shopItem.getShopNo());
                logger.info("库存同步调用结果,shopNo={},{}", shopItem.getShopNo(), result.toString());
                if (result == null || !"200".equals(result.getCode())) {
                    return ApiResult.error("库存同步失败，店铺编码:" + shopItem.getShopNo() + (result == null ? "" : result.getMsg()));
                } else {
                    shopItem.setStockSyncTime(new Date());
                    manager.update(shopItem);
                    saveLogs(shopItem, OpscodeEnum.UP);
                }
            } catch (Exception e) {
                logger.error("库存同步调用异常,shopNo=" + shopItem.getShopNo(), e);
                return ApiResult.error(String.format("库存同步失败,shopNo=%s,%s:", shopItem.getShopNo(), e.getMessage()));
            }
        }
        return ApiResult.ok();
    }

    @RequestMapping("/import")
    @ResponseBody
    public ApiResult<?> importData(HttpServletRequest request, HttpServletResponse response) {
        StandardMultipartHttpServletRequest multipartHttpServletRequest = null;
        if (request instanceof StandardMultipartHttpServletRequest) {
            multipartHttpServletRequest = (StandardMultipartHttpServletRequest) request;
        }

        if (multipartHttpServletRequest == null) {
            return ApiResult.error("File is not found");
        }

        MultipartFile multipartFile = multipartHttpServletRequest.getFile("excelFile");
        if (multipartFile == null) {
            return ApiResult.error("File is not found");
        }

        try {
            List<InternetTakeShopItem> saveDatas = new ArrayList<>();
            List<Object> objectList = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .headRowNumber(0)
                    .autoCloseStream(true).doReadAllSync();
            StringBuilder errorMsg = new StringBuilder();
            Set<String> shopNoSet = new HashSet<>();
            // 校验数据
            for (int i = 1; i < objectList.size(); i++) {
                LinkedHashMap map = (LinkedHashMap) objectList.get(i);
                String shopNo = MapUtils.getString(map, 0);
                if (StringUtils.isBlank(shopNo)) {
                    continue;
                }
                if (shopNoSet.contains(shopNo)) {
                    errorMsg.append(",").append("第" + (i + 1) + "行店铺编码重复");
                    continue;
                }
                InternetTakeShopItem shopItem = new InternetTakeShopItem();
                shopItem.setShopNo(shopNo);
                String msg = validateShopItem(shopItem);
                if (StringUtils.isNotEmpty(msg)) {
                    errorMsg.append(",").append("第" + (i + 1) + "行" + msg);
                    continue;
                }
                shopNoSet.add(shopNo);
                saveDatas.add(shopItem);
            }
            if (errorMsg.toString().length() > 0) {
                return ApiResult.error(errorMsg.toString());
            }
            this.manager.batchSave(saveDatas, null, null);
            for (InternetTakeShopItem saveData : saveDatas) {
                saveLogs(saveData, OpscodeEnum.ADD);
            }
        } catch (Exception e) {
            logger.error("聚合仓范围导入异常", e);
            return ApiResult.error(e.getMessage());
        }
        return ApiResult.ok();
    }

    void saveLogs(InternetTakeShopItem item, OpscodeEnum opsCode) {
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                        .setOpscode(opsCode.getCode()).
                        setSyscode(String.valueOf(MenuTypeEnums.ITSI.getType())).
                        setSysname(MenuTypeEnums.ITSI.getDesc()).
                        setKeyword1(item.getShopNo()).
                        setKeyword1info("店铺编码:" + item.getShopNo() + "(" + item.getShopName() + ")").
                        setKeyword2(item.getChannelNo()).
                        setKeyword2info("渠道编码:" + item.getChannelNo() + "(" + item.getChannelName() + ")").
                        setRemark(String.format("%s：店铺编码:%s(%s);渠道编码:%s(%s);自提标记:%s;库存同步时间:%s;",
                                opsCode.getName(), item.getShopNo(), item.getShopName(), item.getChannelNo(),
                                item.getChannelName(), item.getTakeSign(), item.getStockSyncTime()))
                        .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                        .setCreateTime(new Date())
                        .build()));
    }

}
