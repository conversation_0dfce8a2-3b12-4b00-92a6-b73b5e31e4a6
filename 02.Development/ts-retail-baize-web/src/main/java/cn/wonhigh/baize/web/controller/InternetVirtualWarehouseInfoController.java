/**
 * by
 **/
package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import java.util.Objects;


@RestController

@RequestMapping("/internet/virtual/warehouse/info")
public class InternetVirtualWarehouseInfoController extends ApiController<InternetVirtualWarehouseInfo, String> {
    @Autowired
    private IInternetVirtualWarehouseInfoManager manager;

    protected IManager<InternetVirtualWarehouseInfo, String> getManager() {
        return manager;
    }


    @RequestMapping("/find/")
    public InternetVirtualWarehouseInfo findByUnique(String vstoreCode) {
        return manager.findByUnique(vstoreCode);
    }

    @RequestMapping("/delete/unique/")
    public Integer deleteByUnique(String vstoreCode) {
        return manager.deleteByUnique(vstoreCode);
    }


    @Override
    public ApiResult<PageResult<InternetVirtualWarehouseInfo>> selectByPage(Query query, Pagenation page) {
        String qValue = query.findValue("q");
        if (StringUtils.isNotBlank(qValue)) {
            query = query.and("vstoreCodeAndName", qValue);
        }
        query = query.orderby("update_time", Boolean.TRUE).orderby("create_time", Boolean.TRUE);
        return super.selectByPage(query, page);
    }


    @Override
    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {


        return ExcelExportOperations.builder()
                .fieldFormater("vstoreType", (a) -> {
                    return Objects.deepEquals(a, 1) ? "总仓" : "子仓";
                })
                .fieldFormater("isCalc", (a) -> {
                    return Objects.deepEquals(a, 0) ? "否" : "是";
                })
                .fieldFormater("isSync", (a) -> {
                    return Objects.deepEquals(a, 0) ? "否" : "是";
                })
                .fieldFormater("businessType", (a) -> {
                    String businessType = "";
                    switch (Integer.parseInt(ObjectUtils.defaultIfNull(a, -1).toString())) {
                        case 0:
                            businessType = "特殊";
                            break;
                        case 1:
                            businessType = "网销";
                            break;
                        case 2:
                            businessType = "跨店";
                            break;
                        default:
                            businessType = "未知";
                            break;
                    }
                    return businessType;
                })

                .headers(columns)
                .value();
    }
}