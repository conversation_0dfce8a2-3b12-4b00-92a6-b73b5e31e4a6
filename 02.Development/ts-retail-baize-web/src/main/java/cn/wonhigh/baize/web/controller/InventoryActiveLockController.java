/**
 * by
 **/
package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.UUID;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.basic.query.Statement;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.excel.ExportTask;
import cn.mercury.functions.Function1;
import cn.mercury.manager.IManager;
import cn.mercury.manager.ManagerException;
import cn.mercury.utils.DateUtil;
import cn.wonhigh.baize.events.systemlog.InventoryActiveLockLogHelper;
import cn.wonhigh.baize.manager.gms.*;
import cn.wonhigh.baize.model.dto.AuthorityUserDataDto;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlOccupied;
import cn.wonhigh.baize.model.dto.activelock.InventoryActiveLockDtlQuery;
import cn.wonhigh.baize.model.entity.gms.*;
import cn.wonhigh.baize.model.enums.*;
import cn.wonhigh.baize.service.gms.IInventorySearchService;
import cn.wonhigh.baize.utils.common.QueryUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple6;
import reactor.util.function.Tuples;
import topmall.framework.core.ICodingRuleBuilder;
import topmall.framework.domain.codingrule.DefaultCodingRuleEntry;
import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static cn.wonhigh.baize.model.enums.InventoryActiveLockOpscodeEnum.STOP;


@RestController
@RequestMapping("/inventory/active/lock")
public class InventoryActiveLockController extends ApiController<InventoryActiveLock, String> {
    public static final Logger logger = LoggerFactory.getLogger(InventoryActiveLockController.class);

    @Autowired
    private IInventoryActiveLockManager manager;

    @Autowired
    private IInventoryActiveLockDtlManager inventoryActiveLockDtlManager;

    @Resource
    private IInventorySearchService inventorySearchService;

    @Resource
    private ICodingRuleBuilder codeRuleBuilder;

    @Resource
    private IIcsInventorySyncConfigManager iIcsInventorySyncConfigManager;

    @Resource
    private IOrderSourceTerminalConfigManager orderSourceTerminalConfigManager;

    @Resource
    private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;

    @Resource
    private IInternetSystemLogsManager internetSystemLogsManager;

    @Override
    public ApiResult<Integer> deleteByPrimaryKey(String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResult.error("请选择数据");
        }

        InventoryActiveLock findInventoryActiveLock = this.manager.findByPrimaryKey(id);
        if (findInventoryActiveLock == null) {
            return ApiResult.error("数据不存在");
        }

        ActiveLockStatusEnums findStatusEnums = ActiveLockStatusEnums.getActiveLockEnums(findInventoryActiveLock.getStatus());
        if (findStatusEnums != ActiveLockStatusEnums.NEW_STATUS) {
            return ApiResult.error("锁库活动已生效, 锁库编码:" + findInventoryActiveLock.getBillNo());
        }

        if (findInventoryActiveLock.getIsApprove() != ActiveLockAuditStatusEnums.NOT_AUDIT.getValue()) {
            return ApiResult.error("锁库活动已审批, 锁库编码:" + findInventoryActiveLock.getBillNo());
        }


        this.manager.deleteByUnique(findInventoryActiveLock.getBillNo());
        return ApiResult.ok(null);
    }


    @Override
    public ApiResult<Integer> deleteByPrimaryKeys(String ids) {
        ApiResult<Integer> result = super.deleteByPrimaryKeys(ids);
        if (result.getData() == 0) {
            return  ApiResult.error("删除失败, 已生效数据不能删除!");
        }
        return result;
    }

    @ApiOperation(value = "渠道店铺查询接口")
    @ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
    @PostMapping("/shopList")
    public ApiResult<PageResult<?>> shopList(
            @ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto, HttpServletRequest request) {
        try {
            String channelType = request.getParameter("channelType");
            if (StringUtils.isBlank(channelType)) {
                return ApiResult.error("渠道类型参数不能为空");
            }
            Query query = new Query();
            query.and("queryParam",dto.getQ());
            query.and("channelType", channelType);
            List<OrderSourceTerminalConfig> pageList = orderSourceTerminalConfigManager.selectShopPageByParams(query,new Pagenation(dto.getPage(),dto.getRows()));
            Integer count = orderSourceTerminalConfigManager.selectShopCountByParams(query);
            return ApiResult.ok(new PageResult<>(pageList, count));
        } catch (Exception e) {
            logger.error("查询渠道店铺出错", e);
            return ApiResult.error("查询渠道店铺出错");
        }
    }

    @ApiOperation(value = "商家编码查询接口")
    @ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
    @PostMapping("/merchantList")
    public ApiResult<Set<String>> merchantList(
            @ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto, HttpServletRequest request) {
        try {
            Query query = new Query();
            query.and("terminal", OcsOrderSourceConfigChannelTypeEnum.PPF.getType());
            List<OrderSourceTerminalConfig> configList = orderSourceTerminalConfigManager.selectByParams(query);
            Set<String> merchantCodeList = Optional.ofNullable(configList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(i -> StringUtils.isNotBlank (i.getMerchantCode()) &&
                            !Objects.equals(i.getMerchantCode(), BrandMerchantCodeEnums.ADSFS.getCode()))
                    .map(OrderSourceTerminalConfig::getMerchantCode)
                    .collect(Collectors.toSet());
            return ApiResult.ok(merchantCodeList);
        } catch (Exception e) {
            logger.error("查询商家编码出错", e);
            return ApiResult.error("查询商家编码出错");
        }
    }

    @ApiOperation(value = "日志查询接口")
    @ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
    @GetMapping("/log")
    public ApiResult<List<Map<String, Object>>> log(
            @ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto, HttpServletRequest request) {
        try {
            String billNo = request.getParameter("billNo");
            List<Map<String, Object>> list = internetSystemLogsManager.selectSystemLogsByKey2(billNo,
                    Lists.newArrayList(MenuTypeEnums.ACTIVELOCK.getType(), MenuTypeEnums.ACTIVELOCKADJUST.getType())
                    );
            return ApiResult.ok(list);
        } catch (Exception e) {
            logger.error("查询日志出错", e);
            return ApiResult.error("查询日志出错");
        }
    }

    @PostMapping("/vstoreList")
    public ApiResult<List<InternetVirtualWarehouseInfo>> vstoreList( HttpServletRequest request) {
        try {
            //渠道类型是非品牌方，传选中的渠道店铺OrderSourceTerminalConfig.thirdPlatform
            String shopNo = request.getParameter("shopNo");
            Query query = new Query();
            List<InternetVirtualWarehouseInfo> virInfoList = null;
            if (StringUtils.isNotBlank(shopNo)) {
                query.and("channelType", 2);
                query.and("channelNo", shopNo);
                virInfoList = iIcsInventorySyncConfigManager.selectVstoreListByParams(query);
            }
            //渠道类型是品牌方，传选中的商家编码OrderSourceTerminalConfig.merchantCode
            String sourcePlatform = request.getParameter("sourcePlatform");
            if (StringUtils.isNotBlank(sourcePlatform)) {
                query.and("interfacePlatform", sourcePlatform);
                virInfoList = internetVirtualWarehouseInfoManager.selectByParams(query);
            }
            return ApiResult.ok(virInfoList);
        } catch (Exception e) {
            logger.error("查询渠道店铺出错", e);
            return ApiResult.error("查询渠道店铺出错");
        }
    }

    @ApiOperation(value = "锁库类型查询接口")
    @ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
    @PostMapping("/lockTypeList")
    public ApiResult<List<Map<String, Object>>> lockTypeList(HttpServletRequest request) {
        try {
            return ApiResult.ok(ActiveLockTypeEnums.getActiveLockTypeList());
        } catch (Exception e) {
            logger.error("查询锁库类型出错", e);
            return ApiResult.error("查询锁库类型出错");
        }
    }

    protected IManager<InventoryActiveLock, String> getManager() {
        return manager;
    }

    @RequestMapping("/config")
    public ApiResult<?> configData(@RequestParam String billNo) {
        return ApiResult.ok();
    }


    @RequestMapping("/find")
    public InventoryActiveLock findByUnique(String billNo) {
        return manager.findByUnique(billNo);
    }

    @Override
    public ApiResult<InventoryActiveLock> findByPrimaryKey(String id) {
        return super.findByPrimaryKey(id);
    }



    @Override
    public ApiResult<InventoryActiveLock> create(InventoryActiveLock entry) {
        if (StringUtils.isBlank(entry.getActiveName())) {
            return ApiResult.error("锁库活动名称不能为空");
        }
        if (entry.getActiveName().length() > 50) {
            return ApiResult.error("锁库活动名称长度不能超过50");
        }

        if (StringUtils.isNotBlank(entry.getRemark()) && entry.getRemark().length() > 200) {
            return ApiResult.error("备注长度不能超过200");
        }

        entry.setId(UUID.gernerate());
        DefaultCodingRuleEntry codeRuleEntry = new DefaultCodingRuleEntry("4034", "LI");
        String billNo = codeRuleBuilder.getSerialNo(codeRuleEntry);
        String prefix = StringUtils.substring(billNo, 0, 4);
        entry.setBillNo(billNo.replace(prefix, "LI"));
        entry.setStoreNo("");
        entry.setStoreName("");
        entry.setOrderUnitNo("");
        entry.setOrderUnitName("");
        //商家编码设置默认值
        if(StringUtils.isEmpty(entry.getSourcePlatform())){
            entry.setSourcePlatform("TOPS");
        }
        ApiResult<InventoryActiveLock> inventoryActiveLockApiResult = null;
        try {
            inventoryActiveLockApiResult = super.create(entry);
            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), entry.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_ADD, "活动锁库创建成功");
        } catch (Exception e) {
            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), entry.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_ADD, "活动锁库创建失败," + e.getMessage());
            logger.error("活动锁库创建失败", e);
        }
        return inventoryActiveLockApiResult;
    }

    @Override
    public ApiResult<InventoryActiveLock> update(InventoryActiveLock entry) throws JsonManagerException {
        if (StringUtils.isBlank(entry.getId())) {
            return ApiResult.error("id is not found");
        }
        if (StringUtils.isNotBlank(entry.getRemark()) && entry.getRemark().length() > 200) {
            return ApiResult.error("备注长度不能超过200");
        }
        InventoryActiveLock find = manager.findByPrimaryKey(entry.getId());
        ApiResult<InventoryActiveLock> update = null;
        try {

            InventoryActiveLock updateData = new InventoryActiveLock();
            BeanUtils.copyProperties(find, updateData);
            updateData.setEndTime(entry.getEndTime());
            updateData.setRemark(entry.getRemark());
            updateData.setLockType(entry.getLockType());
            updateData.setActiveName(entry.getActiveName());
            update = super.update(updateData);

            logger.info("单号:{}, 修改前的记录:{}, 修改后的记录:{}", entry.getBillNo(), find, updateData);

            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), entry.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_UP, "活动锁库修改成功");
        } catch (Exception e) {
            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), entry.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_UP, "活动锁库修改失败");
            throw new RuntimeException(e);
        }
        return update;
    }

    /**
     * 终止锁库活动
     * @param id
     * @return
     */
    @RequestMapping(value = "/stop", method = RequestMethod.GET)
    public ApiResult<?> stop (@RequestParam String id) {
        if (StringUtils.isEmpty(id)) {
            return ApiResult.error("请选择数据");
        }

        InventoryActiveLock findInventoryActiveLock = this.manager.findByPrimaryKey(id);
        if (findInventoryActiveLock == null) {
            return ApiResult.error("数据不存在");
        }

        ActiveLockAuditStatusEnums findAuditStatusEnums = ActiveLockAuditStatusEnums.getActiveLockAuditStatusEnums(findInventoryActiveLock.getIsApprove());
        if (findAuditStatusEnums != ActiveLockAuditStatusEnums.AUDIT_SUCCESS) {
            return ApiResult.error("锁库活动未审批, 锁库编码:" + findInventoryActiveLock.getBillNo());
        }

        ActiveLockStatusEnums findStatusEnums = ActiveLockStatusEnums.getActiveLockEnums(findInventoryActiveLock.getStatus());
        if (findStatusEnums != ActiveLockStatusEnums.EFFECTIVE_STATUS) {
            return ApiResult.error("锁库活动未生效, 锁库编码:" + findInventoryActiveLock.getBillNo());
        }

        findInventoryActiveLock.setStatus(ActiveLockStatusEnums.TERMINATION_STATUS.getValue());

        try {
            manager.stop(findInventoryActiveLock);
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), STOP, "活动锁库终止成功, 库存释放成功!");
        } catch (Exception e) {
            logger.error("终止锁库活动失败", e);
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), STOP, "活动锁库终止失败");
            return ApiResult.error("终止锁库活动失败,"+e.getMessage());
        }
        return ApiResult.ok();
    }


    /**
     * 审批数据
     *
     * @param id id
     * @return 结果
     */
    @RequestMapping(value = { "/approve"}, method = RequestMethod.GET)
    public ApiResult<?> approve(@RequestParam String id) {

        if (StringUtils.isEmpty(id)) {
            return ApiResult.error("请选择数据");
        }


        logger.info("开始审批数据, {}", id);

        InventoryActiveLock findInventoryActiveLock = this.manager.findByPrimaryKey(id);
        if (findInventoryActiveLock == null) {
            return ApiResult.error("数据不存在",501);
        }
        ActiveLockAuditStatusEnums findAuditStatusEnums = ActiveLockAuditStatusEnums.getActiveLockAuditStatusEnums(findInventoryActiveLock.getIsApprove());
        if (findAuditStatusEnums == ActiveLockAuditStatusEnums.AUDIT_SUCCESS) {
            return ApiResult.error("数据已审批, 锁库编码:" + findInventoryActiveLock.getBillNo(),501);
        }

        List<InventoryActiveLockDtl> inventoryActiveLockDtls = inventoryActiveLockDtlManager.selectByParams(QueryUtil.mapToQuery(Collections.singletonMap("billNo", findInventoryActiveLock.getBillNo())));

        if (CollectionUtils.isEmpty(inventoryActiveLockDtls)) {
            return ApiResult.error("明细数据不存在!", 501);
        }

        //
        long current = System.currentTimeMillis();
        List<Tuple6<String, String, String, String, String, String>> errors = validaAuditData(findInventoryActiveLock, inventoryActiveLockDtls);
        logger.info("校验数据耗时:{}s", (System.currentTimeMillis() - current)/1000);
        if (CollectionUtils.isNotEmpty(errors)) {
            String ticket = writeToExcel(errors);
            ApiResult<String> result = new ApiResult<>();
            result.setStatus(201);
            result.setMsg("审批失败,请下载错误明细");
            result.setData(ticket);
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_AUDIT, "活动锁库审核失败");
            return result;
        }

        Date now = new Date();

        if (findInventoryActiveLock.getEndTime().before(now)) {
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_AUDIT, "活动锁库审核失败");
            return ApiResult.error("活动已过期, 请重新设置活动时间", 501);
        }

        // 审核成功设置开始时间为当前时间
        findInventoryActiveLock.setStartTime(now);

        // 审批通过
        findInventoryActiveLock.setIsApprove(ActiveLockAuditStatusEnums.AUDIT_SUCCESS.getValue());
        // 生效
        findInventoryActiveLock.setStatus(ActiveLockStatusEnums.EFFECTIVE_STATUS.getValue());

        try {
            long time = System.currentTimeMillis();
            this.manager.approve(findInventoryActiveLock);
            logger.info("审批耗时:{}s", (System.currentTimeMillis() - time)/1000);
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_AUDIT, "活动锁库审核成功, 库存预占成功!");
        } catch (Exception e) {
            logger.error("审批失败", e);
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_AUDIT, "活动锁库审核失败");
            return ApiResult.error("审批失败,"+e.getMessage(), 501);
        }

        return ApiResult.ok();
    }


    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public ApiResult<?> cancel(@RequestParam String id) {
        if (StringUtils.isEmpty(id)) {
            return ApiResult.error("请选择数据");
        }

        InventoryActiveLock findInventoryActiveLock = this.manager.findByPrimaryKey(id);
        if (findInventoryActiveLock == null) {
            return ApiResult.error("数据不存在");
        }

        // 只有新建状态才能作废
        if (!Objects.equals(findInventoryActiveLock.getStatus(), ActiveLockStatusEnums.NEW_STATUS.getValue())) {
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_CANCEL, "活动锁库作废失败");
            return ApiResult.error("锁库活动不是新建状态, 锁库编码:" + findInventoryActiveLock.getBillNo());
        }

        findInventoryActiveLock.setStatus(ActiveLockStatusEnums.INVALID_STATUS.getValue());
        try {
            this.manager.update(findInventoryActiveLock);
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_CANCEL, "活动锁库作废成功");
        } catch (Exception e) {
            logger.error("作废失败", e);
            InventoryActiveLockLogHelper.log(Tuples.of(findInventoryActiveLock.getBillNo(), findInventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.OpscodeEnum_CANCEL, "活动锁库作废失败");
            return ApiResult.error("作废失败,"+e.getMessage());
        }
        return  ApiResult.ok();
    }

    /**
     * 生效
     *
     * @param id id
     * @return 结果
     */
    @RequestMapping(value = "/effect", method = RequestMethod.GET)
    public ApiResult<?> effect(@RequestParam String id) {
        return batchSyncInventory(id, AdjustDtlSyncStatusEnums.NOT_SYNC.getValue());
    }

    /**
     * 失败同步
     *
     * @param id id
     * @return 结果
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.GET},
            value = {"/sync"}
    )
    public ApiResult<?> failureSync(@RequestParam String id) {
        return batchSyncInventory(id, AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue());
    }

    /**
     * 商家编码
     *
     * @return 结果
     */
    @ResponseBody
    @RequestMapping(value = "/merchant", method = RequestMethod.GET)
    public ApiResult<?> merchantList() {
        return ApiResult.ok(BrandMerchantCodeEnums.getBrandMerchantList());
    }

    private ApiResult<?> batchSyncInventory(String id, Integer syncStatus) {
        if (StringUtils.isEmpty(id)) {
            return ApiResult.error("请选择数据");
        }
        logger.info("操作生效同步库存开始, {}", id);
        InventoryActiveLock inventoryActiveLock = this.manager.findByPrimaryKey(id);
        if (inventoryActiveLock == null) {
            return ApiResult.error("数据不存在");
        }

        ActiveLockStatusEnums statusEnums = ActiveLockStatusEnums.getActiveLockEnums(inventoryActiveLock.getStatus());
        if (statusEnums != ActiveLockStatusEnums.EFFECTIVE_STATUS) {
            return ApiResult.error("锁库活动未生效, 锁库编码:" + inventoryActiveLock.getBillNo());
        }
        ActiveLockAuditStatusEnums auditStatusEnums = ActiveLockAuditStatusEnums.getActiveLockAuditStatusEnums(inventoryActiveLock.getIsApprove());
        if (auditStatusEnums != ActiveLockAuditStatusEnums.AUDIT_SUCCESS) {
            return ApiResult.error("锁库活动未审批成功, 锁库编码:" + inventoryActiveLock.getBillNo());
        }
        String billNo = inventoryActiveLock.getBillNo();



        List<InventoryActiveLockDtlOccupied> occupiedDtlList = inventoryActiveLockDtlManager.selectByInventoryActiveLockDtlQuery(
                InventoryActiveLockDtlQuery.builder()
                        .billNo(billNo)
                        .isContainerOccupiedQty(Objects.equals(inventoryActiveLock.getSourcePlatform(), BrandMerchantCodeEnums.PUSFS.getCode()))
                        .channelNo(BrandMerchantCodeEnums.PUSFS.getCode())
                        .syncStatus(Lists.newArrayList(AdjustDtlSyncStatusEnums.NOT_SYNC.getValue(), AdjustDtlSyncStatusEnums.SYNC_FAIL.getValue()))
                        .build());


        Assert.notEmpty(occupiedDtlList, "可操作锁库明细为空, 锁库编码:" + billNo);
        try {
            this.manager.syncInventory(inventoryActiveLock, occupiedDtlList);
            InventoryActiveLockLogHelper.log(Tuples.of(inventoryActiveLock.getBillNo(), inventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.SYNC, "活动锁库同步库存成功");
        } catch (Exception e) {
            InventoryActiveLockLogHelper.log(Tuples.of(inventoryActiveLock.getBillNo(), inventoryActiveLock.getActiveName()), InventoryActiveLockOpscodeEnum.SYNC, "活动锁库同步库存失败");
            throw new RuntimeException(e);
        }
        return ApiResult.ok();
    }

    private String writeToExcel(List<Tuple6<String, String, String, String, String, String>> errors) {
        if (CollectionUtils.isEmpty(errors)) {
            return null;
        }
        ExcelColumn[][] queryExcel = new ExcelColumn[][]{
                new ExcelColumn[]{
                        new ExcelColumn("机构", "t1"),
                        new ExcelColumn("货管", "t2"),
                        new ExcelColumn("商品", "t3"),
                        new ExcelColumn("品牌", "t4"),
                        new ExcelColumn("尺码", "t5"),
                        new ExcelColumn("错误", "t6"),
                }
        };

        Function1<Object, Object> function1 = o -> o;
        ExportTask<Tuple6<String, String, String, String, String, String>> task = new ExportTask(
                ExcelExportOperations.builder()
                        .headers(queryExcel)
                        .fieldFormater("1", o -> {
                            if (o instanceof Tuple6) {
                                return ((Tuple6<?, ?, ?, ?, ?, ?>) o).getT1();
                            }
                            return o;
                        })
                        .fieldFormater("2", o -> {
                            if (o instanceof Tuple6) {
                                return ((Tuple6<?, ?, ?, ?, ?, ?>) o).getT2();
                            }
                            return o;
                        })

                        .fieldFormater("3", o -> {
                            if (o instanceof Tuple6) {
                                return ((Tuple6<?, ?, ?, ?, ?, ?>) o).getT3();
                            }
                            return o;
                        })

                        .fieldFormater("4", o -> {
                            if (o instanceof Tuple6) {
                                return ((Tuple6<?, ?, ?, ?, ?, ?>) o).getT4();
                            }
                            return o;
                        })

                        .fieldFormater("5", o -> {
                            if (o instanceof Tuple6) {
                                return ((Tuple6<?, ?, ?, ?, ?, ?>) o).getT5();
                            }
                            return o;
                        })

                        .fieldFormater("6", o -> {
                            if (o instanceof Tuple6) {
                                return ((Tuple6<?, ?, ?, ?, ?, ?>) o).getT6();
                            }
                            return o;
                        })

                        .value(), function1);


        return task.export("错误信息", tuple6IEntryResultHandler -> {
            for (Tuple6<String, String, String, String, String, String> error : errors) {
                tuple6IEntryResultHandler.handleResult(error);
            }
        }, false).getTicket();
    }


    private List<Tuple6<String, String, String, String, String, String>> validaAuditData(InventoryActiveLock inventoryActiveLock,
                                                                                         List<InventoryActiveLockDtl> oriDtlList) throws ManagerException {

        List<Tuple6<String, String, String, String, String, String>> errors = new ArrayList<>();

        try {
            // 当前活动明细
            List<Tuple6<String, String, String, String, String, String>> oriSkuList = oriDtlList.stream().map(item -> Tuples.of(item.getStoreNo(), item.getOrderUnitNo(), item.getItemCode(), item.getBrandNo(), item.getSizeNo(), "")).distinct().collect(Collectors.toList());

            Map<String, Object> params = new HashMap<>();

            params.put("filterStartDate", DateUtil.format(new Date(), DateUtil.LONG_DATE_FORMAT));
            params.put("filterEndDate", DateUtil.format(inventoryActiveLock.getEndTime(), DateUtil.LONG_DATE_FORMAT));
            params.put("vstoreCode", inventoryActiveLock.getVstoreCode());
            params.put("filterBillNo", inventoryActiveLock.getBillNo());
            params.put("status", ActiveLockStatusEnums.EFFECTIVE_STATUS.getValue());
            params.put("sourcePlatform", inventoryActiveLock.getSourcePlatform());
            params.put("shopNo", inventoryActiveLock.getShopNo());
            // 查询当前虚仓下的审核通过的活动, 有效的锁库活动
            List<InventoryActiveLock> ialList = this.manager.selectByParams(QueryUtil.mapToQuery(params));
            if (!CollectionUtils.isEmpty(ialList)) {

                for (InventoryActiveLock ial : ialList) {

                    Map<String, Object> parameters = new HashMap<>();
                    parameters.put("billNo", ial.getBillNo());
                    List<InventoryActiveLockDtl> dtlList = inventoryActiveLockDtlManager.selectByParams(QueryUtil.mapToQuery(parameters));

                    List<Tuple6<String, String, String, String, String, String>> tarSkuList = dtlList.stream().map(item ->
                                    Tuples.of(item.getStoreNo(), item.getOrderUnitNo(), item.getItemCode(), item.getBrandNo(), item.getSizeNo(), ""))
                            .distinct().collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(tarSkuList)) {
                        continue;
                    }
                    // 如果其他活动明细包含当前活动明细的商品, 则校验不通过
                    List<Tuple6<String, String, String, String, String, String>> intersection = oriSkuList.stream().filter(tarSkuList::contains).collect(Collectors.toList());
                    if (!org.springframework.util.CollectionUtils.isEmpty(intersection)) {
                        intersection.forEach(sku -> {
                            errors.add(sku.mapT6(it -> String.format("商品已存在于有效的活动锁库单，[%s]，不允许重复锁库", ial.getBillNo())));
                        });
                    }
                    intersection.clear();
                }
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(errors)) {
                //return errors;
                return errors;
            }
            params.clear();
            params.put("sourcePlatform", inventoryActiveLock.getSourcePlatform());
            params.put("terminal", orderSourceTerminalConfigManager.getByLazyMerchantCode(inventoryActiveLock.getSourcePlatform()).get().map(OrderSourceTerminalConfig::getTerminal).orElse(null));
            params.put("merchantsCode", inventoryActiveLock.getSourcePlatform());

            // 根据机构货管分组sku
            Map<Tuple2<String, String>, List<InventoryActiveLockDtl>> storeNoOrderUnitSkuMapList = oriDtlList.stream()
                    .collect(Collectors.groupingBy(s -> Tuples.of(s.getStoreNo(), s.getOrderUnitNo())));

            storeNoOrderUnitSkuMapList.forEach((k, v) -> {
                params.put("storeNo", k.getT1());
                params.put("orderUnitNo",  k.getT2());


                List<InternetAvailableInventory> inventoryList = Lists.partition(v.stream().map(InventoryActiveLockDtl::getSkuNo).distinct().collect(Collectors.toList()), 1000)
                        .parallelStream().map(
                                skuNoList -> {
                                    params.put("skuNoList", skuNoList);
                                    return inventorySearchService.findAvailableInOneStore(params);
                                }
                        ).flatMap(Collection::stream).collect(Collectors.toList());

                for (InventoryActiveLockDtl inventoryActiveLockDtl : v) {

                    InternetAvailableInventory find = inventoryList.stream().filter(s -> s.getSkuNo().equals(inventoryActiveLockDtl.getSkuNo())).findFirst().orElse(null);
                    if (find == null) {
                        errors.add(Tuples.of(inventoryActiveLockDtl.getStoreNo(),
                                inventoryActiveLockDtl.getOrderUnitNo(),
                                inventoryActiveLockDtl.getItemCode(),
                                inventoryActiveLockDtl.getBrandNo(),
                                inventoryActiveLockDtl.getSizeNo(),
                                "查询不到可用库存"));
                        continue;
                    }

                    if (find.getAvailableQty() < inventoryActiveLockDtl.getLockQty()) {
                        errors.add(Tuples.of(inventoryActiveLockDtl.getStoreNo(),
                                inventoryActiveLockDtl.getOrderUnitNo(),
                                inventoryActiveLockDtl.getItemCode(),
                                inventoryActiveLockDtl.getBrandNo(),
                                inventoryActiveLockDtl.getSizeNo(),
                                String.format("库存不足,可用库存：%s，活动锁库库存：%s", find.getAvailableQty(), inventoryActiveLockDtl.getLockQty())));
                    }

                }
            });


        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new ManagerException(e);
        }
        return errors;
    }


    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {
        return ExcelExportOperations.builder()
                .fieldFormater("isApprove", (a) -> {
                    if (a instanceof Integer) {
                        ActiveLockAuditStatusEnums activeLockAuditStatusEnums = ActiveLockAuditStatusEnums.getActiveLockAuditStatusEnums((Integer) a);
                        return activeLockAuditStatusEnums == null ? a : activeLockAuditStatusEnums.getDesc();
                    }
                    return a;
                })
                .fieldFormater("status", (a) -> {
                    if (a instanceof Integer) {
                        ActiveLockStatusEnums activeLockStatusEnums = ActiveLockStatusEnums.getActiveLockEnums((Integer) a);
                        return activeLockStatusEnums == null ? a : activeLockStatusEnums.getDesc();
                    }
                    return a;
                })
                .headers(columns)
                .value();
    }
}