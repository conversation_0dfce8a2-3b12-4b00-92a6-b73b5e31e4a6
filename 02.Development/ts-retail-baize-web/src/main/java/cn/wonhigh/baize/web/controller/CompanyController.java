/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.wonhigh.baize.manager.gms.ICompanyManager;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;

import org.springframework.web.bind.annotation.RequestMapping;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.model.entity.gms.Company;



@RestController

@RequestMapping("/company")
public class CompanyController extends ApiController<Company,Integer> {
    @Autowired
    private ICompanyManager manager;

    protected IManager<Company,Integer> getManager(){
        return manager;
    }

    
}