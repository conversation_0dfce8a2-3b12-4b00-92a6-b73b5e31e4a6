/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.business.active.adjust.ActiveLockAdjustProcess;
import cn.wonhigh.baize.manager.gms.IIcsActiveLockAdjustDtlManager;
import cn.wonhigh.baize.manager.gms.IIcsActiveLockAdjustManager;
import cn.wonhigh.baize.model.entity.gms.*;
import cn.wonhigh.baize.model.enums.ActiveLockStatusEnums;
import cn.wonhigh.baize.model.enums.AdjustDtlSyncStatusEnums;
import cn.wonhigh.baize.model.enums.AdjustStatusEnums;

import cn.wonhigh.baize.model.enums.OcsOrderSourceConfigChannelTypeEnum;
import cn.wonhigh.baize.utils.common.QueryUtil;
import com.alibaba.excel.EasyExcel;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;

import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 锁库调整明细
 */
@RestController
@RequestMapping("/ics/active/lock/adjust/dtl")
public class IcsActiveLockAdjustDtlController extends ApiController<IcsActiveLockAdjustDtl,String> {
    private static final Logger LOGGER = LoggerFactory.getLogger(IcsActiveLockAdjustDtlController.class);

    @Autowired
    private IIcsActiveLockAdjustDtlManager manager;
    @Autowired
    private IIcsActiveLockAdjustManager icsActiveLockAdjustManager;

    protected IManager<IcsActiveLockAdjustDtl,String> getManager(){
        return manager;
    }
    
    /**
     * 锁库调整明细更新
     */
    @Override
	public ApiResult<IcsActiveLockAdjustDtl> update(IcsActiveLockAdjustDtl dtl) throws JsonManagerException {
    	String billNo = dtl.getBillNo();
    	if (StringUtils.isBlank(billNo)) {
    		return ApiResult.error("锁库活动调整单号不能为空");
		}
    	IcsActiveLockAdjust adjust = icsActiveLockAdjustManager.findByUnique(billNo);
    	AdjustStatusEnums adjustStatusEnum = AdjustStatusEnums.getAdjustStatusEnums(adjust.getAdjustStatus());
    	if (!AdjustStatusEnums.isUpdateOperable(adjustStatusEnum)) {
    		return ApiResult.error(String.format("调整状态为%s, 不允许做更新操作", adjustStatusEnum.getDesc()));
		}
		return super.update(dtl);
	}

	/**
     * 关联锁库活动明细列表
     * @param query
     * @param page
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.GET, RequestMethod.POST},
            value = {"/merge/list"}
    )
    public ApiResult<PageResult<?>> mergeList(Query query, Pagenation page) {
        PageResult<IcsActiveLockAdjustDtl> result = new PageResult<>(null, 0);
        String billNo = query.findValue("billNo");
        if (StrUtil.isBlank(billNo)) {
            return ApiResult.ok(result);
        }
        int count = manager.selectCount(query);
        if (count > 0) {
            result.setTotal((long) count);
            List<IcsActiveLockAdjustDtl> list = manager.selectActiveDtlList(query.asMap(), page);
            result.setRows(list);
        }
        return ApiResult.ok(result);
    }

    /**
     * 校验锁库活动明细列表
     * @param query
     * @return
     */
    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.POST},
            value = {"/check"}
    )
    public ApiResult<List<IcsActiveLockAdjustDtl>> check(Query query) {
        String billNo = query.findValue("billNo");
        if (StrUtil.isBlank(billNo)) {
            return ApiResult.error("锁库活动调整单号不能为空");
        }
        ActiveLockAdjustProcess process = icsActiveLockAdjustManager.getProcess(billNo);
        List<IcsActiveLockAdjustDtl> list = process.check();
        return ApiResult.ok(list);
    }

    @ResponseBody
    @RequestMapping(
            method =  RequestMethod.POST,
            value = {"/import"}
    )
    public ApiResult<?> importData(Query query, HttpServletRequest request) {
        String billNo = query.findValue("billNo");
        if (StrUtil.isBlank(billNo)) {
            return ApiResult.error("锁库活动调整单号不能为空");
        }
        StandardMultipartHttpServletRequest  multipartRequest = null;
        if (request instanceof StandardMultipartHttpServletRequest) {
            multipartRequest = (StandardMultipartHttpServletRequest) request;
        }
        if (multipartRequest == null) {
            return ApiResult.error("File is not found");
        }
        MultipartFile multipartFile = multipartRequest.getFile("excelFile");
        //读取数据
        List<Object> objectList = readExcelData(multipartFile);
        if (objectList == null || objectList.isEmpty()) {
            return ApiResult.error("Excel数据为空");
        }
        ActiveLockAdjustProcess process = icsActiveLockAdjustManager.getProcess(billNo);
        process.importDtl(objectList);
        return ApiResult.ok();
    }

    @Override
    public ApiResult<IcsActiveLockAdjustDtl> create(IcsActiveLockAdjustDtl entry) {
        String billNo = entry.getBillNo();
        if (StrUtil.isBlank(billNo)) {
            return ApiResult.error("锁库活动调整单号不能为空");
        }
        ActiveLockAdjustProcess process = icsActiveLockAdjustManager.getProcess(billNo);
        process.saveDtl(entry);
        return ApiResult.ok(entry);
    }

    private List<Object> readExcelData(MultipartFile multipartFile) {
        List<Object> objectList = Collections.emptyList();
        try {
            objectList = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .headRowNumber(1)
                    .ignoreEmptyRow(true)
                    .autoCloseStream(true).doReadAllSync();
        } catch (IOException e) {
            LOGGER.error("Excel 读取失败", e);
        }
        return objectList;
    }

    @Override
    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {
        return ExcelExportOperations.builder().fieldFormater("syncStatus", (a) -> {
            if (a instanceof Integer) {
                AdjustDtlSyncStatusEnums statusEnums = AdjustDtlSyncStatusEnums.getAdjustDtlSyncStatusEnums((Integer) a);
                return statusEnums == null ? a : statusEnums.getDesc();
            }
            return a;
        }).headers(columns).value();
    }
}