/** by zkh **/
package cn.wonhigh.baize.web.controller;

import cn.wonhigh.baize.manager.gms.IZoneInfoManager;
import cn.wonhigh.baize.model.entity.gms.ZoneInfo;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;

import org.springframework.web.bind.annotation.RequestMapping;
import cn.mercury.manager.IManager;

@RestController

@RequestMapping("/zone/info")
public class ZoneInfoController extends ApiController<ZoneInfo,Integer> {
    @Autowired
    private IZoneInfoManager manager;

    protected IManager<ZoneInfo,Integer> getManager(){
        return manager;
    }

    
}