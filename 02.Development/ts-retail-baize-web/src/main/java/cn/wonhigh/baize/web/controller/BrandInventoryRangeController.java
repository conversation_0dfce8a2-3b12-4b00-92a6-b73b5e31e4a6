/**
 * by
 **/
package cn.wonhigh.baize.web.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.IBrandInventoryRangeManager;
import cn.wonhigh.baize.manager.gms.IBrandManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.manager.gms.ILookupEntryManager;
import cn.wonhigh.baize.model.dto.brandInventoryRange.BrandInventoryRangeSaveDto;
import cn.wonhigh.baize.model.entity.gms.Brand;
import cn.wonhigh.baize.model.entity.gms.BrandInventoryRange;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.LookupEntry;
import cn.wonhigh.baize.model.enums.BrandInventoryChannelEnum;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.utils.common.QueryUtil;
import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;
import topmall.framework.security.Authorization;
import topmall.framework.security.User;
import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/brand/inventory/range")
public class BrandInventoryRangeController extends ApiController<BrandInventoryRange, String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandInventoryRangeController.class);

    @Autowired
    private IBrandInventoryRangeManager manager;

    @Autowired
    private IBrandManager gsmBrandManager;

    @Autowired
    private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;

    @Autowired
    private ILookupEntryManager lookupEntryManager;

    @Reference
    private AuthorityUserDataApi authorityUserDataApi;

    @Autowired
    private RedisTemplate<String, String> redisStringTemplate;

    protected IManager<BrandInventoryRange, String> getManager() {
        return manager;
    }


    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/createData")
    public ApiResult<BrandInventoryRange> createData(BrandInventoryRangeSaveDto entry) {

        if (entry.getBrandNo() == null) {
            return ApiResult.error("品牌不能为空");
        }



        // 校验新增数据是否存在
        Brand brand = gsmBrandManager.findByUnique(entry.getBrandNo());
        if (brand == null) {
            return ApiResult.error("品牌不存在");
        }

        entry.setBrandName(brand.getName());

        String organTypeNo = brand.getOrganTypeNo();

        List<InternetVirtualWarehouseInfo> internetVirtualWarehouseInfoList = internetVirtualWarehouseInfoManager.selectByParams(
                Query.Where("organTypeNo", organTypeNo).and("vstoreCode", entry.getBussinessType())
        );
        if (internetVirtualWarehouseInfoList == null || internetVirtualWarehouseInfoList.isEmpty()) {
            return ApiResult.error("聚合仓数据和品牌不匹配");
        }



        BrandInventoryRange newData = new BrandInventoryRange();
        newData.setBussinessType(entry.getBussinessType());
        newData.setBrandNo(entry.getBrandNo());
        entry.setChannel(BrandInventoryChannelEnum.getChannelByInterfacePlatform(internetVirtualWarehouseInfoList.get(0).getInterfacePlatform()));
        newData.setChannel(entry.getChannel());

        // 判断品季年是否已经存在

        BrandInventoryRange query = new BrandInventoryRange();
        query.setBussinessType(entry.getBussinessType());
        query.setBrandNo(entry.getBrandNo());
        //query.setPurchaseSeason(season);
        //query.setYears(year);

        Integer existData = this.manager.selectCount(QueryUtil.mapToQuery(Dict.parse(query)
                .set("yearsList", entry.getYears())
                .set("purchaseSeasonList", entry.getPurchaseSeason())
        ));

        if (existData != null && existData > 0) {
            return ApiResult.error("品季年数据已存在");
        }



        String username = ObjectUtil.defaultIfNull(Authorization.getUser(), new User()).getName();

        for (String year : entry.getYears()) {
            for (String season : entry.getPurchaseSeason()) {
                newData.setId(IdUtil.simpleUUID());
                newData.setPurchaseSeason(season);
                newData.setYears(year);

                newData.setSharingRatio(entry.getSharingRatio());
                newData.setSafetyStock(entry.getSafetyStock());
                newData.setBrandName(entry.getBrandName());

                super.create(newData);

                String yearName = redisStringTemplate.opsForValue().get("ISPWEB"+year);
                String purchaseSeasonName = redisStringTemplate.opsForValue().get("ISPWEB"+season);
                if(StrUtil.isEmpty(yearName))
                    yearName= year;
                if(StrUtil.isEmpty(purchaseSeasonName))
                    purchaseSeasonName= season;

                saveLog("insert","新增", username, newData.getBussinessType(), newData.getBrandNo(), purchaseSeasonName,
                        yearName, newData.getSafetyStock() + "", newData.getSharingRatio() + "");

            }
        }

        return ApiResult.ok(null);
    }


    @Override
    public ApiResult<Integer> deleteByPrimaryKeys(String ids) {
        return ApiResult.error("不能删除品季年配置!");
    }

    @Override
    public ApiResult<Integer> deleteByParams(Query query) {
        return ApiResult.error("不能删除品季年配置!");
    }

    @Override
    public ApiResult<Integer> deleteByPrimaryKey(String id) {
        return ApiResult.error("不能删除品季年配置!");
    }

    @Override
    public ApiResult<BrandInventoryRange> update(BrandInventoryRange entry) throws JsonManagerException {

        BrandInventoryRange exists = manager.findByPrimaryKey(entry.getId());
        ApiResult<BrandInventoryRange> result = null;
        if (exists != null) {
            result = super.update(entry);
            String username = ObjectUtil.defaultIfNull(Authorization.getUser(), new User()).getName();

            saveLog("update","修改", username, exists.getBussinessType(), exists.getBrandNo(), entry.getPurchaseSeasonName(),
                    entry.getYearsName(), entry.getSafetyStock() + "", entry.getSharingRatio() + "");

        }

        return result;
    }

    @RequestMapping("/import")
    public ApiResult<?> importData(HttpServletRequest request) {

        StandardMultipartHttpServletRequest multipartHttpServletRequest = null;
        if (request instanceof StandardMultipartHttpServletRequest) {
            multipartHttpServletRequest = (StandardMultipartHttpServletRequest) request;
        }


        if (multipartHttpServletRequest == null) {
            return ApiResult.error("File is not found");
        }

        MultipartFile multipartFile = multipartHttpServletRequest.getFile("excelFile");
        if (multipartFile == null) {
            return ApiResult.error("File is not found");
        }

        // 读取数据
        List<Object> objectList = readExcelData(multipartFile);
        if (objectList == null || objectList.isEmpty()) {
            return ApiResult.error("Excel数据为空");
        }

        String updateUser = Authorization.getUser().getName();


        //List<BrandInventoryRange> brandInventoryRangeList = Collections.emptyList();


        final Map<Tuple3<String, String, String>, List<LookupEntry>> brandYearLookupEntryMap = Maps.newHashMap();
        final Map<Tuple3<String, String, String>, List<LookupEntry>> brandSeasonLookupEntryMap = Maps.newHashMap();


        Set<String> brandNoSet = objectList.stream().map(it -> Objects.toString(MapUtils.getString((Map)it, 1, "")))
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());
        brandNoSet.forEach(brandNo -> {
                    List<Brand> brands = gsmBrandManager.selectByUniques(Lists.newArrayList(brandNo));
            if (brands != null && !brands.isEmpty()) {
                if (!brandYearLookupEntryMap.containsKey(brands.get(0).getBrandNo())) {
                    List<LookupEntry> yearLookupEntryList = lookupEntryManager.selectByParams(Query.Where("lookupId", "5").and("organTypeNo", brands.get(0).getBelonger()));
                    brandYearLookupEntryMap.put(Tuples.of(brands.get(0).getBrandNo() , brands.get(0).getBelonger(), brands.get(0).getName()), yearLookupEntryList);
                }
                if (!brandSeasonLookupEntryMap.containsKey(brands.get(0).getBrandNo())) {
                    List<LookupEntry> seasonLookupEntryList = lookupEntryManager.selectByParams(Query.Where("lookupId", "6").and("organTypeNo", brands.get(0).getBelonger()));
                    brandSeasonLookupEntryMap.put(Tuples.of(brands.get(0).getBrandNo(), brands.get(0).getBelonger(), brands.get(0).getName()), seasonLookupEntryList);
                }
            }
        });

        StringBuilder errorMsg = new StringBuilder();
        int rowNum = 1;
        // 校验数据
        for (int i = 0; i < objectList.size(); i++) {

            rowNum++;

            LinkedHashMap map = (LinkedHashMap) objectList.get(i);
            // 虚仓编码
            String vstoreCode =  MapUtils.getString(map, 0);
            // 品牌
            String brandNo =  MapUtils.getString(map, 1);
            // 年份
            String yearsName =  MapUtils.getString(map, 2);
            // 季节
            String seasonName =  MapUtils.getString(map, 3);
            //共享比率
            String sharingRatio =  MapUtils.getString(map, 4);
            // 安全库存
            String safeStock =  MapUtils.getString(map, 5);
            // 是否触发增量同步
            String isIncrementalSync =  MapUtils.getString(map, 6);

            if (vstoreCode == null || brandNo == null || yearsName == null || seasonName == null || sharingRatio == null || safeStock == null) {
                errorMsg.append(",").append("第" + rowNum + "行数据不正确");
                continue;
            }

            try {
                double sharesRatioDouble = Double.parseDouble(sharingRatio);
                double safeStockDouble = Double.parseDouble(safeStock);

                if (sharesRatioDouble < 0 || sharesRatioDouble > 100 || safeStockDouble < 0) {
                    errorMsg.append(",").append("第" + rowNum + "共享比率或安全库存不正确");
                    continue;
                }
            }catch (Exception e) {
            }


            Tuple3<String, String,String> brandNoAndOrganTypeNoYear = brandYearLookupEntryMap.keySet().stream().filter(it -> StrUtil.equals(it.getT1(), brandNo)).findFirst().orElse(null);
            Tuple3<String, String,String> brandNoAndOrganTypeNoSeason = brandSeasonLookupEntryMap.keySet().stream().filter(it -> StrUtil.equals(it.getT1(), brandNo)).findFirst().orElse(null);

            if (brandNoAndOrganTypeNoYear == null || brandNoAndOrganTypeNoSeason == null) {
                errorMsg.append(",").append("第" + rowNum + "行品牌数据不正确");
                continue;
            }

            List<LookupEntry> yearLookupEntryList = brandYearLookupEntryMap.get(brandNoAndOrganTypeNoYear);
            List<LookupEntry> seasonLookupEntryList = brandSeasonLookupEntryMap.get(brandNoAndOrganTypeNoSeason);
            if (yearLookupEntryList == null || seasonLookupEntryList == null) {
                errorMsg.append(",").append("第" + rowNum + "行品牌,组织对应年份季节数据不正确");
                continue;
            }

            String yearCode = yearLookupEntryList.stream().filter(it -> StrUtil.equals(it.getName(), yearsName)).map(LookupEntry::getCode).findFirst().orElse(null);
            String seasonCode = seasonLookupEntryList.stream().filter(it -> StrUtil.equals(it.getName(), seasonName)).map(LookupEntry::getCode).findFirst().orElse(null);

            if (yearCode == null || seasonCode == null) {
                errorMsg.append(",").append("第" + rowNum + "行品牌,组织对应年份季节数据不正确");
                continue;
            }



            Map<String, Object> params = Maps.newHashMap();
            params.put("organTypeNo", brandNoAndOrganTypeNoSeason.getT2());
            params.put("vstoreCode", vstoreCode);
            List<InternetVirtualWarehouseInfo> infos = internetVirtualWarehouseInfoManager.selectByParams(QueryUtil.mapToQuery(params));

            if (infos == null || infos.isEmpty()) {
                errorMsg.append(",").append("第" + rowNum + "行聚合仓编码不存在");
                continue;
            }


            Map<String, Object> paramMaps = new HashMap<>();
            paramMaps.put("brandNo", brandNo);
            paramMaps.put("years", yearCode);
            paramMaps.put("purchaseSeason", seasonCode);
            paramMaps.put("bussinessTypeExport", vstoreCode);
            // 校验是否存在相同商品相同跟新不相同
            int countBss = manager.selectShareNoExist(paramMaps);

            if (countBss > 0) {
                // 存在则更新SafetyStock
                paramMaps.put("sharingRatio", sharingRatio);
                paramMaps.put("safetystock", safeStock);
                paramMaps.put("updateUser",updateUser);
                manager.updateShare(paramMaps);

                saveLog("update","导入", updateUser, vstoreCode, brandNo, seasonName, yearsName, safeStock, sharingRatio);
            } else {
                try {
                    // 安全转换字符串为整数
                    int sharingRatioInt = Integer.parseInt(sharingRatio);
                    int safeStockInt = Integer.parseInt(safeStock);

                    BrandInventoryRange brandInventoryRange = new BrandInventoryRange();
                    brandInventoryRange.setId(IdUtil.simpleUUID());
                    brandInventoryRange.setBussinessType(vstoreCode);
                    brandInventoryRange.setBrandNo(brandNo);
                    brandInventoryRange.setBrandName(brandNoAndOrganTypeNoYear.getT3());
                    brandInventoryRange.setPurchaseSeason(seasonCode);
                    brandInventoryRange.setYears(yearCode);
                    brandInventoryRange.setSharingRatio(sharingRatioInt);
                    brandInventoryRange.setSafetyStock(safeStockInt);
                    brandInventoryRange.setUpdateTime(new Date());
                    brandInventoryRange.setUpdateUser(updateUser);
                    brandInventoryRange.setCreateTime(new Date());
                    brandInventoryRange.setChannel(BrandInventoryChannelEnum.getChannelByInterfacePlatform(infos.get(0).getInterfacePlatform()));
                    brandInventoryRange.setCreateUser(updateUser);
                    manager.insert(brandInventoryRange);

                    saveLog("insert", "导入", updateUser, vstoreCode, brandNo, seasonName, yearsName, safeStock, sharingRatio);
                    // 进行其他处理...
                } catch (NumberFormatException e) {
                    errorMsg.append(",").append("第" + (rowNum) + "行数据格式错误");
                }
            }
        }
        if (errorMsg.length() > 0) {
            return ApiResult.error(errorMsg.toString());
        }

        return ApiResult.ok();
    }

    private List<Object> readExcelData(MultipartFile multipartFile) {
        List<Object> objectList = Collections.emptyList();
        try {
            objectList = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .headRowNumber(1)
                    .autoCloseStream(true).doReadAllSync();
        } catch (IOException e) {
            LOGGER.error("Excel 读取失败", e);
        }
        return objectList;
    }

    void saveLog(String operate,String operateName, String updateUser, String vstoreCode, String brandNo,
                 String seasonName, String yearsName,String safeStock, String sharingRatio){
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                        .setOpscode(operate)
                        .setSyscode(String.valueOf(MenuTypeEnums.BRANDINVENTORYRANG.getType()))
                        .setSysname(MenuTypeEnums.BRANDINVENTORYRANG.getDesc())
                        .setKeyword1(vstoreCode)
                        .setKeyword1info("聚合仓编码:"+ vstoreCode)
                        .setKeyword2(brandNo)
                        .setKeyword2info("品牌:" + brandNo)
                        .setCreateUser(updateUser)
                        .setCreateTime(new Date())
                        .setRemark(String.format("%s：年份:%s;季节:%s;安全库存:%s;共享比例:%s;" ,
                                operateName, yearsName, seasonName, safeStock, sharingRatio+"%"))
                        .build()));
    }
}