/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.mercury.annotation.JsonVariable;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.domain.Tupe;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExportTask;
import cn.mercury.manager.IEntryResultHandler;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.manager.gms.IIcsShopCreditScoreManager;
import cn.wonhigh.baize.model.entity.gms.IcsShopCreditScore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.core.ApplicationContext;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RestController

@RequestMapping("/ics/shop/credit/score")
public class IcsShopCreditScoreController extends ApiController<IcsShopCreditScore,String> {
    @Autowired
    private IIcsShopCreditScoreManager manager;

    protected IManager<IcsShopCreditScore,String> getManager(){
        return manager;
    }

    @Override
    public ApiResult<PageResult<IcsShopCreditScore>> selectByPage(Query query, Pagenation page) {
        String brandNo = query.findValue("brandNo");
        if (StringUtils.isNotBlank(brandNo)) {
            query = query.and("brandNo", brandNo);
        }
        Map<String, Object> params = query.asMap();
        int count = manager.selectShopCreditScoreByCount(params);
        if (count == 0) {
            return ApiResult.ok(new PageResult<>(new ArrayList<>(), 0));
        }
        List<IcsShopCreditScore> list = manager.selectShopCreditScoreByPage(params, page);
        return ApiResult.ok(new PageResult<>(list, count));
    }

    @Override
    protected void excuteExportQuery(Query query, IEntryResultHandler<IcsShopCreditScore> handler) {
        manager.selectShopCreditScoreByParams(query.asMap(), handler);
    }

    @Override
    public ApiResult<Tupe<String, Integer>> export(Query query, String fileName, @JsonVariable("_columns") ExcelColumn[][] columns, HttpServletResponse response) {
        Integer count = manager.selectShopCreditScoreByCount(query.asMap());
        if (query.getPagenation() == null) {
            query.setPagenation(new Pagenation(1, Integer.MAX_VALUE));
        }
        query.getPagenation().setTotal((long)count);
        ExportTask<IcsShopCreditScore> task = new ExportTask(this.getExportOperation(columns), this.getRowHander(query));
        task.finished = (e) -> ApplicationContext.current().clear();
        String ticket = task.exportSync(fileName, (handler) -> this.excuteExportQuery(query, handler)).getTicket();
        return ApiResult.ok(new Tupe<String, Integer>(ticket, count));
    }
}