package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.manager.gms.IShopManager;
import cn.wonhigh.baize.model.entity.gms.Shop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/shop")
public class ShopController extends ApiController<Shop,Integer> {

    @Autowired
    private IShopManager manager;

    protected IManager<Shop,Integer> getManager(){
        return manager;
    }

    @GetMapping("/shopAndCompany")
    public ApiResult<?> shopAndCompany(Query query) {
        return ApiResult.ok(manager.selectShopAndCompany(query.asMap()));
    }


    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.GET, RequestMethod.POST},
            value = {"/listShop"}
    )
    public ApiResult<PageResult<Map<String, String>>> list(Query query, Pagenation page) {
        List<Map<String, String>> list = new ArrayList<>();
        query.orderby("update_time", Boolean.TRUE);
        ApiResult<PageResult<Shop>> result = super.selectByPage(query, page);
        if(result.getData() != null && !CollectionUtils.isEmpty(result.getData().getRows())){
            for (Shop row : result.getData().getRows()) {
                Map<String, String> item = new HashMap<>();
                item.put("shopNo", row.getShopNo());
                item.put("shopName", row.getShortName());
                list.add(item);
            }
        }
        return ApiResult.ok(new PageResult(list, result.getData().getTotal()));
    }
}
