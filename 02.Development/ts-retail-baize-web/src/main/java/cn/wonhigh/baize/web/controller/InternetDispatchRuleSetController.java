package cn.wonhigh.baize.web.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.manager.IManager;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.mercury.utils.DateUtil;
import cn.wonhigh.baize.converter.InternetDispatchRuleSetConverter;
import cn.wonhigh.baize.events.dispatchrule.DispatchRuleSetEvent;
import cn.wonhigh.baize.events.dispatchrule.DispatchRuleSetMessage;
import cn.wonhigh.baize.manager.gms.IInternetDispatchRuleSetDtlManager;
import cn.wonhigh.baize.manager.gms.impl.InternetDispatchRuleSetManager;
import cn.wonhigh.baize.model.dto.dispatchruleset.DispatchRuleSetDto;
import cn.wonhigh.baize.model.dto.dispatchruleset.DispatchRuleSetSaveDto;
import cn.wonhigh.baize.model.dto.dispatchruleset.ExitsQueryDataDto;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSet;
import cn.wonhigh.baize.model.entity.gms.InternetDispatchRuleSetDtl;
import cn.wonhigh.baize.model.enums.InternetDispatchRuleSetEnum;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import topmall.framework.security.Authorization;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 派单规格设置
 * <AUTHOR>
 */
@RestController
@RequestMapping("/internetDispatchRuleSet")
public class InternetDispatchRuleSetController extends ApiController<InternetDispatchRuleSet, Long> {


    @Resource
    private InternetDispatchRuleSetManager manager;

    @Resource
    private IInternetDispatchRuleSetDtlManager dtlManager;

    @Resource
    private InternetDispatchRuleSetConverter converter;

    @Override
    protected IManager<InternetDispatchRuleSet, Long> getManager() {
        return manager;
    }



    @PostMapping("/enableState")
    public ApiResult<?> enableState (@RequestBody List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return ApiResult.error("参数不能为空");
        }
        String user = Optional.ofNullable(Authorization.getUser()).map(IUser::getAccount).orElse("unknown");
        manager.batchSave(null, ids.stream().map(id -> {
            InternetDispatchRuleSet upd = new InternetDispatchRuleSet();
            upd.setId(id);
            upd.setState(1);
            upd.setUpdateUser(user);
            upd.setUpdateTime(new Date());
            return  upd;
        }).collect(Collectors.toList()), null);

        statusEventPublish(ids, user, DispatchRuleSetMessage.OptTypeEnum.enable, "启用规则");
        return ApiResult.ok(null);
    }

    @PostMapping("/disableState")
    public ApiResult<?> disableState (@RequestBody List<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return ApiResult.error("参数不能为空");
        }
        String user = Optional.ofNullable(Authorization.getUser()).map(IUser::getAccount).orElse("unknown");
        manager.batchSave(null, ids.stream().map(id -> {
            InternetDispatchRuleSet upd = new InternetDispatchRuleSet();
            upd.setId(id);
            upd.setState(0);
            upd.setUpdateUser(user);
            upd.setUpdateTime(new Date());
            return  upd;
        }).collect(Collectors.toList()), null);


        statusEventPublish(ids, user, DispatchRuleSetMessage.OptTypeEnum.disable, "禁用规则");
        return ApiResult.ok(null);
    }

    private void statusEventPublish(List<Long> ids, String user, DispatchRuleSetMessage.OptTypeEnum disable, String message) {
        for (Long id : ids) {
            SpringContext.getContext().publishEvent(new DispatchRuleSetEvent(
                    new DispatchRuleSetMessage.Builder()
                            .setId(id)
                            .setOptUser(user)
                            .setOptTime(DateUtil.format(new Date(), DateUtil.LONG_DATE_FORMAT))
                            .setOptType(disable)
                            .setBeforeRuleName(ListUtil.toLinkedList(message))
                            .build()
            ));
        }
    }

    /**
     * 派单规则策略列表
     * @return 数据
     */
    @PostMapping("/priority/list")
    public ApiResult<?> dispatchRulePriorityData () {
        List<InternetDispatchRuleSetEnum> list = new ArrayList<>(Arrays.asList(InternetDispatchRuleSetEnum.values()));
        return ApiResult.ok(list.stream().filter(i -> i.getState()==1));
    }


    @PostMapping("/save")
    public ApiResult<?> save(@Validated @RequestBody DispatchRuleSetSaveDto dispatchRuleSetSaveDto , BindingResult result) {
        if (result.hasErrors()) {
            return ApiResult.error(result.getAllErrors().get(0).getDefaultMessage());
        }

        if (dispatchRuleSetSaveDto.getDtlList() != null) {
            dispatchRuleSetSaveDto.getDtlList().forEach(ite -> {
                if (StrUtil.equalsIgnoreCase(ite.getOrderSourceNo(), "all-shop")) {
                    ite.setOrderSourceNo("");
                    ite.setOrderSourceName("");
                    ite.setMoreShopFlag(1);
                }
            });
        }

        try {
            manager.save(dispatchRuleSetSaveDto);
        } catch (ManagerException e) {
            return ApiResult.error(e.getMessage());
        }
        return ApiResult.ok(null);
    }


    @GetMapping("/getOneById/{id}")
    public ApiResult<?> findId(@PathVariable Long id) {

        InternetDispatchRuleSet ruleSet = manager.findByPrimaryKey(id);
        if (ruleSet != null && StrUtil.isNotBlank(ruleSet.getRuleNo())) {
            InternetDispatchRuleSetDtl dtl = new InternetDispatchRuleSetDtl();
            List<InternetDispatchRuleSetDtl> dtlList =  dtlManager.selectByParams(dtl.build().ruleNo(ruleSet.getRuleNo()).asQuery());
            ruleSet.setDtlList(dtlList);
        }

        DispatchRuleSetDto dto = converter.entityToDto(ruleSet);

        if (dto == null) {
            dto = new DispatchRuleSetDto();
        }

        if (dto.getDtlList()!=null) {
            dto.getDtlList().forEach(ite -> {
                if (StrUtil.isBlank(ite.getOrderSourceNo()) && ite.getMoreShopFlag() == 1) {
                    ite.setOrderSourceNo("all-shop");
                    ite.setOrderSourceName("所有店铺");
                }
            });
        }

        return ApiResult.ok(dto);
    }

    @Override
    public ApiResult selectByPage(Query query, Pagenation page) {
        ApiResult<PageResult<InternetDispatchRuleSet>> pageResult= super.selectByPage(query, page);
        List<?>  dtos = Optional.ofNullable(pageResult.getData()).map(PageResult::getRows)
                .orElse(new ArrayList<>())
                .stream().map(converter::entityToDto).collect(Collectors.toList());
        return ApiResult.ok(new PageResult<>(dtos, pageResult.getData().getTotal()));
    }


    @PostMapping("/exitsData")
    public ApiResult<?> exitsData(@RequestBody ExitsQueryDataDto exitsQueryDataDto) {
        return ApiResult.ok(this.manager.exitsData(exitsQueryDataDto));
    }
}
