/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.hutool.core.lang.Dict;
import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.manager.gms.IBrandManager;
import cn.wonhigh.baize.model.entity.gms.Brand;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import topmall.framework.web.controller.ApiController;

import cn.mercury.manager.IManager;

import cn.wonhigh.baize.model.entity.gms.LookupEntry;
import cn.wonhigh.baize.manager.gms.ILookupEntryManager;
import topmall.framework.web.vo.ApiResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@RestController

@RequestMapping("/lookup/entry")
public class LookupEntryController extends ApiController<LookupEntry,String> {
    @Autowired
    private ILookupEntryManager manager;

    @Autowired
    private IBrandManager gmsBrandManager;

    @Autowired
    private RedisTemplate<String,String> redisStringTemplate;


    protected IManager<LookupEntry,String> getManager(){
        return manager;
    }



    @RequestMapping(value = "/lookupEntryData")
    @ResponseBody
    public ApiResult<?> brandAndSeasonData ( @RequestParam Map<String,Object> params) {

        if (params == null || params.isEmpty() || !params.containsKey("brandNo") || ObjectUtils.isEmpty(params.get("brandNo"))) {
            return ApiResult.ok(Dict.create().set("rows", new ArrayList<>()).set("total", 0));
        }

        List<Brand> brandList = gmsBrandManager.selectByParams(Query.Where("brandNo", params.get("brandNo")));
        if (brandList == null || brandList.isEmpty()) {
            return ApiResult.error("品牌编码未找到," + params.get("brandNo"));
        }

        List<LookupEntry> list = manager.selectByParams(Query.Where("lookupId", params.get("lookupId"))
                .and("organTypeNo", brandList.get(0).getOrganTypeNo()));
        if (list != null && !list.isEmpty()) {

            for (LookupEntry lookupEntry : list) {
                redisStringTemplate.opsForValue().set("ISPWEB"+lookupEntry.getCode(),lookupEntry.getName(), 60, TimeUnit.MINUTES);
            }
        }
        return ApiResult.ok(list);
    }
}