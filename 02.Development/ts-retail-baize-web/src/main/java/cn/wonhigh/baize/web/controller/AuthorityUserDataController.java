package cn.wonhigh.baize.web.controller;


import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.manager.gms.impl.OrderSourceTerminalConfigManager;
import cn.wonhigh.baize.model.dto.AuthorityUserDataDto;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.Item;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import cn.wonhigh.baize.service.gms.impl.ItemService;
import cn.wonhigh.baize.utils.common.PageUtil;
import cn.wonhigh.retail.uc.common.api.model.*;
import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.yougou.logistics.base.common.exception.ManagerException;
import com.yougou.logistics.base.common.exception.RpcException;
import io.swagger.annotations.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import topmall.framework.security.Authorization;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/authorityUserData")
@Api(value = "用户权限数据", tags = { "用户权限数据接口" })
public class AuthorityUserDataController {

	public static final Logger logger = org.slf4j.LoggerFactory.getLogger(AuthorityUserDataController.class);


	@Reference
	private AuthorityUserDataApi authorityUserDataApi;
	@Resource
	private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;

	@Autowired
	private ItemService itemService;

	@Autowired
	private OrderSourceTerminalConfigManager orderSourceTerminalConfigManager;

	@ApiOperation(value = "用户品牌权限接口")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@PostMapping("/brand")
	public ApiResult<PageResult<?>> brand(
			@ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto) {
		try {
			int userId = Integer.parseInt(Authorization.getUser().getId());
			List<AuthorityUserBrand> brandList = authorityUserDataApi.userBrand(userId);
			brandList = brandList.stream().sorted(Comparator.comparing(AuthorityUserBrand::getBrandDetailNo))
					.collect(Collectors.toList());
			if (StringUtils.isNotBlank(dto.getQ())) {
				brandList = brandList.stream()
						.filter(v -> v.getBrandDetailCname().contains(dto.getQ()) || v.getBrandDetailNo().contains(dto.getQ()))
						.collect(Collectors.toList());
			}
			return ApiResult.ok(PageUtil.startPage(brandList, dto.getPage(), dto.getRows()));
		} catch (Exception e) {
			logger.error("查询用户品牌权限出错", e);
			return ApiResult.error("查询用户品牌权限出错");
		}
	}

	@ApiOperation(value = "用户货管权限接口")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@PostMapping("/orderUnit")
	public ApiResult<PageResult<?>> orderUnit(
			@ApiParam(value = "查询参数", required = true) @RequestBody AuthorityUserDataDto dto) {
		try {
			int userId = Integer.parseInt(Authorization.getUser().getId());
			List<AuthorityUserOrderUnit> orderUnitList = authorityUserDataApi.userOrderUnit(userId);
			orderUnitList = orderUnitList.stream().sorted(Comparator.comparing(AuthorityUserOrderUnit::getOrderUnitNo))
					.collect(Collectors.toList());
			if (StringUtils.isNotBlank(dto.getQ())) {
				orderUnitList = orderUnitList.stream()
						.filter(v -> v.getOrderUnitName().contains(dto.getQ()) || v.getOrderUnitNo().contains(dto.getQ()))
						.collect(Collectors.toList());
			}
			return ApiResult.ok(PageUtil.startPage(orderUnitList, dto.getPage(), dto.getRows()));
		} catch (Exception e) {
			logger.error("查询用户货管权限出错", e);
			return ApiResult.error("查询用户品牌权限出错");
		}
	}
	
	@ApiOperation(value = "用户大区权限接口")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@PostMapping("/region")
	public ApiResult<List<AuthorityUserRegion>> region(
			@ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto) {
		try {
			Integer userId = Integer.valueOf(Authorization.getUser().getId());
			List<AuthorityUserRegion> regionList = authorityUserDataApi.userRegion(userId);
			regionList = regionList.stream().sorted(Comparator.comparing(AuthorityUserRegion::getRegionNo))
					.collect(Collectors.toList());
			if (StringUtils.isNotBlank(dto.getQ())) {
				regionList = regionList.stream()
						.filter(v -> v.getRegionNo().contains(dto.getQ()) || v.getRegionName().contains(dto.getQ()))
						.collect(Collectors.toList());
			}
			return ApiResult.ok(regionList);
		} catch (Exception e) {
			logger.error("查询用户大区权限出错", e);
			return ApiResult.error("查询用户大区权限出错");
		}
	}
	
	@ApiOperation(value = "用户机构权限接口(保护店铺和仓库)")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@RequestMapping("/store")
	@ResponseBody
	public ApiResult<PageResult<?>> store(
			@ApiParam(value = "查询参数", required = true) @RequestBody AuthorityUserDataDto dto) {
		try {
			if (dto.getPage() == null || dto.getPage() <= 0) {
				dto.setPage(1);
			}
			if (dto.getRows() == null) {
				dto.setRows(10);
			}

			int userId = Integer.parseInt(Authorization.getUser().getId());
			List<AuthorityUserStore> shopList = authorityUserDataApi.userStore(userId);
			List<AuthorityUserStorage> storeList = authorityUserDataApi.userStorage(userId);

			// 如果仓库 不为空 和 店铺不为空,则将其放入店铺列表中
			if (storeList != null && shopList != null) {
				for (AuthorityUserStorage authorityUserStorage : storeList) {
					AuthorityUserStore store = new AuthorityUserStore();
					store.setStoreNm(authorityUserStorage.getStorageName());
					store.setStoreNo(authorityUserStorage.getStorageNo());
					shopList.add(store);
				}
			}

			shopList = Optional.ofNullable(shopList).orElse(new ArrayList<>()).stream().sorted(Comparator.comparing(AuthorityUserStore::getStoreNo))
					.collect(Collectors.toList());
			if (StringUtils.isNotBlank(dto.getQ())) {
				shopList = shopList.stream()
						.filter(v -> v.getStoreNo().contains(dto.getQ()) || v.getStoreNm().contains(dto.getQ()))
						.collect(Collectors.toList());
			}
			return ApiResult.ok(PageUtil.startPage(shopList,  dto.getPage(), dto.getRows()));
		} catch (Exception e) {
			logger.error("查询用户机构权限出错", e);
			return ApiResult.error("查询用户机构权限出错");
		}
	}
	
	@ApiOperation(value = "用户结算公司权限接口")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@PostMapping("/company")
	public ApiResult<PageResult<?>> company(
			@ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto) {
		try {
			int userId = Integer.parseInt(Authorization.getUser().getId());
			List<AuthorityUserSettlementCompany> settlementCompanyList = authorityUserDataApi.userSettlementCompany(userId);
			settlementCompanyList = settlementCompanyList.stream().sorted(Comparator.comparing(AuthorityUserSettlementCompany::getSettlementCompanyNo))
					.collect(Collectors.toList());
			if (StringUtils.isNotBlank(dto.getQ())) {
				settlementCompanyList = settlementCompanyList.stream()
						.filter(v -> v.getSettlementCompanyNo().contains(dto.getQ()) || v.getSettlementCompanyName().contains(dto.getQ()))
						.collect(Collectors.toList());
			}
			return ApiResult.ok(PageUtil.startPage(settlementCompanyList,  dto.getPage(), dto.getRows()));
		} catch (Exception e) {
			logger.error("查询用户结算公司权限出错", e);
			return ApiResult.error("查询用户结算公司权限出错");
		}
	}

	@ApiOperation(value = "聚合仓信息查询接口")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@PostMapping("/internetVirtualWarehouseInfo")
	public ApiResult<PageResult<?>> internetVirtualWarehouseInfo(
			@ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto,Integer vstoreMold) {
			List<InternetVirtualWarehouseInfo> resultList = new ArrayList<>();
			int userId = Integer.parseInt(Authorization.getUser().getId());
			try {
				List<AuthorityUserVirtualWarehouse> list = authorityUserDataApi.userVirtualWarehouse(userId);
				if (CollectionUtils.isNotEmpty(list)) {
					List<String> vstoreCodeList = list.stream().map(AuthorityUserVirtualWarehouse::getVstoreCode).collect(Collectors.toList());

					Query query = new Query().and("vstoreCodeList", vstoreCodeList);
                    if (vstoreMold != null) {
						query.and("vstoreMold",vstoreMold);
                    }

					resultList = internetVirtualWarehouseInfoManager.selectByParams(query);
				}
			} catch (RpcException e1) {
				logger.error("根据userId查询聚合仓权限接口异常" + e1.getMessage(), e1);
				return ApiResult.error("根据userId查询聚合仓权限接口异常");
			}
			if (StringUtils.isNotBlank(dto.getQ())) {
				resultList = resultList.stream()
						.filter(v -> StrUtil.equalsIgnoreCase(v.getVstoreCode(), dto.getQ()) || StrUtil.equalsIgnoreCase(v.getVstoreName(), dto.getQ())
							||StrUtil.containsAnyIgnoreCase(v.getVstoreCode(), dto.getQ()) || StrUtil.containsAnyIgnoreCase(v.getVstoreName(), dto.getQ())
						)
						.collect(Collectors.toList());
			}
			return ApiResult.ok(PageUtil.startPage(resultList, dto.getPage(), dto.getRows()));
	}

	@ApiOperation(value = "商品信息查询接口")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@PostMapping("/itemSku")
	public ApiResult<PageResult<?>> itemSku(
			@ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto,String brandNo) {
		try {
			Query query = new Query();
			query.and("code",dto.getQ());
			query.and("brandNo",brandNo);
			List<Item> pageList = itemService.selectByPage(query,new Pagenation(dto.getPage(),dto.getRows()));
			Integer count = itemService.selectCount(query);
			return ApiResult.ok(new PageResult<>(pageList, count));
		} catch (Exception e) {
			logger.error("查询商品信息出错", e);
			return ApiResult.error("查询商品信息出错");
		}
	}

	/**
	 * 查找有权限的大区
	 */
	@RequestMapping(value = "/selectAuthorityZone")
	@ResponseBody
	public List<Map<String, Object>> selectAuthorityZone(HttpServletRequest req, Model model) throws ManagerException {

		List<Map<String, Object>> zone = new ArrayList<Map<String, Object>>();

		int userId = Integer.parseInt(Authorization.getUser().getId());
		try {
			List<AuthorityUserRegion> userRegionList = authorityUserDataApi.userRegion(userId);
			logger.info("用户userName:" + Authorization.getUser().getName() + "，userId：" + userId + "的大区权限：" + JsonUtils.toJson(userRegionList));
			if(CollectionUtils.isNotEmpty(userRegionList)) {
				for (AuthorityUserRegion authorityUserRegion : userRegionList) {
					Map<String, Object> zoneInfo = new HashMap<String, Object>();
					zoneInfo.put("name", authorityUserRegion.getRegionName());
					zoneInfo.put("zoneNo", authorityUserRegion.getRegionNo());
					zone.add(zoneInfo);
				}
			}
		} catch (RpcException e) {
			throw new ManagerException("查询用户大区权限出错:" + e);
		}

		return zone;
	}


	@ApiOperation(value = "渠道店铺查询接口")
	@ApiResponses({ @ApiResponse(code = 200, message = "OK", response = ApiResult.class) })
	@PostMapping("/orderSourceTerminalConfig")
	public ApiResult<PageResult<?>> orderSourceTerminalConfig(
			@ApiParam(value = "查询参数", required = true) AuthorityUserDataDto dto, Query query) {
		try {
			query.and("queryParam",dto.getQ());
			query.groupBy("third_platform");
			List<OrderSourceTerminalConfig> pageList = orderSourceTerminalConfigManager.selectByPage(query,new Pagenation(dto.getPage(),dto.getRows()));
			Integer count = orderSourceTerminalConfigManager.selectCount(query);
			return ApiResult.ok(new PageResult<>(pageList, count));
		} catch (Exception e) {
			logger.error("查询渠道店铺出错", e);
			return ApiResult.error("查询渠道店铺出错");
		}
	}

}