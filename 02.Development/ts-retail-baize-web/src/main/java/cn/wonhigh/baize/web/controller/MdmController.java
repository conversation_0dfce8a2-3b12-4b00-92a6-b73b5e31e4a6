package cn.wonhigh.baize.web.controller;

import cn.wonhigh.baize.manager.gms.MdmManager;
import cn.wonhigh.baize.model.dto.mdm.StoreListQueryParamDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.vo.ApiResult;

/**
 * <AUTHOR>
 * @created 2023/2/17 11:08
 *
 * <p>
 *     MDM 系统接口调用
 * </p>
 */
@RestController
@RequestMapping("/mdm")
public class MdmController {

    public static final Logger logger = LoggerFactory.getLogger(MdmController.class);

    private final MdmManager manager;

    public MdmController(MdmManager manager) {
        this.manager = manager;
    }

    @RequestMapping(value = {"/store/list"})
    public ApiResult<?> getStoreList(
            StoreListQueryParamDto queryParamDto
    ) {

        logger.info("getStoreList, queryParamDto={}", queryParamDto);
        return ApiResult.ok(manager.getStoreList(queryParamDto));
    }
}
