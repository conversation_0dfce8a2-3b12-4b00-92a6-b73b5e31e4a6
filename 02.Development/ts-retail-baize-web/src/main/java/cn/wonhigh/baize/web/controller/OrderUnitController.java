package cn.wonhigh.baize.web.controller;

import cn.mercury.manager.IManager;
import cn.wonhigh.baize.manager.gms.IOrderUnitManager;
import cn.wonhigh.baize.model.entity.gms.OrderUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;


@RestController
@RequestMapping("/orderUnit")
public class OrderUnitController  extends ApiController<OrderUnit,Integer> {

    @Autowired
    private IOrderUnitManager manager;

    protected IManager<OrderUnit,Integer> getManager(){
        return manager;
    }

}
