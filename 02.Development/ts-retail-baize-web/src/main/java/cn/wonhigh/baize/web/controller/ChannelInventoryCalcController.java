package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.query.Query;
import cn.mercury.manager.ManagerException;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.model.dto.InventoryDetailExportDto;
import cn.wonhigh.baize.model.dto.InventoryDetailExportForVstoreDto;
import cn.wonhigh.retail.iis.api.dto.InventoryOmsCalcReq;
import cn.wonhigh.retail.iis.api.dto.InventoryXCXCalcReq;
import cn.wonhigh.retail.iis.api.dto.ItemReq;
import cn.wonhigh.retail.iis.api.dto.NewChannelInventoryExportDto;
import cn.wonhigh.retail.iis.api.dto.NewInventoryCalcDto;
import cn.wonhigh.retail.iis.api.service.InventoryCalculatorApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @create 2024/8/20 14:54
 */
@RestController
@RequestMapping("/channel/inventory/calc")
public class ChannelInventoryCalcController {

    protected Logger LOGGER = LoggerFactory.getLogger(ChannelInventoryCalcController.class);
    @Reference(timeout = 600000)
    private InventoryCalculatorApi inventoryCalculatorApi;

    @RequestMapping("/data")
    @ResponseBody
    public ApiResult<?> xcxInventoryCalc(Query query) {
        NewInventoryCalcDto resultDto = null;
        try {
            resultDto = inventoryCalculatorApi.inventoryXCXCalc(buildXcxParams(query));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ApiResult.error("计算库存异常," + e.getMessage());
        }
        return ApiResult.ok(resultDto);
    }

    @RequestMapping("/oms/data")
    @ResponseBody
    public ApiResult<?> omsInventoryCalc(Query query) {
        NewInventoryCalcDto resultDto = null;
        try {
            resultDto = inventoryCalculatorApi.inventoryOmsCalc(buildOmsParams(query));
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ApiResult.error("计算库存异常," + e.getMessage());
        }
        return ApiResult.ok(resultDto);
    }


    @RequestMapping("/oms/exportExcel")
    public void omsExportExcel(
            @RequestParam String exportType, // 导出类型, 机构或者虚仓
            Query query,
            HttpServletResponse response) {
        try {
            if (Objects.equals(exportType, ExportType.STORE.name())) {
                exportForOmsStore(query, response);
            } else if (Objects.equals(exportType, ExportType.WAREHOUSE.name())) {
                exportForOmsWarehouse(query, response);
            } else {
                throw new ManagerException("导出类型错误");
            }
    
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            responseJson(response,  ApiResult.error("导出库存异常," + e.getMessage()));
        }
    }

    private void exportForOmsWarehouse(Query query, HttpServletResponse response) throws IOException {
        NewInventoryCalcDto resultDto = inventoryCalculatorApi.inventoryOmsCalc(buildOmsParams(query));
        if (CollectionUtils.isEmpty(resultDto.getConfigList())) {
            responseJson(response,  ApiResult.error("未查询到导入数据"));
            return;
        }
        List<InventoryDetailExportForVstoreDto> exportDtos =
                Optional.of(resultDto)
                        .map(NewInventoryCalcDto::getConfigList)
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(this::covert).collect(Collectors.toList());
        export(exportDtos, response,"范围内可发货虚仓库存明细", Lists.newArrayList("lockQty"), InventoryDetailExportForVstoreDto.class);
    }

    private void exportForOmsStore(Query query, HttpServletResponse response) throws IOException {
        List<NewChannelInventoryExportDto> resultDtos = inventoryCalculatorApi.inventoryOmsExport(buildOmsParams(query));

        String fileName = URLEncoder.encode("范围内可发货机构库存明细", "UTF-8");

        List<InventoryDetailExportDto> exportDtos = resultDtos.stream().map(this::covert).collect(Collectors.toList());
        export(exportDtos, response,fileName, null, InventoryDetailExportDto.class);
    }

    @RequestMapping("/exportExcel")
    public void exportExcel(
            @RequestParam String exportType, // 导出类型, 机构或者虚仓
            Query query,
            HttpServletResponse response) {
        try {
            InventoryXCXCalcReq inventoryXCXCalcReq = buildXcxParams(query);

            if (Objects.equals(exportType, ExportType.WAREHOUSE.name())) {
                exportForWarehouse(response, inventoryXCXCalcReq);
            } else if (Objects.equals(exportType, ExportType.STORE.name())){
                exportForStore(response, inventoryXCXCalcReq);
            } else {
                throw new ManagerException("导出类型错误");
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            responseJson(response,  ApiResult.error("导出库存异常," + e.getMessage()));
        }
    }

    private void exportForWarehouse(HttpServletResponse response, InventoryXCXCalcReq inventoryXCXCalcReq) throws IOException {

        String fileName = URLEncoder.encode("范围内可发货虚仓库存明细(", "UTF-8");

        NewInventoryCalcDto newInventoryCalcDto = inventoryCalculatorApi.inventoryXCXCalc(inventoryXCXCalcReq);

        List<InventoryDetailExportForVstoreDto> exportDtos =
                Optional.ofNullable(newInventoryCalcDto)
                        .map(NewInventoryCalcDto::getConfigList)
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(this::covert).collect(Collectors.toList());
        export(exportDtos, response,fileName, null,InventoryDetailExportForVstoreDto.class);
    }

    // 按机构导出明细
    private void exportForStore(HttpServletResponse response, InventoryXCXCalcReq inventoryXCXCalcReq) throws IOException {
        List<NewChannelInventoryExportDto> resultDtos = inventoryCalculatorApi.inventoryXcxExport(inventoryXCXCalcReq);

        String fileName = URLEncoder.encode("范围内可发货机构库存明细", "UTF-8");

        String shopNo = inventoryXCXCalcReq.getShopNo();

        List<InventoryDetailExportDto> exportDtos = resultDtos.stream()
                .map(e -> covert(shopNo,e)).collect(Collectors.toList());

        export(exportDtos, response,fileName, null,InventoryDetailExportDto.class);
    }

    private <T> void export(List<T> resultDtos,
                        HttpServletResponse response,
                        String fileName,
                        List<String> excludeFieldName,
                        Class<T> exportClass
    ) throws IOException {
        if (CollectionUtils.isEmpty(resultDtos)) {
            responseJson(response,  ApiResult.error("未查询到导入数据"));
            return;
        }

        response.reset();
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), exportClass)
                .excelType(ExcelTypeEnum.XLSX)
                .excludeColumnFiledNames(excludeFieldName)
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
        excelWriter.write(resultDtos, writeSheet);
        excelWriter.finish();
    }

    private InventoryXCXCalcReq buildXcxParams(Query query) {
        String data = query.findValue("formData");
        if(StringUtils.isBlank(data)){
            throw new ManagerException("查询参数不能为空");
        }
        InventoryXCXCalcReq req = JSONObject.parseObject(data,InventoryXCXCalcReq.class);

        if(CollectionUtils.isEmpty(req.getVstoreCodes()) && StringUtils.isEmpty(req.getShopNo())){
            throw new ManagerException("渠道店铺编码和聚合仓编码不能同时为空");
        }


        checkItemReq(req);

        return req;
    }

    private InventoryOmsCalcReq buildOmsParams(Query query) {
        String data = query.findValue("formData");
        if(StringUtils.isBlank(data)){
            throw new ManagerException("查询参数不能为空");
        }
        InventoryOmsCalcReq req = JSONObject.parseObject(data,InventoryOmsCalcReq.class);

        if(CollectionUtils.isEmpty(req.getVstoreCodes())){
            throw new ManagerException("聚合仓编码不能为空");
        }


        checkItemReq(req);

        return req;
    }


    private void checkItemReq(ItemReq req){
        if(StringUtils.isEmpty(req.getSkuNo())
                && StringUtils.isEmpty(req.getItemCode())
                && StringUtils.isEmpty(req.getBarcode())
        ){
            throw new ManagerException("商品编码、商品条码、skuNo不能同时为空");
        }
    }

    private void responseJson(HttpServletResponse response, ApiResult<?> apiResult) {
        // response.reset();
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        try {
            response.getWriter().println(JsonUtils.toJson(apiResult));
        } catch (Exception ignored) {
        }
    }

    public InventoryDetailExportForVstoreDto covert(NewInventoryCalcDto.NewInventoryItem dto){
        if (dto == null) {
            return null;
        }

        InventoryDetailExportForVstoreDto inventoryDetailExportForVstoreDto = new InventoryDetailExportForVstoreDto();
        inventoryDetailExportForVstoreDto.setVstoreCode(dto.getVstoreCode());
        inventoryDetailExportForVstoreDto.setBrandNo(dto.getBrandNo());
        inventoryDetailExportForVstoreDto.setSkuNo(dto.getSkuNo());
        inventoryDetailExportForVstoreDto.setItemCode(dto.getItemCode());
        inventoryDetailExportForVstoreDto.setSizeNo(dto.getSizeNo());
        inventoryDetailExportForVstoreDto.setAvailableQty(ObjectUtils.defaultIfNull(dto.getAvailableQty(), 0));
        inventoryDetailExportForVstoreDto.setShareQty(ObjectUtils.defaultIfNull(dto.getShareQty(), 0));
        inventoryDetailExportForVstoreDto.setSafeStockQty(ObjectUtils.defaultIfNull(dto.getSafeStockQty(), 0));
        inventoryDetailExportForVstoreDto.setShareRatio(ObjectUtils.defaultIfNull(dto.getShareRatio(), 0D)*100);
        inventoryDetailExportForVstoreDto.setChannelShareRatio(ObjectUtils.defaultIfNull(dto.getChannelShareRatio(), 0D)*100);
        inventoryDetailExportForVstoreDto.setRyQty(ObjectUtils.defaultIfNull(dto.getRyQty(), 0));
        inventoryDetailExportForVstoreDto.setOmsLockQty(ObjectUtils.defaultIfNull(dto.getOmsLockQty(), 0));
        inventoryDetailExportForVstoreDto.setLockQty(ObjectUtils.defaultIfNull(dto.getLockQty(), 0));
        return inventoryDetailExportForVstoreDto;
    }

    public InventoryDetailExportDto covert(String shopNo,NewChannelInventoryExportDto channelInventory) {
        InventoryDetailExportDto covert = covert(channelInventory);
        if (covert != null) {
            covert.setShopNo(shopNo);
        }
        return covert;
    }

    public InventoryDetailExportDto covert(NewChannelInventoryExportDto channelInventory) {
        if (channelInventory == null) {
            return null;
        }
        InventoryDetailExportDto inventoryDetailExportDto = new InventoryDetailExportDto();
        inventoryDetailExportDto.setVstoreCode(channelInventory.getVstoreCode());
        inventoryDetailExportDto.setVstoreName(channelInventory.getVstoreName());
        inventoryDetailExportDto.setStoreNo(channelInventory.getStoreNo());
        inventoryDetailExportDto.setStoreName(channelInventory.getStoreName());
        inventoryDetailExportDto.setOrderUnitNo(channelInventory.getOrderUnitNo());
        inventoryDetailExportDto.setOrderUnitName(channelInventory.getOrderUnitName());
        inventoryDetailExportDto.setBrandNo(channelInventory.getBrandNo());
        inventoryDetailExportDto.setItemCode(channelInventory.getItemCode());
        inventoryDetailExportDto.setSkuNo(channelInventory.getSkuNo());
        inventoryDetailExportDto.setBalanceQty(channelInventory.getBalanceQty());
        inventoryDetailExportDto.setAvailableQty(channelInventory.getAvailableQty());
        inventoryDetailExportDto.setShareQty(channelInventory.getShareQty());
        inventoryDetailExportDto.setDefectiveGoodsQty(channelInventory.getDefectiveGoodsQty());
        inventoryDetailExportDto.setUnavailableShopFlag(channelInventory.getUnavailableShopFlag() == 1 ? "是" : "否");
        inventoryDetailExportDto.setRefuseRecordFlag(channelInventory.getRefuseRecordFlag() == 1 ? "是" : "否");
        inventoryDetailExportDto.setUnQty(channelInventory.getBalanceQty() - channelInventory.getAvailableQty());
        return inventoryDetailExportDto;
    }




    enum ExportType {
        STORE,WAREHOUSE
    }
}
