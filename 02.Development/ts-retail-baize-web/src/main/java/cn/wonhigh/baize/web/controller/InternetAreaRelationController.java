/** by zkh **/
package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.query.Query;
import cn.wonhigh.baize.manager.gms.IInternetAreaRelationManager;
import cn.wonhigh.baize.model.entity.gms.InternetAreaRelation;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;

import org.springframework.web.bind.annotation.RequestMapping;
import cn.mercury.manager.IManager;
import topmall.framework.web.vo.ApiResult;

import java.util.List;

@RestController

@RequestMapping("/internet/area/relation")
public class InternetAreaRelationController extends ApiController<InternetAreaRelation,String> {
    @Autowired
    private IInternetAreaRelationManager manager;

    protected IManager<InternetAreaRelation,String> getManager(){
        return manager;
    }


    @ResponseBody
    @RequestMapping(
            method = {RequestMethod.GET, RequestMethod.POST},
            value = {"/lists"}
    )
    public ApiResult<List<InternetAreaRelation>> areaRelation(Query query) {
        return ApiResult.ok(manager.selectByRetailCodeName(query.asMap()));
    }
}