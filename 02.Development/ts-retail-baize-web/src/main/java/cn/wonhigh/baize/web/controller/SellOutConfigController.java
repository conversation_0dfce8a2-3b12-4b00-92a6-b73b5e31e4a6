/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.annotation.JsonVariable;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.manager.IEntryResultHandler;
import cn.mercury.manager.IManager;
import cn.mercury.security.IUser;
import cn.mercury.utils.DateUtil;
import cn.wonhigh.baize.manager.gms.IBrandManager;
import cn.wonhigh.baize.manager.gms.IItemManager;
import cn.wonhigh.baize.manager.gms.ISellOutConfigDtlManager;
import cn.wonhigh.baize.manager.gms.ISellOutConfigManager;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigClassifyDto;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigDto;
import cn.wonhigh.baize.model.dto.sellOutConfig.SellOutConfigItemDto;
import cn.wonhigh.baize.model.entity.gms.*;
import cn.wonhigh.retail.uc.common.api.model.AuthorityUserBrand;
import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.yougou.logistics.base.common.exception.RpcException;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;
import topmall.framework.io.excel.EntryResultHandler;
import topmall.framework.security.Authorization;
import topmall.framework.web.controller.BaseController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.mercury.utils.DateUtil.LONG_DATE_FORMAT;


@Controller
@RequestMapping("/sell/out/config")
public class SellOutConfigController extends BaseController<SellOutConfig,String> {
    @Autowired
    private ISellOutConfigManager manager;

    @Autowired
    private ISellOutConfigDtlManager sellOutConfigDtlManager;

    @Autowired
    private IBrandManager brandManager;

    @Autowired
    private IItemManager itemManger;

    @Reference
    private AuthorityUserDataApi authorityUserDataApi;

    private static final Logger LOGGER = LoggerFactory.getLogger(SellOutConfigController.class);

    protected IManager<SellOutConfig,String> getManager(){
        return manager;
    }

    private int taget_idx = 91;

    @Override
	protected String getTemplateFolder() {
		 return "/baize/SellOutConfig";
	}

    @ResponseBody
    @RequestMapping({"/share_classify_list"})
    public ApiResult<List<ItemAttr>> getShareClassifyList(Query query) {
        try {
             List<ItemAttr> attrList = manager.getShareClassifyList(query);
            return ApiResult.ok(attrList);
        } catch (Exception e) {
            logger.error("查询属性分类异常", e);
            return ApiResult.error("查询属性分类异常");
        }
    }


    private Map<String,Object> initNewColumn(String field,String title,int colspan,int rowspan ){
        Map<String,Object> column = new HashMap<>();
        column.put("field",field);
        column.put("title",title);
        column.put("colspan",colspan);
        column.put("rowspan",rowspan);
        return column;
    }

    private List<Map>  getTitleColumns(){
        List<Map> columnList = new ArrayList<>();
        columnList.add(initNewColumn("","售罄目标维护",taget_idx,1));
        return columnList;
    }

    private List<Map> getFieldColumns(String selectType){
        List<Map> columnList = new ArrayList<>();
        if("item".equals(selectType)){
            columnList.add(initNewColumn("brandName","品牌",1,2));
            columnList.add(initNewColumn("itemCode","商品编码",1,2));
            columnList.add(initNewColumn("sizeNo","尺码",1,2));
        }else{
            columnList.add(initNewColumn("brandName","品牌",1,2));
            columnList.add(initNewColumn("classifyName","属性分类",1,2));
            columnList.add(initNewColumn("classifyValueName","属性值",1,2));
        }
        int idx = 1 ;
        while (true){
            columnList.add(initNewColumn("F"+idx,String.valueOf(idx),1,1));
            idx ++;
            if(idx > taget_idx){
                break ;
            }
        }
        columnList.add(initNewColumn("statusName","状态",1,2));
        columnList.add(initNewColumn("updateUser","更新人",1,2));
        columnList.add(initNewColumn("updateTime","更新时间",1,2));
        return columnList;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
	@ResponseBody
    @RequestMapping({"/column_list"})
    public ApiResult<Map> getColumnList(Query query) {
        if(null == query.asMap().get("selectType")){
            return ApiResult.error("参数异常!selectType为空");
        }
        try {
            Map columnMap = new HashMap();
            columnMap.put("titleColomn",this.getTitleColumns());
            columnMap.put("fieldColumn",this.getFieldColumns(query.asMap().get("selectType").toString()));
            return ApiResult.ok(columnMap);
        } catch (Exception e) {
            logger.error("查询列名集合异常", e);
            return ApiResult.error("查询列名集合异常");
        }
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
	@ResponseBody
    @RequestMapping({"/data_list"})
    public ApiResult<PageResult<Map>> getDataList(Query query, Pagenation page) {
        if(StringUtils.isBlank(query.asMap().get("selectType").toString())){
            return ApiResult.error("参数异常!selectType为空");
        }
        try {
            String selectType = query.asMap().get("selectType").toString();
            boolean isSelectItem = selectType.equals("item");
            query = isSelectItem? query.and("type","1") : query.and("type","0");
            int c = manager.selectCount(query);
            if(c == 0){
                return ApiResult.ok(new PageResult(new ArrayList<>(),c));
            }
            List<SellOutConfig> outList = manager.selectByPage(query,page);

            if (outList!=null) {
                List<SellOutConfigDto> configDtos = new ArrayList<>(outList.size());
                List<String> billNos = outList.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList());

                List<SellOutConfigDtl>  dtlList = sellOutConfigDtlManager.selectByParams(new Query().and("billNos",billNos));

                for (SellOutConfig sellOutConfig : outList) {

                    SellOutConfigDto sellOutConfigDto = getSellOutConfigDto(sellOutConfig, isSelectItem, selectType, dtlList);
                    configDtos.add(sellOutConfigDto);
                }
                return ApiResult.ok(new PageResult(configDtos,c));
            }

            return ApiResult.ok(new PageResult(new ArrayList<>(),0));
        } catch (Exception e) {
            logger.error("查询列名集合异常", e);
            return ApiResult.error("查询列名集合异常");
        }
    }

    private SellOutConfigDto getSellOutConfigDto(SellOutConfig sellOutConfig, boolean isSelectItem, String selectType, List<SellOutConfigDtl> dtlList) {
        SellOutConfigDto sellOutConfigDto = null;

        if (isSelectItem) {
            sellOutConfigDto = new SellOutConfigItemDto();
        } else {
            sellOutConfigDto = new SellOutConfigClassifyDto();
        }

        sellOutConfigDto.setBrandNo(sellOutConfig.getBrandNo());
        sellOutConfigDto.setBrandName(sellOutConfig.getBrandName());

        if (sellOutConfigDto instanceof SellOutConfigItemDto) {
            ((SellOutConfigItemDto) sellOutConfigDto).setItemCode(sellOutConfig.getItemCode());
            ((SellOutConfigItemDto) sellOutConfigDto).setItemNo(sellOutConfig.getItemNo());
            ((SellOutConfigItemDto) sellOutConfigDto).setSizeNo(sellOutConfig.getSizeNo());
        } else {
            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyCode(sellOutConfig.getClassifyCode());
            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyName(sellOutConfig.getClassifyName());
            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyValueCode(sellOutConfig.getClassifyValueCode());
            ((SellOutConfigClassifyDto) sellOutConfigDto).setClassifyValueName(sellOutConfig.getClassifyValueName());

        }

        sellOutConfigDto.setUpdateTime(DateUtil.format(sellOutConfig.getUpdateTime(), LONG_DATE_FORMAT));
        sellOutConfigDto.setUpdateUser(sellOutConfig.getUpdateUser());
        sellOutConfigDto.setStatus(sellOutConfig.getStatus());
        sellOutConfigDto.setStatusName(sellOutConfig.getStatus() == 0 ? "启用" : "禁用");
        sellOutConfigDto.setSelectType(selectType);

        List<SellOutConfigDtl> sortAfter = dtlList.stream()
                .filter(dtl -> Objects.equals(dtl.getBillNo(), sellOutConfig.getBillNo()))
                .collect(Collectors.toList());

        Map<String, Object> ext = new TreeMap<>(Comparator.comparingInt(a -> Integer.parseInt(a.substring(1))));
        sortAfter.forEach(dtl -> {
            ext.put("F" + dtl.getTargetField(), dtl.getTargetValue().toPlainString() + "%");
        });
        sellOutConfigDto.setExt(ext);
        return sellOutConfigDto;
    }

    @ResponseBody
    @RequestMapping({"/del_sell_config"})
    public ApiResult<Integer> deleteSellOut(Query query, Pagenation page) {
    	String uk = (String) query.asMap().get("uk");
    	if(null == query.asMap().get("selectType")){
            return ApiResult.error("参数异常!selectType为空");
        }
    	if(StringUtils.isBlank(uk)) {
    		return ApiResult.ok(0);
    	}
        try {
            int c = 0;
            String[] uks = uk.split(",");
            String selectType = query.asMap().get("selectType").toString();
            boolean isSelectItem = "item".equals(selectType);
            for (String str : uks) {
                if (isSelectItem) {
                    String brandNo = str.split("_")[0];
                    String itemNo = str.split("_")[1];
                    String sizeNo = str.split("_")[2];

                    Tuple3<String, String, String> tuple3 = Tuples.of(brandNo, itemNo, sizeNo);

                    c+=manager.deleteByUnique(1,tuple3);
                } else {
                    String brandNo = str.split("_")[0];
                    String classifyCode = str.split("_")[1];
                    String classifyValueCode = str.split("_")[2];
                    Tuple3<String, String, String> tuple3 = Tuples.of(brandNo, classifyCode, classifyValueCode);
                    c+=manager.deleteByUnique(0,tuple3);
                }
            }
            return ApiResult.ok(c);
        } catch (Exception e) {
            logger.error("删除异常", e);
            return ApiResult.error("删除异常");
        }
    }

	@ResponseBody
    @RequestMapping({"/update_sell_config"})
    public ApiResult<Integer> updateSellOut(Query query, Pagenation page) {
    	String uk = (String) query.asMap().get("uk");
    	if(null == query.asMap().get("selectType")){
            return ApiResult.error("参数异常!selectType为空");
        }
    	if(StringUtils.isBlank(uk)) {
    		return ApiResult.ok(0);
    	}
        try {
            int c = 0;
            String status =  (String) query.asMap().get("status");
            String[] uks = uk.split(",");
            String selectType = query.asMap().get("selectType").toString();
            boolean isSelectItem = "item".equals(selectType);
            for (String str : uks) {
                if (isSelectItem) {
                    String brandNo = str.split("_")[0];
                    String itemNo = str.split("_")[1];
                    String sizeNo = str.split("_")[2];
                    if (StringUtils.isNotBlank(itemNo) && StringUtils.isNotBlank(sizeNo)) {
                        manager.updateStatusByParams(new Query().and("itemNo", itemNo).and("sizeNo", sizeNo)
                                        .and("brandNo", brandNo)
                                .and("status", status));
                        c++;
                    }
                } else {
                    String brandNo = str.split("_")[0];
                    String classifyCode = str.split("_")[1];
                    String classifyValueCode = str.split("_")[2];
                    if (StringUtils.isNotBlank(classifyCode) && StringUtils.isNotBlank(classifyValueCode)) {
                        manager.updateStatusByParams(new Query().and("classifyCode", classifyCode).and("classifyValueCode", classifyValueCode)
                                        .and("brandNo", brandNo)
                                .and("status", status));
                        c++;
                    }
                }
			}
            return ApiResult.ok(c);
        } catch (Exception e) {
            logger.error("更新异常", e);
            return ApiResult.error("更新异常");
        }
    }

    private List<Object> readExcelData(MultipartFile multipartFile) {
        List<Object> objectList = Collections.emptyList();
        try {
            objectList = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .headRowNumber(1)
                    .autoCloseStream(true).doReadAllSync();
        } catch (IOException e) {
            LOGGER.error("Excel 读取失败", e);
        }
        return objectList;
    }


    private boolean checkAndSetBrand(String brandNo, SellOutConfig sellOutConfig, List<String> authorityBrandNoList){
        if (!authorityBrandNoList.contains(brandNo)) {
            return false;
        }
        Brand b = brandManager.findByUnique(brandNo);
        if(null == b){
            return false;
        }
        sellOutConfig.setBrandNo(brandNo);
        sellOutConfig.setBrandName(b.getName());
        return true;
    }

    private boolean checkAndSetItem(String importType ,String brandNo,String itemCode,String sizeNo ,SellOutConfig config){
        if(!"item".equals(importType)){
            return true;
        }
        ItemBaseInfo i = itemManger.findSku(brandNo,itemCode,sizeNo);
        if(null == i || i.getStatus() != 1){
            return false;
        }
        config.setItemCode(itemCode);
        config.setSizeNo(sizeNo);
        config.setItemNo(i.getItemNo());

        return true;
    }

    private boolean checkAndSetShareClassify(String importType ,String brandNo,String classifyValue ,SellOutConfig config){
        if("item".equals(importType)){
            return true;
        }
        if(StringUtils.isBlank(classifyValue)){
            return false;
        }
        List<ItemAttr> attrList = manager.getShareClassifyList(new Query().and("brandNo",brandNo).and("attrDtlName",classifyValue));
        if(attrList.isEmpty()){
            return false;
        }
        config.setClassifyCode(attrList.get(0).getAttrNo());
        config.setClassifyName("共享标识");
        config.setClassifyValueCode(attrList.get(0).getAttrDtlNo());
        config.setClassifyValueName(attrList.get(0).getAttrDtlName());
        return true;
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
	private Tuple2<String , SellOutConfig> initNewConfigList(Map map, String importType){
        boolean isImportItem = "item".equals(importType);
        int startIdx = isImportItem ? 3 : 2;
        String createUser = Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown");
        Date createTime = new Date();
        String errMsg = "";

        SellOutConfig newObj = new SellOutConfig();

        List<SellOutConfigDtl> newDtlList = new ArrayList<>();

        for (int i = 1; i <= taget_idx ; i++){
            SellOutConfigDtl newObjDtl = new SellOutConfigDtl();
            newObjDtl.setTargetField(String.valueOf(i));
            Double targetVal = MapUtils.getDouble(map,startIdx);
            if(null == targetVal){
                errMsg = "售罄目标"+ i +"值格式错误!";
                break;
            }
            if(targetVal < 0 || targetVal >100){
                errMsg = "售罄目标"+ i +"值范围错误,正确数值范围0<=x<=100";
                break;
            }
            newObjDtl.setTargetValue(new BigDecimal(targetVal).setScale(1,BigDecimal.ROUND_HALF_UP));
            newDtlList.add(newObjDtl);
            startIdx++;
        }


        newObj.setCreateUser(createUser);
        newObj.setCreateTime(createTime);
        newObj.setUpdateUser(createUser);
        newObj.setUpdateTime(createTime);
        newObj.setType(isImportItem ? 1 : 0);
        newObj.setTargetValue(BigDecimal.ZERO);
        newObj.setTargetField(BigDecimal.ZERO.toString());
        newObj.setSellOutConfigDtlList(newDtlList);

        return Tuples.of(errMsg, newObj);
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @ResponseBody
	@RequestMapping("/import")
    public ApiResult<?> importData(HttpServletRequest request) {
        MultipartHttpServletRequest multipartHttpServletRequest = null;
        if (request instanceof MultipartHttpServletRequest) {
            multipartHttpServletRequest = (MultipartHttpServletRequest) request;
        }
        if (multipartHttpServletRequest == null) {
            return ApiResult.error("File is not found");
        }
        MultipartFile multipartFile = multipartHttpServletRequest.getFile("excelFile");
        if (multipartFile == null) {
            return ApiResult.error("File is not found");
        }
        // 读取数据
        List<Object> objectList = readExcelData(multipartFile);
        if (objectList == null || objectList.isEmpty()) {
            return ApiResult.error("Excel数据为空");
        }
        StringBuilder errorMsg = new StringBuilder();
        String importType = request.getParameter("selectType");
        List<String> authorityBrandNoList = null;
        try {
            // 用户品牌权限
            int userId = Integer.parseInt(Authorization.getUser().getId());
            List<AuthorityUserBrand> authorityBrandList = authorityUserDataApi.userBrand(userId);
            if (authorityBrandList == null || authorityBrandList.isEmpty()) {
                return ApiResult.error("获取用户品牌权限失败");
            }
            authorityBrandNoList = authorityBrandList.stream().map(AuthorityUserBrand::getBrandDetailNo).collect(Collectors.toList());
        } catch (RpcException e) {
            return ApiResult.error("获取用户品牌权限失败");
        }
        int rowNum = 1;

        List<SellOutConfig> configList = new ArrayList<>();

        // 校验数据
        for (int i = 0; i < objectList.size(); i++) {
            LinkedHashMap map = (LinkedHashMap) objectList.get(i);
            String brandNo = MapUtils.getString(map, 0);

            //T1 错误信息, T2 新数据
            Tuple2<String, SellOutConfig> returnData = this.initNewConfigList(map,importType);
            String errMsg = returnData.getT1();
            SellOutConfig sellOutConfig = returnData.getT2();
            if(StringUtils.isNotBlank(errMsg)){
                errorMsg.append(",").append("第" + rowNum + "行数据不正确, "+errMsg);
                continue ;
            }
            boolean checkBrand = this.checkAndSetBrand(brandNo,sellOutConfig, authorityBrandNoList);
            if(!checkBrand){
                errorMsg.append(",").append("第" + rowNum + "行数据不正确,品牌"+brandNo+";校验不通过");
                continue ;
            }
            String itemCode = MapUtils.getString(map, 1);
            String sizeNo = MapUtils.getString(map, 2);
            boolean checkItem = this.checkAndSetItem(importType,brandNo,itemCode,sizeNo,sellOutConfig);
            if(!checkItem){
                errorMsg.append(",").append("第" + rowNum + "行数据不正确,商品"+itemCode+";尺码"+sizeNo+";校验不通过");
                continue ;
            }
            String shareClassify = MapUtils.getString(map, 1);
            boolean checkShareClassify = this.checkAndSetShareClassify(importType,brandNo,shareClassify,sellOutConfig);
            if(!checkShareClassify){
                errorMsg.append(",").append("第" + rowNum + "行数据不正确,共享标识"+shareClassify+";校验不通过");
                continue ;
            }

            rowNum++;
            configList.add(sellOutConfig);
        }
        if(errorMsg.length() > 0) {
            return ApiResult.error(errorMsg.toString());
        }

        if(configList.isEmpty()){
            return ApiResult.error("导入数据为空");
        }

        // 导入的数据, 分别按导入的类型进行存放
        MultiValueMap<Integer, SellOutConfig> multiValueMap = new LinkedMultiValueMap<>();
        configList.forEach(c -> multiValueMap.add(c.getType(), c));

        multiValueMap.forEach((k,vList) -> {

            // 把每个类型的数据, 按照唯一键进行分组, 如果有重复的数据,则取第一条数据
            Map<Tuple3<String,String, String>, SellOutConfig> groupMap = vList.stream().collect(Collectors.toMap(c ->
                    {
                        // k == 1 , 说明是按商品导入, 否则是按分类导入
                        if(k == 1){
                            return Tuples.of( c.getBrandNo(), c.getItemNo(),c.getSizeNo());
                        }else{
                            return Tuples.of(c.getBrandNo(),c.getClassifyCode(),c.getClassifyValueCode());
                        }
                    }, Function.identity(), (s1, s2) -> s1));


            Set<Tuple3<String,String,String>> keySet = groupMap.keySet();

            List<SellOutConfig> findDataAll = Lists.partition(new ArrayList<>(keySet), 200)
                    .parallelStream().map(list -> this.manager.selectByUniqueList(k, list))
                    .flatMap(Collection::stream).collect(Collectors.toList());


            // T1 新增, T2 更新
            Tuple2<List<SellOutConfig>, List<SellOutConfig>> insertOrUpdate = Tuples.of(new ArrayList<>(), new ArrayList<>());

            groupMap.values().forEach(c -> {
                SellOutConfig exited = findDataAll.stream().filter(
                        item -> {
                            if (k == 1) {
                                return c.getBrandNo().equals(item.getBrandNo()) &&
                                        c.getItemNo().equals(item.getItemNo()) && c.getSizeNo().equals(item.getSizeNo());
                            } else {
                                return c.getBrandNo().equals(item.getBrandNo()) &&
                                        c.getClassifyCode().equals(item.getClassifyCode()) && c.getClassifyValueCode().equals(item.getClassifyValueCode());
                            }
                        }
                ).findFirst().orElse(null);

                // 等于null 说明, 在数据库中不存在, 则需要新增
                if(null != exited){
                    c.setId(exited.getId());
                    c.setBillNo(exited.getBillNo());
                    Optional.ofNullable(c.getSellOutConfigDtlList()).ifPresent(list -> {
                        list.forEach(dtl -> {
                            dtl.setBillNo(exited.getBillNo());
                        });
                    });
                    // 放入新增的数组
                    insertOrUpdate.getT2().add(c);
                } else {
                    c.setBillNo(IdUtil.getSnowflakeNextIdStr());
                    Optional.ofNullable(c.getSellOutConfigDtlList()).ifPresent(list -> {
                        list.forEach(dtl -> {
                            dtl.setBillNo(c.getBillNo());
                        });
                    });
                    // 放入更新的数组
                    insertOrUpdate.getT1().add(c);
                }
            });

            this.manager.batchSave(insertOrUpdate.getT1(), insertOrUpdate.getT2(), null);

        });

        return ApiResult.ok("导入成功");
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
	@RequestMapping("/exportExcel")
    public void exportExcel(Query query, Pagenation page, @JsonVariable("_columns") ExcelColumn[][] columns, HttpServletResponse response) {

        try {
            boolean isExportItem = "item".equals(query.asMap().get("selectType"));
            query.and("type",isExportItem ? 1 : 0);

            List<SellOutConfig> allData = new ArrayList<>();
            this.manager.selectByParamsForHandler(query, config -> {
                allData.add(config.getResultObject());
            });

            List<Map<String,Object>> allDataMap = new ArrayList<>();
            Lists.partition(allData, 50)
                    .parallelStream()
                    .peek(
                            list -> {
                                List<String> billNos = list.stream().map(SellOutConfig::getBillNo).collect(Collectors.toList());
                                List<SellOutConfigDtl>  dtlList = sellOutConfigDtlManager.selectByParams(new Query().and("billNos",billNos));

                                for (SellOutConfig sellOutConfig : list) {
                                    sellOutConfig.setSellOutConfigDtlList(dtlList.stream()
                                            .filter(dtl -> dtl.getBillNo().equals(sellOutConfig.getBillNo()))
                                            .collect(Collectors.toList()));
                                }
                            }
                    ).forEach(list -> {
                        for (SellOutConfig d : list) {
                            Map<String, Object> dataMap = new HashMap<>();
                            dataMap.put("brandNo",d.getBrandNo());
                            dataMap.put("brandName",d.getBrandName());
                            dataMap.put("createUser",d.getCreateUser());
                            dataMap.put("createTime",d.getCreateTime());
                            dataMap.put("updateUser",d.getUpdateUser());
                            dataMap.put("updateTime",d.getUpdateTime());
                            dataMap.put("statusName",d.getStatusName());
                            dataMap.put("itemCode",d.getItemCode());
                            dataMap.put("sizeNo",d.getSizeNo());
                            dataMap.put("classifyName",d.getClassifyName());
                            dataMap.put("classifyValueName",d.getClassifyValueName());

                            d.getSellOutConfigDtlList().stream()
                                    .sorted(Comparator.comparing(SellOutConfigDtl::getTargetField))
                                    .forEach(dtl -> {
                                dataMap.put("F" + dtl.getTargetField(), dtl.getTargetValue().toPlainString() + "%");
                            });
                            allDataMap.add(dataMap);
                        }
                    })
            ;
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName =  isExportItem ? "售罄目标配置导出-按商品"  : "售罄目标配置导出-按分类";
            String fileNameEncode = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ".xlsx");

            OutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream).withTemplate(getClass().getResourceAsStream("/template/"+fileName + ".xlsx")).sheet().doFill(allDataMap);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }


    }
}
