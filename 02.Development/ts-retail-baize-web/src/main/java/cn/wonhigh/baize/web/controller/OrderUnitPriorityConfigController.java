/**
 * by
 **/
package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.UUID;
import cn.mercury.basic.query.Query;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.manager.IManager;
import cn.mercury.security.IUser;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseScopeManager;
import cn.wonhigh.baize.manager.gms.IOrderUnitManager;
import cn.wonhigh.baize.manager.gms.IOrderUnitPriorityConfigManager;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseScope;
import cn.wonhigh.baize.model.entity.gms.OrderUnit;
import cn.wonhigh.baize.model.entity.gms.OrderUnitPriorityConfig;
import cn.wonhigh.baize.utils.common.QueryUtil;
import com.alibaba.excel.EasyExcel;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
import topmall.framework.security.Authorization;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/order/unit/priority/config")
public class OrderUnitPriorityConfigController extends ApiController<OrderUnitPriorityConfig, String> {
    @Autowired
    private IOrderUnitPriorityConfigManager manager;

    @Autowired
    private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;

    @Autowired
    private IInternetVirtualWarehouseScopeManager scopeManager;

    @Autowired
    private IOrderUnitManager orderUnitManager;

    public static final Logger LOGGER = LoggerFactory.getLogger(OrderUnitPriorityConfigController.class);

    protected IManager<OrderUnitPriorityConfig, String> getManager() {
        return manager;
    }


    private final Cache<String, InternetVirtualWarehouseInfo> internetVirtualWarehouseInfoCache = CacheBuilder.newBuilder().build();
    private final Cache<String, OrderUnit> orderUnitCache = CacheBuilder.newBuilder().build();


    @RequestMapping("/import")
    public ApiResult<?> importOrderUnitPriorityConfig(HttpServletRequest request) {
        StandardMultipartHttpServletRequest multipartHttpServletRequest = null;
        if (request instanceof StandardMultipartHttpServletRequest) {
            multipartHttpServletRequest = (StandardMultipartHttpServletRequest) request;
        }
        if (multipartHttpServletRequest == null) {
            return ApiResult.error("请选择文件");
        }
        MultipartFile file = multipartHttpServletRequest.getFile("file");
        List<Object> objectList = readExcel(file);
        if (Objects.isNull(objectList)) {
            return ApiResult.error("没有读取到文件数据");
        }

        List<OrderUnitPriorityConfig> orderUnitPriorityConfigList;
        try {
            orderUnitPriorityConfigList = convertOrderUnitArray(objectList);
        } catch (Exception e) {
            LOGGER.error("数据转换失败", e);
            return ApiResult.error(e.getMessage());
        }

        // 货管编码和虚仓编码不能重复
        Map<String, List<OrderUnitPriorityConfig>> groupingByVstoreCodeAndOrderUnitNo = orderUnitPriorityConfigList.stream().collect(Collectors.groupingBy((s) -> s.getVstoreCode() + s.getOrderUnitNo(), Collectors.toList()));
        for (Map.Entry<String, List<OrderUnitPriorityConfig>> entry : groupingByVstoreCodeAndOrderUnitNo.entrySet()) {
            String k = entry.getKey();
            List<OrderUnitPriorityConfig> v = entry.getValue();
            if (v.size() > 1) {
                LOGGER.error("货管编码{}和聚合仓编码{}重复", v.get(0).getOrderUnitNo(), v.get(0).getVstoreCode());
                return ApiResult.error(String.format("第%s行,货管编码%s和聚合仓编码%s重复", v.get(0).getRowNum(), v.get(0).getOrderUnitNo(), v.get(0).getVstoreCode()));
            }
        }

        Map<String, List<OrderUnitPriorityConfig>> storeMap = orderUnitPriorityConfigList.stream()
                .collect(Collectors.groupingBy(s -> s.getVstoreCode(), Collectors.toList()));
        for (Map.Entry<String, List<OrderUnitPriorityConfig>> entry : storeMap.entrySet()) {
            String vstoreCode = entry.getKey();
            List<String> orderUnits = entry.getValue().stream()
                    .map(OrderUnitPriorityConfig::getOrderUnitNo).collect(Collectors.toList());

            Map<String, Object> params = new HashMap<>();
            params.put("vstoreCode", vstoreCode);
            params.put("orderUnitNos", orderUnits);
            params.put("status", 1);
            List<InternetVirtualWarehouseScope> scopes = scopeManager.selectByParams(QueryUtil.mapToQuery(params));

            Set<String> scopeOrderUnitNos = scopes.stream().map(InternetVirtualWarehouseScope::getOrderUnitNo).collect(Collectors.toSet());
            for (String orderUnit : orderUnits) {
                if (!scopeOrderUnitNos.contains(orderUnit)) {
                    return ApiResult.error(String.format("货管单位%s,不在聚合仓%s范围内或状态不正常",orderUnit,vstoreCode));
                }
            }
        }


        List<List<OrderUnitPriorityConfig>> splitArray = ListUtils.partition(orderUnitPriorityConfigList, 100);
        for (List<OrderUnitPriorityConfig> orderUnitPriorityConfigs : splitArray) {
            List<String> orderUnitNoList = orderUnitPriorityConfigs.stream().map(OrderUnitPriorityConfig::getOrderUnitNo).collect(Collectors.toList());
            // 开始缓存货管信息
            Query query = Query.empty().and("orderUnitNos", orderUnitNoList).and("status", 1);
            List<OrderUnit> orderUnitList = orderUnitManager.selectByParams(query);
            if (!CollectionUtils.isEmpty(orderUnitList)) {
                for (OrderUnit orderUnit : orderUnitList) {
                    orderUnitCache.put(orderUnit.getOrderUnitNo(), orderUnit);
                }
            }

        }


        String username = Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown");

        for (OrderUnitPriorityConfig orderUnitPriorityConfig : orderUnitPriorityConfigList) {
            try {
               InternetVirtualWarehouseInfo internetVirtualWarehouseInfo =  internetVirtualWarehouseInfoCache.get(orderUnitPriorityConfig.getVstoreCode(), () -> internetVirtualWarehouseInfoManager.findByUnique(orderUnitPriorityConfig.getVstoreCode()));
               if (Objects.isNull(internetVirtualWarehouseInfo)) {
                    LOGGER.error("聚合仓编码{}不存在", orderUnitPriorityConfig.getVstoreCode());
                    return ApiResult.error(String.format("第%s行,聚合仓编码%s不存在", orderUnitPriorityConfig.getRowNum(), orderUnitPriorityConfig.getVstoreCode()));
               }
                OrderUnit orderUnit = orderUnitCache.getIfPresent(orderUnitPriorityConfig.getOrderUnitNo());
               if (Objects.isNull(orderUnit)) {
                   LOGGER.error("货管编码{}不存在", orderUnitPriorityConfig.getOrderUnitNo());
                   return ApiResult.error(String.format("第%s行,货管编码%s不存在", orderUnitPriorityConfig.getRowNum(), orderUnitPriorityConfig.getOrderUnitNo()));
               }
               orderUnitPriorityConfig.setOrderUnitName(orderUnit.getName());
               orderUnitPriorityConfig.setVstoreName(internetVirtualWarehouseInfo.getVstoreName());
               orderUnitPriorityConfig.setUpdateTime(new Date());
               orderUnitPriorityConfig.setCreateTime(new Date());
               orderUnitPriorityConfig.setUpdateUser(username);
               orderUnitPriorityConfig.setCreateUser(username);



            } catch (ExecutionException e) {
                logger.error("第{}行,处理数据失败", orderUnitPriorityConfig.getRowNum(), e);
                return ApiResult.error("第"+orderUnitPriorityConfig.getRowNum()+"行,处理数据失败");
            }
        }

        long start = System.currentTimeMillis();
        logger.info("开始导入货管优先级配置到数据库..., {}", orderUnitPriorityConfigList.size());
        for (OrderUnitPriorityConfig orderUnitPriorityConfig : orderUnitPriorityConfigList) {

            Query query = Query.empty().and("vstoreCode", orderUnitPriorityConfig.getVstoreCode()).and("orderUnitNo", orderUnitPriorityConfig.getOrderUnitNo());
            List<OrderUnitPriorityConfig> orderUnitPriorityConfigFindList = manager.selectByParams(query);
            if (CollectionUtils.isEmpty(orderUnitPriorityConfigFindList)) {
                orderUnitPriorityConfig.setId(UUID.gernerate());
                manager.insert(orderUnitPriorityConfig);
            } else {
                orderUnitPriorityConfig.setId(orderUnitPriorityConfigFindList.get(0).getId());
                manager.update(orderUnitPriorityConfig);
            }
        }


        logger.info("导入货管优先级配置到数据库完成, 耗时:{}", System.currentTimeMillis() - start);


        return ApiResult.ok();
    }

    private List<OrderUnitPriorityConfig> convertOrderUnitArray(List<Object> objectList) throws Exception {
        int rowNum = 1; // 行号
        List<OrderUnitPriorityConfig> orderUnitPriorityConfigList = new ArrayList<>();
        boolean hasError = false;
        StringBuilder errorMessage = new StringBuilder();

        for (Object object : objectList) {
            rowNum++;
            if (!(object instanceof LinkedHashMap)) {
                errorMessage.append("第").append(rowNum).append("行数据格式错误\n");
                hasError = true;
                continue;
            }

            LinkedHashMap<Integer, Object> linkedHashMap = (LinkedHashMap<Integer, Object>) object;
            OrderUnitPriorityConfig orderUnitPriorityConfig = new OrderUnitPriorityConfig();

            try {
                String vstoreCode = MapUtils.getString(linkedHashMap, 0, "");
                if (vstoreCode.isEmpty()) {
                    errorMessage.append("第").append(rowNum).append("行数据聚合仓编码不能为空\n");
                    hasError = true;
                    continue;
                }
                orderUnitPriorityConfig.setVstoreCode(vstoreCode);

                String orderUnitNo = MapUtils.getString(linkedHashMap, 1, "");
                if (orderUnitNo.isEmpty()) {
                    errorMessage.append("第").append(rowNum).append("行数据货管编码不能为空\n");
                    hasError = true;
                    continue;
                }
                orderUnitPriorityConfig.setOrderUnitNo(orderUnitNo);

                Integer orderUnitPriorityValue = MapUtils.getInteger(linkedHashMap, 2, 0);
                if (orderUnitPriorityValue < 0) {
                    errorMessage.append("第").append(rowNum).append("行数据货管优先级不能小于0\n");
                    hasError = true;
                    continue;
                }
                orderUnitPriorityConfig.setOrderUnitLevel(orderUnitPriorityValue);

                String statusValue = MapUtils.getString(linkedHashMap, 3, "");
                if (statusValue.isEmpty() || !CollectionUtils.containsAny(Arrays.asList("启用", "禁用"), statusValue)) {
                    errorMessage.append("第").append(rowNum).append("行数据状态值错误\n");
                    hasError = true;
                    continue;
                }
                orderUnitPriorityConfig.setStatus(statusValue.equals("启用") ? 1 : 0);
                orderUnitPriorityConfig.setRowNum(rowNum);
                orderUnitPriorityConfigList.add(orderUnitPriorityConfig);
            } catch (Exception e) {
                errorMessage.append("第").append(rowNum).append("行数据解析异常: ").append(e.getMessage()).append("\n");
                hasError = true;
            }
        }

        if (hasError) {
            LOGGER.error(errorMessage.toString());
            throw new Exception(StringUtils.split(errorMessage.toString(), "\n")[0]);
        }

        return orderUnitPriorityConfigList;
    }


    private List<Object> readExcel(MultipartFile file) {
        if (Objects.isNull(file)) {
            LOGGER.error("文件为空");
            return null;
        }
        List<Object> objectList;
        try (InputStream inputStream = file.getInputStream()) {
            objectList = EasyExcel.read(inputStream)
                    .autoTrim(true)
                    .headRowNumber(1)
                    .autoCloseStream(true)
                    .doReadAllSync();
        } catch (IOException e) {
            LOGGER.error("文件读取失败", e);
            return null;
        }
        return objectList;
    }

    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {
        return ExcelExportOperations.builder()
                .fieldFormater("status", (a) -> {
                    return Objects.deepEquals(String.valueOf(a), "0") ? "禁用" : "启用";
                })
                .headers(columns)
                .value();
    }


}