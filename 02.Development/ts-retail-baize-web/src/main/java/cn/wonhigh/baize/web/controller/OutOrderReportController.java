package cn.wonhigh.baize.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.annotation.JsonVariable;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.data.metadata.DataEntry;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.manager.IManager;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.manager.ios.IOutOrderReportManager;
import cn.wonhigh.baize.model.dto.OutReportDTO;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @created 2023/2/17 11:08
 */
@RestController
@RequestMapping("/outOrderReport")
public class OutOrderReportController extends ApiController<DataEntry, String> {

	@Resource
	private IOutOrderReportManager outOrderReportManager;

	@Override
	protected IManager<DataEntry, String> getManager() {
		return null;
	}


	@ResponseBody
	@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/list")
	@Override
	public ApiResult<PageResult<DataEntry>> selectByPage(Query query, Pagenation page) {

		ApiResult<PageResult<DataEntry>> resultApiResult = getPageResultApiResult(query);
		if (resultApiResult != null) return resultApiResult;


		long total = page.getTotal();
		if (total <= 0) {
			total = outOrderReportManager.selectCount(query);
		}
		if (total == 0)
			return ApiResult.ok(new PageResult<DataEntry>(null, total));

		List<OutReportDTO> rows = outOrderReportManager.selectPage(query, page);

		List<DataEntry> rowsDataEntry = Opt.ofEmptyAble(rows)
				.orElse(new ArrayList<>(0))
				.stream().map(item -> {
					DataEntry dataEntry = new DataEntry();
					dataEntry.putAll(BeanUtil.beanToMap(item));
					return dataEntry;

				}).collect(Collectors.toList());

		return ApiResult.ok(new PageResult<>(rowsDataEntry, total));

	}

	private static ApiResult<PageResult<DataEntry>> getPageResultApiResult(Query query) {
		String orderSubNo = query.findValue("orderSubNo"),
				outOrderId = query.findValue("outOrderId"),
				orderSourceNo = query.findValue("orderSourceNo");
		if (StrUtil.isNotBlank(orderSubNo)) {
			List<String> nos = StrUtil.split(orderSubNo, ",");
			if (nos.size() > 200) {
				return ApiResult.error("平台订单号太长了");
			}
			query.and("orderSubNos", nos);
		}
		if (StrUtil.isNotBlank(outOrderId)) {
			List<String> nos = StrUtil.split(outOrderId, ",");
			if (nos.size() > 200) {
				return ApiResult.error("外部订单号太长了");
			}
			query.and("outOrderIds", nos);
		}
		if (StrUtil.isNotBlank(orderSourceNo)) {
			List<String> nos = StrUtil.split(orderSourceNo, ",");
			if (nos.size() > 5) {
				return ApiResult.error("三级来源太长了");
			}
			query.and("orderSourceNos", nos);
		}
		return null;
	}


	@RequestMapping("/exportExcel")
	public void exportExcel(Query query, Pagenation page, @JsonVariable("_columns") ExcelColumn[][] columns, HttpServletResponse response) {

		try {

			ApiResult<PageResult<DataEntry>> resultApiResult = getPageResultApiResult(query);
			if (resultApiResult != null) {
				responseJson(response, resultApiResult);
				return;
			}


			long total = page.getTotal();
			if (total <= 0) {
				total = outOrderReportManager.selectCount(query);
			}
			if (total == 0) {
				responseJson(response, ApiResult.error("为获取到有效数据"));
				return;
			}

			response.reset();
			// 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setCharacterEncoding("utf-8");
			// 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
			String fileName = URLEncoder.encode("已发已退报表", "UTF-8");
			response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

			page.setPageSize(280);
			ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), OutReportDTO.class)
					.excludeColumnFiledNames(Lists.newArrayList("statusStr", "refBillNo", "logisticsCompanyName","sendBrandName","returnBrandName"))
					.excelType(ExcelTypeEnum.XLSX).build();
			WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
			int pages = PageUtil.totalPage((int) total, 280);
			for (int i = 1; i <= pages; i++) {
				page.setPageIndex(i);
				List<OutReportDTO> rows = outOrderReportManager.selectPage(query, page);
				excelWriter.write(rows, writeSheet);
			}
			excelWriter.finish();

		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}


	}


	private void responseJson(HttpServletResponse response, ApiResult<?> apiResult) {
		response.reset();
		response.setContentType("application/json");
		response.setCharacterEncoding("utf-8");
		try {
			response.getWriter().println(JsonUtils.toJson(apiResult));
		} catch (Exception ignored) {
		}
	}


}
