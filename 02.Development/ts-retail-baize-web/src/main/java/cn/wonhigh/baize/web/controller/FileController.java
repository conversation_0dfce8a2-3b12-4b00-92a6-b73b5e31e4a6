package cn.wonhigh.baize.web.controller;

import cn.mercury.utils.Helper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

@RequestMapping("/file")
@Controller
public class FileController {


    public static final Logger LOGGER = LoggerFactory.getLogger(FileController.class);

    @RequestMapping("/download")
    public void downloadTemplate(@RequestParam(name = "fileName") String fileName, HttpServletResponse response) {

        InputStream inputStream;

        try {
            inputStream = getClass().getResourceAsStream("/template/"+fileName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        fileName = Helper.encode(fileName);// new String(fileName.getBytes("ISO-8859-1"), "UTF-8");
        // 文件名
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setHeader("Pragma", "no-cache");

        try {
            if (inputStream != null) {
                Helper.copyStream(inputStream, response.getOutputStream());
                response.flushBuffer();
            }
        } catch (Exception e) {
            LOGGER.error("download file error.", e);
        }finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException ignored) {

            }
        }
    }
}
