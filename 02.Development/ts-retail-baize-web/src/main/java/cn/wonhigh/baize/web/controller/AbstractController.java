package cn.wonhigh.baize.web.controller;

import cn.mercury.domain.IEntry;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExport;
import topmall.framework.io.excel.EntryResultHandler;
import topmall.framework.web.controller.ApiController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * @Name:
 * @Description:
 * @Copyright: Copyright (c) 2020 wonhigh
 * @Author: wlw
 * @Date : 2024/11/21 15:30
 * @Version: v1.0.0
 */
public abstract class AbstractController<T extends IEntry, K> extends ApiController {


    protected void export(String fileName, List<T> dataList, EntryResultHandler<T> handler,
                       ExcelColumn[][] columns, HttpServletResponse response) throws Exception{
        if (handler == null) {
            handler = new EntryResultHandler<T>(true, this.getPersistentClass()) {
            };
        }
        try (final ExcelExport excel = new ExcelExport(columns)) {
            handler.setExcel(excel);
            excel.open(fileName);
            if(dataList != null) {
                for(T item : dataList) {
                    handler.handleResult(item);
                }
            }
            OutputStream stream = getOutputFileStream(fileName, response);
            excel.flush(stream);
        } finally {
            handler.close();
        }
    }

    protected OutputStream getOutputFileStream(String fileName, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        String name = new String(fileName.getBytes("gb2312"), "iso-8859-1");
        // 文件名
        response.setHeader("Content-Disposition", "attachment;filename=" + name + ".xlsx");
        response.setHeader("Pragma", "no-cache");
        return response.getOutputStream();
    }


}
