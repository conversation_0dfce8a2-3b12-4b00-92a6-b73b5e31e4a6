package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.KeyValue;
import cn.mercury.basic.UUID;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.model.dto.AuthorityUserDataDto;
import cn.wonhigh.baize.model.entity.gms.OrgUnitBrandRel;
import cn.wonhigh.baize.model.entity.ios.InternetAppletShopSetting;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import cn.wonhigh.baize.model.enums.StockTypeEnum;
import cn.wonhigh.baize.model.enums.StoreTypeEnums;
import cn.wonhigh.baize.service.gms.IOrgUnitBrandRelService;
import cn.wonhigh.baize.service.ios.IInternetAppletShopSettingService;
import cn.wonhigh.baize.utils.common.PageUtil;
import cn.wonhigh.retail.uc.common.api.model.AuthorityUserBrand;
import cn.wonhigh.retail.uc.common.api.model.AuthorityUserRegion;
import cn.wonhigh.retail.uc.common.api.model.AuthorityUserStore;
import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.date.DateStringConverter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.yougou.logistics.base.common.exception.RpcException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.security.Authorization;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

@RestController
@RequestMapping("/internetAppletShopSetting")
public class InternetAppletShopSettingController {
    private static final Logger logger = LoggerFactory.getLogger(InternetAppletShopSettingController.class);

    @Reference
    private AuthorityUserDataApi authorityUserDataApi;
    @Autowired
    private IOrgUnitBrandRelService iOrgUnitBrandRelService;
    @Autowired
    private IInternetAppletShopSettingService iInternetAppletShopSettingService;


    @PostMapping("/region")
    public ApiResult<PageResult<AuthorityUserRegion>> region(AuthorityUserDataDto dto, String shopNo) {
        return execute(() -> {
            Stream<AuthorityUserRegion> regionList = userAuthRegionNos(null).stream();

            if (StringUtils.isNotBlank(dto.getQ())) {
                regionList = regionList.filter(v -> StringUtils.containsIgnoreCase(v.getRegionNo(), dto.getQ())
                        || StringUtils.containsIgnoreCase(v.getRegionName(), dto.getQ()));
            }

            if (StringUtils.isNotBlank(shopNo)) {
                Map<String, KeyValue<String, String>> stringKeyValueMap = iOrgUnitBrandRelService.selectValidStoreNoZones(Arrays.asList(shopNo));
                if (CollectionUtils.isEmpty(stringKeyValueMap)) {
                    return new PageResult<>(new ArrayList<>(), 0);
                }
                String zoneNo = stringKeyValueMap.get(shopNo).getKey();
                regionList = regionList.filter(v -> zoneNo.equals(v.getRegionNo()));
            }

            List<AuthorityUserRegion> result = regionList.sorted(Comparator.comparing(AuthorityUserRegion::getRegionNo))
                    .collect(Collectors.toList());
            return PageUtil.startPage(result, dto.getPage(), dto.getRows());
        });
    }

    @PostMapping("/brandNo")
    public ApiResult<PageResult<AuthorityUserBrand>> shop(AuthorityUserDataDto dto, String shopNo) {
        return execute(() -> {
            Stream<AuthorityUserBrand> authorityUserBrands = userAuthBrands(null).stream();


            if (StringUtils.isNotBlank(dto.getQ())) {
                authorityUserBrands = authorityUserBrands.filter(v -> StringUtils.containsIgnoreCase(v.getBrandDetailNo(), dto.getQ())
                        || StringUtils.containsIgnoreCase(v.getBrandDetailCname(), dto.getQ()));
            }
            if (StringUtils.isNotBlank(shopNo)) {
                Set<String> brandNos = iOrgUnitBrandRelService.selectValidBrandNoByStoreNo(shopNo);
                authorityUserBrands = authorityUserBrands.filter(v -> brandNos.contains(v.getBrandDetailNo()));
            }

            List<AuthorityUserBrand> collect = authorityUserBrands.sorted(Comparator.comparing(AuthorityUserBrand::getBrandDetailNo))
                    .collect(Collectors.toList());
            return PageUtil.startPage(collect, dto.getPage(), dto.getRows());
        });
    }

    @PostMapping("/sd/shop")
    public ApiResult<PageResult<AuthorityUserStore>> shop(AuthorityUserDataDto dto, String regionNos, String brandNo) {
        return execute(() -> {
            if (dto.getPage() == null || dto.getPage() <= 0) {
                dto.setPage(1);
            }
            if (dto.getRows() == null) {
                dto.setRows(10);
            }

            List<AuthorityUserBrand> authorityUserBrands = userAuthBrands(brandNo);
            if (CollectionUtils.isEmpty(authorityUserBrands)) {
                return PageUtil.startPage(null, dto.getPage(), dto.getRows());
            }
            List<AuthorityUserRegion> authorityUserRegions = userAuthRegionNos(covertResionNos(regionNos));
            if (CollectionUtils.isEmpty(authorityUserRegions)) {
                return PageUtil.startPage(null, dto.getPage(), dto.getRows());
            }

            List<String> brandNos = authorityUserBrands.stream().map(brand -> brand.getBrandDetailNo()).collect(Collectors.toList());
            List<String> regionNoSet = authorityUserRegions.stream().map(region -> region.getRegionNo()).collect(Collectors.toList());

            List<KeyValue<String, String>> storeNoNames = iOrgUnitBrandRelService.pageValidShop(
                    regionNoSet,
                    brandNos,
                    dto.getQ(),
                    (dto.getPage() - 1) * dto.getRows(),
                    dto.getRows());

            List<AuthorityUserStore> datas = storeNoNames.stream()
                    .map(storeNoName -> {
                        AuthorityUserStore store = new AuthorityUserStore();
                        store.setStoreNo(storeNoName.getKey());
                        store.setStoreNm(storeNoName.getValue());
                        return store;
                    })
                    .sorted(Comparator.comparing(AuthorityUserStore::getStoreNo))
                    .collect(Collectors.toList());

            return new PageResult<AuthorityUserStore>(datas, iOrgUnitBrandRelService.countValidShop(regionNoSet, brandNos, dto.getQ()));
        });
    }


    @PostMapping(value = "/sd/list")
    public ApiResult<PageResult<ShopDetailDTO>> selectByPage(String regionNos,
                                                             String shopNo,
                                                             String brandNo,
                                                             Integer status,
                                                             String stockType,
                                                             Pagenation page) {
        return execute(() -> {
            List<ShopDetailDTO> shopDetailDTOS = query(covertResionNos(regionNos), shopNo, brandNo, stockType, status);
            return PageUtil.startPage(shopDetailDTOS, page.getPageIndex(), page.getPageSize());
        });
    }

    @PostMapping(value = "/sd/create")
    public ApiResult create(InternetAppletShopSetting entry) {
        return execute(() -> {
            checkAndFillDefaultValue(entry);
            boolean haveAuth = userHaveShopAuth(entry.getShopNo());
            if (!haveAuth) {
                throw new IllegalStateException("不拥有该店铺权限");
            }

            InternetAppletShopSetting oldShop = iInternetAppletShopSettingService.findByShopNo(entry.getShopNo());
            if (oldShop != null) {
                throw new IllegalStateException("该店铺已存在");
            }

            boolean insert = iInternetAppletShopSettingService.insert(entry);
            if (!insert) {
                throw new IllegalStateException("保存失败");
            }
            saveLogs(entry, OpscodeEnum.ADD);
            return null;
        });
    }

    @PostMapping(value = "/sd/update")
    public ApiResult update(InternetAppletShopSetting entry) {
        return execute(() -> {
            checkAndFillDefaultValue(entry);
            boolean haveAuth = userHaveShopAuth(entry.getShopNo());
            if (!haveAuth) {
                throw new IllegalStateException("不拥有该店铺权限");
            }

            InternetAppletShopSetting oldShop = iInternetAppletShopSettingService.findByShopNo(entry.getShopNo());
            if (oldShop == null) {
                throw new IllegalStateException("店铺不存在");
            }

            InternetAppletShopSetting update = new InternetAppletShopSetting();
            update.setShopNo(entry.getShopNo());
            update.setStockType(entry.getStockType());
            update.setStartTime(entry.getStartTime());
            update.setEndTime(entry.getEndTime());
            update.setSharingRatio(entry.getSharingRatio());
            update.setUpdateUser(entry.getUpdateUser());
            update.setUpdateTime(entry.getUpdateTime());
            boolean succ = iInternetAppletShopSettingService.updateByShopNo(update);
            if (!succ) {
                throw new IllegalStateException("修改失败");
            }
            saveLogs(update, OpscodeEnum.UP);
            return null;
        });
    }


    @PostMapping(value = "/sd/export")
    public void export(String regionNos,
                       String shopNo,
                       String brandNo,
                       Integer status,
                       String stockType,
                       HttpServletResponse response) {
        try {
            List<ShopDetailDTO> shopDetailDTOS = query(covertResionNos(regionNos), shopNo, brandNo, stockType, status);
            export(shopDetailDTOS, response, "库存模式配置");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            responseJson(response, ApiResult.error("导出店铺异常," + e.getMessage()));
        }
    }


    private List<ShopDetailDTO> query(Set<String> regionNos, String shopNo, String brandNo,
                                      String stockType, Integer status) throws RpcException {

        Function<List<InternetAppletShopSetting>, Map<String, KeyValue<String, String>>> store2ZoneNoName = settings -> {
            List<String> stores = settings.stream().map(setting -> setting.getShopNo()).collect(Collectors.toList());
            Map<String, KeyValue<String, String>> storeZoneMap = iOrgUnitBrandRelService.selectValidStoreNoZones(stores);
            return storeZoneMap;
        };

        List<StockTypeEnum> stockTypes = StringUtils.isEmpty(stockType) ? null : InternetAppletShopSetting.stockTypeList(stockType);
        Function<List<StockTypeEnum>, Boolean> stockTypeFilter = new Function<List<StockTypeEnum>, Boolean>() {
            @Override
            public Boolean apply(List<StockTypeEnum> stockTypeEnums) {
                if (stockTypes == null) {
                    return true;
                }
                return stockTypeEnums.containsAll(stockTypes);
            }
        };

        Stream<List<String>> autoShopNoStream = Stream.empty();
        if (!StringUtils.isEmpty(shopNo)) {
            List<OrgUnitBrandRel> orgUnitBrandRels = userHaveShopRelAuth(shopNo);
            if (CollectionUtils.isEmpty(orgUnitBrandRels)) {
                throw new IllegalStateException("不拥有该店铺权限");
            }
            Optional<OrgUnitBrandRel> first = orgUnitBrandRels.stream()
                    .filter(orgUnitBrandRel -> {
                        if (!CollectionUtils.isEmpty(regionNos)) {
                            return regionNos.contains(orgUnitBrandRel.getZoneNo());
                        }
                        return true;
                    })
                    .filter(orgUnitBrandRel -> {
                        if (StringUtils.isEmpty(brandNo)) {
                            return true;
                        }
                        return brandNo.equals(orgUnitBrandRel.getBrandNo());
                    }).findFirst();
            if (first.isPresent()) {
                autoShopNoStream = Stream.of(Collections.singletonList(shopNo));
            }
        } else {
            autoShopNoStream = userHaveAuthShops(regionNos, brandNo, 4000);
        }

        List<ShopDetailDTO> result = autoShopNoStream
                .map(shopNos -> {
                    return selectSds(shopNos);
                })
                .filter(settings -> !CollectionUtils.isEmpty(settings))
                .flatMap(settings -> {
                    Map<String, KeyValue<String, String>> storeZoneMap = store2ZoneNoName.apply(settings);
                    return settings
                            .stream()
                            .filter(setting -> {
                                if (status == null) {
                                    return true;
                                }
                                return (status == 1) == (setting.active());
                            })
                            .filter(setting -> {
                                List<StockTypeEnum> stockTypeEnums = setting.stockTypeList();
                                return stockTypeFilter.apply(stockTypeEnums);
                            })
                            .map(setting -> {
                                return covertShopDTO(storeZoneMap.get(setting.getShopNo()), setting);
                            });
                })
                .sorted(Comparator.comparing(shopDetailDTO -> {
                    return shopDetailDTO.getZoneNo() + shopDetailDTO.getShopNo();
                }))
                .collect(Collectors.toList());

        return result;
    }

    private Stream<List<String>> userHaveAuthShops(Set<String> regionNos, String brandNo, int pageSize) throws RpcException {
        List<AuthorityUserBrand> authorityUserBrands = userAuthBrands(brandNo);
        if (CollectionUtils.isEmpty(authorityUserBrands)) {
            return Stream.empty();
        }
        List<AuthorityUserRegion> authorityUserRegions = userAuthRegionNos(regionNos);
        if (CollectionUtils.isEmpty(authorityUserRegions)) {
            return Stream.empty();
        }


        List<String> brandNos = authorityUserBrands.stream().map(brand -> brand.getBrandDetailNo()).collect(Collectors.toList());
        List<String> regionNoSet = authorityUserRegions.stream().map(region -> region.getRegionNo()).collect(Collectors.toList());


        Iterator<List<String>> iterator = new Iterator<List<String>>() {
            int pageId = 1;

            List<String> result = null;

            @Override
            public boolean hasNext() {
                // 最后一次没查询满代表后续没数据了，则返回false
                if (result != null && !result.isEmpty() && result.size() < pageSize) {
                    return false;
                }

                List<KeyValue<String, String>> storeNoNames = iOrgUnitBrandRelService.pageValidShop(
                        regionNoSet,
                        brandNos,
                        null,
                        (pageId - 1) * pageSize,
                        pageSize);

                result = storeNoNames.stream().map(storeNoName -> storeNoName.getKey()).collect(Collectors.toList());
                if (result == null || result.isEmpty()) {
                    return false;
                }
                pageId++;
                return true;
            }

            @Override
            public List<String> next() {
                return result;
            }
        };
        Spliterator<List<String>> spliterator = Spliterators.spliteratorUnknownSize(iterator, Spliterator.NONNULL);
        return StreamSupport.stream(spliterator, false);
    }

    private List<InternetAppletShopSetting> selectSds(List<String> stores) {
        if (CollectionUtils.isEmpty(stores)) {
            return new ArrayList<>();
        }
        List<InternetAppletShopSetting> internetAppletShopSettings = iInternetAppletShopSettingService.selectSdByShopNos(stores);
        return internetAppletShopSettings;
    }


    private List<AuthorityUserBrand> userAuthBrands(String brandNo) throws RpcException {
        Integer userId = Integer.valueOf(Authorization.getUser().getId());
        List<AuthorityUserBrand> authorityUserBrands = authorityUserDataApi.userBrand(userId);
        if (StringUtils.isNotBlank(brandNo)) {
            authorityUserBrands = authorityUserBrands.stream()
                    .filter(v -> v.getBrandDetailNo().equals(brandNo))
                    .limit(1)
                    .collect(Collectors.toList());
        }
        return authorityUserBrands;
    }

    private List<AuthorityUserRegion> userAuthRegionNos(Set<String> regionNos) throws RpcException {
        Integer userId = Integer.valueOf(Authorization.getUser().getId());
        List<AuthorityUserRegion> regionList = authorityUserDataApi.userRegion(userId).stream()
                .filter(v -> !wholesaleRegion(v.getRegionNo()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(regionNos)) {
            regionList = regionList.stream()
                    .filter(v -> regionNos.contains(v.getRegionNo()))
                    .collect(Collectors.toList());
        }
        return regionList;
    }

    private boolean userHaveShopAuth(String shopNo) throws RpcException {
        List<OrgUnitBrandRel> orgUnitBrandRels = userHaveShopRelAuth(shopNo);
        return !CollectionUtils.isEmpty(orgUnitBrandRels);
    }

    private List<OrgUnitBrandRel> userHaveShopRelAuth(String shopNo) throws RpcException {
        Set<String> authorityUserBrands = userAuthBrands(null).stream()
                .map(v -> v.getBrandDetailNo())
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(authorityUserBrands)) {
            return new ArrayList<>();
        }

        Set<String> authRegionNos = userAuthRegionNos(null)
                .stream().map(v -> v.getRegionNo())
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(authRegionNos)) {
            return new ArrayList<>();
        }

        List<OrgUnitBrandRel> orgUnitBrandRels = iOrgUnitBrandRelService.selectValidByStoreNo(shopNo);
        return orgUnitBrandRels.stream()
                .filter(orgUnitBrandRel -> {
                    Integer storeType = orgUnitBrandRel.getStoreType();
                    StoreTypeEnums storeTypeEnum = StoreTypeEnums.getByIntType(storeType);
                    return storeTypeEnum == StoreTypeEnums.SHOP;
                })
                .filter(orgUnitBrandRel -> authRegionNos.contains(orgUnitBrandRel.getZoneNo()))
                .filter(orgUnitBrandRel -> {
                    return authorityUserBrands.contains(orgUnitBrandRel.getBrandNo());
                })
                .collect(Collectors.toList());
    }


    private Set<String> covertResionNos(String regionNos) {
        if (StringUtils.isBlank(regionNos)) {
            return null;
        }

        Set<String> regionNoSet = Arrays.stream(regionNos.trim().split(","))
                .collect(Collectors.toCollection(HashSet::new));
        return regionNoSet;
    }

    private void checkAndFillDefaultValue(InternetAppletShopSetting entry) {
        String shopNo = entry.getShopNo();
        if (StringUtils.isEmpty(shopNo)) {
            throw new IllegalArgumentException("店铺编码不能为空");
        }
        String shopName = entry.getShopName();
        if (StringUtils.isEmpty(shopName)) {
            throw new IllegalArgumentException("店铺名称不能为空");

        }
        String stockType = entry.getStockType();
        if (StringUtils.isEmpty(stockType)) {
            throw new IllegalArgumentException("售卖品相不能为空");
        }
        List<StockTypeEnum> stockTypeList = entry.stockTypeList();
        if (!stockTypeList.contains(StockTypeEnum.NORMAL)) {
            throw new IllegalArgumentException("售卖品相必须包含正品");
        }
        if (stockTypeList.contains(null)) {
            throw new IllegalArgumentException("售卖品相错误");
        }


        Date startTime = entry.getStartTime();
        if (startTime == null) {
            throw new IllegalArgumentException("生效时间不能为空");
        }
        Date endTime = entry.getEndTime();
        if (endTime == null) {
            throw new IllegalArgumentException("失效时间不能为空");
        }
        if (startTime.after(endTime)) {
            throw new IllegalArgumentException("生效时间不能大于失效时间");
        }

        Integer sharingRatio = entry.getSharingRatio();
        if (sharingRatio == null) {
            throw new IllegalArgumentException("共享比例不能为空");
        }
        if (sharingRatio < 0 || sharingRatio > 100) {
            throw new IllegalArgumentException("共享比例不能小于0或大于100");
        }

        String userName = Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown");
        entry.setCreateUser(userName);
        entry.setUpdateUser(userName);
        entry.setCreateTime(new Date());
        entry.setUpdateTime(new Date());
        //实店
        entry.setGroups(2);
        //共享
        entry.setType(2);
        entry.setId(UUID.gernerate());
    }


    void saveLogs(InternetAppletShopSetting item, OpscodeEnum opsCode) {
        String json = JsonUtils.toJson(item);
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                        .setOpscode(opsCode.getCode()).
                        setSyscode(String.valueOf(MenuTypeEnums.SHOP_SETTING.getType())).
                        setSysname(MenuTypeEnums.SHOP_SETTING.getDesc()).
                        setKeyword1(item.getShopNo()).
                        setKeyword1info("店铺编码:" + item.getShopNo() + "(" + item.getShopName() + ")").
                        setRemark(json)
                        .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                        .setCreateTime(new Date())
                        .build()));
    }

    private <T> ApiResult<T> execute(Callable<T> callable) {
        try {
            T call = callable.call();
            return ApiResult.ok(call);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResult.error(e.getMessage());
        }
    }

    private void responseJson(HttpServletResponse response, ApiResult<?> apiResult) {
        // response.reset();
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        try {
            response.getWriter().println(JsonUtils.toJson(apiResult));
        } catch (Exception ignored) {
        }
    }

    private void export(List<ShopDetailDTO> resultDtos,
                        HttpServletResponse response,
                        String fileName
    ) throws IOException {
        if (CollectionUtils.isEmpty(resultDtos)) {
            responseJson(response, ApiResult.error("未查询到数据"));
            return;
        }

        response.reset();
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), ShopDetailDTO.class)
                .excludeColumnFiledNames(Arrays.asList("stockType"))
                .excelType(ExcelTypeEnum.XLSX).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

        List<List<ShopDetailDTO>> partition = Lists.partition(resultDtos, 100);
        for (List<ShopDetailDTO> list : partition) {
            excelWriter.write(list, writeSheet);
        }
        excelWriter.finish();
    }


    private ShopDetailDTO covertShopDTO(KeyValue<String, String> zoneNoName, InternetAppletShopSetting internetAppletShopSetting) {
        if (internetAppletShopSetting == null) {
            return null;
        }
        ShopDetailDTO shopDetailDTO = new ShopDetailDTO();
        if (zoneNoName != null) {
            shopDetailDTO.setZoneNo(zoneNoName.getKey());
            shopDetailDTO.setZoneName(zoneNoName.getValue());
        }
        shopDetailDTO.setShopNo(internetAppletShopSetting.getShopNo());
        shopDetailDTO.setShopName(internetAppletShopSetting.getShopName());
        shopDetailDTO.setStockType(internetAppletShopSetting.getStockType());

        List<StockTypeEnum> stockTypeEnums = internetAppletShopSetting.stockTypeList();
        shopDetailDTO.setStockTypeName(stockTypeEnums.stream().map(StockTypeEnum::getDesc).collect(Collectors.joining(",")));

        shopDetailDTO.setStatus(internetAppletShopSetting.active() ? "启用" : "禁用");

        shopDetailDTO.setSharingRatio(internetAppletShopSetting.getSharingRatio());
        shopDetailDTO.setStartTime(internetAppletShopSetting.getStartTime());
        shopDetailDTO.setEndTime(internetAppletShopSetting.getEndTime());
        shopDetailDTO.setCreateTime(internetAppletShopSetting.getCreateTime());
        shopDetailDTO.setCreateUser(internetAppletShopSetting.getCreateUser());
        shopDetailDTO.setUpdateTime(internetAppletShopSetting.getUpdateTime());
        shopDetailDTO.setUpdateUser(internetAppletShopSetting.getUpdateUser());
        return shopDetailDTO;
    }

    /**
     * 批发区域
     * @param regionNo
     * @return
     */
    private static boolean wholesaleRegion(String regionNo) {
        // region 完全等于 0 或 1 或 2 或 3 或 4 或 5 或 6 或 7 或 8 或 9
        return regionNo.matches("[0-9]");
    }

    public static class ShopDetailDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ExcelProperty(value = "所属小区编码")
        private String zoneNo;

        @ExcelProperty(value = "所属小区名称")
        private String zoneName;

        @ExcelProperty(value = "店铺编码")
        private String shopNo;

        @ExcelProperty(value = "店铺名称")
        private String shopName;

        private String stockType;

        @ExcelProperty(value = "售卖品相")
        private String stockTypeName;

        @ExcelProperty(value = "是否启用")
        private String status;

        @ExcelProperty(value = "共享比例(%)")
        private Integer sharingRatio;

        @ExcelProperty(value = "生效时间(含)", converter = DateStringConverter.class)
        // @DateTimeFormat("yyyy-MM-dd")
        private Date startTime;

        @ExcelProperty(value = "失效时间(含)", converter = DateStringConverter.class)
        // @DateTimeFormat("yyyy-MM-dd")
        private Date endTime;

        @ExcelProperty(value = "创建时间", converter = DateStringConverter.class)
        private Date createTime;

        @ExcelProperty(value = "创建人")
        private String createUser;

        @ExcelProperty(value = "最后更新时间", converter = DateStringConverter.class)
        private Date updateTime;

        @ExcelProperty(value = "最后更新人")
        private String updateUser;


        public String getZoneNo() {
            return zoneNo;
        }

        public void setZoneNo(String zoneNo) {
            this.zoneNo = zoneNo;
        }

        public String getZoneName() {
            return zoneName;
        }

        public void setZoneName(String zoneName) {
            this.zoneName = zoneName;
        }

        public String getShopNo() {
            return shopNo;
        }

        public void setShopNo(String shopNo) {
            this.shopNo = shopNo;
        }

        public String getShopName() {
            return shopName;
        }

        public void setShopName(String shopName) {
            this.shopName = shopName;
        }

        public String getStockType() {
            return stockType;
        }

        public void setStockType(String stockType) {
            this.stockType = stockType;
        }

        public String getStockTypeName() {
            return stockTypeName;
        }

        public void setStockTypeName(String stockTypeName) {
            this.stockTypeName = stockTypeName;
        }

        public Integer getSharingRatio() {
            return sharingRatio;
        }

        public void setSharingRatio(Integer sharingRatio) {
            this.sharingRatio = sharingRatio;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }

        public Date getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(Date updateTime) {
            this.updateTime = updateTime;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public String getCreateUser() {
            return createUser;
        }

        public void setCreateUser(String createUser) {
            this.createUser = createUser;
        }

        public String getUpdateUser() {
            return updateUser;
        }

        public void setUpdateUser(String updateUser) {
            this.updateUser = updateUser;
        }
    }


}