package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.manager.oms.IInventoryBoardManager;
import cn.wonhigh.baize.model.dto.WarehouseRatioDetailDto;
import cn.wonhigh.baize.model.dto.WarehouseRatioDto;
import cn.wonhigh.baize.model.dto.WarehouseRatioExportDto;
import cn.wonhigh.baize.model.entity.oms.OmsWarehouse;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import topmall.framework.io.excel.EntryResultHandler;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@CrossOrigin
@RestController
@RequestMapping("/inventory/board")
public class InventoryBoardController  extends AbstractController<WarehouseRatioExportDto,Integer> {
    @Autowired
    private IInventoryBoardManager iInventoryBoardManager;

    @Override
    protected IManager<OmsWarehouse, Integer> getManager() {
        return null;
    }
    @ApiOperation("分页加载虚拟仓库存比例")
    @PostMapping("/selectPageForWarehouseRatio")
    public ApiResult selectPageForWarehouseRatio(Query query, Pagenation page){
        PageResult<WarehouseRatioDto> result = iInventoryBoardManager.selectPageForWarehouseRatio(query, page);
        return ApiResult.ok(result);
    }

    @ApiOperation("加载详情")
    @PostMapping("/loadWarehouseRatioByWarehouseCode")
    public ApiResult loadWarehouseRatioByWarehouseCode(Query query, Pagenation page){
        PageResult<WarehouseRatioDetailDto> result = iInventoryBoardManager.loadWarehouseRatioByWarehouseCode(query, page);
        return ApiResult.ok(result);
    }

    @ApiOperation("看板导出")
    @RequestMapping(value = "/exportExcel", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportExcel(Query query, HttpServletResponse response){
        Map<String, Object> param = query.asMap();
        List<String> warehouseCodeParams = null;
        List<String> brandCodeParams = null;
        if(StringUtils.isNotEmpty((String)param.get("warehouseCode"))){
            warehouseCodeParams = Lists.newArrayList(param.get("warehouseCode").toString().split(";"));
        }
        if(StringUtils.isNotEmpty((String)param.get("brandCode"))){
            brandCodeParams = Lists.newArrayList(param.get("brandCode").toString().split(";"));
        }
        List<WarehouseRatioExportDto> exportDtos = iInventoryBoardManager.selectWarehouseRatioByWarehouseCode(warehouseCodeParams,brandCodeParams);
        if(CollectionUtils.isNotEmpty(exportDtos)){
            String fileName = "库存放大看板";
             EntryResultHandler handler = new EntryResultHandler(true, this.getPersistentClass());
            try {
                export(fileName,exportDtos, handler, getColums(), response);
            } catch (Exception e) {
                logger.error("导出失败", e);
            }
        }
    }

    private static ExcelColumn[][] getColums() {
        ExcelColumn[][] columns = new ExcelColumn[1][9];
        columns[0][0] = new ExcelColumn("仓库编码", "warehouseCode");
        columns[0][1] = new ExcelColumn("仓库名称", "warehouseName");
        columns[0][2] = new ExcelColumn("店铺编码", "shopCode");
        columns[0][3] = new ExcelColumn("店铺名称", "shopName");
        columns[0][4] = new ExcelColumn("品牌编码", "brandCode");
        columns[0][5] = new ExcelColumn("品牌名称", "brandName");
        columns[0][6] = new ExcelColumn("中台同步比例", "platformAmplifyRatio");
        columns[0][7] = new ExcelColumn("OMS同步比例", "omsAmplifyRatio");
        columns[0][8] = new ExcelColumn("合计同步比例", "shopAmplifyRatio");
        return columns;
    }


}
