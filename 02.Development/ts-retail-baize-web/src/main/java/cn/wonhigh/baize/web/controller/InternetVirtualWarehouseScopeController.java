/**
 * by
 **/
package cn.wonhigh.baize.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.manager.IManager;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.business.virtualwarehousescope.ChangeStatusValidateService;
import cn.wonhigh.baize.business.virtualwarehousescope.ValidateResult;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.*;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeSave;
import cn.wonhigh.baize.model.dto.virtualwarehouse.InternetVirtualWarehouseScopeStatusChangeDto;
import cn.wonhigh.baize.model.entity.gms.*;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import cn.wonhigh.baize.model.enums.StoreTypeEnums;
import cn.wonhigh.baize.utils.common.QueryUtil;
import com.alibaba.excel.EasyExcel;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
import topmall.framework.security.Authorization;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ValidationException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/internet/virtual/warehouse/scope")
public class InternetVirtualWarehouseScopeController extends ApiController<InternetVirtualWarehouseScope, String> {


    @Autowired
    private IInternetVirtualWarehouseScopeManager manager;

    @Autowired
    private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;

    @Autowired
    private IOrderUnitManager orderUnitManager;

    @Autowired
    private IGmsStoreManager storeManager;

    @Autowired
    private IShopManager shopManager;

    @Autowired
    private IOrgUnitBrandRelManager orgUnitBrandRelManager;

    public static final Logger LOGGER = LoggerFactory.getLogger(InternetVirtualWarehouseScopeController.class);

    protected IManager<InternetVirtualWarehouseScope, String> getManager() {
        return manager;
    }

    //可导入的店仓类型范围
    @Value("#{'${shop.multi:050101,050102,050103,050104,050201,050202,050203,050204,050301,050302,050401,050405,050406}'.split(',')}")
    private List<String> shopMultis = new ArrayList<>();

    @Value("#{'${vstore.needOriginalVstoreCode:QGXXGXC,QGXXGXC-RY}'.split(',')}")
    private List<String> needOriginalVstoreCode;

    @RequestMapping("/queryConfigList")
    public ApiResult<PageResult<?>> queryConfigList(HttpServletRequest req, Query query, Pagenation page) throws ManagerException {
        PageResult<InternetVirtualWarehouseScope> pageResult = new PageResult<>(null, 0);
        List<InternetVirtualWarehouseScope> list = manager.selectScopeListByPage(query.asMap(), page);
        int total = manager.selectScopeListCount(query.asMap());

        pageResult.setTotal((long) total);
        pageResult.setRows(list);
        return ApiResult.ok(pageResult);
    }


    @PostMapping({"/storeList"})
    public ApiResult<PageResult<?>> queryStoreList(HttpServletRequest req, Query query, Pagenation page) throws ManagerException {

        String vstoreCode = query.findValue("vstoreCode");
        if (StringUtils.isEmpty(vstoreCode)) {
            return ApiResult.ok(new PageResult<Object>(new ArrayList<>(0), 0));
        }


        Integer storeType = query.findValue("storeType") == null || StringUtils.isEmpty(query.findValue("storeType")) ? null : Integer.valueOf(query.findValue("storeType").toString());
        String orderUnitNo = query.findValue("orderUnitNo");
        String filterValue = query.findValue("filterValue");
        String storeNoOrName = query.findValue("storeNoOrName");
        String orderUnitNoOrName = query.findValue("orderUnitNoOrName");
        String[] storeNos = null;
        if (StrUtil.isNotBlank(filterValue)) {
            storeNos = filterValue.trim().split(",");
        }

        InternetVirtualWarehouseInfo virtualWarehouseInfo = internetVirtualWarehouseInfoManager.findByUnique(vstoreCode);
        if (virtualWarehouseInfo == null) {
            throw new ManagerException(String.format("查询聚合仓信息失败, 聚合仓=%s不存在!", vstoreCode));
        }
        Map<String, Object> params = new HashMap<>();
        PageResult<InternetVirtualWarehouseScope> pageResult = new PageResult<>(null, 0);

        List<InternetVirtualWarehouseScope> list = null;
        // 判断虚拟仓类型，总仓or子仓？
        if (virtualWarehouseInfo.getVstoreType() == 1) {// 总仓
            params.put("orderUnitNo", orderUnitNo);
            params.put("storeType", storeType);
            params.put("storeNos", storeNos);
            params.put("storeNoOrName", storeNoOrName);
            params.put("orderUnitNoOrName", orderUnitNoOrName);
            params.put("multis", shopMultis);
            int total = manager.selectStoreCount(params);
            if (total > 0) {
                list = manager.selectStoreByPage(params, page);
            }
            pageResult.setTotal((long) total);
        } else {// 子仓，在所属总仓范围内查
            if (Objects.equals(storeType, 21)) {// 添加店范围
                // 查询所属总仓店的库存范围
                params.clear();
                params.put("vstoreCode", virtualWarehouseInfo.getParentVstoreCode());
                params.put("orderUnitNo", orderUnitNo);
                params.put("storeType", storeType);
                params.put("moreStoreFlag", 1);
                params.put("status", 1);
                params.put("storeNos", storeNos);
                params.put("storeNoOrName", storeNoOrName);
                params.put("orderUnitNoOrName", orderUnitNoOrName);
                List<InternetVirtualWarehouseScope> listShopScope = manager.selectByParams(QueryUtil.mapToQuery(params));
                if (listShopScope != null && !listShopScope.isEmpty()) {// 所属总仓店的库存范围为“所有店”，查询所有店列表
                    params.clear();
                    params.put("orderUnitNo", orderUnitNo);
                    params.put("storeType", storeType);
                    params.put("multis", shopMultis);
                    int total = manager.selectStoreCount(params);
                    if (total > 0) {

                        list = manager.selectStoreByPage(params, page);
                    }
                    pageResult.setTotal((long) total);
                } else {// 所属总仓店的范围为指定店，查询指定店范围列表
                    params.clear();
                    params.put("vstoreCode", virtualWarehouseInfo.getParentVstoreCode());
                    params.put("orderUnitNo", orderUnitNo);
                    params.put("storeType", storeType);
                    params.put("moreStoreFlag", 0);
                    params.put("status", 1);
                    params.put("storeNos", storeNos);
                    params.put("storeNoOrName", storeNoOrName);
                    params.put("orderUnitNoOrName", orderUnitNoOrName);
                    int total = manager.selectCount(QueryUtil.mapToQuery(params));
                    if (total > 0) {
                        list = manager.selectByPage(QueryUtil.mapToQuery(params), page);
                    }
                    pageResult.setTotal((long) total);
                }
            } else {// 添加仓范围，在所属总仓的仓库存范围内查询
                params.clear();
                params.put("vstoreCode", virtualWarehouseInfo.getParentVstoreCode());
                params.put("orderUnitNo", orderUnitNo);
                params.put("storeType", storeType);
                params.put("moreStoreFlag", 0);
                params.put("status", 1);
                params.put("storeNos", storeNos);
                params.put("storeNoOrName", storeNoOrName);
                params.put("orderUnitNoOrName", orderUnitNoOrName);
                int total = manager.selectCount(QueryUtil.mapToQuery(params));
                if (total > 0) {
                    list = manager.selectByPage(QueryUtil.mapToQuery(params), page);
                }
                pageResult.setTotal((long) total);
            }
        }
        pageResult.setRows(list);

        return ApiResult.ok(pageResult);
    }


    @PostMapping("/save")
    public ApiResult<?> save(@RequestBody @Validated InternetVirtualWarehouseScopeSave saveData, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            return ApiResult.error(bindingResult.getFieldError().getDefaultMessage());
        }
        LOGGER.info("开始保存聚合仓范围: {}", saveData.toString());

        OrderUnit orderUnit = orderUnitManager.findByUnique(saveData.getOrderUnitNo());
        if (orderUnit == null) {
            return ApiResult.error("未发现有效的货管信息," + saveData.getOrderUnitNo());
        }
        //saveData.setOrderUnitName(orderUnit.getName());

        if (orderUnit.getType() == 1) {
            return ApiResult.error("批发货管，不能添加到虚拟仓库存范围!");
        }
        if (orderUnit.getStatus() != 1) {
            return ApiResult.error("不是有效的货管!");
        }

        InternetVirtualWarehouseInfo info = internetVirtualWarehouseInfoManager.findByUnique(saveData.getVstoreCode());
        if (info == null) {
            return ApiResult.error("未发现有效的聚合仓信息," + saveData.getVstoreCode());
        }

        List<InternetVirtualWarehouseScope> scopes = manager.selectByParams(
                Query.Where("vstoreCode",saveData.getVstoreCode())
                        .and("orderUnitNo",saveData.getOrderUnitNo()));
        if(!CollectionUtils.isEmpty(scopes)){
            Map<String,InternetVirtualWarehouseScope> scopeMap = scopes.stream().collect(Collectors.toMap(x->x.getStoreNo(), y->y, (s1,s2)->s1));
            for (InternetVirtualWarehouseScopeSave.SaveDtl addDtl : saveData.getAddDtls()) {
                addDtl.setId("");
                if(scopeMap.containsKey(addDtl.getStoreNo())){
                    throw new ManagerException("聚合仓范围数据已存在，货管编码：" + saveData.getOrderUnitNo() +",机构编码：" + addDtl.getStoreNo());
                }
            }
        }

        List<OrgUnitBrandRel> orgList = orgUnitBrandRelManager.selectStoreByUnitNo(saveData.getOrderUnitNo());
        if(CollectionUtils.isEmpty(orgList)){
            throw new ManagerException("货管编码：" + saveData.getOrderUnitNo() + "查询不到货管对应的实仓或网销店机构");
        }
        Map<String, OrgUnitBrandRel> orgUnitBrandRelMap = orgList.stream().collect(Collectors.toMap(x->x.getStoreNo(),y->y, (s1,s2)->s1));

        List<String> originalVstoreCodes = saveData.getAddDtls().stream()
                .map(InternetVirtualWarehouseScopeSave.SaveDtl::getOriginalVstoreCode)
                .filter(originalVstoreCode -> !StringUtils.isEmpty(originalVstoreCode))
                .collect(Collectors.toList());
        Map<String, InternetVirtualWarehouseInfo> virtualMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(originalVstoreCodes)){
            virtualMap = internetVirtualWarehouseInfoManager.selectByParams(Query.Where("vstoreCodeList", originalVstoreCodes))
                    .stream()
                    .collect(Collectors.toMap(InternetVirtualWarehouseInfo::getVstoreCode, v -> v));
        }
        for (InternetVirtualWarehouseScopeSave.SaveDtl addDtl : saveData.getAddDtls()) {
            OrgUnitBrandRel rel = orgUnitBrandRelMap.get(addDtl.getStoreNo());
            if(rel == null){
                throw new ManagerException("货管对应的实仓或网销店机构没有对应关系，货管编码：" + saveData.getOrderUnitNo() + ",机构编码：" + addDtl.getStoreNo());
            }
            addDtl.setStatus(1);
            addDtl.setStoreType(rel.getStoreType());
            addDtl.setStoreName(rel.getStoreName());
            addDtl.setOrderUnitName(rel.getOrderUnitName());

            String originalVstoreCode = addDtl.getOriginalVstoreCode();
            if (!info.standardVStore() || !needOriginalVstoreCode.contains(info.getVstoreCode())) {
                if (!StringUtils.isBlank(originalVstoreCode)) {
                    throw new ManagerException("非标准仓不能有'原属聚合仓'，货管编码：" + saveData.getOrderUnitNo() +",机构编码：" + addDtl.getStoreNo());
                }
            }else {
                StoreTypeEnums storeTypeEnums = StoreTypeEnums.getByIntType(rel.getStoreType());
                switch (storeTypeEnums) {
                    case SHOP:
                        if (!StringUtils.isBlank(originalVstoreCode)) {
                            throw new ManagerException("机构类型为店时不能有'原属聚合仓'，货管编码：" + saveData.getOrderUnitNo() +",机构编码：" + addDtl.getStoreNo());
                        }
                        break;
                    case STORE:
                        if (originalVstoreCode == null) {
                            throw new ManagerException("机构类型为仓时'原属聚合仓'不能为空，货管编码：" + saveData.getOrderUnitNo() +",机构编码：" + addDtl.getStoreNo());
                        }
                        InternetVirtualWarehouseInfo internetVirtualWarehouseInfo = virtualMap.get(originalVstoreCode);
                        if (internetVirtualWarehouseInfo == null) {
                            throw new ManagerException("机构类型为仓时'原属聚合仓'不存在，货管编码：" + saveData.getOrderUnitNo()
                                    +",机构编码：" + addDtl.getStoreNo()
                                    +",原属聚合仓：" + originalVstoreCode);
                        }else if (!internetVirtualWarehouseInfo.ryVStore()) {
                            throw new ManagerException("机构类型为仓时'原属聚合仓'是非冗余仓，货管编码：" + saveData.getOrderUnitNo()
                                    +",机构编码：" + addDtl.getStoreNo()
                                    +",原属聚合仓：" + originalVstoreCode);
                        }
                        addDtl.setOriginalVstoreName(internetVirtualWarehouseInfo.getVstoreName());
                        break;
                    default:
                        throw new ManagerException("货管对应的实仓或网销店机构类别错误，货管编码：" + saveData.getOrderUnitNo() + ",机构编码：" + addDtl.getStoreNo());
                }
            }

        }

        try {
            manager.saveInternetVirtualWarehouseScope(saveData, info);

//            for (InternetVirtualWarehouseScopeSave.SaveDtl addDtl : saveData.getAddDtls()) {
//                String inventoryTypeStr = "";
//                //转换数字含义
//                if(saveData.getInventoryType()==1)
//                {
//                    inventoryTypeStr = "共享";
//                }else if(saveData.getInventoryType()==2){
//                    inventoryTypeStr = "独享";
//                }else if(saveData.getInventoryType()==3){
//                    inventoryTypeStr = "电商";
//                }
//
//
//                SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
//                        new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
//                                .setOpscode("insert").setSyscode("1002").
//                                setSysname("虚拟仓-库存范围").
//                                setKeyword1(info.getVstoreCode()).
//                                setKeyword1info("聚合仓编码:" + info.getVstoreCode() + "(" + info.getVstoreName() + ")").
//                                setKeyword2(addDtl.getStoreNo()).
//                                setKeyword2info("机构:" + addDtl.getStoreNo() + "(" + addDtl.getStoreName() + ")").
//                                setRemark("新增：聚合仓编码:"+info.getVstoreCode()+"("+info.getVstoreName()+");机构:"+
//                                        addDtl.getStoreNo()+"("+(saveData.getMoreStoreFlag().equals("1")?"所有店":addDtl.getStoreName())+");货管:"+
//                                        addDtl.getOrderUnitNo()+"("+addDtl.getOrderUnitName()+");库存类型:"+inventoryTypeStr)
//                                .setCreateTime(new Date())
//                                .build()));
//            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResult.error("保存聚合仓范围数据失败," + e.getMessage());
        }
        return ApiResult.ok();
    }


    @PostMapping("/statusChange.ig")
    public ApiResult<?> statusChange(@RequestBody @Validated InternetVirtualWarehouseScopeStatusChangeDto validateDto, BindingResult result) {
        if (result.hasErrors()) {
            return ApiResult.error(Objects.requireNonNull(result.getFieldError(), "参数不能为空").getDefaultMessage());
        }

        // 校验所属虚拟仓编号是否存在
        InternetVirtualWarehouseInfo virtualWarehouseInfo = this.internetVirtualWarehouseInfoManager.findByUnique(validateDto.getVstoreCode());
        if (virtualWarehouseInfo == null) {
            return ApiResult.error("聚合仓信息不存在,聚合仓编码:" + validateDto.getVstoreCode());
        }

        try {
            // 启用校验
            ChangeStatusValidateService service = new ChangeStatusValidateService(virtualWarehouseInfo);
            ValidateResult validateResult = service.validate(validateDto);
            if (!validateResult.isSuccess()) {
                return ApiResult.error(validateResult.getMsg());
            }
            manager.statusChange(validateDto, virtualWarehouseInfo);
        } catch (ValidationException e) {
            LOGGER.error(e.getMessage(), e);
            return ApiResult.error(e.getMessage());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return ApiResult.error("系统错误," + e.getMessage());
        }

       /* //总仓状态禁用
        if (virtualWarehouseInfo.getVstoreType() == 1 && validateDto.getStatus() == 0) {
            headStoreStatusDisabled(validateDto);
        } else {
            // 总仓启用 和 子仓的状态启用或禁用
            otherStoreStatusEnableOrDisabled(validateDto, virtualWarehouseInfo);
        }*/

        return ApiResult.ok();
    }

    private void otherStoreStatusEnableOrDisabled(InternetVirtualWarehouseScopeStatusChangeDto statusChangeDto, InternetVirtualWarehouseInfo virtualWarehouseInfo) {
        for (InternetVirtualWarehouseScopeStatusChangeDto.Dtl itemDtl : statusChangeDto.getDtlData()) {
            InternetVirtualWarehouseScope virtualWarehouseScope = new InternetVirtualWarehouseScope();
            virtualWarehouseScope.setId((String) itemDtl.getId());
            virtualWarehouseScope.setStatus(statusChangeDto.getStatus());
            Date date = new Date();
            String updateUser = Authorization.getUser().getName();
            virtualWarehouseScope.setUpdateUser(updateUser);
            virtualWarehouseScope.setUpdateTime(date);
            this.manager.update(virtualWarehouseScope);

            //标准逻辑:总仓子仓联动启用禁用
            if(virtualWarehouseInfo.getVstoreMold()==1){
                Map<String,Object> params = new HashMap<String,Object>();
                if(virtualWarehouseInfo.getVstoreType() == 1) {
                    params.put("vstoreCode", statusChangeDto.getVstoreCode().concat("-RY"));
                }else if(virtualWarehouseInfo.getVstoreType() == 2) {
                    params.put("vstoreCode",  statusChangeDto.getVstoreCode().substring(0,  statusChangeDto.getVstoreCode().length()-3));
                }
                params.put("storeNo", itemDtl.getStoreNo());
                params.put("orderUnitNo", itemDtl.getOrderUnitNo());
                List<InternetVirtualWarehouseScope> vList = manager.selectByParams(QueryUtil.mapToQuery(params));
                if(vList!= null && !vList.isEmpty()) {
                    InternetVirtualWarehouseScope v = new InternetVirtualWarehouseScope();
                    v.setId(vList.get(0).getId());
                    v.setStatus(statusChangeDto.getStatus());
                    v.setUpdateUser(updateUser);
                    v.setUpdateTime(date);
                    this.manager.update(v);
                }
                SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                        new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                                .setOpscode(OpscodeEnum.UP.getCode())
                                .setSyscode(String.valueOf(MenuTypeEnums.VWS.getType()))
                                .setSysname(MenuTypeEnums.VWS.getDesc()).
                                setKeyword1(statusChangeDto.getVstoreCode()).
                                setKeyword1info("聚合仓编码:" + statusChangeDto.getVstoreCode()).
                                setKeyword2(itemDtl.getOrderUnitNo()).
                                setKeyword2info("货管:" + itemDtl.getOrderUnitNo()).
                                setRemark(String.format("%s所有店：聚合仓编码:%s;货管:%s;机构:%s",
                                        (statusChangeDto.getStatus() == 1 ? "启用": "禁用"),
                                        statusChangeDto.getVstoreCode(),
                                        itemDtl.getOrderUnitNo(), itemDtl.getStoreNo()))
                                .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                                .setCreateTime(new Date())
                                .build()));
            }

            //非标准逻辑
            if(statusChangeDto.getStatus() == 1&&virtualWarehouseInfo.getVstoreMold()==0) {
                //启用双向  子仓启用联动总仓，总仓启用联动子仓
                Map<String,Object> params = new HashMap<String,Object>();
                if(virtualWarehouseInfo.getVstoreType() == 1) {
                    params.put("vstoreCode", statusChangeDto.getVstoreCode().concat("-101"));
                }else if(virtualWarehouseInfo.getVstoreType() == 2) {
                    params.put("vstoreCode",  statusChangeDto.getVstoreCode().substring(0,  statusChangeDto.getVstoreCode().length()-4));
                }
                params.put("storeNo", itemDtl.getStoreNo());
                params.put("orderUnitNo", itemDtl.getOrderUnitNo());
                List<InternetVirtualWarehouseScope> vList = manager.selectByParams(QueryUtil.mapToQuery(params));
                if(vList!= null && !vList.isEmpty()) {
                    InternetVirtualWarehouseScope v101 = new InternetVirtualWarehouseScope();
                    v101.setId(vList.get(0).getId());
                    v101.setStatus(statusChangeDto.getStatus());
                    v101.setUpdateUser(updateUser);
                    v101.setUpdateTime(date);
                    this.manager.update(v101);

                }

                SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                        new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                                .setOpscode(OpscodeEnum.UP.getCode())
                                .setSyscode(String.valueOf(MenuTypeEnums.VWS.getType()))
                                .setSysname(MenuTypeEnums.VWS.getDesc()).
                                setKeyword1(statusChangeDto.getVstoreCode()).
                                setKeyword1info("聚合仓编码:" + statusChangeDto.getVstoreCode()).
                                setKeyword2(itemDtl.getOrderUnitNo()).
                                setKeyword2info("货管:" + itemDtl.getOrderUnitNo()).
                                setRemark(String.format("%s所有店：聚合仓编码:%s;货管:%s;机构:%s",
                                        (statusChangeDto.getStatus() == 1 ? "启用": "禁用"),
                                        statusChangeDto.getVstoreCode(),
                                        itemDtl.getOrderUnitNo(), itemDtl.getStoreNo()))
                                .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                                .setCreateTime(new Date())
                                .build()));
            }
        }
    }

    private void headStoreStatusDisabled(InternetVirtualWarehouseScopeStatusChangeDto statusChangeDto) {
        // 总仓禁用，子仓也要同时禁用
        // 查询下辖子仓
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("parentVstoreCode", statusChangeDto.getVstoreCode());
        params.put("vstoreType", 2);
        List<InternetVirtualWarehouseInfo> listChildVirtual = internetVirtualWarehouseInfoManager.selectByParams(QueryUtil.mapToQuery(params));
        if (listChildVirtual != null && !listChildVirtual.isEmpty()) {
            List<String> listVstoreCode = listChildVirtual.stream().map(InternetVirtualWarehouseInfo::getVstoreCode).collect(Collectors.toList());
            // 有子仓库存范围，先禁用子仓范围
            for (InternetVirtualWarehouseScopeStatusChangeDto.Dtl itemDtl : statusChangeDto.getDtlData()) {

                InternetVirtualWarehouseScope virtualWarehouseScope = this.manager.findByPrimaryKey(itemDtl.getId());

                if (virtualWarehouseScope.getStoreType() == 21
                        && virtualWarehouseScope.getMoreStoreFlag() == 1) {// 总仓所有店删除
                    params.clear();
                    params.put("listVstoreCode", listVstoreCode);
                    params.put("orderUnitNo", itemDtl.getOrderUnitNo());
                    params.put("updateUser", Authorization.getUser().getName());
                    params.put("status", statusChangeDto.getStatus());
                    this.manager.deleteAllShopScope(params);

                    SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                            new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                                    .setOpscode(OpscodeEnum.DEL.getCode())
                                    .setSyscode(String.valueOf(MenuTypeEnums.VWS.getType()))
                                    .setSysname(MenuTypeEnums.VWS.getDesc()).
                                    setKeyword1(statusChangeDto.getVstoreCode()).
                                    setKeyword1info("聚合仓编码:" + String.join(",", listVstoreCode)).
                                    setKeyword2(virtualWarehouseScope.getOrderUnitNo()).
                                    setKeyword2info("货管:" + virtualWarehouseScope.getOrderUnitNo()).
                                    setRemark(String.format("删除所有店：聚合编码:%s;货管:%s",statusChangeDto.getVstoreCode(), virtualWarehouseScope.getOrderUnitNo()))
                                    .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                                    .setCreateTime(new Date())
                                    .build()));
                } else {// 指定店删除、或者总仓 仓删除
                    params.clear();
                    params.put("listVstoreCode", listVstoreCode);
                    params.put("orderUnitNo", itemDtl.getOrderUnitNo());
                    params.put("storeNo", itemDtl.getStoreNo());
                    params.put("updateUser", Authorization.getUser().getName());
                    params.put("status", statusChangeDto.getStatus());
                    this.manager.deleteStoreScope(params);

                    SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                            new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                                    .setOpscode(OpscodeEnum.DEL.getCode())
                                    .setSyscode(String.valueOf(MenuTypeEnums.VWS.getType()))
                                    .setSysname(MenuTypeEnums.VWS.getDesc()).
                                    setKeyword1(statusChangeDto.getVstoreCode()).
                                    setKeyword1info("聚合仓编码:" + String.join(",", listVstoreCode)).
                                    setKeyword2(virtualWarehouseScope.getOrderUnitNo()).
                                    setKeyword2info("货管:" + virtualWarehouseScope.getOrderUnitNo()).
                                    setRemark(String.format("删除所有店：聚合仓编码:%s;货管:%s;机构:%s",statusChangeDto.getVstoreCode(),
                                            virtualWarehouseScope.getOrderUnitNo(), itemDtl.getStoreNo()))
                                    .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                                    .setCreateTime(new Date())
                                    .build()));
                }
            }
        }

        // 禁用总仓
        Date date = new Date();
        for (InternetVirtualWarehouseScopeStatusChangeDto.Dtl itemDtl : statusChangeDto.getDtlData()) {
            InternetVirtualWarehouseScope virtualWarehouseScope = new InternetVirtualWarehouseScope();
            virtualWarehouseScope.setId(itemDtl.getId());
            virtualWarehouseScope.setStatus(statusChangeDto.getStatus());
            virtualWarehouseScope.setUpdateUser(Authorization.getUser().getName());
            virtualWarehouseScope.setUpdateTime(date);
            this.manager.update(virtualWarehouseScope);

            SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                    new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                            .setOpscode(OpscodeEnum.UP.getCode())
                            .setSyscode(String.valueOf(MenuTypeEnums.VWS.getType()))
                            .setSysname(MenuTypeEnums.VWS.getDesc()).
                            setKeyword1(statusChangeDto.getVstoreCode()).
                            setKeyword1info("聚合仓编码:" + statusChangeDto.getVstoreCode()).
                            setKeyword2(itemDtl.getOrderUnitNo()).
                            setKeyword2info("货管:" + itemDtl.getOrderUnitNo()).
                            setRemark(String.format("%s所有店：聚合仓编码:%s;货管:%s;机构:%s",
                                    (statusChangeDto.getStatus() == 1 ? "启用": "禁用"),
                                    statusChangeDto.getVstoreCode(),
                                    itemDtl.getOrderUnitNo(), itemDtl.getStoreNo()))
                            .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                            .setCreateTime(new Date())
                            .build()));
        }
    }


    @PostMapping("/deleteData")
    public ApiResult<?> deleteInternetVirtualWarehouseScope(@RequestBody List<String> dataIds) {
        if (ObjectUtil.isEmpty(dataIds)) {
            return ApiResult.error("删除聚合仓范围, 主键不能为空");
        }
        this.manager.deleteData(dataIds);
        return ApiResult.ok();
    }

    @RequestMapping("/import")
    @ResponseBody
    public ApiResult<?> importData(HttpServletRequest request, HttpServletResponse response) {
        StandardMultipartHttpServletRequest multipartHttpServletRequest = null;
        if (request instanceof StandardMultipartHttpServletRequest) {
            multipartHttpServletRequest = (StandardMultipartHttpServletRequest) request;
        }
        String vStoreCode = request.getParameter("vstoreCode");
        if (StrUtil.isBlank(vStoreCode)) {
            return ApiResult.error("导入数据时,聚合仓编码不能为空");
        }

        InternetVirtualWarehouseInfo info = internetVirtualWarehouseInfoManager.findByUnique(vStoreCode);
        if (info == null) {
            return ApiResult.error("导入数据时,聚合仓信息不存在, 聚合仓编码:"+vStoreCode);
        }


        if (multipartHttpServletRequest == null) {
            return ApiResult.error("File is not found");
        }

        MultipartFile multipartFile = multipartHttpServletRequest.getFile("excelFile");
        if (multipartFile == null) {
            return ApiResult.error("File is not found");
        }

        try {
            List<Object> objectList = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .headRowNumber(0)
                    .autoCloseStream(true).doReadAllSync();
            //改造成批量保存
            InternetVirtualWarehouseScopeSave saveData = convertScopeSave(objectList, info);
            this.manager.saveInternetVirtualWarehouseScope(saveData, info);
        } catch (Exception e) {
            logger.error("聚合仓范围导入异常",e);
            return ApiResult.error(e.getMessage());
        }
        return ApiResult.ok();
    }

    //校验独享货管是否存在
    public void validateIvwScope(int rowIndex, String orderUnitNo, String storeNo, String vstoreCode) {
        List<InternetVirtualWarehouseScope> listScope = manager.selectByParams(
                Query.Where("orderUnitNo", orderUnitNo).and("storeNo", storeNo)
                        .and("inventoryType", 2).and("status", 1));
        if(CollectionUtil.isNotEmpty(listScope) && !listScope.get(0).getVstoreCode().equals(vstoreCode)){
            throw new ManagerException("第"+rowIndex+"行,独享库存的机构和货管已存在,storeNo:" + storeNo + ",orderUnitNo:" + orderUnitNo);
        }
    }


    private InternetVirtualWarehouseScopeSave convertScopeSave(List<Object> excelData, InternetVirtualWarehouseInfo info) {
        InternetVirtualWarehouseScopeSave scopeSave = new InternetVirtualWarehouseScopeSave();
        if (excelData == null) {
            return scopeSave;
        }
        Map<String, Integer> dtlMap = new HashMap<>();
        scopeSave.setMoreStoreFlag("0");
        scopeSave.setInventoryType(1);
        scopeSave.setVstoreCode(info.getVstoreCode());
        //导入模板，1：按货权导入，2：按货权+机构导入
        int exportType = 0;
        int rowIndex = 0;
        for (Object excelDatum : excelData) {
            if (!(excelDatum instanceof LinkedHashMap)) {
                return scopeSave;
            }
            rowIndex ++;
            LinkedHashMap linkedHashMap = (LinkedHashMap) excelDatum;
            String item1 = Convert.toStr(linkedHashMap.get(0));
            if(rowIndex == 1){
                if("机构编号".equals(item1)){
                    exportType = 2;
                } else if("货管编号".equals(item1)) {
                    exportType = 1;
                } else {
                    throw new ManagerException("导入模板有误，请重新下载模板");
                }
                continue;
            }

            String storeNo = null, orderUnitNo = null;
            String item2 = Convert.toStr(linkedHashMap.get(1));
            //基础数据校验
            if(exportType == 2){
                storeNo = item1;
                orderUnitNo = item2;
                if (StrUtil.isBlank(storeNo)) {
                    throw new ManagerException("Excel中,第"+rowIndex+"行,单机构类型机构编码不能为空");
                }
            } else if(exportType == 1){
                orderUnitNo = item1;
            }
            if (!ObjectUtil.isAllNotEmpty(orderUnitNo)) {
                throw new ManagerException("Excel中,第"+rowIndex+"行,有必填字段未填写.");
            }

            //机构+货管导入
            if(exportType == 2){
                if(dtlMap.containsKey(orderUnitNo+"-"+storeNo)){
                    throw new ManagerException("第"+rowIndex+"行和第"+dtlMap.get(orderUnitNo+"-"+storeNo)+"数据重复");
                } else {
                    dtlMap.put(orderUnitNo+"-"+storeNo, rowIndex);
                }
                validateOrderUnit(orderUnitNo, rowIndex);
                //查询机构货管对应关系是否存在
                List<OrgUnitBrandRel> orgList = orgUnitBrandRelManager.selectByParams(
                        Query.Where("orderUnitNo", orderUnitNo).and("storeNo", storeNo).and("status", 1));
                if(CollectionUtils.isEmpty(orgList)) {
                    throw new ManagerException(String.format("机构编码:%s对应的货管:%s不存在关联关系", storeNo, orderUnitNo));
                }
                OrgUnitBrandRel rel = orgList.get(0);
                //校验独享货管是否存在
                validateIvwScope(rowIndex, orderUnitNo, storeNo, info.getVstoreCode());
                //查询货管对应的所有机构,仓库：实仓的正常状态的仓库；店铺：店铺类别为网销店的正常状态的店铺；

                String originalVstoreCode = Convert.toStr(linkedHashMap.get(2));
                InternetVirtualWarehouseInfo orignalVstore = null;
                if(rel.getStoreType() == 21){
                    if (StringUtils.isNotEmpty(originalVstoreCode)) {
                        throw new ManagerException("第"+rowIndex+"行,店仓机构不能设置原属聚合仓,storeNo:" + storeNo);
                    }
                    List<Shop> shopList = shopManager.selectByParams(Query.Where("storeNo", storeNo).and("status", 1));
                    if(shopList==null||shopList.isEmpty()){
                        throw new ManagerException("第"+rowIndex+"行,机构不存在,storeNo:" + storeNo);
                    }
                    Shop shop = shopList.get(0);
                    if(!shopMultis.contains(shop.getMulti())){
                        throw new ManagerException("第"+rowIndex+"行,店仓机构的店铺细类不在范围内,storeNo:" + storeNo);
                    }
                } else  if(rel.getStoreType() == 22){
                    if (!needOriginalVstoreCode.contains(info.getVstoreCode())) {
                        if (StringUtils.isNotEmpty(originalVstoreCode)) {
                            throw new ManagerException("第"+rowIndex+"行,聚合仓不是:" + needOriginalVstoreCode + "的不能指定原属聚合仓");
                        }
                    }else if (info.standardVStore()) {
                        if (StringUtils.isEmpty(originalVstoreCode)) {
                            throw new ManagerException("第"+rowIndex+"行,仓库机构:原属聚合仓 不能为空,storeNo:" + storeNo);
                        }
                        orignalVstore = internetVirtualWarehouseInfoManager.findByUnique(originalVstoreCode);
                        if (orignalVstore == null) {
                            throw new ManagerException("第"+rowIndex+"行,仓库机构:原属聚合仓不存在,storeNo:" + storeNo);
                        }else if(!orignalVstore.ryVStore()){
                            throw new ManagerException("第"+rowIndex+"行,仓库机构:原属聚合仓是非冗余仓,storeNo:" + storeNo);
                        }
                    }else if(StringUtils.isNotEmpty(originalVstoreCode)) {
                        throw new ManagerException("第"+rowIndex+"行,非标仓不能设置原属聚合仓,storeNo:" + storeNo);
                    }

                    List<GmsStore> storeList = storeManager.selectByParams(Query.Where("storeNo", storeNo).and("status", 1));
                    if(storeList==null||storeList.isEmpty()){
                        throw new ManagerException("第"+rowIndex+"行,机构不存在,storeNo:" + storeNo);
                    }
                    GmsStore store = storeList.get(0);
                    if(store.getVirtPhyStorageType() != 0){
                        throw new ManagerException("第"+rowIndex+"行,机构为仓库的只允许实仓导入,storeNo:" + storeNo);
                    }
                }

                InternetVirtualWarehouseScopeSave.SaveDtl saveDtl = InternetVirtualWarehouseScopeSave.buildDtl(rel.getStoreNo(), rel.getStoreName(), rel.getStoreType(),
                        rel.getOrderUnitNo(), rel.getOrderUnitName(), 1);
                if (orignalVstore != null) {
                    saveDtl.setOriginalVstoreCode(orignalVstore.getVstoreCode());
                    saveDtl.setOriginalVstoreName(orignalVstore.getVstoreName());
                }
                scopeSave.getAddDtls().add(saveDtl);
            } else if(exportType == 1){
                if(dtlMap.containsKey(orderUnitNo)){
                    throw new ManagerException("第"+rowIndex+"行和第"+dtlMap.get(orderUnitNo)+"数据重复");
                } else {
                    dtlMap.put(orderUnitNo, rowIndex);
                }
                validateOrderUnit(orderUnitNo, rowIndex);
                //货管导入，查询出货管对应的所有机构
                List<OrgUnitBrandRel> orgList = orgUnitBrandRelManager.selectStoreByUnitNo(orderUnitNo);
                if(CollectionUtils.isEmpty(orgList)){
                    throw new ManagerException("Excel中,第"+rowIndex+"行,货管编码："+orderUnitNo+"查询不到货管对应的实仓或网销店机构");
                }
                //总仓全部添加，子仓只能添加所属仓范围里的货管
                if(info.getVstoreType() == 1){
                    for (OrgUnitBrandRel rel : orgList) {
                        //店铺类型机构，仓库类型机构：过滤聚合仓
                        if((rel.getStoreType() == 21 && shopMultis.contains(rel.getMulti()))
                                || (rel.getStoreType() == 22 && rel.getVirtPhyStorageType() == 0)){
                            //校验独享货管是否存在
                            validateIvwScope(rowIndex, orderUnitNo, storeNo, info.getVstoreCode());
                            scopeSave.getAddDtls().add(InternetVirtualWarehouseScopeSave.buildDtl(rel.getStoreNo(), rel.getStoreName(), rel.getStoreType(),
                                    rel.getOrderUnitNo(), rel.getOrderUnitName(), 1));
                        }
                    }
                } else {
                    //查询上级机构对应的所有货管
                    Query param = Query.Where("vstoreCode", info.getParentVstoreCode())
                            .and("orderUnitNo", orderUnitNo).and("status", 1);
                    List<InternetVirtualWarehouseScope> parentScopes = manager.selectByParams(param);
                    if(!CollectionUtils.isEmpty(parentScopes)) {
                        throw new ManagerException("Excel中,第"+rowIndex+"行,货管编码："+orderUnitNo+"查询不到上级机构的货管范围");
                    }
                    //判断单机构还是多机构，
                    boolean isMoreStore = parentScopes.stream().filter(x->x.getMoreStoreFlag() == 1 && x.getStoreType()==21).findFirst().isPresent();
                    Map<String, InternetVirtualWarehouseScope> scopeMap = parentScopes.stream()
                            .collect(Collectors.toMap(x->x.getStoreNo()+"-"+x.getOrderUnitNo(), y->y, (s1,s2)->s1));
                    for (OrgUnitBrandRel rel : orgList) {
                        InternetVirtualWarehouseScope scope = scopeMap.get(rel.getStoreNo()+"-"+rel.getOrderUnitNo());
                        if((rel.getStoreType() == 21 && isMoreStore) || (scope != null && scope.getInventoryType() != 2)){
                            if((rel.getStoreType() == 21 && shopMultis.contains(rel.getMulti()))
                                    || (rel.getStoreType() == 22 && rel.getVirtPhyStorageType() == 0)){
                                scopeSave.getAddDtls().add(InternetVirtualWarehouseScopeSave.buildDtl(rel.getStoreNo(), rel.getStoreName(), rel.getStoreType(),
                                        rel.getOrderUnitNo(), rel.getOrderUnitName(), 1));
                            }
                        }
                    }
                }
            }
        }
        return scopeSave;
    }

    void validateOrderUnit(String orderUnitNo, Integer rowIndex){
        OrderUnit orderUnit = orderUnitManager.findByUnique(orderUnitNo);
        if (orderUnit == null) {
            throw new ManagerException("Excel中,第"+rowIndex+"行,货管编码填写错误, 货管编码:"+ orderUnitNo);
        }
        if (orderUnit.getType() == 1) {
            throw new ManagerException("批发货管，第"+rowIndex+"行,不能添加到虚拟仓库存范围, 货管编码:"+ orderUnitNo);
        }
        if (orderUnit.getStatus() != 1) {
            throw new ManagerException("Excel中,第"+rowIndex+"行,不是有效的货管, 货管编码:"+ orderUnitNo);
        }
    }


    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {


        return ExcelExportOperations.builder()
                .fieldFormater("storeType", (a) -> {
                    return Objects.deepEquals(a.toString(), "21") ? "店" : "仓";
                })
                .fieldFormater("inventoryType", (a) -> {
                    switch (a.toString()) {
                        case "1":
                            return "共享";
                        case "2":
                            return "独享";
                        case "3":
                            return "电商";
                        default:
                            return "未知";
                    }
                })
                .fieldFormater("status", (a) -> {
                    return Objects.deepEquals(a.toString(), "0") ? "禁用" : "启用";
                })

                .headers(columns)
                .value();
    }
}