/** by Kain **/
package cn.wonhigh.baize.web.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import topmall.framework.web.configuration.AbstractWebMvcConfig;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
/*@ConditionalOnWebApplication
@ConditionalOnClass({ Servlet.class, DispatcherServlet.class, WebMvcConfigurerAdapter.class })
@ConditionalOnMissingBean(WebMvcConfigurationSupport.class)
@Order(Ordered.LOWEST_PRECEDENCE)
@AutoConfigureAfter(DispatcherServletAutoConfiguration.class)*/
public class WebMvcConfig extends AbstractWebMvcConfig {

	public WebMvcConfig() {
		System.out.println("WebMvcConfig init...");
	}

	@Override
	public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
		super.addArgumentResolvers(argumentResolvers);
	}
}
