package cn.wonhigh.baize.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.mercury.annotation.JsonVariable;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.data.metadata.DataEntry;
import cn.mercury.domain.DataChangeEntry;
import cn.mercury.domain.Tupe;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.functions.Function1;
import cn.mercury.manager.IManager;
import cn.mercury.security.IUser;
import cn.mercury.spring.SpringContext;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsEvent;
import cn.wonhigh.baize.events.systemlog.InternetSystemLogsMessage;
import cn.wonhigh.baize.manager.gms.IInternetVirtualWarehouseInfoManager;
import cn.wonhigh.baize.manager.gms.IOrderSourceTerminalConfigManager;
import cn.wonhigh.baize.manager.gms.IStockDistirbutionConfigManager;
import cn.wonhigh.baize.model.entity.gms.InternetVirtualWarehouseInfo;
import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig;
import cn.wonhigh.baize.model.enums.ChannelTypeEnum;
import cn.wonhigh.baize.model.enums.MenuTypeEnums;
import cn.wonhigh.baize.model.enums.OpscodeEnum;
import cn.wonhigh.retail.uc.common.api.model.AuthorityUserVirtualWarehouse;
import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
import com.alibaba.dubbo.config.annotation.Reference;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;
import topmall.framework.security.Authorization;
import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/stockDistirbutionConfig")
public class StockDistirbutionConfigController extends ApiController<StockDistirbutionConfig, String> {
    @Resource
    private IStockDistirbutionConfigManager stockDistirbutionConfigManager;
    @Reference
    private AuthorityUserDataApi authorityUserDataApi;
    @Resource
    private IInternetVirtualWarehouseInfoManager internetVirtualWarehouseInfoManager;
    @Resource
    private IOrderSourceTerminalConfigManager orderSourceTerminalConfigManager;

    @Override
    protected IManager<StockDistirbutionConfig, String> getManager() {
        return stockDistirbutionConfigManager;
    }

    @ResponseBody
    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/list")
    @Override
    public ApiResult selectByPage(Query query, Pagenation page) {
        //查询虚仓权限
        Map<String, String> infosMap = new HashMap<>();
        ApiResult result = getAuthorityUserVirtualWarehouse(query, infosMap);
        if (result != null) {
            return result;
        }
        long total = page.getTotal();
        if (total <= 0) {
            total = stockDistirbutionConfigManager.selectCount(query);
        }
        if (total == 0) {
            return ApiResult.ok(new PageResult<DataEntry>(null, total));
        }
        List<StockDistirbutionConfig> rows = stockDistirbutionConfigManager.selectByPage(query, page);
        for (StockDistirbutionConfig row : rows) {
            row.setVstoreName(infosMap.get(row.getVstoreCode()));
        }
        return ApiResult.ok(new PageResult<>(rows, total));
    }

    @ResponseBody
    @RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/listShop")
    public ApiResult selectChannelByPage(Query query, Pagenation page) {
        //查询虚仓权限
        Map<String, String> infosMap = new HashMap<>();
        ApiResult result = getAuthorityUserVirtualWarehouse(query, infosMap);
        if (result != null) {
            return result;
        }
        Map<String, Object> param = query.asMap();
        param.keySet().stream().forEach(x -> {
            if (!StringUtils.isEmpty(param.get(x))) {
                query.and(x, param.get(x).toString().trim());
            }
        });
        query.orderby("update_time", Boolean.TRUE);
        long total = page.getTotal();
        if (total <= 0L) {
            total = (long) stockDistirbutionConfigManager.countByShop(query);
        }
        if (total == 0L) {
            return ApiResult.ok(new PageResult((List) null, total));
        } else {
            List<StockDistirbutionConfig> rows = stockDistirbutionConfigManager.pageByShop(query, page);
            return ApiResult.ok(new PageResult(rows, total));
        }
    }

    public ApiResult getAuthorityUserVirtualWarehouse(Query query, Map<String, String> infosMap) {
        List<AuthorityUserVirtualWarehouse> authorityUserVirtualWarehouses = new ArrayList<>();
        Integer userId = Integer.parseInt(Authorization.getUser().getId());
        try {
            authorityUserVirtualWarehouses = authorityUserDataApi.userVirtualWarehouse(userId);
        } catch (Exception e1) {
            return ApiResult.error("根据userId查询聚合仓权限接口异常");
        }
        if (authorityUserVirtualWarehouses == null || CollectionUtils.isEmpty(authorityUserVirtualWarehouses)) {// 没有虚仓权限
            return ApiResult.ok(new PageResult<DataEntry>(null, 0));
        }
        List<String> vstoreCodeList = new ArrayList<>();
        for (AuthorityUserVirtualWarehouse authorityUserVirtualWarehouse : authorityUserVirtualWarehouses) {
            infosMap.put(authorityUserVirtualWarehouse.getVstoreCode(), authorityUserVirtualWarehouse.getVstoreName());
            vstoreCodeList.add(authorityUserVirtualWarehouse.getVstoreCode());
        }
        query.and("vstoreCodeList", vstoreCodeList);
        query.and("organTypeNo", Authorization.getUser().getOrganTypeNo());
        return null;
    }


    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/create")
    @ApiIgnore
    public ApiResult<StockDistirbutionConfig> create(StockDistirbutionConfig entry) {
        //1、根据虚仓编码查询online_type、vstore_scope_type
        List<InternetVirtualWarehouseInfo> virtualWarehouses = internetVirtualWarehouseInfoManager.selectByParams(new Query().and("vstoreCode", entry.getVstoreCode()));
        if (CollectionUtil.isEmpty(virtualWarehouses)) {
            return ApiResult.error("虚拟仓配置不存在此聚合仓");
        }
        InternetVirtualWarehouseInfo internetVirtualWarehouseInfo = virtualWarehouses.get(0);
        int vstoreMold = internetVirtualWarehouseInfo.getVstoreMold();
        if (vstoreMold == 1) {    //标准仓
            List<InternetVirtualWarehouseInfo> parentVirList = internetVirtualWarehouseInfoManager.selectByParams(new Query().and("parentVstoreCode", internetVirtualWarehouseInfo.getParentVstoreCode()));
            List<String> vstoreCodeList = parentVirList.stream().map(InternetVirtualWarehouseInfo::getVstoreCode).distinct().collect(Collectors.toList());
            //2、校验channel_type、shop_no、vstore_code是否唯一
            if (stockDistirbutionConfigManager.selectByParams(new Query()
                    .and("channelType", entry.getChannelType())
                    .and("shopNo", entry.getShopNo())
                    .and("vstoreCodeList", vstoreCodeList)).size() > 0) {
                return ApiResult.error("渠道类型、店铺编码、聚合仓编码不能重复");
            }
        }
        entry.setOnlineType(internetVirtualWarehouseInfo.getOnlineType());
        entry.setVstoreScopeType(internetVirtualWarehouseInfo.getVstoreType());
        if (entry.getChannelType() == 1) {
            entry.setShopNo(entry.getVstoreCode());
            entry.setShopName(internetVirtualWarehouseInfo.getVstoreName());
        }
        getManager().insert(entry);
        return ApiResult.ok(entry);
    }

    @Override
    public ApiResult<Integer> batchSave(DataChangeEntry<StockDistirbutionConfig> datas) {
        if (!CollectionUtils.isEmpty(datas.getInserted())) {
            Set<String> configSet = new HashSet<>();
            for (StockDistirbutionConfig entry : datas.getInserted()) {
                if(!StringUtils.hasText(entry.getVstoreCode())){
                    return ApiResult.error("聚合仓编码不能为空");
                }
                //1、根据虚仓编码查询online_type、vstore_scope_type
                InternetVirtualWarehouseInfo warehouseInfo = internetVirtualWarehouseInfoManager.findByUnique(entry.getVstoreCode());
                if (warehouseInfo == null) {
                    return ApiResult.error("虚拟仓配置不存在此聚合仓");
                }
                if (entry.getChannelType() == ChannelTypeEnum.XCX.getCode()) {
                    if(!StringUtils.hasText(entry.getShopNo())){
                        return ApiResult.error("渠道编码不能为空");
                    }
                    List<OrderSourceTerminalConfig> configs = orderSourceTerminalConfigManager.selectByParams(Query.Where("thirdPlatform", entry.getShopNo()));
                    if(CollectionUtils.isEmpty(configs)){
                        return ApiResult.error(String.format("渠道编码：%s，不存在", entry.getShopNo()));
                    }
                    if(!"XCXZBXD".equals(configs.get(0).getTerminal()) && !"XCXDQXD".equals(configs.get(0).getTerminal())){
                        return ApiResult.error(String.format("渠道编码：%s，选择的渠道不属于小程序", entry.getShopNo()));
                    }
                    entry.setShopName(configs.get(0).getThirdPlatformName());
                } else if (entry.getChannelType() == ChannelTypeEnum.OMS.getCode()){
                    entry.setShopNo(warehouseInfo.getVstoreCode());
                    entry.setShopName(warehouseInfo.getVstoreName());
                }
                entry.setOnlineType(warehouseInfo.getOnlineType());
                entry.setVstoreScopeType(warehouseInfo.getVstoreType());
                int vstoreMold = warehouseInfo.getVstoreMold();
                List<String> vstoreCodeList = new ArrayList<>();
                if (vstoreMold == 1) {    //标准仓
                    List<InternetVirtualWarehouseInfo> parentVirList = internetVirtualWarehouseInfoManager.selectByParams(new Query().and("parentVstoreCode", warehouseInfo.getParentVstoreCode()));
                    vstoreCodeList = parentVirList.stream().map(InternetVirtualWarehouseInfo::getVstoreCode).distinct().collect(Collectors.toList());
                } else {
                    vstoreCodeList.add(warehouseInfo.getVstoreCode());
                }
                //2、校验channel_type、shop_no、vstore_code, brand_no是否唯一
                if(!StringUtils.hasText(entry.getBrandNo())){
                    entry.setBrandNo("");
                }
                List<StockDistirbutionConfig> configList = stockDistirbutionConfigManager.selectByParams(new Query()
                        .and("channelType", entry.getChannelType())
                        .and("shopNo", entry.getShopNo())
                        .and("vstoreCodeList", vstoreCodeList)
                        .and("brandNo", entry.getBrandNo()));
                if (configList.size() > 0 || configSet.contains(String.format("%s_%s_%s_%s", entry.getChannelType(), entry.getShopNo(), warehouseInfo.getVstoreCode(), entry.getBrandNo()))) {
                    return ApiResult.error(String.format("渠道类型:%s、店铺编码:%s、聚合仓编码:%s、品牌:%s不能重复", ChannelTypeEnum.getName(entry.getChannelType()), entry.getShopNo(), entry.getVstoreCode(), entry.getBrandNo()));
                }
                for (String vstoreCode : vstoreCodeList) {
                    configSet.add(String.format("%s_%s_%s_%s", entry.getChannelType(), entry.getShopNo(), vstoreCode, entry.getBrandNo()));
                }
            }
            for (StockDistirbutionConfig entry : datas.getInserted()) {
                saveLogs(entry, OpscodeEnum.ADD);
            }
        }
        if (!CollectionUtils.isEmpty(datas.getUpdated())) {
            for (StockDistirbutionConfig addConfig : datas.getUpdated()) {
                StockDistirbutionConfig dbConfig = stockDistirbutionConfigManager.findByPrimaryKey(addConfig.getId());
                if (dbConfig == null) {
                    return ApiResult.error("数据不存在");
                }
                dbConfig.setStatus(addConfig.getStatus());
                saveLogs(dbConfig, OpscodeEnum.UP);
            }
        }
        return super.batchSave(datas);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/export")
    @ApiIgnore
    public ApiResult<Tupe<String, Integer>> export(Query query, String fileName,
                                                   @JsonVariable("_columns") ExcelColumn[][] columns, HttpServletResponse response) {
        //查询虚仓权限
        Map<String, String> infosMap = new HashMap<>();
        ApiResult result = getAuthorityUserVirtualWarehouse(query,infosMap);
        if(result!=null){
            return result;
        }
        query.and("infosMap",infosMap);
        Object shopNos = query.asMap().get("shopNos");
        if(shopNos != null && StringUtils.hasText(shopNos.toString())){
            query.and("shopNoList", new ArrayList<>(Arrays.asList(shopNos.toString().split(","))));
        }
        Integer count = fetchExportRowCount(query);
        if (query.getPagenation() == null) {
            query.setPagenation(new Pagenation(1, Integer.MAX_VALUE));
        }
        query.getPagenation().setTotal(count);
        String ticket = export(columns, fileName, query);
        return ApiResult.ok(new Tupe<String, Integer>(ticket, count));
    }

    protected Function1<StockDistirbutionConfig, Object> getRowHander(Query query) {
        return (row) -> handerData(row,query.findValue("infosMap"));
    }
    private Object handerData(StockDistirbutionConfig row,Map<String, String> infosMap) {
        row.setVstoreName(infosMap.get(row.getVstoreCode()));
        row.setStatusName(row.getStatus()==1?"启用":"禁用");
        return row;
    }

    @Override
    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {
        return ExcelExportOperations.builder()
                .fieldFormater("status", (a) -> {
                    return Objects.deepEquals(a, 1) ? "启用" : "禁用";
                })
                .fieldFormater("channelType", (a) -> {
                    return ChannelTypeEnum.getName(Integer.parseInt(ObjectUtils.defaultIfNull(a, -1).toString()));
                })
                .headers(columns)
                .value();
    }

    @Override
    public ApiResult<StockDistirbutionConfig> update(StockDistirbutionConfig entry) throws JsonManagerException {
        StockDistirbutionConfig dbConfig = stockDistirbutionConfigManager.findByPrimaryKey(entry.getId());
        if (dbConfig == null) {
            return ApiResult.error("数据不存在");
        }
        ApiResult<StockDistirbutionConfig> result = super.update(entry);
        dbConfig.setVstoreLevel(entry.getVstoreLevel());
        saveLogs(dbConfig, OpscodeEnum.UP);
        return result;
    }

    void saveLogs(StockDistirbutionConfig item, OpscodeEnum opsCode) {
        SpringContext.getContext().publishEvent(new InternetSystemLogsEvent(
                new InternetSystemLogsMessage.InternetSystemLogsMessageBuilder()
                        .setOpscode(opsCode.getCode()).
                        setSyscode(String.valueOf(MenuTypeEnums.SDC.getType())).
                        setSysname(MenuTypeEnums.SDC.getDesc()).
                        setKeyword1(item.getShopNo()).
                        setKeyword1info("渠道编码:" + item.getShopNo() + "(" + item.getShopName() + ")").
                        setKeyword2(item.getVstoreCode()).
                        setKeyword2info("聚合仓编码:" + item.getVstoreCode() + "(" + item.getVstoreName() + ")").
                        setRemark(String.format("%s：渠道编码:%s(%s);聚合仓编码:%s(%s);渠道类型:%s;优先级:%s;状态:%s;品牌:%s",
                                opsCode.getName(), item.getShopNo(), item.getShopName(), item.getVstoreCode(),
                                item.getVstoreName(), item.getChannelType(), item.getVstoreLevel(), item.getStatus(), item.getBrandNo()))
                        .setCreateUser(Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("admin"))
                        .setCreateTime(new Date())
                        .build()));
    }


    @Override
    public ApiResult<Integer> deleteByParams(Query query) {
        return ApiResult.error("不允许操作");
    }

    @Override
    public ApiResult<Integer> deleteByPrimaryKey(String id) {
        return ApiResult.error("不允许操作");
    }

}
