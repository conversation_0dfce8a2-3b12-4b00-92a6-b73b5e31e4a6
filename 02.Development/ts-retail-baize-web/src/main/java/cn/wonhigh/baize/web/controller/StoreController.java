package cn.wonhigh.baize.web.controller;

import cn.mercury.manager.IManager;
import cn.wonhigh.baize.manager.ios.IStoreManager;
import cn.wonhigh.baize.model.entity.ios.Store;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;


@RestController
@RequestMapping("/store")
public class StoreController  extends ApiController<Store,Integer> {

    @Autowired
    private IStoreManager manager;

    protected IManager<Store,Integer> getManager(){
        return manager;
    }

}
