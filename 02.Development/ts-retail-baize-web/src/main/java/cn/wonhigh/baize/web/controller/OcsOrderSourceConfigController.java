package cn.wonhigh.baize.web.controller;

import cn.wonhigh.baize.model.enums.InternetDispatchRuleSetEnum;
import cn.wonhigh.baize.model.enums.OcsOrderSourceConfigChannelTypeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.vo.ApiResult;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("/orderSourceConfig")
@RestController
public class OcsOrderSourceConfigController {


    /**
     * 获取枚举数据
     * @return 渠道枚举配置
     */
    @PostMapping("/listChannelConfigEnums")
    public ApiResult<?> listChannelConfigEnums () {
        List<OcsOrderSourceConfigChannelTypeEnum> list = new ArrayList<>(Arrays.asList(OcsOrderSourceConfigChannelTypeEnum.values()));
        return ApiResult.ok(list);
    }
}
