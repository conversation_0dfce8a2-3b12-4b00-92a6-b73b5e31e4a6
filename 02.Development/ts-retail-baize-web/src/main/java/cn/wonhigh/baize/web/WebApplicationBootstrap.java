/** by Kain **/
package cn.wonhigh.baize.web;

import cn.mercury.excel.data.DbExportConfiguartion;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;

import topmall.framework.core.ApplicationBootstrap;
import topmall.framework.web.inspect.SecurityInterceptor;

@EnableApolloConfig
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = { "topmall.framework.web", "cn.wonhigh.baize.web", "cn.wonhigh.baize.web.controller" })
@ServletComponentScan(basePackageClasses = { SecurityInterceptor.class })
@ImportAutoConfiguration(classes = DbExportConfiguartion.class)
public class WebApplicationBootstrap extends ApplicationBootstrap {
    
    public static void main(String[] args) {
        new WebApplicationBootstrap().run(args);
    }
    
}
