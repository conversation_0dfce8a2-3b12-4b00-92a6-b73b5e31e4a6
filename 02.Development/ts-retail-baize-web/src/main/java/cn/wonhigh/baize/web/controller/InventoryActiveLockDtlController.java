/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.mercury.basic.UUID;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.mercury.manager.IManager;
import cn.mercury.security.IUser;
import cn.wonhigh.baize.events.systemlog.InventoryActiveLockLogHelper;
import cn.wonhigh.baize.manager.gms.*;
import cn.wonhigh.baize.manager.ios.IStoreManager;
import cn.wonhigh.baize.model.entity.gms.*;
import cn.wonhigh.baize.model.entity.ios.Store;
import cn.wonhigh.baize.model.enums.*;
import cn.wonhigh.baize.service.gms.IItemService;
import cn.wonhigh.baize.utils.common.QueryUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;
import topmall.framework.security.Authorization;
import topmall.framework.web.JsonManagerException;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;


@RestController
@RequestMapping("/inventory/active/lock/dtl")
public class InventoryActiveLockDtlController extends ApiController<InventoryActiveLockDtl,String> {

    public final static Logger logger = LoggerFactory.getLogger(InventoryActiveLockDtlController.class);


    @Autowired
    private IInventoryActiveLockDtlManager manager;

    @Autowired
    private IInternetVirtualWarehouseScopeManager internetVirtualWarehouseScopeManager;

    @Autowired
    private IOrderUnitManager orderUnitManager;

    @Autowired
    private IStoreManager storeManager;

    @Autowired
    private IInventoryActiveLockManager inventoryActiveLockManager;

    @Autowired
    private IItemService  itemService;

    @Autowired
    private ICommodityCorpMatchProductManager commodityCorpMatchProductManager;

    @Autowired
    private IExternalProductMappingManager externalProductMappingManager;

    @Resource
    private IOrderSourceTerminalConfigManager orderSourceTerminalConfigManager;


    @Resource
    private IOrgUnitBrandRelManager orgUnitBrandRelManager;

    public static final ExecutorService cachedThreadPool = Executors.newFixedThreadPool(
            Runtime.getRuntime().availableProcessors(),
            new ThreadFactoryBuilder().setNamePrefix("import-data").build()
    );


    protected IManager<InventoryActiveLockDtl,String> getManager(){
        return manager;
    }

    private final Cache<String, Store> storeCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).build();
    private final Cache<String, OrderUnit> orderUnitCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).build();


    @Override
    public ApiResult<InventoryActiveLockDtl> update(InventoryActiveLockDtl entry) throws JsonManagerException {

        if (StringUtils.isBlank(entry.getId())) {
            return ApiResult.error("id is not found");
        }

        ApiResult<InventoryActiveLockDtl> billNo_is_not_found = validateData(entry);
        if (billNo_is_not_found != null) return billNo_is_not_found;

        entry.setBalanceLockQty(entry.getLockQty());
        ApiResult<InventoryActiveLockDtl> update = null;
        try {
            update = super.update(entry);
            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), ""), InventoryActiveLockOpscodeEnum.DTL_UPDATE, "活动锁库明细修改成功");
        } catch (Exception e) {
            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), ""), InventoryActiveLockOpscodeEnum.DTL_UPDATE, "活动锁库明细修改失败");
            throw new RuntimeException(e);
        }
        return update;
    }


    @Override
    public ApiResult<Integer> deleteByPrimaryKey(String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResult.error("id is not found");
        }
        InventoryActiveLockDtl inventoryActiveLockDtl = manager.findByPrimaryKey(id);
        if (inventoryActiveLockDtl == null) {
            return ApiResult.error("数据已经被删除!");
        }

        InventoryActiveLock head = inventoryActiveLockManager.findByUnique(inventoryActiveLockDtl.getBillNo());
        if (head == null) {
            return ApiResult.error("数据已经被删除!");
        }

        if (!Objects.equals(head.getStatus(), ActiveLockStatusEnums.NEW_STATUS.getValue())) {
            return ApiResult.error("数据不是新建状态, 不能修改数据!");
        }

        if (!Objects.equals(head.getIsApprove(), ActiveLockAuditStatusEnums.NOT_AUDIT.getValue())) {
            return ApiResult.error("数据已经审核通过, 不能修改数据!");
        }

        return super.deleteByPrimaryKey(id);
    }


    @Override
    public ApiResult<InventoryActiveLockDtl> create(InventoryActiveLockDtl entry) {
        ApiResult<InventoryActiveLockDtl> billNo_is_not_found = validateData(entry);
        if (billNo_is_not_found != null) return billNo_is_not_found;

        entry.setBalanceLockQty(entry.getLockQty());

        ApiResult<InventoryActiveLockDtl> inventoryActiveLockDtlApiResult = null;
        try {
            inventoryActiveLockDtlApiResult = super.create(entry);
            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), ""), InventoryActiveLockOpscodeEnum.DTL_CREATE, "活动锁库明细创建成功");
        } catch (Exception e) {
            InventoryActiveLockLogHelper.log(Tuples.of(entry.getBillNo(), ""), InventoryActiveLockOpscodeEnum.DTL_CREATE, "活动锁库明细创建失败");
            throw new RuntimeException(e);
        }
        return inventoryActiveLockDtlApiResult;
    }

    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public ApiResult<?> importData(HttpServletRequest request, @RequestParam("billNo") String billNo, @RequestParam("sourcePlatform") String sourcePlatform) {
        if (StringUtils.isBlank(billNo) || StringUtils.isBlank(sourcePlatform)) {
            return ApiResult.error("billNo is not found");
        }

        InventoryActiveLock head = inventoryActiveLockManager.findByUnique(billNo);
        if (head == null) {
            return ApiResult.error("数据已经被删除!");
        }

        ActiveLockStatusEnums status = ActiveLockStatusEnums.getActiveLockEnums(head.getStatus());
        if (status != ActiveLockStatusEnums.NEW_STATUS) {
            return ApiResult.error("数据不是新建状态, 不能修改数据!");
        }


        MultipartFile files;
        try {
            MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) request;

            files = multipartHttpServletRequest.getFile("excelFile");
            if (files == null|| files.isEmpty()) {
                return ApiResult.error("请选择上传的文件");
            }
        } catch (Exception e) {
            logger.error("File is not found", e);
            return ApiResult.error("请选择上传的文件");
        }

        Tuple2<List<InventoryActiveLockDtl>, List<String>> tuple2 = readExcel(files, head.getBillNo(),  head.getSourcePlatform(), head.getVstoreCode());
        if (tuple2 == null) {
            return ApiResult.error("获取数据是空");
        }
        if (tuple2.getT1() == null || tuple2.getT1().isEmpty()) {
            return  ApiResult.error("导入数据是空");
        }

        if (CollectionUtils.isNotEmpty(tuple2.getT2())) {
            InventoryActiveLockLogHelper.log(Tuples.of(head.getBillNo(), head.getActiveName()), InventoryActiveLockOpscodeEnum.DTL_IMPORT, "活动锁库明细导入失败");
            return ApiResult.error("导入数据错误," + tuple2.getT2().get(0));
        }

        logger.info("导入数据条数:{}", tuple2.getT1().size());
        try {
            this.manager.batchSaveOrUpdateDtl(tuple2.getT1());
            InventoryActiveLockLogHelper.log(Tuples.of(head.getBillNo(), head.getActiveName()), InventoryActiveLockOpscodeEnum.DTL_IMPORT, "活动锁库明细导入成功");
        } catch (Exception e) {
            InventoryActiveLockLogHelper.log(Tuples.of(head.getBillNo(), head.getActiveName()), InventoryActiveLockOpscodeEnum.DTL_IMPORT, "活动锁库明细导入失败");
            throw new RuntimeException(e);
        }
        return ApiResult.ok();
    }


    private ApiResult<InventoryActiveLockDtl> validateData(InventoryActiveLockDtl entry) {
        String billNo = entry.getBillNo();
        if (StringUtils.isBlank(billNo)) {
            return ApiResult.error("billNo is not found");
        }
        InventoryActiveLock find = inventoryActiveLockManager.findByUnique(billNo);
        if (find == null) {
            return ApiResult.error("数据已经被删除!");
        }

        String terminal = orderSourceTerminalConfigManager.getByLazyMerchantCode(find.getSourcePlatform())
                .get().map(OrderSourceTerminalConfig::getTerminal).orElse(null);

        Class<?> classType = OcsOrderSourceConfigChannelTypeEnum.PPF.getType().equals(terminal) ?  ExternalProductMapping.class : CommodityCorpMatchProduct.class;
        List<?> data = getData(find.getSourcePlatform(),
                entry.getItemCode(),
                entry.getBrandNo(),
                entry.getSizeNo()
                , classType);

        if (data == null || data.isEmpty()) {
            return ApiResult.error("映射表中数据不存在!");
        }


        ActiveLockStatusEnums status = ActiveLockStatusEnums.getActiveLockEnums(find.getStatus());
        if (status != ActiveLockStatusEnums.NEW_STATUS) {
            return ApiResult.error("数据已经生效中, 不能修改数据!");
        }
        String itemCode = entry.getItemCode();
        String brandNo = entry.getBrandNo();
        String sizeNo = entry.getSizeNo();
        if (StringUtils.isBlank(itemCode) || StringUtils.isBlank(brandNo) || StringUtils.isBlank(sizeNo)) {
            return ApiResult.error("商品编码,品牌,尺码不能为空");
        }


        List<OrgUnitBrandRel> orgUnitBrandRels = orgUnitBrandRelManager.selectByParams(
                new OrgUnitBrandRel().build()
                        .storeNo(entry.getStoreNo())
                        .brandNo(entry.getBrandNo())
                        .orderUnitNo(entry.getOrderUnitNo())
                        .status(1)
                        .asQuery()
        );
        if (CollectionUtils.isEmpty(orgUnitBrandRels)) {
            return ApiResult.error("机构货管品牌关系不存在");
        }

        if (orgUnitBrandRels.get(0).getStoreType() == 21) {
            return ApiResult.error("机构类型错误");
        }

        // 校验虚仓范围
        Map<String, Object> params = new HashMap<>();
        params.put("storeNo", entry.getStoreNo());
        params.put("orderUnitNo", entry.getOrderUnitNo());
        params.put("status", 1);
        params.put("vstoreCode", find.getVstoreCode());
        List<InternetVirtualWarehouseScope> scopes = internetVirtualWarehouseScopeManager.selectByParams(QueryUtil.mapToQuery(params));
        if (scopes == null || scopes.isEmpty()) {
            return ApiResult.error("虚仓["+find.getVstoreCode()+"]机构货管关系不存在");
        }


        Map<String, Object> query = new HashMap<>();
        query.put("code", itemCode);
        query.put("sizeNo", sizeNo);
        query.put("brandNo", brandNo);
        query.put("status", 1);

        List<ItemBaseInfo> itemBaseInfos = itemService.queryItemByParams(query);
        if (CollectionUtils.isEmpty(itemBaseInfos)) {
            return ApiResult.error("商品编码,品牌,尺码不存在!");
        }


        // 新增时, 需要判断数据是否存在
        if (entry.getId() == null || entry.getId().isEmpty()) {
            List<InventoryActiveLockDtl> exits =  this.manager.selectByParams(new InventoryActiveLockDtl().build()
                    .billNo(entry.getBillNo())
                    .storeNo(entry.getStoreNo())
                    .orderUnitNo(entry.getOrderUnitNo())
                    .itemCode(entry.getItemCode())
                    .brandNo(entry.getBrandNo())
                    .sizeNo(entry.getSizeNo())
                    .asQuery());

            if (CollectionUtils.isNotEmpty(exits)) {
                return ApiResult.error("数据已经存在!");
            }
        }


        entry.setItemCode(itemBaseInfos.get(0).getItemCode());
        entry.setBarcode(itemBaseInfos.get(0).getBarcode());
        entry.setSkuNo(itemBaseInfos.get(0).getSkuNo());

        return null;
    }



    private Tuple2<List<InventoryActiveLockDtl>, List<String>> readExcel(MultipartFile file, String billNo, String sourcePlatform ,String vstoreCode) {
        if (Objects.isNull(file)) {
            logger.error("文件为空");
            return null;
        }

        List<InventoryActiveLockDtl> dtlList = new ArrayList<>();
        List<String> errorList = new ArrayList<>();

        String username = Optional.ofNullable(Authorization.getUser()).map(IUser::getName).orElse("unknown");

        List<Tuple3<OrgUnitBrandRel, Integer, Boolean>> tuple3List = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream()) {
            List<Object> objectList = EasyExcelFactory.read(inputStream).sheet(0)
                    .autoTrim(true)
                    .headRowNumber(1).doReadSync();
            int rowNum =1;

            for (Object object : objectList) {
                rowNum++;
                if (!(object instanceof LinkedHashMap)) {
                    continue;
                }
                LinkedHashMap<Integer, Object> linkedHashMap = (LinkedHashMap<Integer, Object>) object;
                String storeNo = MapUtils.getString(linkedHashMap, 0, "");
                String orderUnitNo = MapUtils.getString(linkedHashMap, 1, "");
                String itemNo = MapUtils.getString(linkedHashMap, 2, "");
                String brandNo = MapUtils.getString(linkedHashMap, 3, "");
                String sizeNo = MapUtils.getString(linkedHashMap, 4, "");
                Long qty = MapUtils.getLong(linkedHashMap, 5, null);

                if (StringUtils.isBlank(storeNo) || StringUtils.isBlank(orderUnitNo)) {
                    logger.error("第{}行数据错误, 机构编码、货管编码不能为空", rowNum);
                    errorList.add("第" + rowNum + "行数据错误, 机构编码、货管编码不能为空");
                    continue;
                }

                if (storeCache.getIfPresent(storeNo) == null) {
                    Store store = storeManager.findByUnique(storeNo);
                    storeCache.put(storeNo, store);
                }
                Store store = storeCache.getIfPresent(storeNo);
                if (store == null) {
                    logger.error("第{}行数据错误, 机构编码{}不存在", rowNum, storeNo);
                    errorList.add("数据错误, 机构编码" + storeNo + "不存在");
                    continue;
                }

                if (store.getStoreType() == 21) {
                    logger.error("第{}行数据错误, 机构编码{}不是仓库", rowNum, storeNo);
                    errorList.add("机构编码" + storeNo + "不是仓库");
                    continue;
                }

                if (orderUnitCache.getIfPresent(orderUnitNo) == null) {
                    OrderUnit orderUnit = orderUnitManager.findByUnique(orderUnitNo);
                    orderUnitCache.put(orderUnitNo, orderUnit);
                }
                OrderUnit orderUnit = orderUnitCache.getIfPresent(orderUnitNo);
                if (orderUnit == null) {
                    logger.error("第{}行数据错误, 货管编码{}不存在", rowNum, orderUnitNo);
                    errorList.add("货管编码" + orderUnitNo + "不存在");
                    continue;
                }


                if (StringUtils.isBlank(itemNo) || StringUtils.isBlank(brandNo) || StringUtils.isBlank(sizeNo) || qty == null) {
                    logger.error("第{}行数据错误, 商品编码、品牌编码、规格编码不能为空", rowNum);
                    errorList.add("第" + rowNum + "行数据错误, 商品编码、品牌编码、规格编码不能为空");
                    continue;
                }

                final Integer finalRowNum = rowNum;
                boolean exits = tuple3List.stream()
                        .anyMatch(tuple3 -> Objects.equals(tuple3.getT1().getStoreNo(), storeNo)
                                && Objects.equals(tuple3.getT1().getOrderUnitNo(), orderUnitNo)
                                && Objects.equals(tuple3.getT1().getBrandNo(), brandNo));

                if (!exits) {
                    tuple3List.add(Tuples.of(new OrgUnitBrandRel().build()
                            .storeNo(storeNo)
                            .orderUnitNo(orderUnitNo)
                            .brandNo(brandNo)
                            .status(1).object(),
                            finalRowNum, false));
                }

                String terminal = orderSourceTerminalConfigManager.getByLazyMerchantCode(sourcePlatform)
                        .get().map(OrderSourceTerminalConfig::getTerminal).orElse(null);
                Class<?> classType = Objects.equals(OcsOrderSourceConfigChannelTypeEnum.PPF.getType(), terminal) ?ExternalProductMapping.class:CommodityCorpMatchProduct.class;
                List<?> data = getData( sourcePlatform, itemNo, brandNo, sizeNo, classType);



                if (data == null || data.isEmpty()) {
                    logger.error("第{}行数据不存在有效数据", rowNum);
                    errorList.add("第" + rowNum + "行数据不存在有效数据");
                    continue;
                }

                InventoryActiveLockDtl inventoryActiveLockDtl = getInventoryActiveLockDtl(billNo, data.get(0), qty);
                inventoryActiveLockDtl.setId(UUID.gernerate());
                inventoryActiveLockDtl.setBillNo(billNo);
                inventoryActiveLockDtl.setUpdateUser(username);
                inventoryActiveLockDtl.setUpdateTime(new Date());
                inventoryActiveLockDtl.setCreateTime(new Date());
                inventoryActiveLockDtl.setCreateUser(username);
                inventoryActiveLockDtl.setOrderUnitNo(orderUnit.getOrderUnitNo());
                inventoryActiveLockDtl.setOrderUnitName(orderUnit.getName());
                inventoryActiveLockDtl.setStoreNo(store.getStoreNo());
                inventoryActiveLockDtl.setStoreName(store.getShortName());

                if (dtlList.stream().anyMatch(dtl -> dtl.getUniqueKey2().equals(inventoryActiveLockDtl.getUniqueKey2()))) {
                    logger.error("第{}商品行存在重复数据，请核查后再操作", rowNum);
                    errorList.add("第" + rowNum + "商品行存在重复数据，请核查后再操作");
                    break;
                }

                dtlList.add(inventoryActiveLockDtl);
            }

        } catch (IOException e) {
            logger.error("文件读取失败", e);
            return null;
        }


        List<String> orderUnitErrors = getOrderUnitErrors(tuple3List, vstoreCode );

        if (CollectionUtil.isNotEmpty(orderUnitErrors)) {
            errorList.addAll(orderUnitErrors);
        }

        return Tuples.of(dtlList, errorList);
    }

    private <T> List<T> getData(String sourcePlatform, String itemNo, String brandNo, String sizeNo, Class<T> tClass) {

        if (tClass == ExternalProductMapping.class) {

            Map<String, Object> params = new HashMap<>();
            params.put("brandCode", brandNo);
            params.put("productCode", itemNo);
            params.put("sizeCode", sizeNo);
            params.put("merchantsCode", sourcePlatform);

            return (List<T>) externalProductMappingManager.selectItemSkuByParams(
                    params
            );
        } else if (tClass == CommodityCorpMatchProduct.class) {

              CommodityCorpMatchProduct commodityCorpMatchProductMap = new CommodityCorpMatchProduct();
                Map<String, Object> queryMap = commodityCorpMatchProductMap
                        .build().styleColorCode(itemNo)
                        .corpBrandNo(brandNo).corpSizeCode(sizeNo).asQuery().asMap();
                List<CommodityCorpMatchProduct> commodityCorpMatchProducts = commodityCorpMatchProductManager
                        .selectItemSkuByParams(queryMap);

            return (List<T>) commodityCorpMatchProducts;
        }
        return null;
    }

    private List<String> getOrderUnitErrors(List<Tuple3<OrgUnitBrandRel, Integer, Boolean>> tuple3List, String vstoreCode) {
        List<List<Tuple3<OrgUnitBrandRel, Integer, Boolean>>> split = Lists.partition(tuple3List, 5);

        List<String> orderUnitErrors = new ArrayList<>();

        CountDownLatch  countDownLatch = new CountDownLatch((int) split.stream().filter(CollectionUtil::isNotEmpty).count());
        for (List<Tuple3<OrgUnitBrandRel, Integer, Boolean>> tuple3s : split) {
            cachedThreadPool.execute(() -> {
                try {
                    for (Tuple3<OrgUnitBrandRel, Integer, Boolean> tuple3 : tuple3s) {
                        OrgUnitBrandRel orgUnitBrandRel = tuple3.getT1();

                        List<OrgUnitBrandRel> orgUnitBrandRels = orgUnitBrandRelManager.selectByParams(
                                new OrgUnitBrandRel()
                                        .build().storeNo(orgUnitBrandRel.getStoreNo())
                                        .orderUnitNo(orgUnitBrandRel.getOrderUnitNo())
                                        .brandNo(orgUnitBrandRel.getBrandNo())
                                        .status(1)
                                        .asQuery()
                        );
                        if (CollectionUtils.isEmpty(orgUnitBrandRels)) {
                            logger.error("第{}行数据错误, 机构货管品牌关系不存在", tuple3.getT2());
                            orderUnitErrors.add(String.format("机构=%s,货管=%s,品牌=%s关系不存在", orgUnitBrandRel.getStoreNo(), orgUnitBrandRel.getOrderUnitNo(), orgUnitBrandRel.getBrandNo()));
                        }

                        // 校验虚仓范围
                        Map<String, Object> params = new HashMap<>();
                        params.put("storeNo", orgUnitBrandRel.getStoreNo());
                        params.put("orderUnitNo", orgUnitBrandRel.getOrderUnitNo());
                        params.put("status", 1);
                        params.put("vstoreCode", vstoreCode);

                        List<InternetVirtualWarehouseScope> scopes = internetVirtualWarehouseScopeManager.selectByParams(QueryUtil.mapToQuery(params));
                        if (scopes == null || scopes.isEmpty()) {
                            logger.error("第{}行数据错误, 虚仓[{}]机构货管关系不存在", tuple3.getT2(), vstoreCode);
                            orderUnitErrors.add(String.format("虚仓:%s,机构:%s,货管:%s,关系不存在", vstoreCode, orgUnitBrandRel.getStoreNo(), orgUnitBrandRel.getOrderUnitNo()));
                        }
                    }

                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return orderUnitErrors;
    }

    private InventoryActiveLockDtl getInventoryActiveLockDtl(String billNo,
                                                             Object data,
                                                             Long qty) {
        if (data == null) {
            return null;
        }

        Class<?> classType = data.getClass();

        String itemCode = null, brandNo = null, sizeNo = null, barcode = null, skuNo = null;
        if (classType == CommodityCorpMatchProduct.class) {
            CommodityCorpMatchProduct commodityCorpMatchProduct = (CommodityCorpMatchProduct) data;
            itemCode = commodityCorpMatchProduct.getStyleColorCode();
            brandNo = commodityCorpMatchProduct.getCorpBrandNo();
            sizeNo = commodityCorpMatchProduct.getCorpSizeCode();
            barcode = commodityCorpMatchProduct.getInsideBarcode();
            skuNo = commodityCorpMatchProduct.getInsideBarcodeId();
        } else if (classType == ExternalProductMapping.class) {
            ExternalProductMapping externalProductMapping = (ExternalProductMapping) data;
            itemCode = externalProductMapping.getProductCode();
            brandNo = externalProductMapping.getBrandCode();
            sizeNo = externalProductMapping.getSizeCode();
            barcode = externalProductMapping.getBarcode();
            skuNo = externalProductMapping.getSkuNo();
        } else {
            throw new RuntimeException("不支持的类型");
        }
        InventoryActiveLockDtl inventoryActiveLockDtl = new InventoryActiveLockDtl();
        inventoryActiveLockDtl.setBillNo(billNo);
        inventoryActiveLockDtl.setItemCode(itemCode);
        inventoryActiveLockDtl.setBrandNo(brandNo);
        inventoryActiveLockDtl.setSizeNo(sizeNo);
        inventoryActiveLockDtl.setBarcode(barcode);
        inventoryActiveLockDtl.setLockQty(qty.intValue());
        inventoryActiveLockDtl.setBalanceLockQty(qty.intValue());
        inventoryActiveLockDtl.setSkuNo(skuNo);
        return inventoryActiveLockDtl;
    }

    @Override
    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {
        return ExcelExportOperations.builder().fieldFormater("syncStatus", (a) -> {
            if (a instanceof Integer) {
                AdjustDtlSyncStatusEnums statusEnums = AdjustDtlSyncStatusEnums.getAdjustDtlSyncStatusEnums((Integer) a);
                return statusEnums == null ? a : statusEnums.getDesc();
            }
            return a;
        }).headers(columns).value();
    }
}