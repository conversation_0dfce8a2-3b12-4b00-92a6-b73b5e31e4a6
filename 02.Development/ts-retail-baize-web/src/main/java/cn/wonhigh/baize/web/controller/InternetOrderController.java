package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Q;
import cn.mercury.basic.query.Query;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.domain.configuration.datasource.ApDataSourceSwitch;
import cn.wonhigh.baize.manager.gms.IStockDistirbutionConfigManager;
import cn.wonhigh.baize.model.entity.gms.StockDistirbutionConfig;
import cn.wonhigh.baize.model.entity.gms.VstoreStoreAvailableInventory;
import cn.wonhigh.baize.model.entity.ios.AsyncTaskCompleted;
import cn.wonhigh.baize.model.entity.ios.InternetOrder;
import cn.wonhigh.baize.model.entity.ios.InternetOrderDtl;
import cn.wonhigh.baize.model.entity.ios.RetailOrder;
import cn.wonhigh.baize.model.entity.ios.RetailOrderOutNt;
import cn.wonhigh.baize.service.gms.IInventorySearchService;
import cn.wonhigh.baize.service.gms.IStockDistirbutionConfigService;
import cn.wonhigh.baize.service.ios.IAsyncTaskCompletedService;
import cn.wonhigh.baize.service.ios.IInternetOrderDtlService;
import cn.wonhigh.baize.service.ios.IInternetOrderService;
import cn.wonhigh.baize.service.ios.IRetailOrderOutNtService;
import cn.wonhigh.baize.service.ios.IRetailOrderService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.date.DateStringConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.vo.ApiResult;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping("/internet/order")
public class InternetOrderController {
    private static final Logger logger = LoggerFactory.getLogger(InternetOrderController.class);

    @Autowired
    private IInventorySearchService iInventorySearchService;

    @Autowired
    private IRetailOrderOutNtService retailOrderOutNtService;

    @Autowired
    private IRetailOrderService retailOrderService;

    @Autowired
    private IAsyncTaskCompletedService asyncTaskCompletedService;

    @Autowired
    private IInternetOrderDtlService iInternetOrderDtlService;

    @Autowired
    private IInternetOrderService iInternetOrderService;

    @Autowired
    private IStockDistirbutionConfigService stockDistirbutionConfigService;

    @Value("${order.query.maxHours:72}")
    private int recentlyHours;


    @Value("#{'${order.query.vstoreNos:}'.split(',')}")
    private List<String> vstoreNos = new ArrayList<>();


    @PostMapping(value = "/inventory/list")
    public ApiResult<PageResult<OrderInventoryDistributionDTO>> selectByPage(
            String orderNo,
            Pagenation page) {
        return execute(() -> {
            OrderdDispatchDto dispatchInfo = getDispatchInfo(orderNo);
            Date queryTime = dispatchInfo.queryTime();
            Date maxDate = DateUtils.addHours(new Date(), -recentlyHours);
            if (queryTime.before(maxDate)) {
                throw new IllegalStateException("订单已超过" + recentlyHours + "小时");
            }

            try (ApDataSourceSwitch apDataSourceSwitch = ApDataSourceSwitch.openApOnQueryDate(queryTime)) {
                logger.info("派单查询 selectByPage [orderNo={}][page={}][dispatchInfo={}]", orderNo, page, JsonUtils.toJson(dispatchInfo));

                int count = iInventorySearchService.countVstoreQty(dispatchInfo.getSkuNos(), dispatchInfo.getVstoreCodes());
                List<VstoreStoreAvailableInventory> inventories = iInventorySearchService
                        .pageVstoreQty(dispatchInfo.getSkuNos(), dispatchInfo.getVstoreCodes(), page.getPageIndex(), page.getPageSize());

                PageResult<OrderInventoryDistributionDTO> pageResult = new PageResult<>(covert(orderNo, dispatchInfo, inventories), count);

                ApiResult<PageResult<OrderInventoryDistributionDTO>> ok = ApiResult.ok(pageResult);
                ok.setMsg(dispatchInfo.getMsg());
                return ok;
            }
        });
    }


    @PostMapping(value = "/inventory/export")
    public void export(String orderNo,
                       HttpServletResponse response) {
        try {
            OrderdDispatchDto dispatchInfo = getDispatchInfo(orderNo);
            Date queryTime = dispatchInfo.queryTime();
            Date maxDate = DateUtils.addHours(new Date(), -recentlyHours);
            if (queryTime.before(maxDate)) {
                throw new IllegalStateException("订单已超过" + recentlyHours + "小时");
            }

            try (ApDataSourceSwitch apDataSourceSwitch = ApDataSourceSwitch.openApOnQueryDate(queryTime)) {
                logger.info("派单库存 export [orderNo={}][dispatchInfo={}]", orderNo, JsonUtils.toJson(dispatchInfo));
                Stream<List<OrderInventoryDistributionDTO>> listStream = iInventorySearchService
                        .streamVstoreQty(dispatchInfo.getSkuNos(), dispatchInfo.getVstoreCodes())
                        .map(inventories -> covert(orderNo, dispatchInfo, inventories));
                export(listStream, response, "库存分布");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            responseJson(response, ApiResult.error("导出库存分布异常," + e.getMessage()));
        }
    }

    private <T> ApiResult<T> execute(Callable<ApiResult<T>> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResult.error(e.getMessage(),501);
        }
    }

    private void responseJson(HttpServletResponse response, ApiResult<?> apiResult) {
        response.reset();
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        try {
            response.getWriter().println(JsonUtils.toJson(apiResult));
        } catch (Exception ignored) {
        }
    }

    private void export(Stream<List<OrderInventoryDistributionDTO>> resultDtos,
                        HttpServletResponse response,
                        String fileName
    ) throws IOException {
        // if (CollectionUtils.isEmpty(resultDtos)) {
        //     responseJson(response, ApiResult.error("未查询到数据"));
        //     return;
        // }

        response.reset();
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), OrderInventoryDistributionDTO.class)
                .excludeColumnFiledNames(Arrays.asList("skuNo"))
                .excelType(ExcelTypeEnum.XLSX).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();

        resultDtos.forEach(
                list -> {
                    excelWriter.write(list, writeSheet);
                }
        );

        excelWriter.finish();
    }

    private List<OrderInventoryDistributionDTO> covert(String orderNo,
                                                       OrderdDispatchDto dispatchInfo,
                                                       List<VstoreStoreAvailableInventory> vstoreStoreAvailableInventories) {

        return vstoreStoreAvailableInventories.stream()
                .map(vstoreStoreAvailableInventory -> {
                    OrderInventoryDistributionDTO distributionDTO = new OrderInventoryDistributionDTO();

                    distributionDTO.setOrderNo(orderNo);
                    distributionDTO.setVstoreCode(vstoreStoreAvailableInventory.getVstoreCode());
                    distributionDTO.setBrandNo(vstoreStoreAvailableInventory.getBrandNo());
                    distributionDTO.setItemCode(vstoreStoreAvailableInventory.getItemCode());
                    distributionDTO.setSizeNo(vstoreStoreAvailableInventory.getSizeNo());
                    distributionDTO.setSkuNo(vstoreStoreAvailableInventory.getSkuNo());
                    distributionDTO.setOrderTime(dispatchInfo.getDispatchTime());
                    distributionDTO.setSendStoreNo(dispatchInfo.getSendStoreNo());
                    distributionDTO.setStoreNo(vstoreStoreAvailableInventory.getStoreNo());
                    distributionDTO.setOrderUnitNo(vstoreStoreAvailableInventory.getOrderUnitNo());
                    distributionDTO.setAvailableQty(vstoreStoreAvailableInventory.getAvailableQty());
                    distributionDTO.setUnavailableShopFlag(vstoreStoreAvailableInventory.getUnavailableShopFlag());
                    distributionDTO.setDefectiveGoodsQty(vstoreStoreAvailableInventory.getDefectiveGoodsQty());
                    distributionDTO.setWorkQty(vstoreStoreAvailableInventory.getWorkQty());
                    distributionDTO.setRefuseRecordFlag(vstoreStoreAvailableInventory.getRefuseRecordFlag());
                    distributionDTO.setCreditScore(vstoreStoreAvailableInventory.getCreditScore());

                    return distributionDTO;
                })
                .collect(Collectors.toList());
    }


    private OrderdDispatchDto getDispatchInfo(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            throw new IllegalArgumentException("订单号不能为空");
        }

        if (orderNo.indexOf("_") > -1) {
            throw new IllegalArgumentException("不支持使用拆单单号，请用通知单单号进行库存分布查询！");
        }

        RetailOrderOutNt byBillNo = retailOrderOutNtService.findByBillNo(orderNo);
        if (byBillNo != null) {
            String orderSubNo = byBillNo.getOrderSubNo();

            OrderdDispatchDto orderdDispatchDto = new OrderdDispatchDto();
            orderdDispatchDto.setSendStoreNo(byBillNo.getSendStoreNo());
            orderdDispatchDto.setDispatchTime(byBillNo.getOrderCreateTime());
            orderdDispatchDto.setSkuNos(getOrderSkuNos(orderSubNo));
            orderdDispatchDto.setVstoreCodes(getVstoreCodes(orderSubNo));
            return orderdDispatchDto;
        }


        List<RetailOrder> retailOrders = retailOrderService.selectByOrderSubNo(orderNo);

        List<String> aysncBillNos = new ArrayList<>(retailOrders.size() + 1);
        aysncBillNos.add(orderNo);
        if (!CollectionUtils.isEmpty(retailOrders)) {
            List<String> retailBillNO = retailOrders.stream().map(retailOrder -> retailOrder.getBillNo()).collect(Collectors.toList());
            aysncBillNos.addAll(retailBillNO);
        }

        AsyncTaskCompleted lastCreateByBillNos = asyncTaskCompletedService.getLastCreateByBillNos(aysncBillNos);
        if (lastCreateByBillNos == null) {
            throw new IllegalArgumentException("订单未完结");
        }

        OrderdDispatchDto orderdDispatchDto = new OrderdDispatchDto();
        orderdDispatchDto.setDispatchTime(lastCreateByBillNos.getExecuteTime());

        if (isSplitOrder(orderNo)) {
            orderdDispatchDto.setMsg("原单已拆单，点击确认按钮后仅展示拆单前库存分布！若查询拆单后库存分布，请根据通知单号查询");
        } else {
            List<RetailOrderOutNt> retailOrderOutNts = retailOrderOutNtService.selectByOrderSubNo(orderNo, Arrays.asList(5, 70, 100));
            Optional<RetailOrderOutNt> first = retailOrderOutNts.stream().findFirst();
            orderdDispatchDto.setSendStoreNo(first.isPresent() ? first.get().getSendStoreNo() : null);
        }


        orderdDispatchDto.setSkuNos(getOrderSkuNos(orderNo));
        orderdDispatchDto.setVstoreCodes(getVstoreCodes(orderNo));

        return orderdDispatchDto;
    }

    private boolean isSplitOrder(String orderSubNo){
        Query query = new Query().and("prefixOrderSubNo", orderSubNo).and("orderStatus",99);

        List<InternetOrder> internetOrders = iInternetOrderService.selectByParams(query);
        return internetOrders.size() > 0;
    }


    private List<String> getVstoreCodes(String orderSubNo) {
        InternetOrder internetOrder = iInternetOrderService.findByUnique(orderSubNo);
        String interfacePlatform = internetOrder.getInterfacePlatform();
        if (!"XCX".equalsIgnoreCase(interfacePlatform)) {
            List<String> vstores = vstoreNos.stream()
                    .filter(vstoreNo -> StringUtils.isNotBlank(vstoreNo))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(vstores)) {
                throw new IllegalStateException("未指定虚拟仓范围,apollo配置:order.query.vstoreNos");
            }
            logger.info("getVstoreCodes 非小程序虚仓取apollo配置:order.query.vstoreNos[orderSubNo={}][vstores={}]", orderSubNo,vstores);
            return vstores;
        }

        String shopNo = internetOrder.getSendStore();
        List<StockDistirbutionConfig> distirbutionConfigs = stockDistirbutionConfigService.selectByParams(Query.Where("shopNo", shopNo));
        List<String> xcxVstores = distirbutionConfigs.stream()
                .filter(config -> config.getStatus().equals(1))
                .map(StockDistirbutionConfig::getVstoreCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(xcxVstores)) {
            throw new IllegalStateException("小程序订单没有配货规则shopNo:" + shopNo);
        }
        return xcxVstores;
    }

    private List<String> getOrderSkuNos(String orderSubNo) {
        List<InternetOrderDtl> internetOrderDtls = iInternetOrderDtlService.selectByParams(Query.Where("orderSubNo", orderSubNo));
        if (CollectionUtils.isEmpty(internetOrderDtls)) {
            throw new IllegalArgumentException("订单不存在");
        }
        return internetOrderDtls.stream().map(InternetOrderDtl::getSkuNo)
                .distinct()
                .collect(Collectors.toList());
    }

    private static class OrderdDispatchDto {
        private Date dispatchTime;
        private String sendStoreNo;
        private List<String> skuNos;
        private List<String> vstoreCodes;
        private String msg;

        public Date queryTime() {
            //查询时间减1秒，防止查询时间比派单时间早
            return DateUtils.addSeconds(dispatchTime, -1);
        }

        public Date getDispatchTime() {
            return dispatchTime;
        }

        public void setDispatchTime(Date dispatchTime) {
            this.dispatchTime = dispatchTime;
        }

        public String getSendStoreNo() {
            return sendStoreNo;
        }

        public void setSendStoreNo(String sendStoreNo) {
            this.sendStoreNo = sendStoreNo;
        }

        public List<String> getSkuNos() {
            return skuNos;
        }

        public void setSkuNos(List<String> skuNos) {
            this.skuNos = skuNos;
        }

        public List<String> getVstoreCodes() {
            return vstoreCodes;
        }

        public void setVstoreCodes(List<String> vstoreCodes) {
            this.vstoreCodes = vstoreCodes;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }

    public static class OrderInventoryDistributionDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        @ExcelProperty(value = "订单编码")
        private String orderNo;

        /**
         * 聚合仓编码
         */
        @ExcelProperty(value = "虚仓编码")
        private String vstoreCode;

        /**
         * 品牌编码
         */
        @ExcelProperty(value = "品牌编码")
        private String brandNo;


        /**
         * 商品编码
         */
        @ExcelProperty(value = "货号")
        private String itemCode;

        /**
         * 尺寸编码
         */
        @ExcelProperty(value = "尺码")
        private String sizeNo;

        @ExcelProperty(value = "派单时间",converter = DateStringConverter.class)
        private Date orderTime;


        @ExcelProperty(value = "派单命中机构")
        private String sendStoreNo;


        /**
         * sku编码
         */
        private String skuNo;



        @ExcelProperty(value = "库存分布机构")
        private String storeNo;

        @ExcelProperty(value = "货管编码")
        private String orderUnitNo;

        /**
         * 可售库存
         */
        @ExcelProperty(value = "可售存")
        private int availableQty;
        /**
         * 是否保护店 1是
         */
        @ExcelProperty(value = "是否保护店", converter = IntegerToYesNoConverter.class)
        private int unavailableShopFlag;
        /**
         * 线上不可派数量
         */
        @ExcelProperty(value = "线上不可派数量")
        private int defectiveGoodsQty;
        /**
         * 门店bc品
         */
        @ExcelProperty(value = "门店bc品")
        private int workQty;
        /**
         * 是否拒单 1是
         */
        @ExcelProperty(value = "据单记录是否有效", converter = IntegerToYesNoConverter.class)
        private int refuseRecordFlag;

        /**
         * 门店信用分
         */
        @ExcelProperty(value = "门店信用分")
        private Integer creditScore;


        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getVstoreCode() {
            return vstoreCode;
        }

        public void setVstoreCode(String vstoreCode) {
            this.vstoreCode = vstoreCode;
        }

        public String getBrandNo() {
            return brandNo;
        }

        public void setBrandNo(String brandNo) {
            this.brandNo = brandNo;
        }

        public String getItemCode() {
            return itemCode;
        }

        public void setItemCode(String itemCode) {
            this.itemCode = itemCode;
        }

        public String getSizeNo() {
            return sizeNo;
        }

        public void setSizeNo(String sizeNo) {
            this.sizeNo = sizeNo;
        }

        public String getSkuNo() {
            return skuNo;
        }

        public void setSkuNo(String skuNo) {
            this.skuNo = skuNo;
        }


        public Date getOrderTime() {
            return orderTime;
        }

        public void setOrderTime(Date orderTime) {
            this.orderTime = orderTime;
        }

        public String getSendStoreNo() {
            return sendStoreNo;
        }

        public void setSendStoreNo(String sendStoreNo) {
            this.sendStoreNo = sendStoreNo;
        }

        public String getStoreNo() {
            return storeNo;
        }

        public void setStoreNo(String storeNo) {
            this.storeNo = storeNo;
        }

        public String getOrderUnitNo() {
            return orderUnitNo;
        }

        public void setOrderUnitNo(String orderUnitNo) {
            this.orderUnitNo = orderUnitNo;
        }

        public int getAvailableQty() {
            return availableQty;
        }

        public void setAvailableQty(int availableQty) {
            this.availableQty = availableQty;
        }

        public int getUnavailableShopFlag() {
            return unavailableShopFlag;
        }

        public void setUnavailableShopFlag(int unavailableShopFlag) {
            this.unavailableShopFlag = unavailableShopFlag;
        }

        public int getDefectiveGoodsQty() {
            return defectiveGoodsQty;
        }

        public void setDefectiveGoodsQty(int defectiveGoodsQty) {
            this.defectiveGoodsQty = defectiveGoodsQty;
        }

        public int getWorkQty() {
            return workQty;
        }

        public void setWorkQty(int workQty) {
            this.workQty = workQty;
        }

        public int getRefuseRecordFlag() {
            return refuseRecordFlag;
        }

        public void setRefuseRecordFlag(int refuseRecordFlag) {
            this.refuseRecordFlag = refuseRecordFlag;
        }

        public Integer getCreditScore() {
            return creditScore;
        }

        public void setCreditScore(Integer creditScore) {
            this.creditScore = creditScore;
        }
    }


    public static class IntegerToYesNoConverter implements Converter<Integer> {

        @Override
        public Class<?> supportJavaTypeKey() {
            // 支持的 Java 类型（Integer）
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            // Excel 中存储的类型（字符串）
            return CellDataTypeEnum.STRING;
        }

        /**
         * 从 Excel 读取数据时的转换（String → Integer）
         * 例如：Excel 中的 "是" → 1，"否" → 0
         */
        @Override
        public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String cellValue = cellData.getStringValue();
            if (cellValue == null || cellValue.trim().isEmpty()) {
                return 0; // 空值默认转为 0（可根据需求调整）
            }
            return "是".equals(cellValue.trim()) ? 1 : 0;
        }

        /**
         * 写入 Excel 时的转换（Integer → String）
         * 例如：Java 中的 1 → "是"，其他值 → "否"
         */
        @Override
        public CellData<String> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            if (value == null) {
                return new CellData<>("否"); // null 转为 "否"
            }
            return new CellData<>(value == 1 ? "是" : "否");
        }
    }


}