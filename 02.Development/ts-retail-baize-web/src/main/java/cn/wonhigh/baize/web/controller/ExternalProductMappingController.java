/** by  **/
package cn.wonhigh.baize.web.controller;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.RestController;
import topmall.framework.web.controller.ApiController;

import org.springframework.web.bind.annotation.RequestMapping;
import cn.mercury.manager.IManager;

import cn.wonhigh.baize.model.entity.gms.ExternalProductMapping;
import cn.wonhigh.baize.manager.gms.IExternalProductMappingManager;


@RestController

@RequestMapping("/external/product/mapping")
public class ExternalProductMappingController extends ApiController<ExternalProductMapping,String> {
    @Autowired
    private IExternalProductMappingManager manager;

    protected IManager<ExternalProductMapping,String> getManager(){
        return manager;
    }
    

    
}