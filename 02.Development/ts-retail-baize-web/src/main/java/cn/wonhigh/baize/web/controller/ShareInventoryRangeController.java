package cn.wonhigh.baize.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.mercury.annotation.JsonVariable;
import cn.mercury.basic.UUID;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.data.metadata.DataEntry;
import cn.mercury.domain.Tupe;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.functions.Function1;
import cn.mercury.manager.IManager;
import cn.mercury.manager.ManagerException;
import cn.mercury.security.IUser;
import cn.mercury.utils.JsonUtils;
import cn.wonhigh.baize.manager.gms.IShareInventoryRangeManager;
import cn.wonhigh.baize.model.entity.gms.ItemBaseInfo;
import cn.wonhigh.baize.model.entity.gms.OrderSourceVstoreConfig;
import cn.wonhigh.baize.model.entity.gms.ShareInventoryRange;
import cn.wonhigh.baize.service.gms.IOrderSourceVstoreConfigService;
import cn.wonhigh.baize.service.gms.impl.ItemService;
import cn.wonhigh.retail.uc.common.api.model.AuthorityUserVirtualWarehouse;
import cn.wonhigh.retail.uc.common.api.service.AuthorityUserDataApi;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.yougou.logistics.base.common.exception.RpcException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.support.StandardMultipartHttpServletRequest;
import springfox.documentation.annotations.ApiIgnore;
import topmall.framework.security.Authorization;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/shareInventoryRange")
public class ShareInventoryRangeController extends ApiController<ShareInventoryRange, String>  {
		@Resource
		private IShareInventoryRangeManager shareInventoryRangeManager;
		@Reference
		private AuthorityUserDataApi authorityUserDataApi;
		@Resource
		private ItemService itemService;
		/**
		 * 集仓库存范围限制数量
		 */
		@Value("${jc.rang.limit:5000}")
		private int jcRangLimit;
		@Resource
		private IOrderSourceVstoreConfigService orderSourceVstoreConfigService;

		@Override
		protected IManager<ShareInventoryRange, String> getManager() {
			return shareInventoryRangeManager;
		}


		@ResponseBody
		@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/list")
		@Override
		public ApiResult selectByPage(Query query, Pagenation page) {
			//查询虚仓权限
			Map<String, String> infosMap = new HashMap<>();
			ApiResult result = getAuthorityUserVirtualWarehouse(query,infosMap);
			if(result!=null){
				return result;
			}

			//查询页面数据
			String errorMsg = getPageResultApiResult(query);
			if(StrUtil.isNotBlank(errorMsg)){
				return ApiResult.error(errorMsg);
			}
			long total = page.getTotal();
			if (total <= 0) {
				total = shareInventoryRangeManager.selectCount(query);
			}
			if (total == 0)
				return ApiResult.ok(new PageResult<DataEntry>(null, total));
			List<ShareInventoryRange> rows = shareInventoryRangeManager.selectByPage(query, page);
			if (rows != null && !rows.isEmpty() ) {
				for (ShareInventoryRange range : rows) {
					range.setVstoreName(infosMap.get(range.getInterfacePlatform()));
				}
			}
			List<DataEntry> rowsDataEntry = Opt.ofEmptyAble(rows)
					.orElse(new ArrayList<>(0))
					.stream().map(item -> {
						DataEntry dataEntry = new DataEntry();
						dataEntry.putAll(BeanUtil.beanToMap(item));
						return dataEntry;

					}).collect(Collectors.toList());
			return ApiResult.ok(new PageResult<>(rowsDataEntry, total));
		}

	public ApiResult getAuthorityUserVirtualWarehouse(Query query,Map<String, String> infosMap){
			List<AuthorityUserVirtualWarehouse> authorityUserVirtualWarehouses = new ArrayList<>();
			Integer userId = Integer.parseInt(Authorization.getUser().getId());
			try {
				authorityUserVirtualWarehouses = authorityUserDataApi.userVirtualWarehouse(userId);
			} catch (Exception e1) {
				return ApiResult.error("根据userId查询聚合仓权限接口异常");
			}
			if(authorityUserVirtualWarehouses == null || CollectionUtils.isEmpty(authorityUserVirtualWarehouses)) {// 没有虚仓权限
				return ApiResult.ok(new PageResult<DataEntry>(null, 0));
			}
			List<String> vstoreCodeList = new ArrayList<>();
			for (AuthorityUserVirtualWarehouse authorityUserVirtualWarehouse : authorityUserVirtualWarehouses) {
				infosMap.put(authorityUserVirtualWarehouse.getVstoreCode(), authorityUserVirtualWarehouse.getVstoreName());
				vstoreCodeList.add(authorityUserVirtualWarehouse.getVstoreCode());
			}
			query.and("vstoreCodeList", vstoreCodeList);
			query.and("organTypeNo", Authorization.getUser().getOrganTypeNo());
			return null;
		}

		private static String getPageResultApiResult(Query query) {
			String barcode = query.findValue("barcode"),
					sharingRatio = query.findValue("sharingRatio"),
					brandNo = query.findValue("brandNo");
			if (StrUtil.isNotBlank(barcode)) {
				List<String> barcodeList = StrUtil.split(barcode, ",");
				if (barcodeList.size() > 100) {
					return "条码太长了";
				}
				query.and("barcodeList", barcodeList);
				query.and("barcode",null);
			}
			if (StrUtil.isNotBlank(sharingRatio)) {
				if (sharingRatio.length() > 3) {
					return "比例太长了";
				}
			}
			if (StrUtil.isNotBlank(brandNo)) {
				List<String> brandNoList = StrUtil.split(brandNo, ",");
				if (brandNoList.size() > 200) {
					return "品牌太多了";
				}
				query.and("brandNoList", brandNoList);
				query.and("brandNo",null);
			}
			return null;
		}

		@ResponseBody
		@RequestMapping(method = RequestMethod.POST, value = "/export")
		@ApiIgnore
		public ApiResult<Tupe<String, Integer>> export(Query query, String fileName,
													   @JsonVariable("_columns") ExcelColumn[][] columns, HttpServletResponse response) {
			String errorMsg = getPageResultApiResult(query);
			if(StrUtil.isNotBlank(errorMsg)){
				return ApiResult.error(errorMsg);
			}
			//查询虚仓权限
			Map<String, String> infosMap = new HashMap<>();
			ApiResult result = getAuthorityUserVirtualWarehouse(query,infosMap);
			if(result!=null){
				return result;
			}
			query.and("infosMap",infosMap);
			Integer count = fetchExportRowCount(query);
			if (query.getPagenation() == null) {
				query.setPagenation(new Pagenation(1, Integer.MAX_VALUE));
			}
			query.getPagenation().setTotal(count);
			String ticket = export(columns, fileName, query);
			return ApiResult.ok(new Tupe<String, Integer>(ticket, count));
		}

	protected Function1<ShareInventoryRange, Object> getRowHander(Query query) {
		return (row) -> handerData(row,query.findValue("infosMap"));
	}
	private Object handerData(ShareInventoryRange row,Map<String, String> infosMap) {
		row.setVstoreName(infosMap.get(row.getInterfacePlatform()));
		if(new Date().getTime()<row.getStartTime().getTime()){
			row.setStatus("未生效");
		}else if(new Date().getTime()>row.getStartTime().getTime()&&new Date().getTime()<row.getEndTime().getTime()){
			row.setStatus("生效中");
		}else if(new Date().getTime()>row.getEndTime().getTime()){
			row.setStatus("已失效");
		}
		return row;
	}

	@RequestMapping("/import")
	@ResponseBody
	public ApiResult<?> importData(HttpServletRequest request, HttpServletResponse response) {
		// 1、查询虚仓权限
		List<AuthorityUserVirtualWarehouse> authorityUserVirtualWarehouses;
		int userId = Integer.parseInt(Authorization.getUser().getId());
		try {
			authorityUserVirtualWarehouses = authorityUserDataApi.userVirtualWarehouse(userId);
		} catch (RpcException e1) {
			logger.error("根据userId查询聚合仓权限接口异常" + e1.getMessage(), e1);
			return ApiResult.error("根据userId查询聚合仓权限接口异常");
		}
		if (ObjectUtil.isEmpty(authorityUserVirtualWarehouses)) {
			return ApiResult.error("没有配置聚合仓的数据权限");
		}

		//2、文件非空判断
		StandardMultipartHttpServletRequest multipartHttpServletRequest = null;
		if (request instanceof StandardMultipartHttpServletRequest) {
			multipartHttpServletRequest = (StandardMultipartHttpServletRequest) request;
		}
		if (multipartHttpServletRequest == null) {
			return ApiResult.error("File is not found");
		}
		MultipartFile multipartFile = multipartHttpServletRequest.getFile("excelFile");
		if (multipartFile == null) {
			return ApiResult.error("File is not found");
		}

		try {
			List<Object> objectList = EasyExcel.read(multipartFile.getInputStream())
					.autoTrim(true)
					.headRowNumber(1)
					.autoCloseStream(true).doReadAllSync();
			if (objectList == null) {
				return ApiResult.error("File is null");
			}
			if(objectList.size()>2000){
				return ApiResult.error("导入数据不能超过2000行！");
			}
			//3、转换数据
			List<ShareInventoryRange> list = convertData(objectList);
			//4、校验基本数据
			String errorMsg = validateData(list,authorityUserVirtualWarehouses);
			if(StrUtil.isNotBlank(errorMsg)){
				return ApiResult.error(errorMsg);
			}
 		} catch (Exception e) {
			return ApiResult.error(e.getMessage());
		}
		return ApiResult.ok();
	}

	private String validateData(List<ShareInventoryRange> list,List<AuthorityUserVirtualWarehouse> authorityUserVirtualWarehouses) throws Exception{
		List<String> vscodeList = authorityUserVirtualWarehouses.stream().map(object->object.getVstoreCode()).collect(Collectors.toList());
		logger.info("此用户拥有的聚合仓编码权限有：{}", JsonUtils.toJson(vscodeList));
		List<ShareInventoryRange> addList = new ArrayList<>();
		List<ShareInventoryRange> updateList = new ArrayList<>();
		for(ShareInventoryRange obj:list){
			//1、校验数据和封装数据
			String itemCode = obj.getItemCode();
			String brandNo = obj.getBrandNo();
			String sizeNo = obj.getSizeNo();
			Integer sharingRatio = obj.getSharingRatio();
			Integer safetyStock = obj.getSafetyStock();
			Date startTime = obj.getStartTime();
			Date endTime = obj.getEndTime();
			String interfacePlatform = obj.getInterfacePlatform();
			Integer rowIndex = obj.getRowIndex();
			if (!ObjectUtil.isAllNotEmpty(itemCode, brandNo,sharingRatio, safetyStock, startTime, endTime,interfacePlatform)) {
				throw new ManagerException("Excel中,第"+rowIndex+"行,有必填字段未填写.");
			}
			StringBuilder builder = new StringBuilder();
			if (sharingRatio < 0 || sharingRatio > 100 || safetyStock < 0) {
				builder.append("共享比例:").append(sharingRatio).append(",只可以是:0-100或者安全库存:").append(safetyStock).append(",只可以大于0");
			}else if (DateUtil.date(startTime).isAfter(endTime)) {
				builder.append(",开始时间必须大于结束时间");
			}else if(!vscodeList.contains(interfacePlatform)){
				builder.append(",聚合仓编码没有权限");
			}
			List<ItemBaseInfo> itemBaseInfoList = null;
			Map<String, Object> map = new HashMap<>();
			map.put("code", itemCode);
			map.put("brandNo", brandNo);
			if (StrUtil.isNotBlank(sizeNo)&&!StrUtil.isNullOrUndefined(sizeNo)) {
				map.put("sizeNo",sizeNo);
			}
			itemBaseInfoList = itemService.queryItemByParams(map);
			if (ObjectUtil.isEmpty(itemBaseInfoList)) {
				builder.append(",未发现商品, itemCode="+itemCode+", brandNo="+brandNo+",sizeNo:"+sizeNo);
			}
			if(!StrUtil.isEmpty(builder.toString())){
				throw new Exception("Excel中,第"+rowIndex+"行"+builder);
			}
			for (ItemBaseInfo itemBaseInfo : itemBaseInfoList) {
				map.put("sizeNo",itemBaseInfo.getSizeNo());
				map.put("interfacePlatform",interfacePlatform);
				Integer count = shareInventoryRangeManager.findExistInBrandInventoryRange(map);
				if(count==0){
					throw new Exception("商品:itemCode="+itemCode+", brandNo="+brandNo+",sizeNo:"+sizeNo+",聚合仓:"+interfacePlatform+"没有在品季年里面配置");
				}
				Query query = new Query();
				query.and("itemCode", itemBaseInfo.getItemCode()).and("sizeNo", itemBaseInfo.getSizeNo())
						.and("brandNo", itemBaseInfo.getBrandNo()).and("interfacePlatform", interfacePlatform);
				List<ShareInventoryRange> existList = shareInventoryRangeManager.selectByParams(query);
				if (!CollectionUtils.isEmpty(existList)) {
					obj.setId(existList.get(0).getId());
					obj.setUpdateTime(new Date());
					updateList.add(obj);
				} else {
					addList.add(buildAddShareInventoryRangeData(obj,itemBaseInfo));
				}
			}
		}
		//2、校验表格重复性
		Map<String, Long> excelData =  list.stream()
				.collect(Collectors.groupingBy( i -> StrUtil.join("-",i.getItemCode(),i.getBrandNo(),i.getSizeNo(),i.getInterfacePlatform()),
						Collectors.counting()));
		for(String key:excelData.keySet()){
			Long value = excelData.get(key);
			if(value>1){
				return "Excel中商品:"+key+"有重复数据";
			}
		}

		//3、校验集仓数据
		String errorMsg = validateInventoryJCSetting(list);
		if(StrUtil.isNotBlank(errorMsg)){
			return errorMsg;
		}

		//4、插入数据
		if(!CollectionUtils.isEmpty(addList)){
			List<List<ShareInventoryRange>> addListPartition = Lists.partition(addList,10);
			for(List<ShareInventoryRange> objList:addListPartition){
				shareInventoryRangeManager.batchInsert(objList);
			}
		}
		if(!CollectionUtils.isEmpty(updateList)){
			List<List<ShareInventoryRange>> updateListPartition = Lists.partition(updateList,10);
			for(List<ShareInventoryRange> objList:updateListPartition){
				shareInventoryRangeManager.batchUpdate(objList);
			}
		}
		return "";
	}

	private String validateInventoryJCSetting (List<ShareInventoryRange> list) {
		//1、虚仓编码集合
		List<String> vstoreCodeList = new ArrayList<>();
		for (ShareInventoryRange object : list) {
			String interfacePlatform = object.getInterfacePlatform();
			if(StrUtil.isEmpty(interfacePlatform)||StrUtil.isNullOrUndefined(interfacePlatform)){
				return StrUtil.format("Excel中,第"+object.getRowIndex()+"行聚合仓编码为空");
			}
			vstoreCodeList.add(interfacePlatform);
		}
		//2、虚仓编码转换和计算
		List<String> vstoreCodeSet = vstoreCodeList.stream().distinct().collect(Collectors.toList());//虚仓编码去重
		Map<String, Long> vstoreCodeMap = vstoreCodeList.stream().collect(Collectors.groupingBy(object->object,Collectors.counting()));
		for (String vstoreCode : vstoreCodeSet) {
			List<OrderSourceVstoreConfig> shopSettingDtos;
			try {
				// 根据导入的虚仓编码查询4 代表集仓, 查不到继续
				shopSettingDtos = orderSourceVstoreConfigService.selectByParams(new Query().and("vstoreCode", vstoreCode).and("groups", 4));
			} catch (Exception e) {
				return StrUtil.format("查询集仓配置失败, 编码:" + vstoreCode);
			}
			if (ObjectUtil.isEmpty(shopSettingDtos)) {
				continue;
			}
			long count = MapUtil.getLong(vstoreCodeMap, vstoreCode);
			if (count > jcRangLimit) {
				return StrUtil.format("此集仓[{}], 配置数据超过了数量[{}]限制, 导入数量为:{}", vstoreCode, jcRangLimit, count);
			}
			int countBss = shareInventoryRangeManager.selectCount(new Query().and("interfacePlatform", vstoreCode));
			if (countBss > jcRangLimit) {
				return StrUtil.format("此集仓[{}], 数据库中配置数据超过了数量[{}]限制, 导入数量为:{}", vstoreCode, jcRangLimit, count);
			}
			if (countBss + count > jcRangLimit) {
				return StrUtil.format("此集仓[{}], 导入数据 + 数据库中配置数据超过了数量[{}]限制, 导入数量为:{}, 数据库数量为:{}", vstoreCode, jcRangLimit, count, countBss);
			}
		}
		return "";
	}

	private List<ShareInventoryRange> convertData(List<Object> excelData) throws ManagerException {
		int rowIndex = 1;
		List<ShareInventoryRange> list = new ArrayList<>();
		for (Object excelDatum : excelData) {
			rowIndex++;
			if (excelDatum instanceof LinkedHashMap) {
				//1、转换数据
				LinkedHashMap linkedHashMap = (LinkedHashMap) excelDatum;
				String itemCode = Convert.toStr(linkedHashMap.get(0));
				String brandNo = Convert.toStr(linkedHashMap.get(1));
				String sizeNo = Convert.toStr(linkedHashMap.get(2));
				Integer sharingRatio = Convert.toInt(linkedHashMap.get(3));
				Integer safetyStock = Convert.toInt(linkedHashMap.get(4));
				Date startTime = Convert.toDate(linkedHashMap.get(5));
				Date endTime = Convert.toDate(linkedHashMap.get(6));
				String interfacePlatform = Convert.toStr(linkedHashMap.get(7));
				String userName = Optional.ofNullable(Authorization.getUser()).map(IUser::getAccount).orElse("unknown");
				ShareInventoryRange shareInventoryRange = new ShareInventoryRange();
				shareInventoryRange.setItemCode(itemCode);
				shareInventoryRange.setBrandNo(brandNo);
				shareInventoryRange.setSizeNo(sizeNo);
				shareInventoryRange.setSharingRatio(sharingRatio);
				shareInventoryRange.setSafetyStock(safetyStock);
				shareInventoryRange.setStartTime(startTime);
				shareInventoryRange.setEndTime(endTime);
				shareInventoryRange.setInterfacePlatform(interfacePlatform);
				shareInventoryRange.setCreateUser(userName);
				shareInventoryRange.setUpdateUser(userName);
				shareInventoryRange.setRowIndex(rowIndex);
				list.add(shareInventoryRange);
			}
		}
		return list;
	}

	private ShareInventoryRange buildAddShareInventoryRangeData(ShareInventoryRange shareInventoryRange, ItemBaseInfo itemBaseInfo) {
		shareInventoryRange.setId(UUID.gernerate());
		shareInventoryRange.setItemNo(itemBaseInfo.getItemNo());
		shareInventoryRange.setItemName(itemBaseInfo.getItemName());
		shareInventoryRange.setBrandName(itemBaseInfo.getBrandName());
		shareInventoryRange.setCreateTime(new Date());
		shareInventoryRange.setUpdateTime(new Date());
		shareInventoryRange.setSizeKind(itemBaseInfo.getSizeKind());
		shareInventoryRange.setOrganTypeName(itemBaseInfo.getOrganTypeName());
		shareInventoryRange.setOrganTypeNo(itemBaseInfo.getOrganTypeNo());
		shareInventoryRange.setBarcode(itemBaseInfo.getBarcode());
		shareInventoryRange.setSkuNo(itemBaseInfo.getSkuNo());
		shareInventoryRange.setSizeNo(itemBaseInfo.getSizeNo());
		return shareInventoryRange;
	}


}
