/** by  **/
package cn.wonhigh.baize.web.controller;

import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.excel.ExcelExportOperations;
import cn.wonhigh.baize.business.virtualwarehousescope.ValidateResult;
import cn.wonhigh.baize.manager.gms.IInternetAreaRelationManager;
import cn.wonhigh.baize.manager.gms.IZoneInfoManager;
import cn.wonhigh.baize.model.dto.OrderSourceTerminalConfigSaveDto;
import cn.wonhigh.baize.model.entity.gms.InternetAreaRelation;
import cn.wonhigh.baize.model.entity.gms.ZoneInfo;
import cn.wonhigh.baize.model.enums.ManagerZoneEnum;
import cn.wonhigh.baize.model.enums.PlatformEnum;
import cn.wonhigh.baize.model.enums.SecondPlatformEnum;
import cn.wonhigh.baize.model.validate.CreateGroup;
import cn.wonhigh.baize.model.validate.EditGroup;
import cn.wonhigh.baize.utils.common.ValidationUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import topmall.framework.web.controller.ApiController;

import cn.mercury.manager.IManager;


import cn.wonhigh.baize.model.entity.gms.OrderSourceTerminalConfig;
import cn.wonhigh.baize.manager.gms.IOrderSourceTerminalConfigManager;

import topmall.framework.web.vo.ApiResult;

import java.util.*;

@RestController
@RequestMapping("/order/source/terminal/config")
public class OrderSourceTerminalConfigController extends ApiController<OrderSourceTerminalConfig, String> {
    @Autowired
    private IOrderSourceTerminalConfigManager manager;

    @Autowired
    private IInternetAreaRelationManager internetAreaRelationManager;

    @Autowired
    private IZoneInfoManager zoneInfoManager;

    //省市区缓存
    private static final Map<String, String> areaMap = new HashMap<String, String>();
    //经营区域缓存
    private static final Map<String, String> zoneMap = new HashMap<String, String>();

    protected IManager<OrderSourceTerminalConfig, String> getManager() {
        return manager;
    }

    @Override
    public ApiResult<PageResult<OrderSourceTerminalConfig>> selectByPage(Query query, Pagenation page) {
        query.orderby("update_time", Boolean.TRUE);
        ApiResult<PageResult<OrderSourceTerminalConfig>> result = super.selectByPage(query, page);
        if (result.getData().getTotal() == 0) {
            result.getData().setRows(new ArrayList<>());
        } else {
            initMap();
            for (OrderSourceTerminalConfig row : result.getData().getRows()) {
                if(StringUtils.isNotEmpty(row.getProvinceNo())){
                    row.setProvinceName(areaMap.get(row.getProvinceNo()));
                }
                if(StringUtils.isNotEmpty(row.getCityNo())){
                    row.setCityName(areaMap.get(row.getCityNo()));
                }
                if(StringUtils.isNotEmpty(row.getZoneNo())){
                    row.setZoneName(zoneMap.get(row.getZoneNo()));
                }
            }
        }
        return result;
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/saveData")
    public ApiResult<OrderSourceTerminalConfig> createData(OrderSourceTerminalConfigSaveDto data) {

        Class<?> [] groups = null;
        if(data.getId() == null || data.getId().trim().isEmpty()){
            groups = new Class[]{CreateGroup.class};
        } else {
            groups = new Class[]{EditGroup.class};
        }

        ValidateResult validateResult = ValidationUtil.validateByGroup(data, groups);

        if(!validateResult.isSuccess()){
            return ApiResult.error(validateResult.getMsg());
        }

        OrderSourceTerminalConfig config = new OrderSourceTerminalConfig();
        BeanUtils.copyProperties(data, config);

        manager.insert(config);
        return ApiResult.ok(null);
    }

    String validateDate(OrderSourceTerminalConfig data){
        if(StringUtils.isEmpty(data.getPlatform())){
            return "一级来源不能为空";
        }
        if(StringUtils.isEmpty(data.getSecondPlatform())){
            return "二级来源不能为空";
        }
        if(StringUtils.isEmpty(data.getTerminal())){
            return "所属渠道不能为空";
        }
        if(StringUtils.isEmpty(data.getThirdPlatform())){
            return "店铺编码";
        }
        if(data.getThirdPlatform().length() > 10){
            return "店铺编码不能超过10字";
        }
        if(StringUtils.isEmpty(data.getThirdPlatformName())){
            return "店铺名称";
        }
        if(data.getThirdPlatformName().length() > 32){
            return "店铺名称不能超过32字";
        }
        data.setThirdPlatform(data.getThirdPlatform().trim());
        data.setThirdPlatformName(data.getThirdPlatformName().trim());
        if(StringUtils.isNotEmpty(data.getRemark())){
            if(data.getRemark().length() > 200){
                return "备注不能超过200字";
            }
            data.setRemark(data.getRemark().trim());
        }
        return "";
    }

    /**
     * 获取一级来源枚举
     * @return
     */
    @PostMapping("/listPlatformEnums")
    public ApiResult<?> listPlatformEnums() {
        List<PlatformEnum> list = new ArrayList<>(Arrays.asList(PlatformEnum.values()));
        return ApiResult.ok(list);
    }

    /**
     * 获取二级来源枚举
     * @return
     */
    @PostMapping("/listSecondPlatformEnums")
    public ApiResult<?> listSecondPlatformEnums() {
        List<SecondPlatformEnum> list = new ArrayList<>(Arrays.asList(SecondPlatformEnum.values()));
        return ApiResult.ok(list);
    }

    /**
     * 初始化缓存数据
     * @return
     */
    private void initMap() {
        try {
            if (areaMap.isEmpty()) {
                Query query = Query.Where("status",1);
                List<InternetAreaRelation> areaRelations = internetAreaRelationManager.selectByRetailCodeName(query.asMap());
                if (!CollectionUtils.isEmpty(areaRelations)) {
                    for (InternetAreaRelation areaRelation : areaRelations) {
                        if (!areaMap.containsKey(areaRelation.getRetailCode())) {
                            areaMap.put(areaRelation.getRetailCode(), areaRelation.getRetailName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("初始化优购与新零售地区关系表数据异常", e);
        }

        try {
            if (zoneMap.isEmpty()) {
                List<ZoneInfo> zoneInfos = zoneInfoManager.selectByParams(Query.empty());
                if (!CollectionUtils.isEmpty(zoneInfos)) {
                    for (ZoneInfo item : zoneInfos) {
                        if (!zoneMap.containsKey(item.getZoneNo())) {
                            zoneMap.put(item.getZoneNo(), item.getName());
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("初始化经营区域信息表数据异常", e);
        }
    }

    @Override
    protected ExcelExportOperations getExportOperation(ExcelColumn[][] columns) {
        for (ExcelColumn[] column : columns) {
            for (ExcelColumn excelColumn : column) {
                if (Objects.equals(excelColumn.getField(), "cityName")) {
                    excelColumn.setField("cityNo");
                }
                if (Objects.equals(excelColumn.getField(), "zoneName")) {
                    excelColumn.setField("zoneNo");
                }
                if (Objects.equals(excelColumn.getField(), "managerZoneName")) {
                    excelColumn.setField("managerZoneNo");
                }
            }
        }
        return ExcelExportOperations.builder()
                .fieldFormater("cityNo",  (a) -> {
                    return a == null ? "" : areaMap.get(a.toString());
                })
                .fieldFormater("zoneNo",  (a) -> {
                    return a == null ? "" : zoneMap.get(a.toString());
                })
                .fieldFormater("managerZoneNo",  (a) -> {
                    return a == null ? "" : ManagerZoneEnum.getName(a.toString());
                })
                .headers(columns)
                .value();
    }
}