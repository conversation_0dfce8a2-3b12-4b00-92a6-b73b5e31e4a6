package cn.wonhigh.baize.web.configuration.exception;

import cn.mercury.manager.ManagerException;
import com.yougou.logistics.base.common.exception.RpcException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import topmall.framework.web.vo.ApiResult;


/**
 * <AUTHOR>
 */
@RestControllerAdvice
public class DefaultExceptionHandler {

    public static final Logger LOGGER = LoggerFactory.getLogger(DefaultExceptionHandler.class);
    @ExceptionHandler(value = {ManagerException.class,com.yougou.logistics.base.common.exception.ManagerException.class})
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<?> managerException (ManagerException e) {
        LOGGER.error(e.getMessage(), e);
        return ApiResult.error(e.getMessage());
    }

    @ExceptionHandler(value = {RpcException.class , com.alibaba.dubbo.rpc.RpcException.class})
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<?> dubboRpcException (Exception e) {
        LOGGER.error(e.getMessage(), e);
        return ApiResult.error("系统远程调用失败");
    }



    @ExceptionHandler(value = {Exception.class})
    @ResponseStatus(HttpStatus.OK)
    public ApiResult<?> exception (Exception e) {
        LOGGER.error(e.getMessage(), e);
        return ApiResult.error("系统错误,请联系管理员");
    }



}
