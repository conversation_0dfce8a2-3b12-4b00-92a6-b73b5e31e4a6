package cn.wonhigh.baize.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Opt;
import cn.mercury.annotation.JsonVariable;
import cn.mercury.basic.query.PageResult;
import cn.mercury.basic.query.Pagenation;
import cn.mercury.basic.query.Query;
import cn.mercury.data.metadata.DataEntry;
import cn.mercury.domain.Tupe;
import cn.mercury.excel.ExcelColumn;
import cn.mercury.functions.Function1;
import cn.mercury.manager.IManager;
import cn.wonhigh.baize.manager.gms.ICommodityCorpMatchProductManager;
import cn.wonhigh.baize.model.entity.gms.CommodityCorpMatchProduct;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;
import topmall.framework.web.controller.ApiController;
import topmall.framework.web.vo.ApiResult;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/commodityCorpMatchProduct")
public class CommodityCorpMatchProductController extends ApiController<CommodityCorpMatchProduct, String>  {
		@Resource
		private ICommodityCorpMatchProductManager commodityCorpMatchProductManager;
		@Override
		protected IManager<CommodityCorpMatchProduct, String> getManager() {
			return commodityCorpMatchProductManager;
		}

		@ResponseBody
		@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST}, value = "/list")
		@Override
		public ApiResult selectByPage(Query query, Pagenation page) {
			long total = page.getTotal();
			if (total <= 0) {
				total = commodityCorpMatchProductManager.selectCount(query);
			}
			if (total == 0)
				return ApiResult.ok(new PageResult<DataEntry>(null, total));
			List<CommodityCorpMatchProduct> rows = commodityCorpMatchProductManager.selectByPage(query, page);
			List<DataEntry> rowsDataEntry = Opt.ofEmptyAble(rows)
					.orElse(new ArrayList<>(0))
					.stream().map(item -> {
						DataEntry dataEntry = new DataEntry();
						dataEntry.putAll(BeanUtil.beanToMap(item));
						return dataEntry;

					}).collect(Collectors.toList());
			return ApiResult.ok(new PageResult<>(rowsDataEntry, total));
		}

		@ResponseBody
		@RequestMapping(method = RequestMethod.POST, value = "/export")
		@ApiIgnore
		public ApiResult<Tupe<String, Integer>> export(Query query, String fileName,
													   @JsonVariable("_columns") ExcelColumn[][] columns, HttpServletResponse response) {
			Integer count = fetchExportRowCount(query);
			if (query.getPagenation() == null) {
				query.setPagenation(new Pagenation(1, Integer.MAX_VALUE));
			}
			query.getPagenation().setTotal(count);
			String ticket = export(columns, fileName, query);
			return ApiResult.ok(new Tupe<String, Integer>(ticket, count));
		}

	protected Function1<CommodityCorpMatchProduct, Object> getRowHander(Query query) {
		return (row) -> handerData(row);
	}
	private Object handerData(CommodityCorpMatchProduct row) {
		row.setIsSyncInvName(row.getIsSyncInv()==1?"可通":"不可通");
		return row;
	}


}
