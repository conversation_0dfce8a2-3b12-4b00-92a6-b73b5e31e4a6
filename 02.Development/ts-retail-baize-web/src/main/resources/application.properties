# PROFILES
## dev | prod | test
spring.profiles.active=default,dev
server.session.cookie.path=/
server.context-path=/baize-api

topmall.app.name=ts-retail-baize-web

# apollo
app.id=ts-retail-baize
apollo.cluster=default
apollo.meta=http://apollo-test.tstech.top:18082
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=web,application,retail.public
apollo.bootstrap.eagerLoad.enabled=true

# swagger
swagger.title=ts-retail-baize
swagger.description=ts-retail-baize api docs
swagger.version=1.0
swagger.packages=cn.wonhigh.baize.web.controller
swagger.protocols=http

# dubbo
dubbo.annotation.package=topmall.framework.servicemodel,topmall.framework.web.security,cn.wonhigh.baize.web.controller,cn.wonhigh.baize.manager.impl,cn.wonhigh.retail.iis.api.service
db.server.auto=false
db.server=